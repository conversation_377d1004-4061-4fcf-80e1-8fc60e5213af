window.viewStaffData = null;
var layout_SiteReady = function () {
  var staffId,
    staffTelNum,
    companyId,
    stafflist_el,
    stafflist_panel,
    staffMobileNum;

  function layout_SetRnssLanguage() {
    var panel = $("#r_sitenav");
    panel.find("[name=r_sitenav_language]").change(function () {
      Cookies.set('Language', $(this).val(), { expires: 365 });
      window.location.reload();
    });
  }
  function layout_SetSiteNavStatus() {
    var _cookie = Cookies.get('r_sitenav_issimple');
    var obj = $("[name=r_sitenav_issimple]");
    obj.on("change", function () {
      var val = $(this).val();
      Cookies.set('r_sitenav_issimple', val, { expires: 365 });
      $(".menu-toggler.sidebar-toggler").trigger("click");
    });
  }
  function layout_SearchUserStaff() {
    var btn = $(".index-searchgroup-staff .btn"),
      txt = $(".index-searchgroup-staff .form-control"),
      listpanel = $(".index-searchgroup-list");
    var list = listpanel.find(".index-sg-items");
    txt.keydown(function (e) {
      var key = e || event;
      var currkey = key.keyCode || key.charCode;
      if (currkey == 13) {
        btn.triggerHandler("click");
        return false;
      }
      key.stopPropagation();
    });
    btn.click(function () {
      var val = $.trim(txt.val());
      if (val.length > 0) {
        //GetCompanyStaffsByKeyWords
        listpanel.show();
        getCompanySatff(list, val);
      }
    });
    function getCompanySatff(el, keywords) {
      ajaxPost3("/home/<USER>", { keywords: keywords }, 'html', function (result) {
        el.html(result);
      }, function (xhr, et) {
        var msg = setRnssLanguage("抱歉，出现错误，错误信息: ") + xhr.status + " " + xhr.statusText;
        if (el.find(".nodata").length <= 0) {
          el.append('<li class="nodata">' + msg + '</li>');
        }
        else {
          el.find(".nodata").text(msg);
        }
      });
    }
  }
  function layout_LoadStaffTask(staffid, top) {
    var panel = $("#header_task_bar"), list = panel.find(".ms2_content_box");
    ajaxPost3("/home/<USER>", { staffId: staffid, top: top || 10 }, 'html', function (result) {
      if (!result || !result.Status) return;
      if (typeof result == "string") {
        result = JSON.parse(result);
      }
      if (result.Status == 1) {
        var recordCount = result.RecordCount;
        panel.find(".badge.badge-primary").text(recordCount);
        panel.find(".bold").text(recordCount);
        list.html(buildTaskData(result.Data));
      }
      else {
        layout_SetRequestFailed(list, result.Message);
      }
    }, function (xhr, et) {
      var msg = setRnssLanguage("抱歉，出现错误，错误信息: ") + xhr.status + " " + xhr.statusText;
      layout_SetRequestFailed(list, msg);
    });
    function buildTaskData(data) {
      var html = '';
      $.each(data, function (i, n) {
        html += '<li title="' + setRnssLanguage("您设定的提醒日期：") + n.TaskTime + '"><a target="_blank" href="' + n.Href + '" data-id="' + n.Id + '" class="ms-link">';
        html += '<span class="task"><span class="desc">' + n.Title + ' </span><span class="percent">' + setRnssLanguage("剩余时间：") + (100 - n.Progress) + '%</span></span>';
        html += '<div>' + n.Content + '</div>';
        html += '<span class="progress"><span style="width: ' + n.Progress + '%;" class="progress-bar ' + n.CssByProgress + '" aria-valuenow="' + n.Progress + '" aria-valuemin="0" aria-valuemax="100"><span class="sr-only">' + setRnssLanguage("已用时：") + n.Progress + '%</span></span></span>';
        html += '</a></li>';
      });
      return html;
    }
  }
  function layout_LoadStaffMessage(staffid, top) {
    var panel = $("#header_inbox_bar"), list = panel.find(".ms_content_box");
    ajaxPost3("/home/<USER>", { staffId: staffid, top: top || 10 }, 'html', function (result) {
      if (typeof result == "string") {
        result = JSON.parse(result);
      }
      if (result.Status == 1) {
        var recordCount = result.RecordCount;
        panel.find(".badge.badge-danger").text(recordCount);
        panel.find(".bold").text(recordCount);
        list.html(buildMessageData(result.Data));
      }
      else {
        layout_SetRequestFailed(list, result.Message);
      }
    }, function (xhr, et) {
      var msg = setRnssLanguage("抱歉，出现错误，错误信息: ") + xhr.status + " " + xhr.statusText;
      layout_SetRequestFailed(list, msg);
    });
    function buildMessageData(data) {
      var html = '';
      $.each(data, function (i, n) {
        html += '<li><a target="_blank" href="' + n.Href + '" data-id="' + n.Id + '" class="ms-link">';
        html += '<span class="photo"><img src="' + n.StaffPhoto + '" class="img-circle" alt=""></span>';
        html += '<span class="subject"><span class="from"> ' + n.Title + ' </span><span class="time">' + n.CreatedTime + '</span></span>';
        html += '<span class="message"> ' + n.Body + ' </span>';
        html += '</a></li>';
      });
      return html;
    }
  }
  function layout_SetRequestFailed(el, msg) {
    if (el.find(".nodata").length <= 0) {
      el.append('<li class="nodata">' + msg + '</li>');
    }
    else {
      el.find(".nodata").text(msg);
    }
  }
  function layout_SiteMore() {
    $(".foot_site_more").click(function () {
      if ($(this).hasClass("active")) {
        $(this).removeClass("active");
      }
      else {
        $(this).addClass("active");
      }
      if ($(".sitemore-box").is(":hidden")) {
        var pos = $(".sitemore-icon").position();
        $(".sitemore-box").css({ position: "absolute", left: -$(".sitemore-box").width() + 10, bottom: 15, "z-index": 13 }).fadeIn(100);
      } else {
        $(".sitemore-box").fadeOut(100);
      }
    });
    $(".sitemore-title-close").click(function () {
      $(this).closest(".sitemore-box").siblings(".foot_site_more").removeClass("active");
      $(this).closest(".sitemore-box").hide();
    });
  }
  function layout_SetMessageStatus(staffId) {
    $(".ms_content_box .ms-link").on("click", function () {
      var id = $(this).data("id");
      ajaxPost3("/home/<USER>", { staffid: staffId, id: id }, 'json', function (result) {
      });
    });
  }
  function layout_StaffLevelTip() {
    var options = {
      content: $("#staffleveltip-box").html(),
      delay: { "show": 500, "hide": 1000 },
      placement: "left",
      html: true,
      title: setRnssLanguage("猎头分级提示"),
      trigger: "hover"
    };
    $(".stafflevel").popover(options);
  }
  function layout_ShowBootbox() {//显示对话框
    bootbox.dialog({
      message: "I am a custom dialog",
      title: "Custom title",
      buttons: {
        success: {
          label: "Success!",
          className: "green",
          callback: function () {
            alert("great success");
          }
        },
        danger: {
          label: "Danger!",
          className: "red",
          callback: function () {
            alert("uh oh, look out!");
          }
        },
        main: {
          label: "Click ME!",
          className: "blue",
          callback: function () {
            alert("Primary button");
          }
        }
      }
    });
  }
  function layout_CompanyStaff(companyId, el, panel, staffid) {
    getCompanySatff(panel, companyId, 1);
    lazyLoadContainer(el);
    el.scrollTop(0);
    function lazyLoadContainer(el) {
      el = $(el);
      return el.paged_scroll({
        handleScroll: function (page, container, doneCallback) {
          getCompanySatff(panel, companyId, page + 1);
        },
        pagesToScroll: 100,
        triggerFromBottom: '5%',
        loader: '',
        debug: false,
        targetElement: el
      });
    }
    function getCompanySatff(el, companyid, p, top) {
      if (el.find(".nodata").length > 0) {
        return;
      }
      ajaxPost3("/home/<USER>", { c: companyid, p: p, s: staffid, top: top || 15 }, 'html', function (result) {
        if (p == 1) {
          el.html(result);
          if (el.find("#listaffcount").length > 0) {
            $("#index-cgl-staffcount").text("（" + el.find("#listaffcount").text() + "人）")
          }
          else {
            $("#index-cgl-staffcount").text("");
          }
        }
        else {
          el.append(result);
        }
      }, function (xhr, et) {
        var msg = setRnssLanguage("抱歉，出现错误，错误信息: ") + xhr.status + " " + xhr.statusText;
        if (el.find(".nodata").length <= 0) {
          el.append('<li class="nodata">' + msg + '</li>');
        }
        else {
          el.find(".nodata").text(msg);
        }
      });
    }
  }
  function layout_Search() {
    var rSearch = $("#r-search");
    var currentOption = $(".js-currentOption", rSearch);
    var getAction = function (type) {
      switch (type) {
        case 1:
          return "/client/manage";
        case 2:
          return "/job/managejob";
        case 3:
          return "/resume/nsearchresume";
      }
    }

    var updateForm = function (text, actionType) {
      rSearch.prop("action", getAction(actionType));
      currentOption.text(text);
    }
    var ss = setRnssLanguage("简历");
    updateForm(setRnssLanguage("简历"), 3);

    $(".js-options li a", rSearch).on("click", function () {
      var action = $(this).data("value");
      var text = $(this).text();
      updateForm(text, action);
    });


    $(".js-submit", rSearch).on("click", function () {
      var schtext = $("#r-search input[name=keywords]").val();
      rSearch.submit();
    });

  }

  //达人之星
  function layout_SetStar() {
    $("#staffSmallCard").on("click", ".tstar", function () {
      var userid = $("#staffSmallCard").data("model").StaffId;//被点亮的人的staffid
      var type = $(this).data("tagid");

      ajaxPost3("/home/<USER>", { userId: userid, type: type }, 'json', function (result) {
        if (result.Success) {
          var plus1 = $(this).next("span");
          plus1.show().animate({ top: -5 }, 500, function () {
            plus1.css({ "top": "4px" }).hide();
          });

          var userId = { userId: userid };
          get_tStarDetails(userId);
        } else {
          r_Layout_BeautAlert.done(setRnssLanguage("感情再深也只能点亮一次喔"), "hits", 1200);
        }
      });

    })

    //点亮规则
    $("#staffSmallCard").on("click", "#seeRules", function () {
      $("#rulesBox").show();
    })

    $("#staffSmallCard").on("click", "#closeTips", function () {
      $("#rulesBox").hide();
    })
  }
  //获取达人之星数据
  function get_tStarDetails(userId) {
    ajaxPost3("/home/<USER>", userId, 'json', function (r) {
      if (r.Success) {
        var data = r.Data;
        var modal = $('#staffSmallCard');
        var zhenchen = data.ZhenChen,
          jiqnu = data.JinQu,
          fenxiang = data.FenXiang,
          zeren = data.ZeRen,
          chuangxin = data.ChuangXin;

        modal.find("#starScore1").html(zhenchen);
        modal.find("#starScore2").html(jiqnu);
        modal.find("#starScore3").html(fenxiang);
        modal.find("#starScore4").html(zeren);
        modal.find("#starScore5").html(chuangxin);

        //真诚
        if (zhenchen >= 1) {
          modal.find("#starImg1").attr("src", "../../Css/images/1star_100.png");
        } else {
          modal.find("#starImg1").attr("src", "../../Css/images/1star_0.png");
        }

        //进取
        if (jiqnu >= 1) {
          modal.find("#starImg2").attr("src", "../../Css/images/1star_100.png");
        } else {
          modal.find("#starImg2").attr("src", "../../Css/images/1star_0.png");
        }

        //分享
        if (fenxiang >= 1) {
          modal.find("#starImg3").attr("src", "../../Css/images/1star_100.png");
        } else {
          modal.find("#starImg3").attr("src", "../../Css/images/1star_0.png");
        }

        //责任
        if (zeren >= 1) {
          modal.find("#starImg4").attr("src", "../../Css/images/1star_100.png");
        } else {
          modal.find("#starImg4").attr("src", "../../Css/images/1star_0.png");
        }

        //创新
        if (chuangxin >= 1) {
          modal.find("#starImg5").attr("src", "../../Css/images/1star_100.png");
        } else {
          modal.find("#starImg5").attr("src", "../../Css/images/1star_0.png");
        }

        r_Layout_BeautAlert.done("点亮成功", "hits", 1200);
      } else {
        r_Layout_BeautAlert.done("点亮失败", "hits", 1200);
      }
    })
  }

  //个人名片
  function layout_ShowStaffSmallCard(staffMobileNum, staffTelNum) {
    $('#staffSmallCard').on('show.bs.modal', function (event) {
      var data = $(this).data("model");
      if (!data || data.Id == 0) {
        return false;
      }
      var li = $(event.relatedTarget);

      var modal = $(this),
        gender = data.Gender,
        id = data.StaffId,
        level = data.Level,
        qq = data.QQ,
        email = data.Email,
        company = data.CompanyName,
        industries = [],
        evaluationScoreHtml = '',
        tel = data.TelNumber,
        department = data.DepartmentName,
        name = data.Name,
        number = data.MobileNumber,
        photo = data.MiddlePictureUrl,
        position = data.PositionName;

      data.GoodIndustrys.forEach(function (elem) {
        elem.Name && industries.push('<span class="industry-label">' + elem.Name + '</span>');
      });
      industries = industries.join("");

      var html = '';
      var evaluationScoreNum = Number(data.EvaluationScore);
      if (evaluationScoreNum > 0) {
        for (var i = 0; i < 5; i++) {
          if (evaluationScoreNum <= i) {
            html += '<img class="evaluation-score-star-img" src="/images/cooperation/icon-star-empty.png">';
          } else if (evaluationScoreNum > i && evaluationScoreNum < i + 1) {
            html += '<img class="evaluation-score-star-img" src="/images/cooperation/icon-star-half.png">';
          } else {
            html += '<img class="evaluation-score-star-img" src="/images/cooperation/icon-star-full.png">';
          }
        }
        evaluationScoreHtml = html + '<span class="evaluation-score-num">' + data.EvaluationScore + '</span>';
      } else {
        evaluationScoreHtml = '暂无评价';
      }



      //达人之星
      var zhenchen = data.ZhenChen,
        jiqnu = data.JinQu,
        fenxiang = data.FenXiang,
        zeren = data.ZeRen,
        chuangxin = data.ChuangXin;

      modal.find("#starScore1").html(zhenchen);
      modal.find("#starScore2").html(jiqnu);
      modal.find("#starScore3").html(fenxiang);
      modal.find("#starScore4").html(zeren);
      modal.find("#starScore5").html(chuangxin);

      //真诚
      if (zhenchen >= 1) {
        modal.find("#starImg1").attr("src", "../../Css/images/1star_100.png");
      } else {
        modal.find("#starImg1").attr("src", "../../Css/images/1star_0.png");
      }
      //进取
      if (jiqnu >= 1) {
        modal.find("#starImg2").attr("src", "../../Css/images/1star_100.png");
      } else {
        modal.find("#starImg1").attr("src", "../../Css/images/1star_0.png");
      }
      //分享
      if (fenxiang >= 1) {
        modal.find("#starImg3").attr("src", "../../Css/images/1star_100.png");
      } else {
        modal.find("#starImg1").attr("src", "../../Css/images/1star_0.png");
      }
      //责任
      if (zeren >= 1) {
        modal.find("#starImg4").attr("src", "../../Css/images/1star_100.png");
      } else {
        modal.find("#starImg1").attr("src", "../../Css/images/1star_0.png");
      }
      //创新
      if (chuangxin >= 1) {
        modal.find("#starImg5").attr("src", "../../Css/images/1star_100.png");
      } else {
        modal.find("#starImg1").attr("src", "../../Css/images/1star_0.png");
      }

      var sc_staffid = modal.find(".staffid"),
        gendericon = modal.find(".modal-title i"),
        sc_photo = modal.find(".sc-photo"),
        sc_name = modal.find(".sc-name"),
        sc_phone = modal.find(".sc-phone .sc-txt"),
        sc_phone_call = modal.find(".sc-phone .rnss-callphone"),
        sc_tel = modal.find(".sc-tel"),
        sc_email = modal.find(".sc-email"),
        sc_company = modal.find(".sc-company"),
        sc_department = modal.find(".sc-department"),
        sc_position = modal.find(".sc-position"),
        sc_level = modal.find(".sc-level"),
        sc_evaluationScore = modal.find(".sc-evaluation-score"),
        sc_industries = modal.find(".sc-industries"),
        sc_qq = modal.find(".sc-qq");

      sc_staffid.val(id);
      if (gender == 1) {
        gendericon.attr("class", "icon-user");
      }
      else {
        gendericon.attr("class", "icon-user-female");
      }

      //sc_photo.attr("src", photo).attr("alt", name);
      sc_photo.css("width", "90px")
        .css("height", "90px")
        .css("background-repeat", "round")
        .css("border-radius", "46px !important")
        .css("background-image", "url('" + photo + "')");

      sc_name.attr("href", "/staff/personalhomepage?id=" + id).text(name);
      sc_phone.text(number);

      if (!number || number.length == 0) {
        sc_phone_call.hide();
      }
      else {
        sc_phone_call.attr("data-transfernumber", number).attr("data-usnumber", staffMobileNum).attr("data-ustel", staffTelNum).css("display", "inline-block");
      }
      sc_tel.text(tel);
      sc_email.text(email);
      sc_company.text(company);
      sc_department.text(department);
      sc_position.text(position);
      sc_industries.html(industries);
      sc_evaluationScore.html(evaluationScoreHtml);
      if (level && !isNaN(parseInt(level, 10))) {
        sc_level.closest(".staffcard-level-panel").removeClass("withoutlevel");
        sc_level.text("LV." + level).show();
        sc_level.siblings(".staffcard-level-img").prop("src", '/images/cooperation/img-userlevel' + (Math.ceil(level / 3) - 1) + '.png');
      }
      else {
        sc_level.closest(".staffcard-level-panel").addClass("withoutlevel");
      }
      sc_qq.attr("href", 'http://wpa.qq.com/msgrd?V=1&uin=' + qq + '&menu=yes').text(qq);

      if (data.CooperationStatus == 0 || data.CooperationStatus == 2) {
        $(".btn-invite-cooperation.btn-disabled").css("display", "");
        $(".btn-invite-cooperation.btn-allow").css("display", "none");
      } else {
        $(".btn-invite-cooperation.btn-disabled").css("display", "none");
        $(".btn-invite-cooperation.btn-allow").css("display", "");
      }
    });


    r_Layout_SetModalPosition($('#staffSmallCard'));
    $(document).delegate(".sc-txt-view", "click", function () {
      var _this = $(this);
      console.log(_this)
      if (window.viewStaffData == null) {
        if (_this.attr("data-StaffId") == undefined || _this.attr("data-StaffId") == "") {
          r_Layout_BeautAlert.done("要查看的信息不存在");
          return;
        }
        else {
          window.viewStaffData = {};
          window.viewStaffData.StaffId = _this.attr("data-StaffId");
        }
      }
      var data = { ViewStaffId: window.viewStaffData.StaffId, ViewPage: window.location.href };
      ajaxPost3("/Staff/GetStaffContactViewLog", data, 'json', function (result) {
        if (result.StatusCode == 1) {
          var count = result.Data.Count;
          var allow = false;
          var limitCount = result.Data.LimitCount
          if (!result.Data.IsView) {
            if (count >= limitCount) {
              r_Layout_BeautAlert.done("可查看次数不足");
              return;
            }
            allow = confirm("确定要查看吗？今日还剩" + (limitCount - count) + "次（每日限" + limitCount+"次）系统会记录您的查看记录");
          }
          else {
            allow = true;
          }
          if (allow) {
            ajaxPost3("/Staff/AddStaffContactViewLog", data, 'json', function (result) {
              if (result.StatusCode == 1) {
                _this.parent().hide();
                var moreInfo = "";
                if (_this.attr("data-moreInfo") != undefined) {
                  moreInfo = _this.attr("data-moreInfo");
                };
                _this.parent().prev().find(".sc-txt").text(result.Data + moreInfo);
                _this.parent().prev().find(".show-phone").attr("data-transfernumber", result.Data);
                _this.parent().prev().find(".show-phone1").attr("data-number", result.Data);
                _this.parent().prev().show();
              }
              else {
                r_Layout_BeautAlert.done(result.Message);
              }
            });
          }
        }
        else {
          r_Layout_BeautAlert.done(result.Message);
        }
      });
      if (_this.attr("data-StaffId") != undefined) {
        window.viewStaffData = null;
      }
    });
    //$(document).on("click", ".showCard-item", function (event) {
    //  var eve = window.event || event;
    //  eve.stopPropagation();
    //  var staffId = this.dataset.id, isclick = $(this).data("isclick");
    //  ajaxPost3("/home/<USER>", { staffid: staffId }, 'json', function (result) {
    //    if (result.Status == 1) {
    //      if (result.Data.StaffId > 0) {
    //        $(".all-titphone").show();
    //        $(".all-titphone").prev().hide();
    //        window.viewStaffData = result.Data;
    //        //$('#staffSmallCard').trigger("show.bs.modal", [result.Data]);
    //        $("#staffSmallCard").data("model", result.Data).modal('show');
    //        var modal = $("#staffSmallCard"),
    //          zhenchen = result.Data.ZhenChen,
    //          jiqnu = result.Data.JinQu,
    //          fenxiang = result.Data.FenXiang,
    //          zeren = result.Data.ZeRen,
    //          chuangxin = result.Data.ChuangXin;
    //        //真诚
    //        if (zhenchen >= 1) {
    //          modal.find("#starImg1").attr("src", "../../Css/images/1star_100.png");
    //        } else {
    //          modal.find("#starImg1").attr("src", "../../Css/images/1star_0.png");
    //        }

    //        //进取
    //        if (jiqnu >= 1) {
    //          modal.find("#starImg2").attr("src", "../../Css/images/1star_100.png");
    //        } else {
    //          modal.find("#starImg2").attr("src", "../../Css/images/1star_0.png");
    //        }

    //        //分享
    //        if (fenxiang >= 1) {
    //          modal.find("#starImg3").attr("src", "../../Css/images/1star_100.png");
    //        } else {
    //          modal.find("#starImg3").attr("src", "../../Css/images/1star_0.png");
    //        }

    //        //责任
    //        if (zeren >= 1) {
    //          modal.find("#starImg4").attr("src", "../../Css/images/1star_100.png");
    //        } else {
    //          modal.find("#starImg4").attr("src", "../../Css/images/1star_0.png");
    //        }

    //        //创新
    //        if (chuangxin >= 1) {
    //          modal.find("#starImg5").attr("src", "../../Css/images/1star_100.png");
    //        } else {
    //          modal.find("#starImg5").attr("src", "../../Css/images/1star_0.png");
    //        }
    //      }
    //      else {
    //        r_Layout_BeautAlert.done(setRnssLanguage("没有该用户信息！"));
    //      }
    //    }
    //    else {
    //      r_Layout_BeautAlert.done(result.Message);
    //    }
    //  });
    //});
  }

  function setStaffRCoin() {
    var staffRCoin;
    L_plugin.getStaffR();
    var dataStaff = {};

    $("#transferR").click(function () {
      $("#R_modal").modal("show");
      $("#inputNum").val('');
      $("#selTeam_R .outbox-item-list").html('');
      $(".outbox-tip").show();
      dataStaff = {};
      $("#Rnum").html(window.staffRCoin);
    });
    // 选择推荐人
    $("#selTeam_R").completeTeam({
      type: 0, userKey: "leader", maxCount: 1, width: 172, append: $("#selTeam_R"), selectcallback: function () {
        var list = $("#selTeam_R .outbox-item-list").find("li");
        dataStaff = {
          ReceiveStaffId: $(list[0]).data("id")
          //PersonName: $(list[0]).children(".people").html()
        }
      }
    });
    $("#selTeam_R .outbox-item-list .cancel").click(function () {
    });
    $("#submitRpay").click(function () {
      var list = $("#selTeam_R .outbox-item-list").find("li").length;

      var num = $("#inputNum").val();
      if (list == 0) {
        beautAlert.done(setRnssLanguage("请选择转出对象"), 'hits');
        return
      }
      if (!num) {
        beautAlert.done(setRnssLanguage("请输入转出数量"), 'hits');
        return
      }

      if (!(/^[1-9]\d*$/.test(Number(num)))) {
        beautAlert.done(setRnssLanguage("转出数量请输入正整数"), 'hits');
        return
      }
      if (parseFloat(num) > parseFloat($("#Rnum").html())) {
        beautAlert.done(setRnssLanguage("转出数量不能超过已有数量"), 'hits');
        return
      }
      dataStaff.Amt = $("#inputNum").val();
      $.ajax({
        url: '/StaffCredit/TransferAccounts',
        type: 'POST',
        data: dataStaff,
        success: function (res) {
          if (res.Success) {
            beautAlert.done('转账成功', 'hits');
            $("#R_modal").modal("hide");
            L_plugin.getStaffR();
          } else {
            beautAlert.done(res.Message, 'hits');
          }
        },
        fail: function (fail) {
          console.log(fail)
        }
      });
    });
  }

  return {
    init: function (_staffId, _staffTelNum, _companyId, _stafflist_el, _stafflist_panel, _staffMobileNum) {
      staffId = _staffId;
      staffTelNum = _staffTelNum;
      companyId = _companyId;
      stafflist_el = _stafflist_el;
      stafflist_panel = _stafflist_panel;
      staffMobileNum = _staffMobileNum;

      // 添加点击事件 获取同事列表
      var clickCompanyStaffFlag = true;
      $('#clickCompanyStaff').click(function () {
        if (clickCompanyStaffFlag) {
          layout_CompanyStaff(companyId, stafflist_el, stafflist_panel, staffId);
          clickCompanyStaffFlag = false;
        }
      });
      // 添加hover 事件
      var header_inbox_barFlag = true;
      var header_task_barFlag = true;
      $('#header_inbox_bar').hover(function () {
        if (header_inbox_barFlag) {
          header_inbox_barFlag = false;
          layout_LoadStaffMessage(staffId, 10);//加载登录用户的消息
        }
      });
      $('#header_task_bar').hover(function () {
        if (header_task_barFlag) {
          header_task_barFlag = false;
          layout_LoadStaffTask(staffId, 10); //加载日历提醒
        }
      });


      layout_StaffLevelTip();
      layout_SetMessageStatus(staffId);
      layout_SiteMore();
      layout_SearchUserStaff();
      layout_SetSiteNavStatus();
      layout_Search();
      layout_SetRnssLanguage();

      layout_ShowStaffSmallCard(staffMobileNum, staffTelNum);
      layout_SetStar();
    },
    setStaffRCoin: setStaffRCoin
  };
}();