var rowArr = [];
var server = localStorage.getItem("r_WebsocketUrl");
var pageUrl = window.location.host + window.location.pathname;
// 窗口改变时 需要销毁之前存在的长连接
window.onbeforeunload = function (event) {
  if (bottomMessageTip.curWs) {

    var msg = { pageUrl: pageUrl, action: 'leave', SendClientId: bottomMessageTip._ConnectId, nick: userstaffname, staffId: userstaffid, };
    if (bottomMessageTip.curWs.readyState == 1) {
      bottomMessageTip.curWs.send(JSON.stringify(msg));
      // 关闭当前链接
      bottomMessageTip.curWs.close();
    }
  }
}

// 日程提醒弹框位置
var bottomMessageTip = new Vue({
  el: '#bottomMessageTip',
  data: function () {
    return {
      // 2021-01-21
      // 工单数据
      workOrderList: [],
      // 2020-12-18 智能小达
      jobsObjList: [],
      xiaodaJobType: '',
      jobNames: [],//职位名称
      mobileNumber: '', //手机号
      // 2020-12-09交付中心 提醒新增字段
      // 7：简历推荐简历报告 
      // 8：推荐反馈 
      // 9：交付提醒（下架提醒）
      // 10：职位提醒（超15天未交付）
      remindTab: 2,
      deliverList: [],
      curWs: null,   // 当前页面的ws
      _ConnectId: '',  // 用于销毁长连接需要的Id
      _webScokeTime: '60000',   // 初始化时间 60000  保持心跳时间 1800000 
      initWebSocketErrorFrequency: 0, // 初始化 WebSocket 错误次数统计
      // 本地存放鸡汤的位置和图片是一一对应的
      mottoContent: {
        "1": "为自己选择的跑道去冲刺，即使很漫长，即使有阻碍，即使会跌倒；但是，坚定的信念会一直陪伴着我欢笑的努力地飞快地奔跑。",
        "2": "即使生活在阴霾里，依然有向往星空的权利。风雨兼程，披星戴月，诗和远方，我都要去看看。总会有意想不到的收获人生，总有不期而遇的温暖和生生不息的希望 。",
        "3": "人生道虽很曲折，却很美丽。只要你细心观看，就能饱尝沿途美景，征服畏惧建立自信的最快最确实的方法，就是去做你害怕的事，直到你获得成功的经验。",
        "4": "一个崇高的目标，只要不渝地追求，就会成为壮举。这个世界从来没有什么事情是随随便便成功的，实现目标，你就得付出艰苦。",
        "5": "急速流逝的时间，一去不返的时间，是人最宝贵的财富，如果把它虚度，那是最大的挥霍。零星的时间，如果能敏捷地加以利用，可成为完整的时间。",
      },
      countDown: { // 时间倒计时
        // '0':'',
        //'1':'',
        //'2':''
      },
      showAlertModel: false,    // 展开 false | 收起来 true
      showAlertList: [],       // 今日日程提醒数据位置 
      showAlertTips: 0,        // 提醒条数位置
      showLeftAlert: false,    // 是否显示昨日总结
      hasShowSummary: true,    // 是否显示工作总结
      week: 1,                 // 当前星期几
      showLeftList: {          // 昨日工作总结数据位置  
        "summary": {
          "interview": 1,     // 面试
          "recommend": 2,     // 推给客户
          "offer": 3,         // offer
          "serch": 4          // 寻访
        },
        "motto": {            // 鸡汤人名
          "staffName": "lisa",
          "mottoContent": ""
        }
      },
      processedIndex: {        // 点击已处理索引
        '0': true,
        '1': true,
        '2': true
      },
      timer: {                 // 仨个定时器
        '0': null,
        '1': null,
        '2': null
      },
      webSocketNum: 0,
      expiredDisplay: false,
      expiredDisplayNum: 0
    };
  },
  filters: {
    // 2020-12-23
    // 小达时间过滤
    xiaodaTimeFilters: function (time) {
      if (!time) {
        return '--'
      }
      let date = new Date(time);
      let getFullYear = date.getFullYear();
      let getMonth = date.getMonth() + 1;
      let getDate = date.getDate();
      getMonth = getMonth >= 10 ? getMonth : '0' + getMonth;
      getDate = getDate >= 10 ? getDate : '0' + getDate;

      return getFullYear + '-' + getMonth + '-' + getDate;
    },
    // 过滤小达跳转显示的提示语
    xiaodaJobTypelinkFilters: function (xiaodaJobType) {
      xiaodaJobType = Number(xiaodaJobType);
      /*switch (xiaodaJobType) {*/
      //case 0:
      //  return "更多合作职位，请移驾去海斗网 >";
      //case 1:
      //  return '去海斗网 >';
      /*case 2:*/
      return '更多合作职位，请移驾去海斗网 >';
      //}
    },
    // 过滤小达显示的提示语
    xiaodaJobTypeFilters: function (xiaodaJobType) {
      xiaodaJobType = Number(xiaodaJobType);
      switch (xiaodaJobType) {
        //case 0:
        //  return "根据您近期职位运作行为分析，小达为您推荐以下可以进行合作的同类职位，快来看看吧！";
        case 1:
          return '根据您近期职位运作行为分析，为您推荐可以合作的同类职位，快来看看吧！';
        case 2:
          return '温馨提示，海斗网发布了最新职位，快来看看吧！';
      }
    },
    // 不同类型显示的操作按钮不一样
    xiaodaButtonFilters: function (type) {
      switch (type) {
        case 0:
          return "去看看>";
        case 1:
          return '发布到交付中心>';
        case 2:
          return "去看看>";
      }
    },


    // 过滤交付中心提醒按钮文字
    // 7：简历推荐简历报告 
    // 8：推荐反馈 
    // 9：交付提醒（下架提醒）
    // 10：职位提醒（超15天未交付）
    taskTypeFilters: function (type) {
      switch (type) {
        case '7':
          return "去查看";
        case '8':
          return "去查看";
        case '9':
          return "去处理";
        case '10':
          return "发布到交付中心";
      }
    },
    // 过滤掉显示交付中心提醒时候的多余文字
    taskContentFilters: function (title) {
      var newTitle = title.replace("<span style='color:#F83D3D'>", '').replace("</span>", '');
      return newTitle
    },
    // 过滤显示时间
    filtersTime: function (time) {
      var newDate = new Date(time);
      var minutes = newDate.getMinutes() >= 10 ? newDate.getMinutes() : '0' + newDate.getMinutes();
      var newTime = newDate.getHours() + ':' + minutes;
      return newTime
    },
    filtersTaskTime: function (taskTime, index, id) {
      return bottomMessageTip.addTime(taskTime, index, id)
    }
  },
  mounted() {

    // 获取日期显示小达
    this.getdates();

    // 是否关闭了昨日工作总结
    var _this = this;
    // 需要创建连接的地址
    var connectionAddress = 'http://staff.risfond.com/';   // 首页

    // 20201020 根据业务需求增加 相应的页面
    var addressLink_2 = "http://staff.risfond.com/client/manage"                                  // 客户列表页
    var addressLink_3 = "http://staff.risfond.com/resume/nsearchresume"                   // 搜索简历页面
    var addressLink_4 = "http://staff.risfond.com/profile/myrecommendmanage.aspx"  // 推荐管理页面
    var addressLink_5 = "http://staff.risfond.com/job/managejob"                                   // 职位列表页
    var href = window.location.href;

    // 20201021 修改内容

    // 获取实时的消息个数
    setInterval(function () {
      var messageNum = _this.showAlertList.length + _this.deliverList.length + _this.workOrderList.length;
      messageNum = messageNum > 99 ? "99+" : messageNum;
      if (messageNum > 0) {
        $("#systemMessageNum").show();
        $("#systemMessageNum").text(messageNum);
      } else {
        $("#systemMessageNum").hide();
        $("#systemMessageNum").text(messageNum);
      }

    }, 10000);


    setInterval(function () {
      var showAlertName = "";
      var deliverAletName = "";
      var workOrderListName = "";
      if (userstaffid) {
        showAlertName = "showAlertList" + userstaffid.toString().trim();
        // 交付中心提醒消息位置
        deliverAletName = "deliverAletList" + userstaffid.toString().trim();
        // 工单提醒
        workOrderListName = 'workOrderList' + userstaffid.toString().trim();
      } else {
        showAlertName = "showAlertList";
        // 交付中心提醒消息位置
        deliverAletName = "deliverAletList";
        // 工单提醒
        workOrderListName = 'workOrderList';
      }
      var LastTimeData = JSON.parse(localStorage.getItem(showAlertName)) || [];
      _this.deliverList = JSON.parse(localStorage.getItem(deliverAletName)) || [];
      _this.workOrderList = JSON.parse(localStorage.getItem(workOrderListName)) || [];

      _this.showAlertList = LastTimeData;
      _this.processedIndex = { '0': true, '1': true, '2': true };
      _this.timer = { '0': null, '1': null, '2': null };
      _this.hasShowModelState('showAlertModel');

      // 控制顶部tab
      var tabIndex = JSON.parse(localStorage.getItem('remindTab')) || '2';
      _this.remindTabChange(tabIndex);

    }, 30000)
    //30000

    if (this.showAlertList.length > 0) {
      // 根据当前操作状态显示 是否展开 | 关闭
      this.hasShowModelState('showAlertModel');
    }
  },
  watch: {
    showAlertList(val) {
      // console.log(val, 'cccccc');
    }
  },
  methods: {
    goToPositionDetails: function (item) {
      //var _czc = _czc || [];
      window._czc.push(["_trackEvent", "Rnss小达", "职位详情页"]);
      window.open(HdUrl + '/position_details/' + item.jobId);
      this.ajaxPostViewPosition(item.jobId);
    },
    // 2020-12-23 智能小达按类型区分增加不同事件
    xiaodaButtonChangge: function (type, item) {
      type = Number(type);
      switch (type) {
        case 0:
          // 去看看
          localStorage.setItem('xiaodakwd', JSON.stringify(item.title));
          window.open('/deliverycenter/index?xiaoDaComeJobId=' + item.jobId, 'self');
          return
        case 1:
          // 发布到交付中心
          window.open('/deliverycenter/editjobinfo?id=' + item.jobId + '&jobsourcetype=1', '_self');
          return
        case 2:
          // 去看看
          localStorage.setItem('xiaodakwd', JSON.stringify(item.title));
          window.open('/deliverycenter/index?xiaoDaComeJobId=' + item.jobId, 'self');
          return
      }
    },
    // 2020-12-18 智能小达数据位置
    openJiaoFu: function () {
      //var _czc = _czc || [];
      window._czc.push(["_trackEvent", "Rnss小达", "职位列表"]);
      //window.open("/DeliveryCenter/index?typeId=2", '_self');
      window.open(HdUrl + "/haidou_main");
    },
    openZhiNengXiaoDa: function (key) {
      var _this = this;
      // 临时修改 只有有权限的才会查看
      var showArr = [5208, 2167, 15796, 1368, 3619, 7213, 12054, 5842, 12260, 10951, 4362, 935, 4127, 19461, 2673, 794, 6999, 18074, 12012, 4604, 2, 22, 5773];
      // 当前登录人的Id
      var currentId = Number(window.userstaffid);
      this.ajaxGetJobList(function (res) {
        _this.ajaxGetJobList1(function (res) {
          _this.$nextTick(function () {
            // 2020-12-30 权限改为所有人可以查看
            $("#ZhiNengXiaoDa").modal("show")
          });
        })
      });

      if ($("#jobDetailPanel #jobDetailApp").length == 0) {
        _this.$nextTick(function () {
        })
      }
    },
    // 显示小达 每天主动显示一遍
    getdates: function (fn) {
      var _this = this;
      var getDay = new Date().getDay();
      var weeks = new Array(7, 1, 2, 3, 4, 5, 6);
      var week = weeks[getDay];
      if (getDay >= 0 && (week == 1 || week == 3 || week == 5)) {
        // var showOnceXiaoDa = localStorage.getItem('showOnceXiaoDa') || -1;
        // if (showOnceXiaoDa != getDay) {
          // setTimeout(function () {
            // _this.openZhiNengXiaoDa();
          // }, 20)
          // localStorage.setItem('showOnceXiaoDa', getDay);
        // }
      }
    },
    // 我的关注职位详情弹框
    ajaxGetJobDetailView: function () {
      $.ajax({
        url: "/deliverycenter/jobdetailpartialview",
        data: {},
        type: "GET",
        dataType: "html",
        success: function (html) {
          $("#jobDetailPanel").html(html);
        },
        error: function (xhr, status) {
        },
      });
    },
    formatDate: function (dateStr) {
      if (!dateStr) {
        return "";
      }
      // NaNNaN
      var date = new Date(Date.parse(dateStr.replace(/-/g, "/")));
      var now = new Date();
      var cur = new Date(date);
      // 一个小时内
      if (now - cur < 3600000) {
        return "[刚刚发布]"
      }
      // 一天内
      if (now.getFullYear() == cur.getFullYear() && now.getMonth() == cur.getMonth() && now.getDate() == cur.getDate()) {
        return "[" + cur.getHours() + ":" + (cur.getMinutes() < 10 ? "0" + cur.getMinutes() : cur.getMinutes()) + " 发布]";
      }
      // 本年度
      if (now.getFullYear() == cur.getFullYear()) {
        return "[" + (cur.getMonth() + 1) + "月" + cur.getDate() + "日 发布]"
      }
      return "[" + cur.getFullYear() + "年" + (cur.getMonth() + 1) + "月" + cur.getDate() + "日 发布]";
    },
    setEduLevel: function (v) {
      let str = "";
      if (v == 7) {
        str = "大专及以下";
      } else if (v == 8) {
        str = "专科";
      } else if (v == 16) {
        str = "本科";
      } else if (v == 24) {
        str = "硕士";
      } else if (v == 32) {
        str = "博士";
      } else if (v == 40) {
        str = "博士后";
      } else if (v == 29) {
        str = "MBA/EMBA";
      } else if (v == 41) {
        str = "学历不限";
      }
      return str;
    },
    setAmount: function (amount) {
      if (amount) {
        Math.round((amount / 1000) * Math.pow(10, 2)) / Math.pow(10, 2);
        return (
          Math.round((amount / 1000) * Math.pow(10, 2)) / Math.pow(10, 2)
        );
      } else {
        return 0;
      }
    },
    //ajaxGetJobList: function (fn) {
    //  var _this = this;
    //  $.ajax({
    //    type: "post",
    //    url: "/deliverycenter/GetJobRecommend",
    //    success: function (res) {
    //      if (res.success) {
    //        _this.xiaodaJobType = res.data.jobType;
    //        _this.jobsObjList = res.data.jobList;
    //        fn && fn(res);
    //      } else {
    //        beautAlert.done(res.message, "hits");
    //      }
    //    },
    //  });
    //  },
    ajaxGetJobList: function (fn) {
      var _this = this;
      $.ajax({
        type: "post",
        url: "/DeliveryCenter/GetUpdateJobName",
        success: function (res) {
          if (res.success) {
            _this.jobNames = res.data.jobNames;
            _this.mobileNumber = res.data.mobileNumber;
            fn && fn(res);
          } else {
            beautAlert.done(res.message, "hits");
          }
        },
      });
    },
    // 2022-5-31 调用海斗推荐职位
    ajaxGetJobList1: function (fn) {
      var _this = this;
      var jobNameStr = '';
      if (_this.jobNames.length > 0) {
        jobNameStr = _this.jobNames.join(',');
      }
      $.ajax({
        type: "GET",
        url: HdUrl + "/api/positionInfo/rnss-job-recommend/job-infos?jobTitles=" + jobNameStr + "&phone=" + _this.mobileNumber,
        success: function (res) {
          if (res.success) {
            _this.jobsObjList = res.data.data;
            _this.xiaodaJobType = res.data.dataFilterType;
            if (_this.jobsObjList.length > 0) {
              _this.jobsObjList.forEach((e) => {
                if (Array.isArray(e.jobIndustries)) {
                  let industryArr = [];
                  e.jobIndustries.forEach((item) => {
                    industryArr.push(item.industryName);
                  });
                  e.industries = industryArr.join("、");
                }
                if (Array.isArray(e.jobLocationList)) {
                  if (e.jobLocationList.length) {
                    let locationArr = "";
                    e.jobLocationList.forEach((item) => {
                      locationArr += "[" + item.locationName + "]";
                    });
                    e.locationList1 = locationArr;
                  }
                }
              });
            }
            fn && fn(res);
          } else {
            beautAlert.done(res.message, "hits");
          }
        },
      });
    },
    // 2022-5-31 设置已读
    ajaxPostViewPosition: function (jobId) {
      $.ajax({
        url: HdUrl + "/api/home/<USER>",
        data: { phone: this.mobileNumber, jobId: jobId },
        type: "post",
        success: function () {
        },
        error: function (xhr, status) {
        },
      });
    },

    // 2020-12-11
    openTapXiaoXi: function (status) {
      if (status) {
        $(".xiaoxi-box").show();
      } else {
        $(".xiaoxi-box").hide();
      }
    },
    // 2020-12-09
    // 消息服务新增交付提醒
    remindTabChange: function (tabIndex) {
      this.remindTab = tabIndex;
      localStorage.setItem('remindTab', JSON.stringify(tabIndex));
    },

    remindButChange: function (taskType, jobId, jobCandidateId, index, IsJump) {
      var _this = this;
      switch (taskType) {
        case '7': // 去查看 跳到交付中心简历报告
          _this.setJobMessageProcessed(jobId, index);
          window.open('/DeliveryCenter/RecommendInfo?jobId=' + jobId + '&typeId=1&isEdit=false&jobCandidateId=' + jobCandidateId);
          return;
        case '8': // 去查看 跳到交付中心简历报告
          window.open('/DeliveryCenter/RecommendInfo?jobId=' + jobId + '&typeId=1&isEdit=false&jobCandidateId=' + jobCandidateId);
          _this.setJobMessageProcessed(jobId, index);
          return;
        case '9': // 去处理 跳到交付中心 我发布的职位
          window.open('/DeliveryCenter/CooperationJob?typeId=1');
          _this.setJobMessageProcessed(jobId, index);
          return;
        case '10': // 跳到交付中心创建职位
          if (IsJump) {
            window.open('/deliverycenter/editjobinfo?id=' + jobId + '&jobsourcetype=1');
          } else {
            _this.setJobMessageProcessed(jobId, index);
          }
          return;
      }
    },
    // 打开工单消息提行
    openworkOrder: function (id, index) {
      this.workIgnore(id, index, function () {
        window.open('/workorder/workorderlist', '_self');
      });
    },
    // 工单忽略
    workIgnore: function (id, index, fn) {
      // 删除本地数据
      var _this = this;
      _this.workOrderList.splice(index, 1);
      if (userstaffid) {
        workOrderListName = "workOrderList" + userstaffid.toString().trim();
      } else {
        workOrderListName = "workOrderList"
      }
      localStorage.setItem(workOrderListName, JSON.stringify(_this.workOrderList));
      fn && fn();
    },
    // 合作职位消息提醒已读
    setJobMessageProcessed: function (jobId, index) {
      // 删除本地数据
      var _this = this;
      _this.deliverList.splice(index, 1);
      var deliverAletName = "";
      if (userstaffid) {
        deliverAletName = "deliverAletList" + userstaffid.toString().trim();
      } else {
        deliverAletName = "deliverAletList"
      }
      localStorage.setItem(deliverAletName, JSON.stringify(_this.deliverList));

      // 标记已读
      $.ajax({
        url: '/DeliveryCenter/SetJobMessageProcessed',
        type: 'POST',
        data: {
          jobId: jobId
        },
        success: function (res) {
          if (res.success) { }
        }
      })
    },
    // 倒计时位置
    addTime: function (t, index, tId) {
      // 临时模拟 timer
      var t1 = t;
      var tIndex = index;
      var _this = this;
      if (typeof this.timer[tIndex] == 'object') {
        this.timer[tIndex] = setInterval(function () {
          _this.settingTiming(t1, tIndex, tId);
        }, 1000);
      }
      return this.countDown[tId];
    },
    // 点击题型跳到详情
    messageOpeningCorrespondingDetails: function (id) {
      $.ajax({
        url: '/services/taskactionhandler.ashx?action=gettaskforedit',
        type: 'POST',
        data: {
          taskid: id
        },
        success: function (res) {
          if (res.Status === 1) {
            // 其他提醒 不跳转
            if (res.Data.Type == 4) {
              return;
            } else {
              window.open(res.Data.ViewUrl);

              // window.location.href = res.Data.ViewUrl
            }
          } else {
            r_Layout_BeautAlert.done(res.message, "hits", 3000);
          }
        }
      })
    },

    // 标记已处理
    markProcessed: function (tIndex, tId) {
      // 阻止冒泡
      window.event ? window.event.cancelBubble = true : e.stopPropagation();

      var _this = this;
      $.ajax({
        url: '/services/taskactionhandler.ashx?action=settaskstatus',
        data: {
          taskid: tId,
          Status: 1
        },
        success: function (res) {
          if (res.Status == 1) {
            var showAlertName = ""
            if (userstaffid) {
              showAlertName = "showAlertList" + userstaffid.toString().trim();
            } else {
              showAlertName = "showAlertList"
            }

            if (_this.showAlertList === null) {
              _this.showAlertList = JSON.parse(localStorage.getItem(showAlertName));
            }

            var oldShowAlertList = JSON.parse(localStorage.getItem(showAlertName));

            oldShowAlertList.forEach(function (item, index) {
              if (item.id == tId) {
                oldShowAlertList.splice(tIndex, 1);
              }
            })
            _this.processedIndex[tIndex] = false;
            // 保存在本地 下个页面使用
            localStorage.setItem(showAlertName, JSON.stringify(oldShowAlertList));
            // 在日程管理页面需要刷新页面 才能获取最新数据
            var linkUrl = [
              "/profile/taskmanage.aspx",
              "/admin/taskmanage.aspx",
              "/branchadmin/taskmanage.aspx",
            ]
            var curLink = window.location.pathname;
            linkUrl.forEach(function (item, index) {
              if (item == curLink) {
                setTimeout(function () { location = location }, 1000);
              }
            })

          } else {
            r_Layout_BeautAlert.done(res.message, "hits", 3000);
          }
        }
      })
    },
    initScoket: function () {
      var _this = this;
      _this.webSocketNum = 0;
      console.log('--------------')
      try {
        _this.curWs = new WebSocket(server + '/ws');
        _this.initWebSocket();
      } catch (err) {
        _this.initWebSocketErrorFrequency += 1;
      }
    },
    initWebSocket: function () {
      var _this = this;
      var ws = _this.curWs;
      var stateErr = true;
      ws.onopen = function (evt) {
        stateErr = false;
        var msg = { pageUrl: pageUrl, action: 'join', msg: "8888", nick: userstaffname, staffId: userstaffid };
        var no_1 = { pageUrl: pageUrl, action: 'send_to_room', msg: 'ping', nick: 'ping', staffid: userstaffid };
        ws.send(JSON.stringify(msg));
      };
      // 当通过 WebSocket 收到数据时触发
      ws.onmessage = function (evt) {
        // 当结束会议时 前端也断开连接 目前最大连接会议是俩个
        if (evt.data == 'close websocket') {
          bottomMessageTip.curWs.close();
          return;
        }
        var showAlertName = "";
        var deliverAletName = "";
        var workOrderListName = "";
        if (userstaffid) {
          showAlertName = "showAlertList" + userstaffid.toString().trim();
          deliverAletName = "deliverAletList" + userstaffid.toString().trim();
          // 工单提醒
          workOrderListName = 'workOrderList' + userstaffid.toString().trim();
        } else {
          showAlertName = "showAlertList";
          deliverAletName = "deliverAletList"
          // 工单提醒
          workOrderListName = 'workOrderList';
        }
        // 别的页面操作以后告诉这个页面 有人操作过
        if (evt.data == 'bi : bi') {
          var localWindow = localStorage.getItem('localWindow');
          if (window.location.href != localWindow) {
            _this.showAlertList = JSON.parse(localStorage.getItem(showAlertName));
          } else {
            _this.expiredDisplay = true;
            var expiredDisplayNum = _this.expiredDisplayNum || _this.showAlertList.length;
            _this.expiredDisplayNum = expiredDisplayNum - 1;
            if (_this.expiredDisplayNum == 0) {
              _this.showAlertList = [];
            }
          }

          var linkUrl = [
            "/profile/taskmanage.aspx",
            "/admin/taskmanage.aspx",
            "/branchadmin/taskmanage.aspx",
          ];
          var curLink = window.location.pathname;
          linkUrl.forEach(function (item, index) {
            if (item == curLink) {
              setTimeout(function () { location = location }, 500);
            }
          })

        }

        // 第一次连接 保存 ConnectId && userstaffid 用作销毁当前会议
        if (_this.webSocketNum == 0) {
          _this._ConnectId = evt.data.split(".ConnectId")[1];
        }
        _this.webSocketNum = _this.webSocketNum + 1;
        // 第三次链接以后 才处理数据
        if (_this.webSocketNum >= 1) {
          if (_this.webSocketNum > 1) {
            // 避免数据返回超时 时候断开乱接的问题
            if (evt.data && evt.data != 'ping : ping' && evt.data != 'bi : bi' && evt.data != null) {
              rowArr = [];
              rowArr.push(evt.data);
              if (localStorage.getItem(showAlertName) != null) {
                _this.showAlertList = JSON.parse(localStorage.getItem(showAlertName)) || [];
              }
              if (localStorage.getItem(deliverAletName) != null) {
                _this.deliverList = JSON.parse(localStorage.getItem(deliverAletName)) || [];
              }

              if (localStorage.getItem(workOrderListName) != null) {
                _this.workOrderList = JSON.parse(localStorage.getItem(workOrderListName)) || [];
              }


              rowArr.forEach(function (row, index) {
                if (row != null) {
                  var rowIndex = row.indexOf(":") + 1;
                  var rowData = row.slice(rowIndex);
                  if (rowData != null) {
                    var rowList = JSON.parse(rowData);
                    var AlertListId = [];
                    _this.showAlertList.forEach(function (my, index) {
                      AlertListId.push(my.id);
                    });
                    _this.deliverList.forEach(function (my, index) {
                      AlertListId.push(my.id);
                    });
                    _this.workOrderList.forEach(function (my, index) {
                      AlertListId.push(my.id);
                    });
                    if (AlertListId.indexOf(rowList.id) == -1) {
                      rowList.taskTypeText = Number(rowList.taskType);
                      // 消息类型为10的
                      // 职位提醒（ 超15天未交付 ）
                      // 在小达中提示 此处过滤
                      if (rowList.taskTypeText != 10) {
                        // 大于6 是 交付中心提醒~
                        if (rowList.taskTypeText > 6) {
                          _this.deliverList.push(rowList);
                        } else {
                          if (rowList.taskTypeText === 0) {
                            // 等于0是  工单提醒数据~
                            _this.workOrderList.push(rowList);
                          } else {
                            // 小于6是  我的提醒数据~
                            _this.showAlertList.push(rowList);
                          }

                        }
                      }
                    }
                  }
                }
              });
              // 保存在本地 下个页面使用
              localStorage.setItem(showAlertName, JSON.stringify(_this.showAlertList));

              // 2020-12-09 保存在本地
              // 交付中心提醒消息
              localStorage.setItem(deliverAletName, JSON.stringify(_this.deliverList));

              // 2021-01-21 保存在本地
              // 工单提醒消息
              localStorage.setItem(workOrderListName, JSON.stringify(_this.workOrderList));

              // 根据当前操作状态显示 是否展开 | 关闭
              _this.hasShowModelState('showAlertModel');
              // 是否关闭了昨日工作总结  暂时关闭 
              // _this.hasShowModelState('showLeftAlert');
              // 关闭当前链接
              // ws.close();
            }
          } else {
            var LastTimeData = JSON.parse(localStorage.getItem(showAlertName)) || [];

            // 交付中心日程提醒数据位置
            _this.deliverList = JSON.parse(localStorage.getItem(deliverAletName)) || [];

            _this.workOrderList = JSON.parse(localStorage.getItem(workOrderListName)) || [];

            // 保存在本地 下个页面使用
            localStorage.setItem(showAlertName, JSON.stringify(LastTimeData));
            _this.showAlertList = LastTimeData;

            var messageNum = _this.showAlertList.length + _this.deliverList.length + _this.workOrderList.length;
            messageNum = messageNum > 99 ? "99+" : messageNum;
            if (messageNum > 0) {
              $("#systemMessageNum").show();
              $("#systemMessageNum").text(messageNum);
            } else {
              $("#systemMessageNum").hide();
              $("#systemMessageNum").text(messageNum);
            }
            _this.hasShowModelState('showAlertModel');
          }
        }
      };
      ws.onclose = function (evt) {
        stateErr = true;
      };
      ws.onerror = function (evt) {
        _this.initWebSocketErrorFrequency += 1;
      }
    },
    settingTiming: function (t1, tIndex, tId) {
      var _this = this;
      if (new Date(t1) - new Date() <= 0) {
        var oldShowAlertList = JSON.parse(JSON.stringify(_this.showAlertList));

        var showAlertName = "";
        if (userstaffid) {
          showAlertName = "showAlertList" + userstaffid.toString().trim();
        } else {
          showAlertName = "showAlertList"
        }

        // 保存在本地 下个页面使用

        clearInterval(this.timer[tIndex]);
        _this.$set(_this.countDown, tId, "00:00")

      } else {
        var d = this.computingTime(t1, new Date(), 'd');
        var h = this.computingTime(t1, new Date(), 'h');
        var m = this.computingTime(t1, new Date(), 'm');
        var s = this.computingTime(t1, new Date(), 's');
        _this.$set(_this.countDown, tId, m + ':' + s)
      }
    },
    computingTime: function (t1, t2, tg) {
      // 相差的毫秒数
      var ms = Date.parse(t1) - Date.parse(t2);
      var minutes = 1000 * 60;
      var hours = minutes * 60;
      var days = hours * 24;
      var years = days * 365;
      // 求出天数
      var d = Math.floor(ms / days);
      // 求出除开天数，剩余的毫秒数
      ms %= days;
      var h = Math.floor(ms / hours);
      ms %= hours;
      var m = Math.floor(ms / minutes) >= 10 ? Math.floor(ms / minutes) : "0" + Math.floor(ms / minutes);
      ms %= minutes;
      var s = Math.floor(ms / 1000) >= 10 ? Math.floor(ms / 1000) : "0" + Math.floor(ms / 1000);
      // 返回所需值并退出函数
      switch (tg) {
        case 'd': return d;
        case 'h': return h;
        case 'm': return m;
        case 's': return s;
      }
    },
    // 判断是否显示昨日总结 或者 今日提醒弹框
    hasShowModelState: function (state) {
      // 正常的 看是否操作过
      var currentState = JSON.parse(localStorage.getItem(state));
      if (currentState === null) {
        if (state == 'showAlertModel') {
          // 只有这个状态是张开的
          this[state] = false;
        } else {
          this[state] = true;
        }
      } else {
        this[state] = currentState.showAlert;
      }

      // 根据日期显示不一样的背景图
      if (state == 'showLeftAlert') {
        var week = new Date().getDay();
        // 周六 周日 显示 周一
        if (week == 0 || week == 6) {
          this.week = 1;
        } else {
          // 工作日 按照图片顺序展示
          this.week = week
        }
      }
    },
    // 关闭昨日工作总结
    closeLeft: function () {
      localStorage.setItem('showLeftAlert', JSON.stringify({ showAlert: false }));
      this.showLeftAlert = false;
    },
    // 获取昨日工作总结
    initLeft: function () {
      $.ajax({
        url: '',
        data: {

        },
        success: function (res) {
          if (res.Success) {

          } else {
            r_Layout_BeautAlert.done(res.message, "hits", 3000);
          }
        }
      })
    },
    // 差看今日日程提醒详情
    openReminderDetails: function (id) {
      window.open('/profile/taskmanage.aspx?p=1', '_blank')
    },
    // 打开今日提醒弹框
    openAlert: function (showModel) {
      window.open('/message/managemessages.aspx', '_blank')
      //localStorage.setItem('showAlertModel', JSON.stringify({ showAlert: !showModel }));
      //this.showAlertModel = !showModel;
    },
    // 获取今天日程提醒数据
    init: function () {
      $.ajax({
        url: '',
        data: {

        },
        success: function (res) {
          if (res.Success) {

          } else {
            r_Layout_BeautAlert.done(res.message, "hits", 3000);
          }
        }
      })
    }
  }
})

var url = "https://staff-exam-api.risfond.com"
//var url = "http://teststaff-exam-api.risfond.com"
//var url = "http://************:8277"
//考试和培训题型弹框
var examsTrainingTip = new Vue({
  el: '#examsTraining',
  data: function () {
    return {
      staffId: userstaffid,
      examsTrainData: {}, 
      examsObj: {},
      examsArray: [],
      examsTrainLength: 0,
      trainObj: {},
      trainArray: [],
      trainIndex: 0,
      examsTrainArray: [],
      getApiUrl: getApiUrl
    }
  },
  mounted() {
    var _this = this
    if (_this.getApiUrl) {
    this.ajaxExamsTraining()

    }
  },
  methods: {
    //下次参加
    nextOkClick() {
      var _this = this;
      var data = {
        staffId: _this.staffId,
        type: _this.examsObj.type,
        noticeId: _this.examsObj.id,
      }

      $.ajax({
        url: _this.getApiUrl+"/api/RnssNotice/GetNextNotice",
        data: JSON.stringify(data),
        type: "post",
        contentType: "application/json", //发送服务器数据的编码方式
        success: function (res) {
          if (res.succeeded) {
            r_Layout_BeautAlert.done("设置成功", "hits", 2000);
            _this.trainIndex++
            if (_this.trainIndex <= _this.examsTrainArray.length - 1) {
              _this.examsObj = _this.examsTrainArray[_this.trainIndex]
            } else {
              $("#examsDialog").modal("hide")
            }
          } else {
            beautAlert.done(res.message, "hits");
          }
        },
      });
    },

    openExamsClick() {
      var _this = this;
      window.open("http://staff-exam.risfond.com/answerNotice?id=" + _this.examsObj.id, '_target')
      _this.trainIndex++
      if (_this.trainIndex <= _this.examsTrainArray.length - 1) {
        _this.examsObj = _this.examsTrainArray[_this.trainIndex]
      } else {
        $("#examsDialog").modal("hide")
      }
    },
    openTrainClick() {
      var _this = this;
      window.open("http://staff-exam.risfond.com/trainDetail?id=" + _this.examsObj.id, '_target')
    },

    ajaxExamsTraining: function () {
      var _this = this;
      _this.examsArray = []
      _this.trainArray = []
      $.ajax({
        type: "get",
        url: _this.getApiUrl +"/api/RnssNotice/GetList/" + _this.staffId,
        success: function (res) {
          if (res.succeeded) {
            if (res.data.exams.length > 0 || res.data.training.length > 0) {
              $("#examsDialog").modal("show")
              res.data.exams.forEach(e => {
                const item = {
                  id: e.id,
                  name: e.name,
                  notice: e.notice,
                  target: e.target,
                  type: 2
                }
                _this.examsArray.push(item)
              })
              res.data.training.forEach(e => {
                const item = {
                  id: e.id,
                  name: e.theme,
                  introduce: e.introduce,
                  target: e.target,
                  type: 1
                }
                _this.trainArray.push(item)
              })
              _this.examsTrainArray = _this.examsArray.concat(_this.trainArray);
              console.log("_this.examsTrainArray", _this.examsTrainArray)
              _this.examsObj = _this.examsTrainArray[_this.trainIndex]
            } else {
              $("#examsDialog").modal("hide")
            }
          } else {
            beautAlert.done(res.errors, "hits");
          }
        },
      });
    },


  },



})




if ($("#newAddScheduleDom").length > 0) {
  // 添加日程的model
  var newAddSchedule = new Vue({
    el: '#newAddScheduleDom',
    data: function () {
      return {
        openTimepickerSchedule: null, // 时间选择插件的实例存放位置
        currentDate: '',             // 当前日期
        typeName: '',               // 添加分类名称
        remindtimelength: 5,        // 提前时间
        titleName: "",              // 弹框标题
        titleNameArr: {
          '1': '客户日程',           // 客户详情页面
          '2': '简历日程',           // 简历详情页面
          '5': '职位日程',           // 职位详情页面
          '3': '人选日程',           // 人选详情页面
          '4': '其他日程',           // 其他提醒页面
          '6': '面试日程'            // 面试提醒页面 
        },
        typeNameArr: {
          '1': '客户提醒',           // 客户详情页面
          '2': '简历提醒',           // 简历详情页面
          '5': '职位提醒',           // 职位详情页面
          '3': '人选提醒',           // 人选详情页面
          '4': '其他提醒',           // 其他提醒页面
          '6': '面试提醒'            // 面试提醒页面 
        },
        showNewAddSchedule2: false,  // 二次确认弹框
        showNewAddSchedule: false,   // 显示弹框
        addScheduleTextarea: '',     // 提醒内容
        dataType: '',                // 添加分类名称类型id 
        dataId: '',                  // 添加日程 id 
        taskid: '',                  // 编辑日程 id
        openTaskId: ''               // 日程管理页面回显展开需要的 id
      }
    },
    methods: {
      // 查看今日日程提醒详情
      openReminderDetails: function () {
        window.open('/profile/taskmanage.aspx', 'blank')
      },
      // 关闭添加日程的model
      closeModel: function () {
        this.showNewAddSchedule2 = false;
        this.showNewAddSchedule = false;
      },
      // 打开编辑日程model
      openEditModel: function (_this, e) {
        // 阻止冒泡
        window.event ? window.event.cancelBubble = true : e.stopPropagation();
        this.taskid = $(_this).attr("data-taskid");
        this.gettaskforedit(this.taskid);
      },

      // 获取日程详情
      gettaskforedit: function (taskid) {
        var _this = this;
        $.ajax({
          url: '/services/taskactionhandler.ashx?action=gettaskforedit',
          type: 'POST',
          data: {
            taskid: taskid
          },
          success: function (res) {
            if (res.Status === 1) {
              var taskTime = res.Data.TaskTime.split('T')                     // 日期时间
              $("#addSchedule__id_1").val(taskTime[0]);
              $("#addSchedule__id_2").val(taskTime[1].slice(0, 5));
              _this.typeName = _this.typeNameArr[res.Data.Type];            // 显示名称类型
              _this.titleName = _this.titleNameArr[res.Data.Type];          // 显示不同的title
              _this.remindtimelength = res.Data.RemindTimeLength;           // 提前时间
              _this.addScheduleTextarea = res.Data.Content;                 // 题型内容
              _this.showNewAddSchedule = true;
            } else {
              r_Layout_BeautAlert.done(res.message, "hits", 3000);
            }
          }
        })
      },
      // 打开添加日程的model
      openAddModel: function (_this) {
        $("#addSchedule__id_2").val("");
        this.dataType = $(_this).attr("data-type");
        this.dataId = $(_this).attr("data-id");
        this.remindtimelength = '5';
        this.typeName = this.typeNameArr[this.dataType];
        this.addScheduleTextarea = '';
        this.showNewAddSchedule = true;
        this.titleName = "其他日程"
      },
      // 在日程管理页面的 点击我知道了 需要刷新页面
      closeModel2: function () {
        var url = "";
        var currentId = '';
        var data = {}
        var time1 = $("#addSchedule__id_1").val();
        var time2 = $("#addSchedule__id_2").val();
        var TaskTime = time1 + ' ' + time2;   // 拼接时间格式
        var _this = this;
        if (!time2) {
          r_Layout_BeautAlert.done('请选择提醒时间', "hits", 2000);
          return;
        }
        if (!this.addScheduleTextarea) {
          r_Layout_BeautAlert.done('请输入提醒内容', "hits", 2000);
          return;
        }

        if (this.dataId) {
          // 添加
          url = '/services/taskactionhandler.ashx?action=addtask';
          currentId = this.dataId;
          data =
          {
            Clientid: currentId,
            Resumeid: currentId,
            Jobcandidateid: currentId,
            Jobid: currentId,
            type: this.dataType,
            Title: '',    // 默认传空 本次迭代没有用到此字段
            Content: this.addScheduleTextarea,
            TaskTime: TaskTime,
            remindtimelength: this.remindtimelength
          }
        } else {
          // 编辑
          url = '/services/taskactionhandler.ashx?action=updatetask';
          currentId = this.taskid;
          data =
          {
            taskid: this.taskid,
            Title: '',    // 默认传空 本次迭代没有用到此字段
            Content: this.addScheduleTextarea,
            TaskTime: TaskTime,
            remindtimelength: this.remindtimelength
          }
        }
        $.ajax({
          url: url,
          data: data,
          type: "POST",
          success: function (res) {
            if (res.Status === 1) {
              setTimeout(function () { location = location }, 500);
            } else {
              r_Layout_BeautAlert.done(res.Message, "hits", 3000);
            }
          }
        })
      },
      // 保存添加日程的model
      saveDetail: function () {
        var url = "";
        var currentId = '';
        var data = {}
        var time1 = $("#addSchedule__id_1").val();
        var time2 = $("#addSchedule__id_2").val();
        var TaskTime = time1 + ' ' + time2;   // 拼接时间格式
        var _this = this;
        if (!time2) {
          r_Layout_BeautAlert.done('请选择提醒时间', "hits", 2000);
          return;
        }
        if (!this.addScheduleTextarea) {
          r_Layout_BeautAlert.done('请输入提醒内容', "hits", 2000);
          return;
        }

        if (this.dataId) {
          // 添加
          url = '/services/taskactionhandler.ashx?action=addtask';
          currentId = this.dataId;
          data =
          {
            Clientid: currentId,
            Resumeid: currentId,
            Jobcandidateid: currentId,
            Jobid: currentId,
            type: this.dataType,
            Title: '',    // 默认传空 本次迭代没有用到此字段
            Content: this.addScheduleTextarea,
            TaskTime: TaskTime,
            remindtimelength: this.remindtimelength
          }
        } else {
          // 编辑
          url = '/services/taskactionhandler.ashx?action=updatetask';
          currentId = this.taskid;
          data =
          {
            taskid: this.taskid,
            Title: '',    // 默认传空 本次迭代没有用到此字段
            Content: this.addScheduleTextarea,
            TaskTime: TaskTime,
            remindtimelength: this.remindtimelength
          }
        }
        $.ajax({
          url: url,
          data: data,
          type: "POST",
          success: function (res) {
            if (res.Status === 1) {
              _this.showNewAddSchedule2 = true;
              _this.showNewAddSchedule = false;
              _this.openTaskId = res.Data;  // 保存跳转到日程管理页面 回显需要字段
            } else {
              r_Layout_BeautAlert.done(res.Message, "hits", 3000);
            }
          }
        })

      },
      getCurrentDate: function () {
        var year = new Date().getFullYear();
        var month = new Date().getMonth() + 1;
        var date = new Date().getDate();
        this.currentDate = year + '-' + month + '-' + date;
      },
      initDatepicker: function () {
        var _this = this;
        var myDate = new Date();
        $("#addSchedule__id_1").prop("readonly", true).datepicker({
          minDate: 0,
          changeMonth: true,
          changeYear: true,
          showButtonPanel: true,
          yearRange: "2005:" + (myDate.getFullYear() + 1),
          showOtherMonths: true,
          selectOtherMonths: true,
          onSelect: function (time) {
            // 判断是不是 当前时间 从而销毁实例重新创建实例
            if (_this.currentDate == time) {
              console.log("The currently selected date is today");
              _this.openTimepickerSchedule.removeData();
              _this.initTimepicker();
            } else {
              _this.openTimepickerSchedule.removeData();
              _this.initTimepicker("00:00");
            }
          }
        }).val(new Date(myDate.setDate(myDate.getDate())).format("yyyy-MM-dd"));
      },
      initTimepicker: function (updateTime) {
        var _this = this;
        // 修改时间显示问题只能显示整点 或者 半点 所以处理一下
        var myDate = new Date();
        var getMinutes = myDate.getMinutes() > 30 ? 00 : 30;
        var getHours = myDate.getMinutes() > 30 ? myDate.getHours() + 1 : myDate.getHours();
        var startTime = getHours + ':' + getMinutes;

        if ($('#addSchedule__id_2').length == 0) {
          return;
        }
        if ($('#addSchedule__id_1').length == 0) {
          return;
        }

        _this.openTimepickerSchedule = $('#addSchedule__id_2').prop("placeholder", "请选择提醒时间").timepicker({
          start: '00:00',                                   // 修改器起始时间
          minTime: updateTime || startTime,  // 修改为只显示当前时间
          maxTime: '00:00',
          timeFormat: "H:i",
          scrollDefaultNow: true
        });
      }
    },
    mounted() {
      this.getCurrentDate();
      this.initTimepicker();
      this.initDatepicker();
    }
  });

}
