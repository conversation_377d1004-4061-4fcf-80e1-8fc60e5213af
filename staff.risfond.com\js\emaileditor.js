/// <reference path="../Scripts/jquery-1.8.2.js" />
/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
(function ($) {
	var EmailEditor = {
		Opts: {
			Category: [],
			CategorySelected:0,
			Content: "",
			DataRecipients: [],
			Subject:"",
			Submit: function (data) { },
			DialogCallBack: function () { }
		},
		//初始化
		init: function (params) {
			this.Opts = $.extend({}, this.Opts, params || {});
			var html = this.loadDom();//加载dom
			this.showDialog(html);//弹出窗口
			this.modalityBox = this.setupRecipients();//初始化收件人
			this.setupDefault(this.Opts, html);
			this.setupSubmit(html);
		},
		//显示弹出层
		showDialog: function (html) {
			DialogAlert.msgOpen({ msgTitle: setRnssLanguage("编辑邮件"), msgBody: html, msgWidth: 580, msgPanelWidth: 570 });
		},
		//初始化收件人
		setupRecipients: function () {
			return $(".modalityboxes-email").modalityboxes({
				Regex: /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/,
				Format: function (value) { return $.trim(value); },
				Scope: ".email-box",
				AddressLimit: 20,
				ContactLimit: 15,
				IsNullContact: function (contact, address) {
					var d=address.split("@");
					return d.length == 2 ? d[0] : address
				}
			});
		},
		//设置默认值
		setupDefault: function (params,el) {
			var content = el.find(".emailcontent");
			var category = el.find(".emailcategory");
			var emailtitle = el.find(".emailtitle");
			content.val(params.Content);
			category.html(this.setupGetOption(params.Category, params.CategorySelected));
			emailtitle.val(params.Subject);
			var $this = this;
			$.each(params.DataRecipients, function () {
				$this.modalityBox.dataaddlist(this.Contact, this.Address);
			});
			this.Opts.DialogCallBack.call(this,el,params);
		},
		//获取option的元素结构
		setupGetOption: function (category,selected) {
			var option = '';
			$.each(category, function () {
				if (parseInt(selected) == parseInt(this.Value)) {
					option += '<option value="' + this.Value + '" selected="selected">' + this.Text + '</option>';
				}
				else {
					option += '<option value="' + this.Value + '">' + this.Text + '</option>';
				}
			});
			return option;
		},
		//提交表单
		setupSubmit: function (el) {
			var $this = this;
			$(el).find(".btn-submit").click(function () {
				$this.Opts.Submit(el,$this.modalityBox.getalldata());
			});
			$(el).find(".btn-cancel").click(DialogAlert.msgClose);
		},
		//DOM元素
		loadDom: function () { return $('<div class="email-box cf"><div class="line cf"><label class="emailkey">' + setRnssLanguage('类型：') + '</label><div class="emailvalue email-change-box"><select class="emailcategory"></select><a href="javascript:;" class="btn-setconfig">' + setRnssLanguage('邮件设置') + '</a></div></div><div class="line cf"><label class="emailkey">' + setRnssLanguage('收件人：') + '</label><div class="emailvalue"><div class="modalityboxes-email"></div></div></div><div class="line cf"><label class="emailkey">' + setRnssLanguage('邮件标题：') + '</label><div class="emailvalue"><input type="text" class="input-v1 emailtitle" /></div></div><div class="line cf"><label class="emailkey">' + setRnssLanguage('邮件内容：') + '</label><div class="emailvalue"><textarea class="emailcontent textarea-v1"></textarea></div></div></div><div class="dialog-footer"><div class="my-dialog-form my-editer-email-box"><input type="button" value="' + setRnssLanguage('发送') + '" class="btn-submit"><input type="button" value="' + setRnssLanguage('取消') + '" class="btn-cancel"></div></div>'); },
		//设置
		setting: function () {
			return {

			};
		}
	}
	$.fn.emailEditor = function (params) {
		this.each(function () {
			$(this).data("emailEditor", EmailEditor.setting);
			EmailEditor.init(params);
		});
		return $(this).data('emailEditor');
	};
})(jQuery)