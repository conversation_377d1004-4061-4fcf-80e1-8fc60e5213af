.r_width_25 {
    width: 25% !important;
}

.bg-orange {
    background: #F57C2B !important;
}

.bg-purple-blue {
    background: #6543ef !important;
}

.bg-dark-blue {
    background: #1868AE !important;
}

.bg-cyan-green {
    background: #1BBC9C !important;
}


#yc_ssxxhd .slimScrollDiv {
    height: 215px !important;
}

#cooperation_wrapper.isadmin .r-tb-cpt .r-to {
    width: 93px;
}

#cooperation_wrapper .cpt__wrapper {
    height: 249px;
}

.index-iconinfo-notice {
    background-color: #F94444;
    position: absolute;
    top: 5px;
    right: -26px;
    line-height: 1;
    padding: 3px 8px;
    font-size: 12px;
    border-radius: 10px !important;
}

.page-head, .performance-summary {
    background: #fff;
}
.darenwangjiaokecheng {
    margin-top:15px!important;
}
.keh<PERSON><PERSON><PERSON>an {
    background:RGB(233,236,243);
    padding-left:0px!important;
    padding-right:0px!important;
    padding-bottom:0px!important;
}
.page-head {
    padding: 15px;
    margin-top: -10px;
}
.paihangbang {
    /*background: RGB(233,236,243);*/
    background: transparent;
    padding-left:0px;
    padding-right:0px;
    padding-bottom:0px;
    margin-top:0px!important;

}

.isadmin {
    margin-top:-11px;
}
.lianmengbang {
    padding-top: 0px;
}
.page-title > h1 {
    font-weight: bold !important;
}

.dashboard-stat2.bordered {
    background: RGB(249,249,249);
    border: none !important;
    width:100%;
    height:95%;
    margin: 0 auto;
    margin-bottom:14px;
}

.KA_kaopu_dakehu {
    margin-top: 7px !important;
}
.progressbar {
    position: relative;
    display: block;
    width: 200px;
    height: 20px;
    padding: 10px 20px;
    border-bottom: 1px solid rgba(255,255,255,0.25);
    border-radius: 16px;
}
.progressbar1 {
    position: relative;
    display: block;
    width: 200px;
    height: 20px;
    padding: 10px 20px;
    border-bottom: 1px solid rgba(255,255,255,0.25);
    border-radius: 16px;
}

.progressbar2 {
    position: relative;
    display: block;
    width: 200px;
    height: 20px;
    padding: 10px 20px;
    border-bottom: 1px solid rgba(255,255,255,0.25);
    border-radius: 16px;
}
.progressbar3 {
    position: relative;
    display: block;
    width: 200px;
    height: 20px;
    padding: 10px 20px;
    border-bottom: 1px solid rgba(255,255,255,0.25);
    border-radius: 16px;
}

.progressbar:before {
    position: absolute;
    display: block;
    content: "";
    width: 198px;
    height: 4px;
    top: 11px;
    left: 20px;
    -webkit-border-radius: 20px;
    border-radius: 20px;
    background: RGB(255,226,206);
    margin-left: -20px;
}

.progressbar1:before {
    position: absolute;
    display: block;
    content: "";
    width: 198px;
    height: 4px;
    top: 11px;
    left: 20px;
    -webkit-border-radius: 20px;
    border-radius: 20px;
    background: RGB(203,232,252);
    margin-left: -20px;
}
.progressbar2:before {
    position: absolute;
    display: block;
    content: "";
    width: 198px;
    height: 4px;
    top: 11px;
    left: 20px;
    -webkit-border-radius: 20px;
    border-radius: 20px;
    background: RGB(117,255,228);
    margin-left: -20px;
}
.progressbar3:before {
    position: absolute;
    display: block;
    content: "";
    width: 198px;
    height: 4px;
    top: 11px;
    left: 20px;
    -webkit-border-radius: 20px;
    border-radius: 20px;
    background: RGB(222,237,249);
    margin-left: -20px;
}

.bar {
    position: absolute;
    display: block;
    width: 0px;
    height: 4px;
    top: 11px;
    left: 22px;
    background: #FF6600;
    border-radius: 16px;
    overflow: hidden;
    margin-left: -22px;
}

    .bar.color2 {
        background: rgb(58,155,222);
      
    }

    .bar.color3 {
        background: rgb(33,190,158);
        
    }

    .bar.color4 {
        background: rgb(93,157,215);
       
    }

    .bar:before {
        position: absolute;
        display: block;
        content: "";
        width: 306px;
        height: 100%;
        top: -25%;
        left: -25px;
        background: -moz-radial-gradient(center, ellipse cover, rgba(255,255,255,0.35) 0%, rgba(255,255,255,0.01) 50%, rgba(255,255,255,0) 51%, rgba(255,255,255,0) 100%);
    }

    .bar:after {
        position: absolute;
        display: block;
        content: "";
        width: 64px;
        height: 6px;
        right: 0;
        top: 0;
        -webkit-border-radius: 0px 16px 16px 0px;
        border-radius: 0px 16px 16px 0px;
        background: -moz-linear-gradient(left, rgba(255,255,255,0) 0%,  98%, rgba(255,255,255,0) 100%);
        
    }

    .bar span {
        position: absolute;
        display: block;
        width: 100%;
        height: 64px;
        -webkit-border-radius: 16px;
        border-radius: 16px;
        top: 0;
        left: 0;
        -webkit-animation: sparkle 1500ms linear infinite;
        -moz-animation: sparkle 1500ms linear infinite;
        -o-animation: sparkle 1500ms linear infinite;
        animation: sparkle 1500ms linear infinite;
        opacity: 0.2;
    }

@-webkit-keyframes sparkle {
    from {
        background-position: 0 0;
    }

    to {
        background-position: 0 -64px;
    }
}

@-moz-keyframes sparkle {
    from {
        background-position: 0 0;
    }

    to {
        background-position: 0 -64px;
    }
}

@-o-keyframes sparkle {
    from {
        background-position: 0 0;
    }

    to {
        background-position: 0 -64px;
    }
}

@keyframes sparkle {
    from {
        background-position: 0 0;
    }

    to {
        background-position: 0 -64px;
    }
}
.icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

.icon1 {
    width: 1.2em;
    height: 1.2em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}
.icon5 {
    width: 3.2em;
    height: 3.2em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
    color: red;
}
.icon9 {
    width: 2.5em;
    height: 2.5em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

.icon123 {
   font-size:16px!important;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

.caption_icon9 {
    margin-left: 70px;
}

.icon-item {
    width: 5.0em;
    height: 5.0em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

.dashboard-report-range {
    width: 140px;
    height: 28px;
    border: 1px solid #2A7FCC;
    line-height: 28px;
    margin-left: 15px;
    border-radius: 13px !important;
    text-align: center;
    color: #2A7FCC;
}

.dashboard-report-range1 {
    width: 66px;
    height: 28px;
    border: 1px solid #2A7FCC;
    line-height: 28px;
    margin-left: 15px;
    border-radius: 13px !important;
    text-align: right;
    color: #2A7FCC;
}
.dashboard-report-range2 {
    width: 76px;
    height: 28px;
    border: 1px solid #2A7FCC;
    line-height: 28px;
    margin-left: 15px;
    border-radius: 13px !important;
    text-align: center;
    color: #2A7FCC;
    display: flex;
    align-items: center;
    justify-content: center;
}
        .page-title :hover {
    cursor: pointer
}
.page-title > h1 {
    font-size: 20px!important;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400!important;
    color: #333333!important;
    line-height: 28px!important;
}
.page-toolbar :hover {
    cursor: pointer
}

.KA-kaopudakehu {
    width: 100%;
}

.KA-kaopudakehu-top {
    width: 100%;
    height: 50px;
}

.kuaijierukou-top {
    width: 100%;
    height: 50px;
}

.KA-kaopudakehu-content {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
}

.KA-kaopudakehu-content_first {
    width: 31%;
    margin-left:20px
}
    .KA-kaopudakehu-content_first:hover {
        cursor:pointer;
    }

    .KA-kaopudakehu-content-img > img {
        width: 100%
    }

.KA-kaopudakehu-content-title {
    font-size: 18px;
    font-weight: 500;
    line-height: 25px;
    margin-top: 28px;
    white-space: nowrap;
    text-overflow: ellipsis; /* ��ʡ�Ժ� */
    overflow:hidden;
}
.KA-kaopudakehu-content-title-white {
    width:80%;
    font-size: 22px;
    font-weight: 600;
    line-height: 1.4em;
    color: #FFF;
    margin-top: -108px;
    border:1px solid #fff;



}
.KA-kaopudakehu-content-title:hover {
    color: #2A7FCC;
}

.KA-kaopudakehu-content-date {
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 14px;
    margin-top: 10px;
}

.KA-kaopudakehu-content-description {
    font-size: 13px;
    font-weight: 400;
    color: #666666;
    line-height: 22px;
    margin-top: 5px;
    word-break: break-all;
    height: 60px;
    display: block;
    overflow: hidden;
    white-space:pre-wrap;
    text-overflow: ellipsis;
    border:1px solid #fff;
}

.KA-kaopudakehu-content-description > p {
    font-size: 14px;
    color:#666666;
    line-height:22PX;
    overflow: hidden; /* �������ı����� */
    text-overflow: ellipsis; /* �����ʡ�Ժ���ʾ */
    display: -webkit-box; /* ��������Ϊ������������ģ����ʾ */
    -webkit-box-orient: vertical; /*  ���ϵ��´�ֱ������Ԫ�أ������������ӵ���Ԫ�����з�ʽ�� */
    -webkit-line-clamp: 2; /* ������Բ���css�Ĺ淶���ԣ���Ҫ��������������ԣ���ʾ��ʾ������ */
}

.kuaijierukou-content {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
}
.kuaijierukou-content-item {
text-align:center;
}
.kuaijierukou-content-item:hover {
    cursor:pointer
    
}

.kuaijierukou-content-item > img {
    width: 50%;
}

.kuaijierukou-content-item-title {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 20px;
    margin:12px 0;
}

.gaiban {
    color: #2A7FCC !important;
    border-color: #2A7FCC !important;
}
.gaiban:hover {
    background: #2A7FCC!important;
    color:#FFF!important;
}
.lianmengbang_sort_123 {
    width: 100%;
    height: 213px;
    zoom: 1;
    background-color: #fff;
    background: url(../images/lianmengbang_sort.png) no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
}
.border {
    border:1px solid red;
}
.marginL1 {
    margin-left: 6.5%;
    width: 122px;
}
.marginL2 {
    width:122px;
    margin-left: 4.0%;
}
.marginL3 {
    width: 122px;
    margin-left: 6.8%;
}
.lianmengbang_sort_1, .lianmengbang_sort_2, .lianmengbang_sort_3 {
    font-size: 16px;
    color: #fff;
    text-align: center;
    font-size: 14px;
}

.lianmengbang_sort_4 {
    margin-top: 8px;
}
.lianmengbang_sort_5 {
    margin-top: 8px;
}

.lianmengbang_sort_1 {
    margin-top: 15px !important;
}

.lianmengbang_sort_2 {
    margin-top:-22px;
}

.lianmengbang_sort_3 {
    margin-top:-22px;
}
.lianmengbang_sort_2_shouru, .lianmengbang_sort_1_shouru, .lianmengbang_sort_3_shouru {
    width: 100%;
    height: 24px;
    border: 1px solid #fff;
    margin-top: 6px;
    border-radius: 12px !important;
    font-size: 12px;
    font-weight: 600;
    color: #fff;
    line-height: 24px;
    text-align:center;
}
.huikuanjine {
    float:right
}
.male {
    background: #26C281;
    color: #fff;
    font-size: 13px;
    padding: 4px 4px;
    border-radius: 3px !important;
    font-weight: 400;
}
.male:hover {
   color:#fff;
   font-weight:400;
   text-decoration:none;
}

.female {
    background: #E26A6B;
    color: #fff;
    font-size: 13px;
    padding: 4px 4px;
    border-radius: 3px !important;
    font-weight: 400;
}
    .female:hover {
        color: #fff;
        font-weight: 400;
        text-decoration: none;
    }
.text-color {
    color: #D91E18 !important;
    font-weight: bold;
}

.portlet-body111 {
    display:flex;
    flex-direction:row;
    justify-content:space-between;
}
.portlet-body111-item {
    width:18%;
}
.portlet-body111-item:hover {
    cursor:pointer;
    transform:scale(1.1);
    transition-duration:0.5s;
}

.portlet-body111-item > a >img {
    width: 100%;
    border-radius: 8px !important;
}
.portlet-body-newsList {
    display:flex;
    flex-direction:row;
    justify-content:space-around;
}
.portlet-body-newsList-icon, .portlet-body-newsList-title, .portlet-body-newsList-date {

}
    .portlet-body-newsList-icon > i {
        font-size: 16px;
        background: RGB(237,107,117);
        color: #fff;
        padding: 6px 7px;
    }
    .portlet-body-newsList:hover {
        background: #F9F9F9;
        cursor: pointer;
    }
.portlet-body-newsList-icon {
    width: 10%;
}
.portlet-body-newsList-icon > svg {
    width:80%;    
}
.portlet-body-newsList-title {
    width: 60%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.portlet-body-newsList-date {
    width: 30%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.bg-white {
    display:flex;
    flex-direction:row;
    justify-content:space-around
}
.titlewgs {
    width:90%;
}
.titlewgs > span {
    font-size:14px;
}
.closeed {
    width: 10%;
    text-align: right
}
.closeed:hover {
    cursor:pointer;
    transform:scale(1.1);
    transition-duration:0.5s;
}
.custom_shortcut_sure_desc > span {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
    line-height: 20px;
}
.custom_shortcut_sure_item {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    margin-top: 10px;
    text-align: center;
}
.custom_shortcut_sure_items {
    margin-top: 20px;
    width: 12%;
    margin-left:18px;
}
.custom_shortcut_sure_items_div > span {
    opacity: 0;
}
.custom_shortcut_sure_items_div:hover span {
    opacity: 1;
}
.custom_shortcut_sure_items > span {
    opacity: 0;
}

.custom_shortcut_sure_items:hover span {
    opacity: 1;
}
.custom_shortcut_sure_items-img {
    width: 40%;
    margin-top: 12px;
    margin-left: 30px;
}
.custom_shortcut_sure_items:hover {
    cursor: pointer;
    background: #EBF6FF;
    border-radius: 5px!important
}
.closed {
    color: red !important;
    font-size: 22px !important;
    margin-top: 100px !important;
    position: relative !important;
    left: 14px;
    top: -21px;
}
.add_add {
    color: RGB(8,186,32)!important;
    font-size: 22px !important;
    margin-top: 100px !important;
    position: relative !important;
    left: 18px;
    top: -25px;
}
.item_content ul {
    list-style: none;
}
.item_content ul li {
    width: 200px;
    height: 120px;
    float: left;
    margin: 10px
}
.item_content {
    width: 740px;
    height: 460px;
    border: 1px solid #ccc;
    margin: 0 auto;
}
.item_content .item {
    width: 200px;
    height: 120px;
    line-height: 120px;
    text-align: center;
    cursor: pointer;
    background: #ccc;
}
.item_content .item img {
    width: 200px;
    height: 120px;
    border-radius: 6px;
}
.cancel, .save {
    float: right;
    margin-left: 15px;
    text-decoration: none
}
    .cancel:hover {
        text-decoration: none;
    }
    .save:hover {
        text-decoration: none;
        color:#fff;
    }
.save {
    width: 90px;
    height: 34px;
    background: #2A7FCC;
    border-radius: 3px!important;
    line-height: 34px;
    padding: 0 24px;
    border: 1px #26bbdb solid;
    display: inline-block;
    text-decoration: none;
    font-size: 16px;
    outline: none;
    color:#fff;
}

.cancel {
    width: 90px;
    height: 34px;
    background: #fff;
    border-radius: 3px !important;
    line-height: 34px;
    padding: 0 24px;
    border: 1px solid #DDDDDD;
    display: inline-block;
    text-decoration: none;
    font-size: 16px;
    outline: none;
}
.actions > ul > li {
    float: right;
    border: 1px solid #DDDDDD;
    border-right: 0.5px solid #DDDDDD;
    padding: 4px 12px;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #333333;
}

.actions > ul > li:hover {
    cursor: pointer;
}

.current_li {
    width: 40px;
    height: 30px;
    background: #2A7FCC;
    border-radius: 0px 2px 2px 0px !important;
    color: #fff!important
}

.customerCollectionRankingList > img {
    width: 45%;
}
.customerCollectionRankingList_span {
    font-size: 16px !important;
    color: #fff;
    background: #2A7FCC;
    padding:3px 5px;
}
.text-left > a {
    color: #333333;
}
.text-left:hover .text-left > a {
    color: #2A7FCC
}
.custom_shortcut_sure_items:active {
    cursor:move
}
.wait_loading {
text-align:center
}

.svg_wgs {
    height: 80px;
    width: 80px;
    margin: 0 2em 2em;
    overflow: visible;
    /*   border:1px solid red; */
}

circle {
    fill: dodgerblue;
    fill-opacity: 0;
    -webkit-animation: opacity 1.2s linear infinite;
    animation: opacity 1.2s linear infinite;
}

    circle:nth-child(12n + 1) {
        -webkit-animation-delay: -0.1s;
        animation-delay: -0.1s;
    }

    circle:nth-child(12n + 2) {
        -webkit-animation-delay: -0.2s;
        animation-delay: -0.2s;
    }

    circle:nth-child(12n + 3) {
        -webkit-animation-delay: -0.3s;
        animation-delay: -0.3s;
    }

    circle:nth-child(12n + 4) {
        -webkit-animation-delay: -0.4s;
        animation-delay: -0.4s;
    }

    circle:nth-child(12n + 5) {
        -webkit-animation-delay: -0.5s;
        animation-delay: -0.5s;
    }

    circle:nth-child(12n + 6) {
        -webkit-animation-delay: -0.6s;
        animation-delay: -0.6s;
    }

    circle:nth-child(12n + 7) {
        -webkit-animation-delay: -0.7s;
        animation-delay: -0.7s;
    }

    circle:nth-child(12n + 8) {
        -webkit-animation-delay: -0.8s;
        animation-delay: -0.8s;
    }

    circle:nth-child(12n + 9) {
        -webkit-animation-delay: -0.9s;
        animation-delay: -0.9s;
    }

    circle:nth-child(12n + 10) {
        -webkit-animation-delay: -1s;
        animation-delay: -1s;
    }

    circle:nth-child(12n + 11) {
        -webkit-animation-delay: -1.1s;
        animation-delay: -1.1s;
    }

    circle:nth-child(12n + 12) {
        -webkit-animation-delay: -1.2s;
        animation-delay: -1.2s;
    }

.g-circles--v2 circle {
    fill-opacity: 0;
    stroke-opacity: 0;
    stroke-width: 1;
    stroke: yellowgreen;
    -webkit-animation-name: opacity-stroke, colors, colors-stroke, transform-2;
    animation-name: opacity-stroke, colors, colors-stroke, transform-2;
}

.g-circles--v3 circle {
    fill-opacity: 1;
    -webkit-animation-name: opacity, colors;
    animation-name: opacity, colors;
}

.g-circles--v4 circle {
    fill-opacity: 1;
    fill: orange;
    -webkit-transform-origin: 60px 60px;
    -ms-transform-origin: 60px 60px;
    transform-origin: 60px 60px;
    -webkit-animation-name: opacity, colors-3, transform;
    animation-name: opacity, colors-3, transform;
}

@-webkit-keyframes opacity {
    3% {
        fill-opacity: 1;
    }

    75% {
        fill-opacity: 0;
    }
}

@keyframes opacity {
    3% {
        fill-opacity: 1;
    }

    75% {
        fill-opacity: 0;
    }
}

@-webkit-keyframes opacity-stroke {
    10% {
        stroke-opacity: 1;
    }

    85% {
        stroke-opacity: 0;
    }
}

@keyframes opacity-stroke {
    10% {
        stroke-opacity: 1;
    }

    85% {
        stroke-opacity: 0;
    }
}

@-webkit-keyframes colors {
    0% {
        fill: yellowgreen;
    }

    10% {
        fill: gold;
    }

    75% {
        fill: crimson;
    }
}

@keyframes colors {
    0% {
        fill: yellowgreen;
    }

    10% {
        fill: gold;
    }

    75% {
        fill: crimson;
    }
}

@-webkit-keyframes colors-stroke {
    0% {
        stroke: yellowgreen;
    }

    10% {
        stroke: gold;
    }

    75% {
        stroke: crimson;
    }
}

@keyframes colors-stroke {
    0% {
        stroke: yellowgreen;
    }

    10% {
        stroke: gold;
    }

    75% {
        stroke: crimson;
    }
}

@-webkit-keyframes colors-2 {
    0% {
        fill: yellow;
    }

    50% {
        fill: red;
    }

    65% {
        fill: orangered;
    }

    95% {
        fill: gold;
    }
}

@keyframes colors-2 {
    0% {
        fill: yellow;
    }

    50% {
        fill: red;
    }

    65% {
        fill: orangered;
    }

    95% {
        fill: gold;
    }
}

@-webkit-keyframes colors-3 {
    0% {
        fill: yellowgreen;
    }

    50% {
        fill: turquoise;
    }

    65% {
        fill: yellow;
    }

    95% {
        fill: orange;
    }
}

@keyframes colors-3 {
    0% {
        fill: yellowgreen;
    }

    50% {
        fill: turquoise;
    }

    65% {
        fill: yellow;
    }

    95% {
        fill: orange;
    }
}

@-webkit-keyframes transform {
    10% {
        -webkit-transform: scale(0.75);
        transform: scale(0.75);
    }
}

@keyframes transform {
    10% {
        -webkit-transform: scale(0.75);
        transform: scale(0.75);
    }
}

@-webkit-keyframes transform-2 {
    40% {
        -webkit-transform: scale(0.85);
        transform: scale(0.85);
    }

    60% {
        stroke-width: 20;
    }
}

@keyframes transform-2 {
    40% {
        -webkit-transform: scale(0.85);
        transform: scale(0.85);
    }

    60% {
        stroke-width: 20;
    }
}
.lianmengbang_sort_1_sss {
    text-align: right;
    margin-bottom: -35px;
    margin-right:12px;
}

.lianmengbang_sort_2_sss {
    text-align: right;
    margin-top: 35px;
    position: relative;
    left: -10px;
}

.lianmengbang_sort_3_sss {
    text-align: right;
    margin-top: 45px;
}

.itemRank {
    position:relative;
    
}
.itemRank > p{
    height:100%;
    position:absolute;
    transform:translateX(50%,50%)
}
.icow_wgs {
    font-size: 16px !important;
    color: #FFF;
    background: #4B77BE;
    padding: 3px 6px;
    border-radius: 2px !important;
}
.col-xs-12 {
}
.table_td_div_name, .table_td_div_rank, .table_td_div {
    width: 100%;
    height: 30px;
    line-height:30px;
}
    .table_td_div_name:hover, .table_td_div_rank:hover, .table_td_div:hover {
        cursor: pointer;
        color: #2A7FCC!important;
    }

    .table_td_div_name:after {
        content: '';
        display: inline-block;
        height: 100%;
        width: 1px;
        vertical-align: middle;
    }
    .table_td_div_rank:after {
        content: '';
        display: inline-block;
        height: 100%;
        width: 1px;
        vertical-align: middle;
    }

.table_td_div_name > img {
    width: 13%;
    vertical-align: middle;
}
    .table_td_div_rank > img {
        width: 22px;
        height:28px;
        vertical-align: middle;
    }
.title_hover:hover {
    color: #2A7FCC;
    cursor:pointer;
}
.yc_xszw {
    padding: 12px 22px 15px
}
.hui-offer-ruzhi {
    display: flex;
    flex-direction: row;
    justify-content:space-between;
}
.IncomeAmount, .EntryNum, .OfferCount,.NewResumeCount{
    width:22%;
    background:RGB(249,249,249);
    margin-bottom:15px;
}
.more_paihang {
    width: 100%;
    height: 6px;
    text-align:center;
    border:1px solid #fff;
    position:relative;
    top:-10px;
}
.more_paihang > a {
    line-height: 40px;
    font-size: 14px;
    font-weight: 400;
    color: #2A7FCC;
}
.more_paihang > a:hover {
    text-decoration:none;
}
.no_data_vp {
    font-size: 14px;
    color: #999;
    padding: 5px 0;
    text-align: center;
    height:259px;
}
    .no_data_vp > img {
        width: 15%;
        margin-top: 70px;
    }
.no_data_vp1 {
    font-size: 14px;
    color: #999;
    padding: 5px 0;
    text-align: center;
    height: 270px;
}
.no_data_vp1 > img {
    width: 7%;
    margin-top:60px;
}
/* v6.0.4��ҳ�İ���ʽ*/
.yysj-title {
    padding: 0 !important;
}
.yysj-title .caption > span {
    line-height: 24px;
}
/*.page-title-svg {
    position: relative;
    top: 3px;
}*/
.operational-data .operational-data-items {
    display: flex;
    flex-flow: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    margin-left: 20px;
    margin-right: 10px;
    z-index: 8;
}
.operational-data .operational-data-item {
    /*width: 186px;*/
    width: calc(20% - 20px);
    margin-right: 10px;
    height: 66px;
    background: #F9F9F9;
    border-radius: 4px !important;
    margin-bottom: 20px;
}
.operational-data-item .item-img {
    display: inline-block;
    float: left;
    margin: 17px 18px 12px 20px;
}
.operational-data-item .item-content {
    display: inline-block;
}
.item-content .content-num {
    margin-top: 10px;
    margin-bottom: 1px;
    height: 26px;
    line-height: 26px;
}
.item-content .font-color-haze1 {
    font-size: 20px;
    height: 26px;
    width: 104px;
    display: block;
    overflow: hidden;
    letter-spacing: 0.57px;
    white-space: nowrap;
    -ms-text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
}
.item-content .font-color-blue {
    color: #41A5F8;
}
.item-content .font-color-yellow {
    color: #FF8800
}
.item-content .font-color-pink {
    color: #FF8981;
}
.item-content .font-color-violet {
    color: #B283F6;
}
.item-content .font-color-green {
    color: #15C978;
}
.item-content .status-title1 {
    height: 14px;
    line-height: 14px;
    font-size: 14px;
    letter-spacing: 0.4px;
}
.index-paihangbang {
    width: 1034px;
}
.index-paihangbang .portlet {
    margin-bottom: 0;
}
.index-paihangbang .index-paihangbang-left {
    float: left;
    width: 510px;
    background-color: #FFFFFF;
}
.index-paihangbang-left .kpbig-link {
    display: block;
    width: 100%;
    height: 100%;
}
.index-paihangbang-left .portlet-title-svg {
    width: 26px;
    height: 26px;
    float: left;
}
.index-paihangbang-left .base-title {
    color: rgb(42, 127, 204) !important;
    font-weight: 600 !important;
    font-size: 18px !important;
    height: 18px;
    line-height: 26px;
    margin-left: 9px;
}
.page-head .page-mr-left {
    margin-left: 5px;
}
.page-head .caption-icon {
    width: 23px;
    height: 23px;
    float: left;
}
.kehuhuikuan-icon {
    width: 23px;
    height: 23px;
}
.page-head .caption-sharp {
    margin-left: 12px;
    color: rgb(42, 127, 204) !important;
    font-weight: 600 !important;
    font-size: 18px;
    line-height: 28px;
    margin-top: -2px;
    display: block;
    float: left;
}
.index-paihang-right {
    padding-right: 10px;
}
.index-paihang-bold {
    font-weight: 600 !important;
}