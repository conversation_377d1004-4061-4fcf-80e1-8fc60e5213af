#chat-modal .chat-hide-icon {
  position: absolute;
  top: 0px;
  right: -45px;
  height: 30px;
  cursor: pointer;
  display: none;
}
#chat-modal.in ~ .modal-backdrop {
  opacity: .6;
}
#chat-modal .chat-modal-close {
  font-size: 40px;
  position: absolute;
  right: 0;
  color: #333;
  font-weight: lighter;
  cursor: pointer;
}
#chat-modal .modal-content {
  border: 0;
  position: relative;
}
#chat-modal .modal-dialog {
  width: 1045px!important;
}
#chat-modal .chat-container {
  height: 100%;
  width: 100%;
  display: flex;
  color: #fff;
  font-family: Helvetica Neue,Helvetica,Hiragino Sans GB,Microsoft YaHei,\\5FAE\8F6F\96C5\9ED1,Arial,sans-serif;
}

#chat-modal .chat-menu {
  width: 250px;
  background-color: #2e3238;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}
#chat-modal .chat-menu-header {
}
#chat-modal .chat-menu-header-profile-panel {
  display: flex;
  align-items: center;
  display: flex;
  align-items: center;
  padding: 18px;
  position: relative;
}
#chat-modal .chat-menu-header-profile {
  width: 100%;
}
#chat-modal .chat-menu-profile-logo {
  width: 40px;
  height: 40px;
  border-radius: 2px!important;
  cursor: pointer;
}
#chat-modal .chat-menu-profile-name {
  font-size: 16px;
}
#chat-modal .chat-menu-header-opt {
  width: 20px;
  text-align: center;
  cursor: pointer;
}
#chat-modal .chat-menu-header-opt-icon {
  color: #777;
  font-size: 15px;
}
#chat-modal .chat-menu-opt-popup {
  position: absolute;
  top: calc(100% - 17px);
  right: 10px;
  z-index: 1;
  background-color: #fff;
  color: #000;
  border-radius: 4px !important;
  min-width: 100px; 
}
#chat-modal .chat-menu-opt-popup-item {
  font-size: 14px;
  padding: 8px;
  border-bottom: 1px solid #f1f1f1;
  cursor: pointer;
  transition: background-color .2s;
  text-align: center;
}
#chat-modal .chat-menu-opt-popup-item:last-child {
  border-bottom-width: 0px;
}
#chat-modal .chat-menu-opt-popup-item:hover {
  background-color: #f5f5f5;
  border-radius: 4px!important;
}


#chat-modal .chat-menu-search-panel {
  position: relative;
  width: calc(100% - 36px);
  margin: 0 18px 6px;
}

#chat-modal .chat-menu-search-input {
  width: 100%;
  height: 32px;
  line-height: 32px;
  border: 0;
  border-radius: 2px;
  background-color: #26292e;
  color: #fff;
  padding-left: 30px;
  padding-right: 30px;
  font-size: 12px;
}
#chat-modal .chat-menu-search-icon {
  position: absolute;
  z-index: 101;
  top: 8px;
  left: 8px;
  height: 16px;
  width: 16px;
  margin: auto;
}
#chat-modal .chat-menu-search-icon.search-remove {
  left: initial;
  right: 8px;
  cursor: pointer;
  opacity: 0.7;
}
#chat-modal .chat-menu-search-popup {
  position: absolute;
  background-color: #fff;
  z-index: 99;
  top: 0;
  left: 0;
  outline: none;
  background: #33363b;
  width: 100%;
  top: 36px;
  left: 0;
  box-shadow: 0 0 10px #2a2a2a;
  border-radius: 2px!important;
}
#chat-modal .chat-menu-search-scroll-wrapper {
  position: relative;
  max-height: 420px;
  padding: 0;
  overflow-y: scroll;
}
#chat-modal .chat-menu-search-scroll-wrapper::-webkit-scrollbar {
  width: 0px; /* Remove scrollbar space */
  background: transparent; /* Optional: just make scrollbar invisible */
}
#chat-modal .chat-menu-search-title {
  padding: 3px 9px;
  font-weight: 400;
  color: #787b87;
  margin-top: 10px;
  background-color: #393c43;
}
#chat-modal .chat-menu-search-title:first-child {
  margin-top: 0;
}
#chat-modal .chat-menu-search-item {
  overflow: hidden;
  padding: 10px 9px;
  cursor: pointer;
  border-bottom: 1px solid #33363b;
  background-color: #393c43;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
#chat-modal .chat-menu-search-item:hover {
  background-color: #595b64;
}
#chat-modal .chat-menu-search-staff-info {
  display: flex;
  align-items: center;
  width: 100%;
  overflow: hidden;
}
#chat-modal .chat-menu-search-staff-logo {
  display: block;
  width: 40px;
  height: 40px;
  border-radius: 2px!important;
  margin-right: 10px;
}
#chat-modal .chat-menu-search-staff-profile {
  width: calc(100% - 50px);
}
#chat-modal .chat-menu-search-staff-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
#chat-modal .chat-menu-search-staff-company {
  color: #989898;
  font-size: 13px;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
  height: 1.5em;
}
#chat-modal .chat-menu-search-loading-more {
  padding: 3px 9px;
  font-weight: 400;
  color: #fff;
  background-color: #393c43;
  cursor: pointer;
}
#chat-modal .chat-menu-search-loading-more:hover {
  background-color: #595b64;
}
#chat-modal .chat-menu-tab {
  display: flex;
  padding-top: 5px;
  padding-bottom: 14px;
  border-bottom: 1px solid #24272c;
  cursor: pointer;
}

#chat-modal .chat-menu-tab-item {
  flex-grow: 1;
  text-align: center;
  border-right: 1px solid #24272c;
}

#chat-modal .chat-menu-tab-item .tab-icon {
  height: 25px;
  font-size: 25px;
  line-height: 1;
  transition: color .3s;
  position: relative;
}
#chat-modal .tab-icon-wrapper {
  display: inline-block;
  position: relative;
}
#chat-modal .chat-menu-tab-item .tab-icon-selected {
  display: none;
}
#chat-modal .chat-menu-tab-item .tab-icon-unselected {
  display: initial;
}
#chat-modal .tab-icon-unread {
  height: 10px;
  width: 10px;
  background-color: #f51c1c;
  border-radius: 5px !important;
  top: 0px;
  left: -2px;
  position: absolute;
}
#chat-modal .chat-menu-tab-item.active .tab-icon-selected, #chat-modal .chat-menu-tab-item:hover .tab-icon-selected {
  display: initial;
}
#chat-modal .chat-menu-tab-item.active .tab-icon-unselected, #chat-modal .chat-menu-tab-item:hover .tab-icon-unselected {
  display: none;
}
#chat-modal .chat-menu-tab-item:last-child {
  border-right-width: 0;
}
#chat-modal .chat-menu-content {
  height: 100%;
  /*overflow: scroll;*/
  overflow-y: auto;
}
#chat-modal .chat-menu-content.menu-mask {
  opacity: 0.05;
}
#chat-modal .chat-menu-content::-webkit-scrollbar {
  width: 0px; /* Remove scrollbar space */
  background: transparent; /* Optional: just make scrollbar invisible */
}
#chat-modal .chat-menu-content-item {
  overflow-y: scroll;
  height: 100%;
}
#chat-modal .chat-menu-content-item::-webkit-scrollbar {
  width: 0px; /* Remove scrollbar space */
  background: transparent; /* Optional: just make scrollbar invisible */
}
#chat-modal .chat-menu-messages-panel {
  height: initial;
}
#chat-modal .chat-list {
  overflow-y: scroll;
  overflow-x: hidden;
}
#chat-modal .chat-list::-webkit-scrollbar {
  width: 0px; /* Remove scrollbar space */
  background: transparent; /* Optional: just make scrollbar invisible */
}
#chat-modal .chat-item-wrapper {
}
#chat-modal .chat-item {
  overflow: hidden;
  padding: 12px 18px 11px;
  padding-top: 12px;
  padding-right: 18px;
  padding-bottom: 11px;
  padding-left: 18px;
  border-bottom: 1px solid #292c33;
  cursor: pointer;
  position: relative;
  transition: background-color .2s;
}
#chat-modal .chat-item.company-item {
  position: sticky;
  top: 0;
  background-color: #2e3238;
  z-index: 1;
}
#chat-modal .chat-message-item-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  display: block!important;/**不知道为何第一次点击好友顾问打开聊天面板时，左侧会话列表倒数第二条出现隐藏*/
}
#chat-modal .chat-item-icon {
  float: right;
}
#chat-modal .chat-item-icon-close {
  display: none;
}
#chat-modal .chat-item-role {
  display: none;
}
#chat-modal .chat-item-role-item {
  background-color: #26292e;
}
#chat-modal .chat-item-loading {
  display: none;
}
#chat-modal .chat-item:hover, #chat-modal .chat-item.active  {
  background-color: #3a3f45;
  color: inherit;
}
#chat-modal .chat-item-wrapper.chat-item-open .chat-item-icon-close {
  display: block;
}
#chat-modal .chat-item-wrapper.chat-item-open .chat-item-icon-open {
  display: none;
}
#chat-modal .chat-item-wrapper.chat-item-open .chat-item-role {
  display: block;
}
#chat-modal .chat-item-time {
  float: right;
  color: #6b6f7c;
  font-size: 13px;
  text-align: right;
}
#chat-modal .chat-item-avatar {
  float: left;
  margin-right: 10px;
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 2px !important;
}
#chat-modal .chat-item-avatar-icon {
  height: 40px;
  border-radius: 4px !important;
}
#chat-modal .chat-item-info {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
#chat-modal .chat-item-nickname {
  font-weight: 400;
  font-size: 13px;
  color: #fff;
  line-height: 20px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
#chat-modal .chat-item-msg {
  color: #989898;
  font-size: 13px;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
  height: 1.5em;
}
#chat-modal .chat-item-msg-notice {
  color: #f51c1c;
}
#chat-modal .chat-list-unread-icon {
  min-width: 21px;
  text-align: center;
  background-color: #f51c1c;
  border-radius: 50% !important;
  top: -5px;
  left: -5px;
  position: absolute;
  padding: 2px 4px;
  font-size: 12px;
}
#chat-modal .chat-menu-groups-empty {
  text-align: center;
  margin-top: 140px;
}
#chat-modal .chat-menu-groups-empty-img {
  width: 80px;
  opacity: 0.5;
}
#chat-modal .chat-menu-groups-empty-name {
  color: #9c9c9c;
}

#chat-modal .chat-panel {
  width: calc(100% - 250px);
  color: #000;
  background-color: #eee;
  display: flex;
  flex-direction: column;
}
#chat-modal .chat-panel-header {
  line-height: 30px;
  text-align: center;
  position: relative;
}
#chat-modal .chat-panel-title {
  position: relative;
  padding: 10px 0;
  margin: 0 19px;
  border-bottom: 1px solid #d6d6d6;
}

#chat-modal .chat-panel-header-opt {
  display: inline-block;
  cursor: pointer;
}


#chat-modal .chat-panel-panel {
  height: calc(100% - 51px);
}

/** 群组设置 start */
#chat-modal .chat-group-setting-wrapper {
  height: 100%;
  padding: 17px;
}
#chat-modal .chat-group-setting-item {
  padding: 10px;
  background-color: #fff;
  border-radius: 4px !important;
  margin-bottom: 15px;
}
#chat-modal .chat-group-setting-item-title {
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
  display: flex;
  border-bottom: 1px solid #eee;
  justify-content: space-between;
}
#chat-modal .chat-group-setting-item-title-opt {
  cursor: pointer;
}

#chat-modal .chat-group-setting-item-content {
  display: flex;
  padding-top: 10px;
  color: #888;
  flex-wrap: wrap;
}
#chat-modal .user-list {
  display: flex;
  padding-bottom: 10px;
  color: #888;
  flex-wrap: wrap;
}

#chat-modal .chat-group-member-item {
  width: 80px;
  padding-right: 20px;
  text-align: center;
  padding-bottom: 5px;
  cursor: pointer;
}
#chat-modal .chat-group-member-item.is-owner {
  order: -2;
}
#chat-modal .chat-group-member-item.is-admin {
  order: -1;
}
#chat-modal .chat-group-member-wrapper {
  position: relative;
}
#chat-modal .chat-group-member-logo {
  width: 100%;
  border-radius: 4px !important;
  border: solid 1px #ccc;
  display: flex;
  line-height: 1;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  font-weight: lighter;
  color: #ccc;
}
#chat-modal .chat-group-member-name {
  color: #888;
}
#chat-modal .chat-group-member-remove {
  position: absolute;
  top: 0;
  right: 0;
  color: #ff0000;
  cursor: pointer;
}
#chat-modal .chat-group-member-item.add-member {
  cursor: pointer;
}
#chat-modal .chat-group-member-item.add-member .chat-group-member-logo {
  height: 60px;
}
#chat-modal .chat-group-setting-list-item {
  display: flex;
  justify-content: space-between;
  padding-bottom: 5px;
  border-bottom: solid 1px #eee;
}
#chat-modal .chat-group-setting-list-item.last-item {
  border-bottom-width: 0px;
  padding-top: 5px;
  padding-bottom: 0px;
}
#chat-modal .chat-group-setting-list-title {

}
#chat-modal .chat-group-setting-list-content {
  color: #888;
}
#chat-modal .chat-group-leave-btn {
  display: inline-block;
  width: 50%;
  text-align: center;
  position: relative;
  left: 25%;
  padding-top: 7px;
  padding-bottom: 7px;
  background-color: #d23d3d;
  color: #fff;
  border-radius: 4px !important;
}
/** 群组设置 end */
/** 未选择聊天对象时出现的内容面板 start */
#chat-modal .chat-empty-wrapper {
  height: 100%;
}
#chat-modal .chat-empty-content {
  height: 100%;
  text-align: center;
  padding-top: 20%;
}
#chat-modal .chat-empty-icon {
  color: #e0e0e0;
  font-size: 100px;
}
#chat-modal .chat-empty-notice {
  margin-top: 15px;
  color: #c0c0c0;
}
/** 未选择聊天对象时出现的内容面板 start */

/** 员工详情面板 start */
#chat-modal .chat-staff-intro-wrapper {
  height: 100%;
}
#chat-modal .chat-staff-intro-panel {
  height: 100%;
  padding: 30px;
  text-align: center;
}
#chat-modal .chat-staff-intro-profile {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: solid 1px #ddd;
}
#chat-modal .chat-staff-intro-name-panel {
  display: inline-block;
  padding-top: 20px;
}
#chat-modal .chat-staff-intro-name {
  font-size: 20px;
}
#chat-modal .chat-staff-intro-title {
  color: #888;
}
#chat-modal .chat-staff-intro-img {
  width: 90px;
  border-radius: 4px !important;
}
#chat-modal .chat-staff-intro-info {
  display: flex;
  flex-wrap: wrap;
  padding-top: 20px;
}
#chat-modal .chat-staff-intro-item {
  width: 50%;
  display: flex;
  color: #888;
  margin-bottom: 10px;
}
#chat-modal .chat-staff-intro-item-title {
  width: 100px;
  text-align: right;
  padding-right: 20px;
}
#chat-modal .chat-staff-intro-item-content {
  width: 100%;
  text-align: left;
}
#chat-modal .chat-staff-chat {
  margin-top: 100px;
}
#chat-modal .chat-staff-chat-btn {
  display: inline-block;
  width: 200px;
  text-align: center;
  color: #fff;
  line-height: 40px;
  background-color: #42ac3e;
  font-size: 14px;
  text-decoration: none;
  border-radius: 4px !important;
  cursor: pointer;
}
/** 员工详情面板 end */
/** 群消息面板 start */
#chat-modal .chat-notice-wrapper {
  padding-left: 19px;
  padding-right: 19px;
  height: 100%;
}
#chat-modal .chat-notice-list-wrapper {
  height: 100%;
  overflow-y: scroll;
}
#chat-modal .chat-notice-list-wrapper::-webkit-scrollbar {
  width: 0px; /* Remove scrollbar space */
  background: transparent; /* Optional: just make scrollbar invisible */
}
#chat-modal .chat-notice-loading {
  text-align: center;
  padding-top: 10px;
}
#chat-modal .chat-notice-list {
}
#chat-modal .chat-notice-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
  padding-bottom: 10px;
  border-bottom: solid 1px #ddd;
}
#chat-modal .chat-notice-info {
  display: flex;
  align-items: center;
}
#chat-modal .chat-notice-icon {
  color: #487d45;
  height: 40px;
  border-radius: 2px!important;
}
#chat-modal .chat-notice-icon-group {
  height: 40px;
  width: 40px;
  flex-wrap: wrap;
  line-height: 1;
}
#chat-modal .chat-notice-icon-group-item {
  width: 20px;
}
#chat-modal .chat-notice-desc {
  padding-left: 10px;
}
#chat-modal .chat-notice-group-name {

}
#chat-modal .chat-notice-msg {
  color: #989898;
}
#chat-modal .chat-notice-time {
  color: #989898;
}
/** 群消息面板 end */
/** 聊天面板 start */
#chat-modal .chat-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}
#chat-modal .chat-content {
  height: 100%;
  overflow-y: scroll;
}
#chat-modal .chat-content::-webkit-scrollbar {
  width: 0px; /* Remove scrollbar space */
  background: transparent; /* Optional: just make scrollbar invisible */
}
#chat-modal .chat-msg-list {
  padding: 0 19px;
}
#chat-modal .chat-msg-load-more {
  color: #0077ff;
  text-align: center;
  font-size: 12px;
  padding-top: 10px;
  letter-spacing: 0.5px;
  cursor: pointer;
}
#chat-modal .chat-msg-item {
  margin-bottom: 16px;
  width: 100%;
}
#chat-modal .chat-msg-time {
  text-align: center;
  margin: 10px auto;
  max-width: 50%;
  font-size: 12px;
  padding: 1px 18px;
  color: #b2b2b2;
}
#chat-modal .chat-msg-item-logo {
  width: 40px;
  height: 40px;
  border-radius: 2px!important;
  float: left;
  cursor: pointer;
}
#chat-modal .chat-msg-item-info {
  overflow: hidden;
}
#chat-modal .me-item-info {
  display: flex;
  justify-content: flex-end;
}
#chat-modal .read-stats {
  font-size: 12px;
  line-height: 22px;
}
#chat-modal .chat_box_status {
  color: #999;
}
#chat-modal .chat_box_status1 {
  color: #0063F5;
}
#chat-modal .chat-msg-info-name {
  font-weight: 400;
  padding-left: 10px;
  font-size: 12px;
  height: 22px;
  line-height: 24px;
  color: #4f4f4f;
  width: 350px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
}
#chat-modal .chat-msg-info-bubble {
  max-width: 500px;
  min-height: 1em;
  display: inline-block;
  vertical-align: top;
  position: relative;
  text-align: left;
  font-size: 14px;
  border-radius: 3px!important;
  margin: 0 10px;
  background-color: #fff;
}
#chat-modal .chat-msg-info-bubble:before, #chat-modal .chat-msg-info-bubble:after{
  content: " ";
  right: 100%;
  position: absolute;
  top: 13px;
  border: 6px solid transparent;
}
#chat-modal .chat-msg-info-bubble:before {
  position: absolute;
  top: 14px;
  border: 6px solid transparent;
}
#chat-modal .chat-msg-info-bubble:after {
  border-right-color: #fff;
  border-right-width: 4px;
}
#chat-modal .chat-msg-info-plain {
  padding: 9px 13px;
  word-break: break-all;
  min-height: 25px;
  line-height: 1.6;
}
#chat-modal .chat-msg-resume {
  width: 300px;
  cursor: pointer;
}
#chat-modal .chat-msg-resume .reusmeCard_mian_box {
  margin: 0;
}
#chat-modal .chat-msg-resume .reusmeCard_mian_box {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  padding: 7px 0;
}
#chat-modal .chat-msg-resume .reusmeCard_mian_box image {
  width: 30px;
  height: 30px;
  margin-right: 5px !important;
}

#chat-modal .chat-msg-resume .reusmeCard_mian_box .reusmeCardimg {
  width: 38px;
  height: 38px;
  display: block;
  margin: auto 12px;
  margin-left: 12px;
}

#chat-modal .chat-msg-resume .reusmeCard_mian_box .resumeSignView {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
#chat-modal .chat-msg-job {
  width: 400px;
  cursor: pointer;
}
#chat-modal .chat-msg-resume:hover,
#chat-modal .chat-msg-job:hover {
  background-color: #f8f8f8;
}
#chat-modal .chat-resume-profile {
  display: flex;
  justify-content: space-between;
}
#chat-modal .chat-resume-name {

}
#chat-modal .chat-resume-salary {
  color: #ec3434;
}
#chat-modal .chat-resume-company {
  color: #888;
  font-size: 13px;
  padding-top: 5px;
}
#chat-modal .chat-resume-info-list {
  display: flex;
  padding-top: 5px;
  flex-wrap: wrap;
}
#chat-modal .chat-resume-info {
  background-color: #eee;
  margin-right: 10px;
  color: #888;
  border-radius: 2px !important;
  font-size: 13px;
  padding: 1px 8px;
  white-space: nowrap;
  margin-top: 10px;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
}

#chat-modal .chat-msg-emotion {
  height: 20px;
  vertical-align: middle;
}
#chat-modal .chat-msg-img-emoji {
  height: 60px;
  width: 60px;
}
#chat-modal .chat-msg-info-file {
  padding: 10px;
  background-color: #fff;
  min-height: 75px;
  border-radius: 4px !important;
  min-width: 250px;
  max-width: 300px;
  margin: 2px;
  position: relative;
}
#chat-modal .chat-msg-info-image {
  padding: 10px;
  background-color: #fff;
  border-radius: 4px !important;
  max-width: 300px;
  margin: 2px;
  position: relative;
}
#chat-modal .chat-msg-info-audio {
  padding: 9px 13px;
  word-break: break-all;
  min-height: 25px;
  line-height: 1.6;
}
#chat-modal .chat-msg-info-audio .content-audio {

}
#chat-modal .chat-msg-info-audio .content-audio:focus {
  outline: none;
}
#chat-modal .chat-msg-info-audio .content-video {
  height: 225px;
  align-items: center;
  display: flex;
  /*padding: 12px;*/
  padding-left: 0;
  /*width: 165px;*/
}
#chat-modal .chat-msg-info-audio .content-video:focus {
  outline: none;
}
#chat-modal .chat-msg-info-file-logo-panel {
  display: table-cell;
  padding-right: 10px;
}
#chat-modal .chat-msg-info-file-logo {
  display: inline-block;
  vertical-align: middle;
  font-size: 76px;
  height: 76px;
  line-height: 1;
  color: #68c864;
}
#chat-modal .chat-msg-info-file-info {
  display: table-cell;
  vertical-align: top;
}
#chat-modal .chat-msg-info-file-title {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
  max-width: 200px;
}
#chat-modal .chat-msg-info-file-desc {
  padding-top: 25px;
}
#chat-modal .file-download {
  color: #35ac2f;
  cursor: pointer;
}

#chat-modal .chat-msg-info-image-img {
  max-width: 350px;
  width: 100%;
  cursor: pointer;
}
#chat-modal .chat-image-loadding {
  /*height: 240px;*/
}
#chat-modal .chat-msg-item .chat-read-status {
  display: none;
}
/* 自己的聊天框 start */
#chat-modal .chat-msg-item.me {
}
#chat-modal .chat-msg-item.me .chat-msg-item-logo {
  float: right;
}
#chat-modal .chat-msg-item.me .chat-msg-item-info {
  text-align: right;
}
#chat-modal .chat-msg-item.me .chat-msg-info-name {
  display: none;
}
#chat-modal .chat-msg-item.me .chat-msg-info-bubble {
  background-color: #b2e281;
}

#chat-modal .chat-msg-item.me .chat-msg-info-bubble:before {
  left: 100%;
}
#chat-modal .chat-msg-item.me .chat-msg-info-bubble:after {
  left: 100%;
  border-left-color: #b2e281;
  border-right-color: transparent;
}
#chat-modal .chat-msg-item.me .chat-read-status {
  display: block;
}
  /* 自己的聊天框 end */
  /* 聊天输入框 start */
  #chat-modal .chat-input-panel {
    height: 210px;
    margin-right: 19px;
    border-top: 1px solid #d6d6d6;
  }
#chat-modal .chat-input-panel-toolbar {
  display: flex;
  height: 30px;
  padding: 5px 17px;
  position: relative;
}
#chat-modal .chat-toolbar-item {
  height: 30px;
  width: 30px;
  text-align: center;
  margin-right: 10px;
}
#chat-modal .chat-toolbar-icon {
  font-size: 20px;
  height: 20px;
  line-height: 1;
  cursor: pointer;
}
#chat-modal .chat-toolbar-icon.file {
  font-size: 17px;
}
#chat-modal .chat-expression-panel {
  position: absolute;
  bottom: 33px;
  background-color: #fff;
  left: 15px;
}
#chat-modal .chat-expression-wrapper {
  border: 1px solid #dedede;
}
#chat-modal .chat-expression-wrapper:after, #chat-modal .chat-expression-wrapper:before {
  content: "";
  position: absolute;
  left: 16px;
  top: 100%;
  margin-left: -7px;
}
#chat-modal .chat-expression-wrapper:after {
  margin-top: -1px;
  border: 7px solid transparent;
  border-top-color: #fff;
}
#chat-modal .chat-expression-wrapper:before {
  border: 7px solid transparent;
  border-top-color: #cfcfcf;
}
#chat-modal .chat-expression-header {
  padding: 8px 20px 0;
  overflow: hidden;
  background-color: #f2f2f2;
}
#chat-modal .chat-expression-item {
  float: left;
  cursor: pointer;
}
#chat-modal .chat-expression-item.active {
  background-color: #fff;
  border-top-left-radius: 4px!important;
  border-top-right-radius: 4px!important;
  cursor: initial;
}
#chat-modal .chat-expression-item-words {
  display: block;
  text-decoration: none;
  color: #333;
  padding: 5px 20px;
  font-size: 14px;
}
#chat-modal .chat-expression-content-panel {
  overflow: hidden;
  margin: 15px 20px;
}
#chat-modal .chat-expression-emoji-list {
  overflow: hidden;
  /*width: 420px;
  height: 202px;*/
  width: 364px;
  height: 142px;
  margin-right: -1px;
}
#chat-modal .chat-expression-emoji-item {
  float: left;
  width: 36px;
  height: 36px;
  font-size: 0;
  text-indent: -999em;
  border-bottom: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
#chat-modal .chat-expression-emoji-item:hover {
  background-color: #ddd;
}
#chat-modal .chat-expression-emoji-icon {
  width: 24px;
  margin-top: 2px;
  margin-left: 2px;
}

#chat-modal .chat-expression-emoji-list.big-img .chat-expression-emoji-item {
  width: 70px;
  height: 70px;
}
#chat-modal .chat-expression-emoji-list.big-img .chat-expression-emoji-icon {
  margin-top: 5px;
  margin-left: 5px;
}
#chat-modal .chat-expression-emoji-list.big-img .chat-expression-emoji-icon {
  width: 60px;
}
#chat-modal .chat-expression-emoji-list.hover-to-gif .chat-expression-emoji-icon.emoji-hover {
  display: none;
}

#chat-modal .chat-expression-emoji-list.hover-to-gif .chat-expression-emoji-item:hover .chat-expression-emoji-icon {
  display: none;
}

#chat-modal .chat-expression-emoji-list.hover-to-gif .chat-expression-emoji-item:hover .chat-expression-emoji-icon.emoji-hover {
  display: initial;
}





#chat-modal .chat-input-panel-input {
  height: 73px;
  padding-top: 10px;
  padding-bottom: 0px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-left: 20px;
  outline: none;
  border: 0;
  font-size: 14px;
  line-height: 1.5;
  background-color: transparent;
  white-space: pre-wrap;
  word-break: normal;
}
#chat-modal .chat-input-at-popup {
  position: fixed;
  background-color: #fff;
  border-radius: 4px !important;
  width: 200px;
}
#chat-modal .chat-input-at-item {
  padding: 3px 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}
#chat-modal .chat-input-at-item:hover {
  background-color: #ddd;
}
#chat-modal .chat-input-at-info {

}
#chat-modal .chat-input-at-img {
  height: 25px;
}
#chat-modal .chat-input-at-name {

}
#chat-modal .chat-input-at-company {

}




#chat-modal .chat-input-panel-opt {
  text-align: right;
  margin-top: 5px;
}
#chat-modal .chat-input-opt-notice {
  color: #888;
  font-size: 12px;
  margin-left: 10px;
  margin-right: 7px;
}
#chat-modal .chat-input-opt-send {
  position: relative;
  background-color: #fff;
  color: #222;
  padding-left: 30px;
  padding-right: 30px;
  text-decoration: none;
  display: inline-block;
  border: 1px solid #c1c1c1;
  border-radius: 4px!important;
  padding: 3px 20px;
  font-size: 14px;
  cursor: pointer;
}
#chat-modal .chat-input-opt-send:hover {
  background-color: #f8f8f8; 
}
#chat-modal .chat-empty-msg-notice {
  position: absolute;
  bottom: 33px;
  white-space: nowrap;
  background-color: #fff;
  padding: 5px 8px;
  border-radius: 3px !important;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
}
#chat-modal .chat-empty-msg-notice:after {
  content: " ";
  left: 10px;
  position: absolute;
  top: 100%;
  border: 6px solid transparent;
  border-top-color: #fff;
  border-top-width: 5px;
}


/* 聊天输入框 end */
/** 聊天面板 end */
/** 弹出框 start */
#chat-modal .chat-popup {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: Helvetica Neue,Helvetica,Hiragino Sans GB,Microsoft YaHei,\\5FAE\8F6F\96C5\9ED1,Arial,sans-serif;
}
#chat-modal .chat-popup-panel {
  width: 500px;
  background-color: #fff;
  height: 300px;
  display: inline-block;
  border-radius: 4px !important;
  display: flex;
  flex-direction: column;
}
#chat-modal .chat-popup-create-group {

}
#chat-modal .chat-popup-panel-title {
  padding: 10px;
  border-bottom: solid 1px #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
#chat-modal .chat-popup-panel-name {

}
#chat-modal .chat-popup-panel-opt {

}
#chat-modal .chat-popup-panel-content {
  padding: 10px;
  height: calc(100% - 40px);
}
#chat-modal .create-group {
  height: initial;
}
#chat-modal .create-group .create-group-input {
  width: 100%;
}

#chat-modal .create-group .create-group-opt {
  text-align: right;
}
#chat-modal .create-group .create-group-opt-btn {
  color: #222;
  padding-left: 30px;
  padding-right: 30px;
  text-decoration: none;
  display: inline-block;
  border-radius: 4px!important;
  padding: 3px 20px;
  font-size: 14px;
  cursor: pointer;
  background-color: #42ac3e;
  color: #fff;
}
#chat-modal .create-group .create-group-opt-btn:hover {

}

#chat-modal .chat-popup-panel-item {
  display: flex;
  margin-bottom: 5px;
}
#chat-modal .create-group-name {
  width: 80px;
}
#chat-modal .create-group-checkbox {
  margin-left: 20px;
}
#chat-modal .menu-popup {
  position: fixed;
  z-index: 1;
  background-color: #fff;
  color: #000;
  border-radius: 4px !important;
  min-width: 100px;
  font-family: Helvetica Neue,Helvetica,Hiragino Sans GB,Microsoft YaHei,\\5FAE\8F6F\96C5\9ED1,Arial,sans-serif;
}
#chat-modal .menu-popup-item {
  font-size: 14px;
  padding: 8px;
  border-bottom: 1px solid #f1f1f1;
  cursor: pointer;
  transition: background-color .2s;
  text-align: center;
  border-radius: 4px !important;
}
#chat-modal .search-group-list {
  overflow: hidden;
}
#chat-modal .search-group-item {
  display: flex;
  padding-bottom: 5px;
}
#chat-modal .search-group-list-content {
  overflow-y: scroll;
  height: 180px;
  margin-right: -17px;
}
#chat-modal .search-group-item-id {
  width: 166px;
}
#chat-modal .search-group-item-name {
  width: 100%;
}
#chat-modal .search-group-item-opt {
  width: 40px;
  cursor: pointer;
}

#chat-modal .select-user-panel {
  width: 300px;
  height: 500px;
  padding-left: 0;
}
#chat-modal .select-user-users-title {

}
#chat-modal .select-user-users {
  height: calc(100% - 50px);
  overflow-y:scroll;
}
#chat-modal .select-user-users::-webkit-scrollbar {
  display: none;
}
#chat-modal .select-user-user-item {
  
}

#chat-modal .select-user-company {
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}
#chat-modal .select-user-company:hover {
  background-color: #eee;
}
#chat-modal .select-user-company-icon-open {
  display: none;
}
#chat-modal .select-user-staff-list {
  display: none;
}
#chat-modal .select-user-staff {
  display: flex;
  justify-content: space-between;
}
#chat-modal .select-user-user-item.show-staff .select-user-staff-list {
  display: initial;
}
#chat-modal .select-user-user-item.show-staff .select-user-company-icon-open {
  display: initial;
}
#chat-modal .select-user-user-item.show-staff .select-user-company-icon-close {
  display: none;
}

#chat-modal .user-detail-popup {
  width: 220px;
  z-index: 999;
}
#chat-modal .user-detail-logo {
  width: 100%;
  border-radius: 4px !important;
}
#chat-modal .user-detail-info {
  padding: 5px;
}
#chat-modal .user-detail-brief {
  border-bottom: solid 1px #eee;
  padding-bottom: 5px;
}
#chat-modal .user-detail-name {
  font-size: 16px;
  text-align: center;
}
#chat-modal .user-detail-title {
  text-align: center;
  color: #888;
}
#chat-modal .user-detail-details-list {
  padding-top: 10px;
}
#chat-modal .user-detail-details-item {
  display: flex;
  padding-bottom: 5px;
  font-size: 13px;
}
#chat-modal .user-detail-item-name {
  width: 50px;
  padding-right: 5px;
}
#chat-modal .user-detail-item-val {
  color: #888;
  width: calc(100% - 50px);
  overflow: hidden;
  text-overflow: ellipsis;
}
#chat-modal .user-detail-opt-list {
  display: flex;
  flex-wrap: wrap;
  border-top: solid 1px #ccc;
  padding-top: 5px;
}
#chat-modal .user-detail-opt-item {
  width: 50%;
  text-align: center;
  cursor: pointer;
}
#chat-modal .user-detail-opt-item:hover {
  background-color: #eee;
}

#chat-modal .chat-notice-popup {
  background-color: #eee;
  width: 400px;
  height: 200px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
}
#chat-modal .chat-notice-popup .chat-popup-panel-content {
  display: flex;
  flex-direction: column;
}
#chat-modal .chat-notice-popup-wrapper {
  height: 100%;
}
#chat-modal .chat-notice-popup-msg {
  padding-top:30px;
  text-align: center;
}
#chat-modal .chat-notice-popup-opt {
  height: 30px;
  text-align: center;
}
#chat-modal .chat-notice-popup-btn {
  background-color: #42ac3e;
  color: #fff;
  padding: 4px 15px;
  border-radius: 3px !important;
  cursor: pointer;
  margin-left: 5px;
  margin-right: 5px;
}
#chat-modal .chat-notice-popup-btn:hover {
  background-color: #299e25;
}
#chat-modal .msg-link {
  color: #ff0000;
  cursor: pointer;
}
#chat-modal .chat-clipboard-image-popup {
  height: 400px;
}
#chat-modal .chat-clipboard-image-popup .chat-popup-panel-content {
  display: flex;
  flex-direction: column;
}
#chat-modal .chat-clipboard-image-popup .chat-notice-popup-wrapper {
  height: calc(100% - 60px);
}
#chat-modal .chat-clipboard-image-popup .chat-notice-popup-msg {
  height: 100%;
  padding-top: 0;
  padding-bottom: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
#chat-modal .chat-clipboard-image-popup .chat-clipboard-img {
  max-width: 100%;
  max-height: 100%;
}
#chat-modal .chat-clipboard-image-popup .chat-notice-popup-opt {
  height: 60px;
  border-top: solid 1px #eee;
  display: flex;
  justify-content: center;
  align-items: center;
}
#chat-modal .chat-clipboard-image-popup .chat-notice-popup-btn {
  width: 150px;
  margin-left: 10px;
  margin-right: 10px;
}
#chat-modal .chat-clipboard-image-popup .chat-notice-popup-btn.chat-notice-cancel-btn {
  background-color: #c9c9c9;
}
#chat-modal .chat-clipboard-image-popup .chat-notice-popup-btn.chat-notice-cancel-btn:hover {
  background-color: #a5a5a5;
}

#chat-modal .preview-popup {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 99999;
}
#chat-modal .preview-popup .preview-popup-mask {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #000;
  opacity: 0.9;
  z-index: -1;
}
#chat-modal .preview-popup .preview-popup-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
}
#chat-modal .preview-popup .preview-popup-img {
  max-width: 100%;
  max-height: 100%;
}
#chat-modal .preview-popup .preview-popup-remove-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  height: 40px;
  cursor: pointer;
}

/** 弹出框 end */
/** 移动端适配 start */
@media screen and (max-height: 800px) {
  #chat-modal .modal-dialog {
    height: 100%!important;
    margin: auto;
  }
  #chat-modal .chat-msg-info-file-logo {
    height: 66px;
    font-size: 66px;
  }
  #chat-modal .chat-msg-info-file-desc {
    padding-top: 20px;
  }
}
/** 移动端适配 end */

.img-box {
  width: 870px;
  height: 830px;
}

.el-message--warning {
  z-index: 20000!important;
}

.chat-box .chat-box-unread {
  min-width: 21px;
  text-align: center;
}