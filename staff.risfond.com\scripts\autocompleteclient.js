/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
/**
 * 功能：用来选择客户的插件
 * 依赖：[jQuery]
 * 文档：http://192.168.30.10:8888/index.php?s=/9&page_id=2101
 */
var autoCompleteClient = (function ($) {
    var opts = {
        root: '',  // 	插件根目录的selector名称，如果不为空，则会自动加载组件所需要的html结构，反之则认为是想自定义html结构，此时将不会生成html结构
        localData: null,
        teamList: null,
        listNum: 5,
        boxId: "autocomplete",
        selectedNum: 5,
        onSelected: null,
        onCancel: null,
        rootSelector: "",
        addBtnId: "addClient",
    },
    reEscape = new RegExp('(\\' + ['/', '.', '*', '+', '?', '|', '(', ')', '[', ']', '{', '}', '\\'].join('|\\') + ')', 'g');
  function formatResult(value, currentValue) {
    var pattern = currentValue.replace(reEscape, '\\$1');
    return value.replace(new RegExp(pattern, 'gi'), function ($0) {
      return '<span class="highlight">' + $0 + '</span>';
    });
  }
  function Autocomplete(el, params) {
    if (!params) {
      params = el;
      el = "#selector_input_1";
    }
    this.params = $.extend({}, opts, params || {});
    if (this.params.root && $(this.params.root)) {
      $(this.params.root).html(`
        <div id="selector_outerbox_1" class="cf outbox" style="width: 250px !important; float: left">
              <ul class="outbox-item-list" style="display: none"></ul>
              <input placeholder="可以动态输入查询，也可右侧点击查询" style="padding-left: 14px; margin-top: -3px" type="text" autocomplete="off" id="selector_input_1">
            </div>
            <div id="clientList" class="category-list"></div>
            <ul id="selector_list_1" class="selector-list cf"></ul>
      `);
      this.params.boxId = "#selector_list_1";
    }

    this.obj = $(el);
    this.autocomplete = $(this.params.boxId);
    this.selectedIdx = -1;
    this.result = [];
    this.nomatch = $("#selector_hintbox_1");
    this.addBtn = $(this.params.rootSelector + " #" + this.params.addBtnId);
    this.tips = $j("#selClient").find(".outbox-tip");
    this.init();
  }
  Autocomplete.prototype = {
    init: function () {
      var self = this, auto = this.autocomplete;
      this.obj.keydown(this.onKeyPress.bind(this));
      this.obj.keyup(this.onKeyUp.bind(this));
      this.obj.blur(this.kill.bind(this));
      this.obj.focus(this.showTip.bind(this, "none"));
      auto.on("mouseenter", ".result-item", function () {
        var $this = $(this);
        auto.find(".selected").removeClass("selected");
        $this.addClass("selected");
        self.selectedIdx = parseInt($this.attr("id").replace(/[a-z_]/g, ""));
        self.changeStatus();
      });
      auto.on("click", ".result-item", this.select.bind(this));
      if (document.addEventListener) {
        auto[0].addEventListener('DOMMouseScroll', this.scroll.bind(this), false);
      }
      auto[0].onmousewheel = this.scroll.bind(this);
      this.addBtn.click(function (e) {
        e.stopPropagation();
        self.showAddTeam();
      });

      $(this.params.rootSelector + " #clientList").on("click", "dt", function () {
        var $this = $(this), p = $this.parent();
        if (p.hasClass("ts-unfold")) {
          p.removeClass("ts-unfold");
        } else {
          $j(self.params.rootSelector + " #clientList").find("dl").removeClass("ts-unfold");
          p.addClass("ts-unfold");
        }
      });

      $(this.params.rootSelector + " #clientList").on("click", "li", function () {
        var $this = $(this);
        self.showTip();
        $this.toggleClass("ct-selected");
        self.toggleItem($this.attr("data-id"), $this.text(), $this);
      });
      $(this.params.rootSelector + " #clientList").click(function (e) {
        e.stopPropagation();
      });
      $j(this.params.rootSelector +  " #selector_outerbox_1").on("click", ".cancel", function (e) {
        var p = $(this).parent(), id = p.attr("data-id");
        e.stopPropagation();
        p.remove();
        $j(this.params.rootSelector + " #clientList").find("li[data-id=" + id + "]").removeClass("ct-selected");
        !$j("#selector_outerbox_1").find("li").length && self.showTip("block");
      });
      this.updateHint();
    },
    onKeyPress: function (e) {

      switch (e.keyCode) {
        case 9: //keyTab
          this.disappear(); break;
        case 13: //keyEnter
          this.select();
          e.preventDefault();
          this.disappear();
          break;
        case 27: this.disappear(); break;
        case 38: //keyUp
          this.moveUp(); break;
        case 40: //keyDown
          this.moveDown(); break;
      }
    },
    onKeyUp: function (e) {
      var v = $.trim(this.obj.val());
      //keyup keydown
      if (e.keyCode == 38 || e.keyCode == 40 || e.keyCode == 13 || e.keyCode == 27 || e.keyCode == 9) {
        e.preventDefault();
        return;
      }
      if (e.keyCode == 8 && !v) {
        this.disappear();
        if (this.locked) {
          this.locked = false;
          return;
        }
        if (!this.locked) {
          $j("#selector_outerbox_1").find("li:last").remove();
        }
        return;
      }
      v && this.processResult();
    },
    moveUp: function () {
      this.selectedIdx = this.selectedIdx-- ? this.selectedIdx : 0;
      this.activate("up");
    },
    moveDown: function () {
      this.selectedIdx = this.selectedIdx++ >= this.result.length - 1 ? this.result.length - 1 : this.selectedIdx;
      this.activate("down");
    },
    scroll: function (e) {
      var v = "";
      e = e || window.event;
      if (e.preventDefault) {
        e.preventDefault();
      } else {
        e.returnValue = false;
      }
      if (e.wheelDelta) {//IE/Opera/Chrome
        v = e.wheelDelta;
      } else if (e.detail) {//Firefox
        v = e.detail;
      }
      switch (v) {
        case 3: this.moveDown(); break;
        case -3: this.moveUp(); break;
        case 120: this.moveUp(); break;
        case -120: this.moveDown(); break;
      }

    },
    processResult: function () {
      this.locked = true;
      var i = 0, l = this.params.localData.length, d = this.params.localData, q = $.trim(this.obj.val());
      var r = this.result;
      r.length = 0;
      for (; i < l; i++) {
        if (d[i].ClientName.indexOf(q) != -1) {
          r.push(d[i]);
        }
      }
      !r.length && this.disappear();
      this.obj.css("width", q.length * 12);
      this.selectedIdx = 0;
      this.showList();
    },
    showList: function (index) {
      var r = this.result, q = $.trim(this.obj.val()), l = r.length;
      l = l >= this.params.listNum ? this.params.listNum : l;
      var html = "<li class='hint' title='" + setRnssLanguage('可以使用鼠标滚轮进行操作') + "'>" + setRnssLanguage("找到{0}个,当前第{1}个").format(r.length, (this.selectedIdx + 1)) + "</li>";
      for (var i = 0; i < l; i++) {
        if (i == 0) {
          html += "<li class='result-item selected' id='selector_listitem" + i + "'><div>" + formatResult(this.result[i].ClientName, q) + "</div><span class='name-follow'>" + this.result[i].ClientName + "</span></li>";
        } else {
          html += "<li class='result-item' id='selector_listitem" + i + "'><div>" + formatResult(this.result[i].ClientName, q) + "</div><span class='name-follow'>" + this.result[i].ClientName + "</span></li>";
        }
      }
      if (r.length) {
        this.autocomplete.html(html).css("display", "block");
        this.nomatch.css("display", "none");
      } else {
        this.nomatch.css("display", "block").html(setRnssLanguage("没有找到匹配的客户"));
      }

    },
    activate: function (dir) {
      var auto = this.autocomplete, has = $("#selector_listitem" + this.selectedIdx), q = $.trim(this.obj.val());
      var li;
      if (!has.length) li = $("<li class='result-item' id='selector_listitem" + this.selectedIdx + "'><div>" + formatResult(this.result[this.selectedIdx].ClientName, q) + "</div><span class='name-follow'>" + this.result[this.selectedIdx].ClientName + "</span></li>").addClass("selected");
      auto.find(".selected").removeClass("selected");
      if (has.length) {
        has.addClass("selected");
      } else if (dir == "down") {
        auto.find(".result-item:first").remove().end().append(li);
      } else if (dir == "up") {
        li.insertAfter(auto.find(".result-item:last").remove().end().find(".hint"));
      }
      this.changeStatus();
    },
    changeStatus: function () {
      this.autocomplete.find(".hint").html(setRnssLanguage("找到{0}个,当前第{1}个").format(this.result.length, (this.selectedIdx + 1)));
    },
    select: function () {
      var selectedValue = this.result[this.selectedIdx].ClientName,
        id = this.result[this.selectedIdx].ClientId;

      if ($j(this.params.rootSelector + "#selector_outerbox_1").find("li").length < this.params.selectedNum) {
        if (!this.isSelected(id)) {
          if (this.params.onSelected) {
            this.params.onSelected(id, selectedValue);
          }
        }
      } else {
        beautAlert.done(setRnssLanguage("您最多可以选择{0}位客户").format(this.params.selectedNum), "hits");
      }
      this.obj.val("").css("width", 12);
    },
    disappear: function () {
      this.selectedIdx = -1;
      this.autocomplete.css("display", "none");
      this.nomatch.css("display", "none");
    },
    kill: function () {
      var self = this;
      //!$j("#selector_outerbox_1").find("li").length && this.showTip("block");
      this.obj.val("").css("width", 12);
      $(document).one("click", function (e) {
        self.disappear();
      });
    },
    isSelected: function (id) {
      return !!$j(this.params.rootSelector +"#selector_outerbox_1").find("li").filter("[data-id=" + id + "]").length;
    },
    showAddTeam: function () {
      var list = [], data = this.params.teamList;
      $(this.params.rootSelector + " #clientList").css("display", "block");
      for (var i = 0, l = data.length; i < l; i++) {
        var item = "<dl class='cf category-each'><dt>" + data[i].StatusText + "</dt><dd><ul>";
        for (var j = 0, m = data[i].Clients.length; j < m; j++) {
          item += "<li data-id='" + data[i].Clients[j].ClientId + "' class='category-item'";
          if (this.isSelected(data[i].Clients[j].ClientId)) {
            item = item.replace(/\'$/, " ct-selected'");
          }
          item += "><span title='" + data[i].Clients[j].ClientName + "'>" + data[i].Clients[j].ClientName + "</span></li>";
        }
        item += "</ul></dd></dl>";
        list.push(item);
      }
      list = list.join("");
      $(this.params.rootSelector + " #clientList").html(list);
      this.hiddenAddTeam();
    },
    hiddenAddTeam: function () {
      var self = this;
      $("body").bind("click.client", function (e) {
        console.log(e.target.closest("#clientList"));
        $(self.params.rootSelector + " #clientList").css("display", "none");
        $("body").unbind("click.client")
      });
    },
    toggleItem: function (id, value, li) {
      if (this.isSelected(id)) {
        $j(this.params.rootSelector +" #selector_outerbox_1").find("li").filter("[data-id=" + id + "]").remove();
        this.params.onCancel(id, value);
      } else {
        if ($j(this.params.rootSelector + " #selector_outerbox_1").find("li").length < this.params.selectedNum) {
          if (this.params.onSelected) {
            this.params.onSelected(id, value);
          }
        } else {
          beautAlert.done(setRnssLanguage("您最多可以选择{0}位客户").format(this.params.selectedNum), "hits");
          li.removeClass("ct-selected");
        }
      }
    },
    showTip: function (action) {
      this.tips.css("display", action || "none");
    },
    updateHint: function () {
      if (!$j(this.params.rootSelector + " #selector_outerbox_1").find("li").length) {
        this.showTip("block");
      }
      else {
        this.showTip();
      }
    }
  };
  return Autocomplete;
})(jQuery);

