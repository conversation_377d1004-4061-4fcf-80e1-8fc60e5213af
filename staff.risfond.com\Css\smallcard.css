.p{

}
.newCard-people p {
  margin: 0;
}
.newCardD .el-dialog .el-dialog__header{
  display:none;
}
.newCardD .el-dialog{
  background:none;
}
.newCardD .el-dialog .el-dialog__body {
  padding: 0;
}
.newCardD-cont{
  position:relative;
}
.close-newCard {
  width: 18px;
  height: 18px;
  position: absolute;
  right: -30px;
  cursor:pointer;
}
.newCardD-top {
  width: 100%;
  height: 198px;
  background: url(../images/newCard.png) no-repeat;
  background-size: cover;
  padding: 20px 30px;
  display: flex;
  border-top-left-radius: 10px !important;
  border-top-right-radius: 10px !important;
}
.newCardD-top-information {
  flex: 1;
}
.newCardD-top-download{
  width:107px;
  text-align:center;
  color:#fff;
}
.download-img{
  width:107px;
  height:107px;
  margin-bottom:18px;
  border-radius:4px !important;
}
.newCardD-top-img {
  margin-right: 14px;
  width: 68px;
  height: 68px;
  position: relative;
  border-radius: 50% !important;
}
.newCardD-top-grade {
  width: 18px;
  height: 18px;
  background: #DBB36C;
  font-size: 12px;
  color: #fff;
  position: absolute;
  bottom: 0;
  right: -2px;
  border-radius:50% !important;
  text-decoration:none;
  font-style:normal;
  text-align:center;
  line-height:18px;
}
.newCard-people{
  display:flex;
  align-items:center;
  margin-bottom:14px;
}

  .newCard-name {
    font-size: 16px;
    color: #fff;
    margin-right: 10px;
    font-weight: 600;
  }
.newCard-position, .newCard-company{
  font-size:12px;
  color:#fff;
}
.newCard-company{
  margin-left:4px;
}
.newCard-phone, .newCard-email {
  margin-bottom: 8px;
}
.newCard-phone{
  height:20px;
}
.newCard-feedback {
  margin-bottom: 14px;
}
.newCard-phone, .newCard-email, .newCard-span {
  font-size: 14px;
  color: #fff;
}
.newCard-email {
  display: flex;
  align-items: center;
}
.newCard-call, .newCard-call:hover, .newCard-call:active, .newCard-call:focus {
  background: #FCCB76;
  color: #674200;
  font-size: 14px;
  border: none;
  padding: 6px 11px;
}
.newCard-btn, .newCard-btn:hover, .newCard-btn:active, .newCard-btn:focus {
  border: 1px solid #FCCB76;
  background: none;
  color: #FCCB76;
  font-size: 14px;
  padding: 5px 11px;
}

.newCard-moddle{
  background:#fff;
}
.newCard-tab .el-tabs__header{
  margin:0;
}
  .newCard-tab .el-tabs__header .el-tabs__nav-wrap{
    width:600px;
  }
  .newCard-tab .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll {
    padding: 0 30px;
  }
.newCard-tab .el-tabs__header .el-tabs__nav-wrap::after {
  height: 1px;
}
.newCard-tab .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav .el-tabs__item {
  font-size: 16px;
  color: #666666;
}
  .newCard-tab .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav .el-tabs__item.is-active {
    font-size: 16px;
    color: #333333;
    font-weight: 600;
  }

.tab-pad{
  padding:20px 20px 40px;
}
.newCard-introduce{
  display:flex;
  justify-content:space-between;
}
.newCard-introduce-cont {
  width: 132px;
  height: 60px;
  background: #F5F7FF;
  border-radius: 4px !important;
  display: flex;
  padding:10px 4px;
  align-items:center;
}
.newCard-introduce-cont img{
  margin-right:6px;
}
.newCard-introduce-p{
  font-size:12px;
  color:#333;
}
.newCard-type{
  position:relative;
  margin-top:20px;
}
.newCard-font{
  font-size:14px;
  color:#333;
  font-weight:600;
}
.newCard-type-top {
  display: flex;
  align-items: center;
  margin-bottom:20px;
}
.newCard-echarts {
  margin-bottom: 20px;
}
.newCard-type-line {
  width: 2px;
  height: 16px;
  background: #2A7FCC;
  display: inline-block;
  margin-right: 5px;
}
.newCard-type-tag {
  font-size: 12px;
  color: #333;
  background: #E5F4FF;
  display: inline-block;
  padding: 6px 8px;
  margin-right:14px;
}

.echarts-top, .echarts-bottom {
  display: flex;
  justify-content: space-between;
}


.newCard-page{
  display:flex;
  justify-content:space-between;
  align-items:center;
  margin-top:30px;
}
.newCard-page .el-pagination{
  padding:0;
}

.need-position {
  display: flex;
  position:relative;
}
.need-position-tag {
  padding: 3px 10px 3px 10px;
  background: #E9F2FF;
  font-size: 12px;
  color: #003683;
  border-radius: 12px !important;
  margin-right:8px;
  margin-bottom:8px;
  display:inline-block;
}
.numCricle {
  width: 16px;
  height: 16px;
  border-radius: 50% !important;
  background: #004CB6;
  color: #fff;
  line-height: 16px;
  text-align: center;
  display: inline-block;
  cursor:pointer;
}
.ageIcon {
  width: 16px;
  height: 16px;
  display: inline-block;
  vertical-align: sub;
  position: absolute;
  right:10px;
  top:3px;
}
.newCard-position-tag {
  height: 62px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  cursor:pointer;
}
.newCard-hover-tag {
  position: absolute;
  right: 0px;
  z-index: 2004;
  background: #fff;
  display: none;
  border-radius: 4px !important;
}
.need-position:hover .newCard-hover-tag {
  display: block;
  cursor: pointer;
}
.input-newCard {
  width: 110px;
  height: 24px;
  background: #E9F2FF;
  border-radius:12px !important;
  border:none;
  margin:5px 0;
}
.download-newCard {
  width: 600px;
  margin: 0 auto;
  position: fixed;
  bottom: -1400px;
}
.download-newCard-top {
  width: 100%;
  height: 166px;
  background: url(../images/newCard.png) no-repeat;
  background-size: cover;
  display: flex;
  padding: 20px 30px;
}
.download-newCard-bottom {
  padding: 30px 30px 20px;
  background: #fff;
}
.newCard-bottom-title {
  font-size: 20px;
  color: #333;
  font-weight: 600;
  z-index: 1;
  position: relative;
}
.download-newCard-line {
  position: absolute;
  width: 80px;
  height: 8px;
  display: inline-block;
  background: #8FC9FF;
  left: 0;
  bottom: 0;
  opacity: .8;
}
/*******   添加团队   *******/
.project-input {
  width: 430px;
  height: 36px;
  border: 1px solid #E2E2E2;
  padding: 8px 10px;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.addTema-cont{
  padding:20px;
}
.addTeamBtn, .addTeamBtn:hover, .addTeamBtn:active, .addTeamBtn:focus {
  padding: 9px 25px !important;
  background: #1C68AE !important;
  font-size: 14px !important;
  color: #fff !important;
  border-radius:2px !important;
  border:none !important;
}
.index-staffcard .sc-con{
  width:100% !important;
}
.cardTable .el-table__body-wrapper {
  border: 1px solid #DDD
}
/* el-table 列数据为空自动显示 -- */
/*.cardTable :empty::before {
  content: '--';
  color: gray;
}*/
.cardTable .cell {
  padding-left: 4px;
  padding-right: 2px;
}
.teamStaff .selTeam .text-5 {
  width: 430px !important;
  height: 36px !important;
  box-shadow: none;
  padding-left: 12px;
}
.teamStaff .selTeam .r-submit{
  display:none !important;
}
.teamStaff .selTeam .text-5 .outbox-item-list {
  position: relative;
  top: 7px;
}
  .teamStaff .selTeam .text-5 .outbox-item-list .cancel{
    padding:0 !important;
    display:none !important;
  }
  .teamStaff .selTeam {
    width: 430px !important;
    margin: 0 !important;
  }
  .teamStaff .selTeam .r-icon {
    left: auto;
    right: 8px;
    top: 10px;
  }
.el-select-dropdown{
  z-index:10054 !important;
}
.project-Select {
  width: 430px;
  height: 36px;
}
  .project-Select .el-input .el-input__inner{
    width:100%;
    height:36px;
    line-height:36px;
  }
.newCard-phone .rnss-callphone {
  vertical-align: unset;
}
.newCard-phone .sc-con {
  display: flex;
  align-items: center;
}
.fileData {
  display: inline-block;
  margin-right: 14px;
  width: 100%;
  margin-bottom: 14px;
}
.fileDiv .fileData:last-child {
  margin-bottom: 0;
}
.el-popover{
  z-index:10099 !important;
  /*left:310px !important;*/
}
.hover-table .cf{
  /*display:flex;*/
}

.hover-table{
  overflow:auto;
}
.hover-table .cf .PB-shiJian {
  width: 120px !important;
}
  .hover-table .cf li {
    display: flex;
    justify-content: space-between;
  }

  .noData {
    width: 100%;
    height: 70%;
    position: relative;
  }

.noData-content {
  width: 72px;
  height: 104px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
}
.h-nodata {
  width: 72px;
  height: 72px;
}
.noData-content-p {
  width: 99px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #999999;
  line-height: 20px;
  margin-top: 12px;
}

.publisHead {
  padding: 20px;
  border-bottom: 1px solid #F4F4F4;
  display: flex;
  justify-content: space-between;
}

.publisHead-fon {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.publisHead-close {
  color: #4E5969;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
}
.project-select {
  padding: 0 20px;
}

.project-flex {
  display: flex;
  margin-bottom: 20px;
}
.el-popper[x-placement^=left] .popper arrow::after {
  display:none;
}

.Approval {
  padding: 30px 12px 18px 12px !important;
}
  .Approval:hover {
    background: #f9fafc !important;
  }
  .Approval .icon-bell {
    font-size: 16px;
    color: #C0CDDC;
  }
  .Approval .badge-danger1 {
    display: inline-block;
    font-family: "Open Sans", sans-serif;
    margin: -20px 0 0 0;
    font-weight: 600;
    padding: 6px 9px;
    height: 25px;
    background-color: #ed6b75;
  }
  .Approval .badge-danger2 {
    display: inline-block;
    font-family: "Open Sans", sans-serif;
    margin: -16px 0 0 0;
    font-weight: 600;
    padding: 6px 9px;
    height: 25px;
    background-color: #337ab7;
  }
.dropdown-toggle .badge.badge-danger {
  background-color: #ed6b75;
}
.dropdown-toggle .badge.badge-primary {
  background-color: #337ab7;
}















