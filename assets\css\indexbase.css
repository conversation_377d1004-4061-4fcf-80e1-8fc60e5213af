/* 首页专用样式 - 基于原始RISFOND网站的indexbase.css */

/* 顶部信息区域 */
.index-top {
  width: 100%;
  height: 245px;
  margin-bottom: 20px;
}

.index-top-new {
  display: flex;
  gap: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
}

.head-11-wrap {
  margin-top: 10px;
}

/* 左侧用户信息 */
.index-top-left {
  flex: 1;
  display: flex;
  height: 247px;
}

.top-left-left {
  width: 190px;
  height: 247px;
  background-color: #F9F9F9;
  position: relative;
  border-right: 1px solid #DDDDDD;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.demo-img {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  margin-bottom: 13px;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 24px;
}

.top-left-left-p {
  font-size: 14px;
  font-weight: 500;
  color: #2A7FCC;
  line-height: 20px;
  text-align: center;
  margin-bottom: 6px;
}

.top-left-left-p2 {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
  text-align: center;
  margin-bottom: 20px;
}

.hengxian {
  width: 100%;
  height: 1px;
  background-color: #DDDDDD;
  margin: 20px 0;
}

.top-left-left-p3 {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
  text-align: center;
  padding: 0 16px;
}

/* 右侧业绩数据 */
.top-left-right {
  flex: 1;
  background-color: #FFFFFF;
  height: 247px;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  padding: 15px;
}

.top-left-right-div {
  width: 107px;
  height: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 5px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.top-left-right-div:hover {
  background-color: #f8f9fa;
}

.left-right-div-p {
  font-size: 18px;
  font-weight: bold;
  color: #2A7FCC;
  line-height: 1;
  margin-bottom: 4px;
}

.left-right-div-p2 {
  font-size: 12px;
  color: #666;
  line-height: 1;
}

.div-hengxian {
  width: 1px;
  height: 30px;
  background-color: #DDDDDD;
  margin: 10px 5px;
}

/* 右侧事项提醒 */
.index-top-right {
  width: 381px;
  background-color: #FFFFFF;
  height: 247px;
  display: flex;
  flex-direction: column;
}

.index-top-right-top {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.index-top-right-top-p {
  color: #2A7FCC;
  font-weight: 600;
  font-size: 18px;
  margin: 0;
}

.h-tixing {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.index-top-right-top-div {
  display: flex;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.sxtj-left {
  padding: 6px 12px;
  background: #fff;
  color: #666;
  cursor: pointer;
  border-right: 1px solid #ddd;
  transition: all 0.3s;
}

.sxtj-left:last-child {
  border-right: none;
}

.sxtj-left.sxtj-active {
  background: #2A7FCC;
  color: #fff;
}

.sxtj-left-p {
  margin: 0;
  font-size: 14px;
}

.index-top-right-bottom {
  flex: 1;
  padding: 15px 20px;
  overflow-y: auto;
}

.index-top-right-bottom-content {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  gap: 10px;
}

.sxtx-qiu {
  width: 6px;
  height: 6px;
  background: #2A7FCC;
  border-radius: 50%;
  flex-shrink: 0;
}

.index-top-right-bottom-content-p,
.index-top-right-bottom-content-p2,
.index-top-right-bottom-content-p3 {
  font-size: 12px;
  color: #333;
  margin: 0;
  cursor: pointer;
  transition: color 0.3s;
}

.index-top-right-bottom-content-p2:hover,
.index-top-right-bottom-content-p3:hover {
  color: #2A7FCC;
}

/* 无数据状态 */
.noData {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.noData-content {
  text-align: center;
}

.h-nodata {
  width: 60px;
  height: 60px;
  opacity: 0.5;
  margin-bottom: 10px;
}

.noData-content-p {
  color: #999;
  font-size: 14px;
  margin: 0;
}

/* 快捷入口样式 */
.kuaijie-wrap-new1 {
  margin: 20px 0;
}

.page-head {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  padding: 20px;
}

.page-head-new {
  position: relative;
}

.page-head-icon {
  position: absolute;
  top: 15px;
  right: 20px;
  color: #2A7FCC;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.page-dead-icon {
  width: 16px;
  height: 16px;
}

.page-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.caption {
  display: flex;
  align-items: center;
  gap: 10px;
}

.caption-icon {
  width: 20px;
  height: 20px;
  color: #2A7FCC;
}

.caption-subject {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.page-toolbar {
  display: flex;
  align-items: center;
  gap: 15px;
}

.dashboard-report-range2 {
  color: #2A7FCC;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 快捷入口内容 */
.kuaijierukou-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.kuaijierukou-content-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s;
}

.kuaijierukou-content-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.kuaijierukou-content-item img {
  width: 48px;
  height: 48px;
  margin-bottom: 10px;
  border-radius: 8px;
}

.kuaijierukou-content-item-title {
  font-size: 14px;
  color: #333;
  text-align: center;
  margin: 0;
}

/* 收起状态的快捷入口 */
.page-no-kuaijie {
  text-align: center;
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.page-kuaijie-icon {
  color: #2A7FCC;
  cursor: pointer;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.page-icon1, .page-icon2 {
  width: 16px;
  height: 16px;
}

/* 排行榜样式 */
.index-paihangbang {
  display: flex;
  gap: 20px;
  margin: 20px 0;
}

.index-paihangbang-left {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table-box-new {
  overflow: hidden;
}

.portlet-title {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.portlet-title-svg {
  width: 20px;
  height: 20px;
  color: #2A7FCC;
}

.base-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.kpbig-top-more {
  display: flex;
  align-items: center;
}

.kpbig-link {
  color: #2A7FCC;
  text-decoration: none;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.moreSvg {
  width: 14px;
  height: 14px;
}

.kpbig-top-more-p {
  margin: 0;
}

/* 表格样式 */
.portlet-body {
  padding: 0;
}

.tbl-header {
  background: #f8f9fa;
}

.tbl-body {
  max-height: 300px;
  overflow-y: auto;
}

.r-tb-tuijian {
  width: 100%;
  margin: 0;
  border-collapse: collapse;
}

.r-tb-tuijian th,
.r-tb-tuijian td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
}

.r-tb-tuijian th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.r-tb-tuijian tbody tr:hover {
  background: #f8f9fa;
}

.text-color {
  color: #2A7FCC !important;
}

.r-tuijian-new-a {
  color: #2A7FCC;
  text-decoration: none;
  cursor: pointer;
}

.r-tuijian-new-a:hover {
  text-decoration: underline;
}

.r-tuijian-new-a.male {
  color: #2A7FCC;
}

.r-tuijian-new-a.female {
  color: #e91e63;
}

/* 加载动画 */
.wait_loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.svg_wgs {
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.g-circles circle {
  fill: #2A7FCC;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .index-top-new {
    flex-direction: column;
    height: auto;
  }
  
  .index-top-left {
    height: auto;
  }
  
  .top-left-left {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #DDDDDD;
  }
  
  .index-top-right {
    width: 100%;
    height: auto;
  }
  
  .index-paihangbang {
    flex-direction: column;
  }
  
  .kuaijierukou-content {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 15px;
  }
}
