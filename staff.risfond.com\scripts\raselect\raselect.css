/* 公用样式 */
em {
  font-style: normal
}
/* 弹窗 */
.k-dialog {
  position: relative;
  background: #fff;
  border-radius: 0 !important;
  -webkit-box-shadow: 0 1px 3px rgba(0,0,0,.3);
  box-shadow: 0 1px 3px rgba(0,0,0,.3);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0 auto;
  /*width: 650px*/
}

.k-dialog--flex {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: 0 !important
}

.k-dialog--flex, .k-dialog--flex .k-dialog {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column
}

.k-dialog--flex .k-dialog {
  margin: 0 auto
}

.k-dialog--flex .k-dialog__body {
  overflow: auto;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  min-height: 100px;
}

.k-dialog--flex .k-dialog__footer, .k-dialog--flex .k-dialog__header {
  -ms-flex-negative: 0;
  flex-shrink: 0
}

.k-dialog__wrapper {
  /*position: fixed;*/
  max-height: calc(100% - 60px);
  position: absolute;
  width: 765px;
  top: 0;
  right: 0;
  /*bottom: 0;*/
  left: 0;
  /*overflow: auto;*/
  margin: 0;
  display: none;
  -webkit-transition: opacity .3s,-webkit-transform .4s;
  transition: opacity .3s,-webkit-transform .4s;
  transition: opacity .3s,transform .4s;
  transition: opacity .3s,transform .4s,-webkit-transform .4s;
  opacity: 1;
  padding: 0;
  z-index: 99999;
  box-shadow: 0 8px 10px -5px rgba(0,0,0,.2),0 16px 24px 2px rgba(0,0,0,.14),0 6px 30px 5px rgba(0,0,0,.12);
}

.k-dialog--tiny {
  width: 450px
}

.k-dialog--small {
  width: 550px
}

.k-dialog--large {
  width: 950px
}

.k-dialog--full {
  width: 100%;
  top: 0;
  margin-bottom: 0;
  height: 100%;
  overflow: auto
}

.k-dialog--thin .k-dialog__body {
  padding: 0
}

.k-dialog__icon {
  color: #696e7d
}

.k-dialog__header {
  padding: 20px 18px 15px;
  border: 1px solid #eee;
}

.k-dialog__header:after {
  clear: both;
  content: " ";
  display: table
}

.k-dialog__header-btn {
  float: right;
  background: transparent;
  border: none;
  outline: none;
  padding: 0;
  cursor: pointer;
  font-size: 14px;
  line-height:1;
}

.k-dialog__header-btn > img {
  width: 14px;
  height: 14px;
}
/*.k-dialog__header-btn:focus .k-dialog__icon, .k-dialog__header-btn:hover .k-dialog__icon {
  color: #262b33
}*/

.k-dialog__title {
  line-height: 1;
  font-size: 14px;
  color: #333;
  font-weight:600;
}

.k-dialog__body {
  padding: 3px 0 0;
  color: #262b33;
  font-size: 14px
}

.k-dialog__footer {
  padding: 12px 25px;
  text-align: right;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-top: 1px solid #eee;
  font-size:0;
}
/* 按钮 */
.k-button{
  margin-left:10px;
  -moz-appearance:none;
  -webkit-appearance:none;
  border:1px solid transparent;
  border-radius:2px;
  -webkit-box-shadow:none;
  box-shadow:none;
  display:inline-block;
  font-size:14px;
  line-height:1;
  padding:9px 30px;
  position:relative;
  -webkit-touch-callout:none;
  -webkit-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none;
  -webkit-tap-highlight-color:rgba(0,0,0,0);
  background-color:#f9f9f9;
  border-color:#eee;
  color:#373f55;
  cursor:pointer;
  text-align:center;
  white-space:nowrap
}

.k-button.is-primary {
  background-color: #026FB2;
  border-color: transparent;
  color: #fff
}

.k-button.is-primary.is-hovered, .k-button.is-primary:hover {
  background-color: #1296f4;
  border-color: transparent;
  color: #fff
}

.k-button.is-primary.is-focused, .k-button.is-primary:focus {
  border-color: transparent;
  color: #fff
}

.k-button.is-primary.is-focused:not(:active), .k-button.is-primary:focus:not(:active) {
  -webkit-box-shadow: 0 0 0 .125em rgba(30,155,245,.25);
  box-shadow: 0 0 0 .125em rgba(30,155,245,.25)
}

.k-button.is-primary.is-active, .k-button.is-primary:active {
  background-color: #0b8fef;
  border-color: transparent;
  color: #fff;
  -webkit-box-shadow: inset 0 1px 2px hsla(0,0%,4%,.2);
  box-shadow: inset 0 1px 2px hsla(0,0%,4%,.2)
}

.k-button.is-primary[disabled] {
  background-color: #1e9bf5;
  border-color: transparent;
  -webkit-box-shadow: none;
  box-shadow: none
}

.k-button.is-primary.is-inverted {
  background-color: #fff;
  color: #1e9bf5
}

.k-button.is-primary.is-inverted:hover {
  background-color: #f2f2f2
}

.k-button.is-primary.is-inverted[disabled] {
  background-color: #fff;
  border-color: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #1e9bf5
}

.k-button.is-primary.is-loading:after {
  border-color: transparent transparent #fff #fff !important
}

.k-button.is-primary.is-outlined {
  background-color: #fff;
  border-color: #1e9bf5;
  color: #1e9bf5
}

.k-button.is-primary.is-outlined:focus, .k-button.is-primary.is-outlined:hover {
  background-color: #f2f2f2;
  background-color: rgba(30,155,245,.15);
  border-color: #1e9bf5;
  color: #1e9bf5
}

.k-button.is-primary.is-outlined.is-loading:after {
  border-color: transparent transparent #1e9bf5 #1e9bf5 !important
}

.k-button.is-primary.is-outlined[disabled] {
  background-color: transparent;
  border-color: #1e9bf5;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #1e9bf5
}

.k-button.is-primary.is-inverted.is-outlined {
  background-color: transparent;
  border-color: #fff;
  color: #fff
}

.k-button.is-primary.is-inverted.is-outlined:focus, .k-button.is-primary.is-inverted.is-outlined:hover {
  background-color: #fff;
  color: #1e9bf5
}

.k-button.is-primary.is-inverted.is-outlined[disabled] {
  background-color: transparent;
  border-color: #fff;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #fff
}
/* 数据部分 */
.job-type-selector-container {
  height: 442px;
  overflow-y: hidden;
  clear: both;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.job-type-selector--item {
  width: 249px;
  float: left;
  height: 442px;
  overflow-y: auto;
}

.job-type-selector--item ul li {
  height: 34px;
  line-height: 34px;
  color: #666;
  padding-left: 17px;
  position: relative;
  cursor: pointer;
  transition: background-color .25s ease-in-out;
}

.job-type-selector--item ul li:hover {
  background: #f9f9f9;
}

.job-type-selector--item ul.job-type-selector--item--first li {
  border-left: 3px solid transparent;
}

.job-type-selector--item ul.job-type-selector--item--first li:hover {
  background: #E3EEFF;
}

.job-type-selector--item ul.job-type-selector--item--first li.job-type-selector--item__selected {
  border-left-color: #026FB2;
  background: #E3EEFF;
}

.job-type-selector--item ul li i {
  position: absolute;
  color: #979797;
  right: 19px;
  top: 10px
}

.job-type-selector--item--third {
  height: 442px;
  overflow-y: auto;
}

.job-type-selector--item .job-type-selector--item--third li i {
  /*display: none;*/
  top: 10px;
  font-size: 0;
  width: 16px;
  height: 16px;
  background: transparent url(/scripts/raselect/images/checked.png) left top no-repeat;
  background-size: 16px;
}

.job-type-selector--item--third .job-type-selector--item__selected i.fa-check {
  background-image: url(/Scripts/raSelect/images/checked_on.png)
}

.job-type-selector--item--first {
  background: #f9f9f9
}

.job-type-selector--item__selected {
  color: #026FB2 !important;
  background: #f9f9f9
}

/*.job-type-selector--item__selected i.fa-angle-right {
  display: none !important
}*/

.k-dialog--flex .p2 {
  padding-left: 20px !important;
  padding-right: 20px !important;
}

.k-dialog--flex .checked-area {
  margin: 0 0 15px;
  padding: 10px 0 0;
  background: #fff;
}

.k-tip {
  font-size: 14px;
  color: #999;
  display: inline-block;
  vertical-align: middle;
}

.k-dialog--flex .checked-area .ca_item {
  white-space: nowrap;
  border: 1px solid #eee;
  margin: 0 10px 0 0;
  overflow: hidden;
  position: relative;
  padding: 7px 7px 7px 14px;
  line-height: 1;
  float: none;
  display: inline-block;
  vertical-align: middle;
  background-color: #F5F9FF;
  border-radius: 15px !important;
  font-size: 14px;
  color: #026FB2;
}

.k-dialog--flex .cancel-item {
  width: 10px;
  height: 10px;
  background: url(/Scripts/raSelect/images/remove.png) no-repeat left top;
  background-size: 10px 10px;
  cursor: pointer;
  margin: 0 8px;
  display: inline-block;
}

.k-dialog--flex .txtcompanel {
  background: #fff;
  display: block;
  height: 30px;
  padding: 0;
  margin: 15px 0 8px 0;
}

.k-dialog--flex .txtcompanel .textbox {
  padding: 0;
  background-color: #f9f9f9;
  display: block;
  white-space: nowrap;
  margin: 0;
  overflow: hidden;
  vertical-align: middle;
  border: 1px solid #eee;
  -ms-border-radius: 3px;
  border-radius: 3px;
}

.k-dialog--flex .txtcompanel .textbox .textbox-text {
  background: none;
  border: 0 none;
  width: 100% !important;
  height: 34px !important;
  padding: 0 6px;
  outline: none;
  font-size: 14px;
}

.k-dialog--flex .txtcompanel .textbox input::-webkit-input-placeholder {
  color: #999;
}
.k-dialog--flex .txtcompanel .textbox input:-ms-input-placeholder {
  color: #999;
}
.k-dialog--flex .txtcompanel .textbox input::-moz-placeholder {
  color: #999;
}
/*.k-dialog--flex .txtcompanel .txtcomtip {
  display: inline-block;
  margin: 0;
}*/
.shishou {
  width: 798px;
  padding: 15px 20px;
  position: fixed;
  bottom: 36px;
  background: #fff;
  z-index: 1;
  border-top:1px solid #ddd;
}

@media screen and (max-height:1080px) {
  .incomeDia {
    margin: 40px auto 0 !important;
  }
  .shishou {
    width: 815px;
    padding: 15px 20px;
    position: fixed;
    bottom: 20px;
    background: #fff;
    z-index: 1;
  }
}
.ClientDataAnalysisSelect {
  width: 100px;
  height: 34px;
  float: left;
  margin: 5px 12px 0 0;
}
  .ClientDataAnalysisSelect .el-input input {
    height: 34px;
    line-height: 34px;
    border-radius: 5px !important;
    border: 1px solid #C4C4C4;
    padding-right: 20px;
  }
  .ClientDataAnalysisSelect .el-input input::-webkit-input-placeholder {
    color: #333;
  }
  .ClientDataAnalysisSelect .el-input .el-input__suffix-inner .el-input__icon {
    color: #333 !important;
    line-height: 34px !important;
    width: 16px;
  }
.NewCustomStatusDefine .modal-body div {
  text-indent: 0;
  display: flex;
}

.NewCustomStatusDefine .modal-title {
  font-family: '微软雅黑';
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.NewCustomStatusDefine span {
  width: 96%;
  color: #333;
  font-size: 14px;
}

.NewCustomStatusDefine_cricle-i {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  display: block;
  margin-right: 8px;
  position: relative;
  top: 7px;
}

.NewCustomStatusDefine_cricle-shou {
  background: #6D7278;
}

.NewCustomStatusDefine_cricle-zai {
  background: #33C000;
}

.NewCustomStatusDefine_cricle-qian {
  background: #2973DB;
}

.NewCustomStatusDefine_cricle-yue {
  background: #FF7010;
}

.NewCustomStatusDefine_cricle-zan {
  background: #FF7010;
}














