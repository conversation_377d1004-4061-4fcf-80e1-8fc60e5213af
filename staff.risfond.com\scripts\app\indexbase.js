window.onload = function () {
  //var list = $(".poster-txt>.audiotShow")
  //var listed = $(".poster-txt>.audiotHide")
  //for (var i = 0; i < list.length; i++) {
  //  list[i].onclick = (function (index) {
  //    return function () {
  //      $($(this).siblings('p.test-p')).attr({ style: '-webkit-line-clamp: unset' })
  //      $($(this)).hide()
  //      $($(this).siblings('p.audiotHide')).show()
  //    }
  //  })(i)
  //}
  //for (var i = 0; i < listed.length; i++) {
  //  listed[i].onclick = (function (index) {
  //    return function () {
  //      $($(this).siblings('p.test-p')).attr({ style: '-webkit-line-clamp: 3' })
  //      $($(this)).hide()
  //      $($(this).siblings('p.audiotShow')).show()
  //    }
  //  })(i)

  //}


  var sm_open1 = document.getElementById("sm_open1");//展开操作详情按钮 
  var jobTagsClass1 = document.getElementById("jobTagsClass1");//展开操作详情按钮
  var sm_close1 = document.getElementById("sm_close1");//关闭操作详情按钮
  var sm_open2 = document.getElementById("sm_open2");//展开操作详情按钮 
  var jobTagsClass2 = document.getElementById("jobTagsClass2");//展开操作详情按钮
  var sm_close2 = document.getElementById("sm_close2");//关闭操作详情按钮
  //操作详情表的展开和收起
  sm_open1.onclick = function () {
    jobTagsClass1.style.maxHeight = "100%";
    sm_close1.style.display = "block";
    sm_open1.style.display = "none";
  };
  sm_close1.onclick = function () {
    jobTagsClass1.style.minHeight = "39px";
    jobTagsClass1.style.maxHeight = "78px";
    sm_close1.style.display = "none";
    sm_open1.style.display = "block";
  };
  //操作详情表的展开和收起
  sm_open2.onclick = function () {
    jobTagsClass2.style.maxHeight = "100%";
    sm_close2.style.display = "block";
    sm_open2.style.display = "none";
  };
  sm_close2.onclick = function () {
    jobTagsClass2.style.minHeight = "39px";
    jobTagsClass2.style.maxHeight = "78px";
    sm_close2.style.display = "none";
    sm_open2.style.display = "block";
  };
};
var concenter = new Vue({
  el: '#concenter',
  data: {
    scrollOrdera: 0,//控制滚动加载
    scrollOrderb: 0,//控制滚动加载
    scrollOrderc: 0,//控制滚动加载
    scrollNumber: 0,//控制滚动加载
    tableThree: "所得业绩",//排行榜动态head
    moreData: 0,//更多公共事件赋值
    remainAddDTO: [],//快捷方式存值
    noAddDTO: "",//不知道干嘛的 问冠胜
    editYesAddDto: "",//不知道干嘛的 问冠胜
    yesAddDTO: [],//不知道干嘛的 问冠胜
    isKuaijieFlag: true,//不知道干嘛的 问冠胜
    dialogVisible: true,//不知道干嘛的 问冠胜
    prsonziliao: {},//登录人姓名id等资料
    userYearTxt: '', //根据登录人的时间反馈内容
    personYj: "",//登录人业绩目标
    personJd: "",//登录人完成进度
    personWwc: "",//登录人未完成
    personHk: "",//登录人本月回款
    personTj: "",//登录人本月推荐
    personMs: "",//登录人本月面试
    personOffer: "",//登录人本月offer
    personRz: "",//登录人本月入职
    kechengList: [],//达人网课列表
    timeMessage: [],//实时信息互动列表
    timetype: 1,//本周1，本月2，本季3，本年4
    rankingLeftValue: 100,//排行榜左边的筛选
    rankingLeft: [//排行榜筛选框
      {
        value: 100,
        label: "龙虎榜",
      }, {
        value: 8,
        label: "offer榜",
      }, {
        value: 200,
        label: "回款榜",
      }, {
        value: 300,
        label: "签约榜",
      }, {
        value: 9,
        label: "入职榜",
      }, {
        value: 6,
        label: "面试榜",
      },
      {
        value: 400,
        label: "offer金额",
      },
      {
        value: 600,
        label: "线索奖",
      },
    ],
    rankingRightValue: 1,//排行榜右边的筛选
    rankingRight: [
      {
        value: 1,
        label: "全国榜",
      }, {
        value: 3,
        label: "大区榜",
      }, {
        value: 2,
        label: "本地榜",
      }
    ],
    rankingPearsonValue: -1,//排行榜右边的筛选
    rankingPearson: [
      {
        value: -1,
        label: "全部职位",
      },
      {
        value: 2,
        label: "顾问",
      }, {
        value: 3,
        label: "AC",
      }, {
        value: 1,
        label: "助理",
      }
    ],
    rankingDateValue: 4,//排行榜右边的筛选
    rankingDate: [
      {
        value: 1,
        label: "周"
      }, {
        value: 2,
        label: "月"
      }, {
        value: 3,
        label: "季"
      }, {
        value: 4,
        label: "年"
      }
    ],
    rankingData: [],//排行榜数据
    companyNews: [],//公司新闻数据
    kpBig: [],//靠谱大客户列表
    topChangeMonth: 1,//事项提醒切换年月
    monthChangeData: 1,//事项提醒下拉菜单
    monthChange: [//事项提醒下拉内容
      {
        value: 1,
        label: "面试提醒"
      }, {
        value: 2,
        label: "合同提醒"
      }
    ],
    thingRemind: [],//事项提醒数据
    positionList: [],//联合交付列表
    lhjfisLoading: true,
    unionBroadcast: [//联合交付实时播报列表
    ],
    messageCate: [ //信息类别按钮
      {
        value: 1,
        label: '靠谱职位'
      },
      {
        value: 2,
        label: '萝卜求坑'
      }
    ],
    subMessages: [],//订阅信息保存内容
    resumeDynamics: [],//员工订阅简历动态数据
    lianhebobao: [],//合作联盟实时播报
    submitValue: 1,//发消息类型值
    submitLeixing: [],//发消息类型集合
    submitInput: "",//发消息内容
    industryValue: "",//
    industry: [],//行业
    occupation: [],//职业类别
    ilocationTree: [],//工作地点
    followIndustry: "请选择行业，可选择3个行业",//行业显示文字
    followOccupation: "请选择职位类别，可选择3个类别",//类别显示文字
    followIlocationTree: "请选择职位地点，可选择3个地点",//地点显示文字
    industryData: [],//行业选中数组
    occupationData: [],//类别选中数组
    ilocationTreeData: [],//地点选中数组
    selectType: 0,//更多选择类型
    checkeda: false,//弹框里的够选 按顺序
    checkedb: false,
    checkedc: false,
    checkedd: false,
    checkede: false,
    jobDetailLock: false,//不知道干嘛的
    realTimeList: [], //实时回款数据
    collectionList: [], //实时推荐数据
    shishituijaingbangIsflag: true,
    shishihuikuangbangIsflag: true,
    tjDataLenFlag: false,
    hkDataLenFlag: false,
    //联合交付
    lianheListId: 3,
    checkList: [],
    select: '',
    projectDialog: false,
    smallSalary: 0,
    bigSalary: 0,
    staffId: staffIdp,
    jobTagsData: [],
    resumeTagsData: [],
    jobTagsArray: [],
    resumeTagsArray: [],
    pluginSelectorIndustry: null,
    GoodIndustrys: [],
    GoodOccupations: [],
    GoodLocations: [],
    PreferenceData: {},
    //发布线索
    clueDialog: false,
    value: "",
    imgData: [],
    ResumeESData: [],
    ResumeESTotal: 0,
    pageCount: 1,
    //资源大厅
    GetPageInterData: [],
    TagsVal: {
      Content: "",
      Category: 1,
      dynamicTags: [],
      Category: 1,
    },
    inputVisible: false,
    inputValue: '',
    searchStaffId: "",
    searchCategroy: "",
    startFrom: "",
    startTo: "",
    searchKeyInput: "",
    dateDialog: false,

    pickerOptions: {
      shortcuts: [{
        text: '最近一周',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          picker.$emit('pick', [start, end]);
        }
      }, {
        text: '最近一个月',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
          picker.$emit('pick', [start, end]);
        }
      }, {
        text: '最近三个月',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
          picker.$emit('pick', [start, end]);
        }
      }]
    },
    clueDateVal: '',
    InterDataCont: 10,
    InterDataIndex: 1,
    clueLoading: false,
    ESLoading: false,
    placeholderText: "请输入职位线索内容，我们建议格式如下：\n \n职位找人: EHS高级经理 / 总监, 要求化工行业背景, 持有注册安全工程师, 熟悉安全、环境、职业卫生、消防等管理标准, 硕士以上学历, base苏州+ 能接受到工厂出差, 男女不限, 薪资待遇open可谈, 食宿皆有, 欢迎合作！",
    moddleListShow: false,
    //23.10.30改版新增
    timeSel: 1, //时间段选择值
    timeTxt: '今日', //时间展示
    isZanLoading: false,//点赞loading
    isCaiLoading: false,//踩Loading
    curIndex: -1, //当前点赞index

    startTime: '',
    signNowTime: null,
    MyMarhq: '', //实时推荐滚动
    tuijianhq: '', //推荐滚动
    isKuaijie: true,//是否展开快捷入口
    personalStatisItem: {},//顾问统计
    timerhuikuan: null,
    timertuijian: null,
    timermianshi: null,
    timeroffer: null,
    timerruzhi: null,
    timercomhuikuan: null,
    timercomtuijian: null,
    timercommianshi: null,
    timercomroffer: null,
    timercomruzhi: null,
    huikuan: 0,
    tuijian: 0,
    mianshi: 0,
    offer: 0,
    ruzhi: 0,
    comhuikuan: 0,
    comtuijian: 0,
    commianshi: 0,
    comoffer: 0,
    comruzhi: 0,
    //交付中心筛选
    JobScreenDialog: false,
    nativeLocation: null,
    GoodIndustrysed: [],
    GoodLocationsed:[],
    kewords: "",
    JobScreenSaraly: [
      {
        id: 1,
        text: "10万以下",
      },
      {
        id: 2,
        text: "10-20万",
      },
      {
        id: 3,
        text: "30-50万",
      },
      {
        id: 4,
        text: "50-100万",
      },
      {
        id: 5,
        text: "100万及以上",

      },
      {
        id: 6,
        text: "其他",

      }
    ],
    JobScreenSaralyVal: "",
    JobScreenSaralyInput: false,
    shortWarrantyPeriod: undefined,
    moreCommision: undefined,
    hasOrder: 1,
    hasMoneyBack: undefined,
    fastFeedback: undefined,
    JobScreenSaralyFrom: null,
    JobScreenSaralyTo: null,
    JobType:null,
    query: {
      cities: [],
      industries: [],
      kwd: "",
      pageIndex: 1,
      pageSize: 10,
      salaryFrom: "",
      salaryTo: "",
      hasMoneyBack: undefined,//有回款
      hasOrder: undefined,//有成单
      moreCommision: undefined,//提点
      shortWarrantyPeriod: undefined,//保用期短
      hasMoneyBack: undefined,//有回款
      fastFeedback: undefined,//反馈快
    },
  },
  computed: {
    setIcon() {
      return function (num1, num2) {
        //debugger;
        if (parseFloat(num1)>= parseFloat(num2)) {
          return '/images/index/sheng.png';
        } else {
          return '/images/index/jiang.png';
        }
      }
    }
  },
  mounted() {
    let _this = this;
    //this.getPersonData()//获取登录人信息
    this.getPersonData2(),//获取登录人信息
      this.GetPerformanceSummaryPersonalStatistics(1);//获取顾问统计
      this.getReliableKAPageList(),//分页获取KA靠谱大客户
      this.getClientRemindMessage(),//查询事项提醒
      this.quickEntrance(),//获取快捷入口
      this.onScroll(),//滚动监听
      this.getRealTimeList(),//4、查询实施推荐榜
      this.getCollectionList(),//5、实时回款榜
      this.getStaffAttentionJob()//查询用户关注交付中心职位
      //this.getKechengList()获取达人网校课程推荐模块
      //this.getInteractionList()获取实时信息互动
      //this.getRankingList()查询排行榜
      //this.getGetCompanyNewsList()查询公司新闻
      //this.getStaffSubscriptionInteraction()获取订阅信息
      //this.getSubscriptionResumeDynamic()查询员工订阅简历动态
      //this.getjoblist()合作联盟实时播报
      //this.getConfigsList()实时信息互动类型
      //this.getIndustry()获取行业
      //this.getOccupation()获取职业类别
      //this.getILocationTree()获取工作地点
      //this.getStaffAttentionJob()查询用户关注交付中心职位
      //_this.InterDataCont = 10
      this.GetPageInter()

    this.IsEjectPreference(),//是否弹出偏好设置窗口
      this.GetSatffPreference(),
      // 户籍地址
      this.nativeLocation = new AdvancedSelect2({
        title: "选择地址",
        width: 520,
        data: data_location,
        showmeassage: function (txt) { beautAlert.done(txt, "hits"); }
      }),
      this.pluginSelectorIndustry = new r_AdvancedSelect({
        title: "选择行业",
        initType: "interface",
        requestOpts: { url: "/RnssIndustry/GetIndustry", requestType: "get", responseDataStructure: "tree" },
        showmeassage: function (txt) {
          beautAlert.done(setRnssLanguage(txt), "hits");
        }
      })
  },
  methods: {
    //2024-06 - 26//交付中心筛选
    JobScreenClick() {
      var _this = this
      _this.JobScreenDialog = true
    },
    closeJobScreen() {
      var _this = this
      _this.JobScreenDialog = false
    },
    JobScreenClearClick() {
      var _this = this
      _this.kewords = ""
      _this.GoodIndustrysed = []
      _this.GoodLocationsed = []
      _this.JobScreenSaralyVal = ""
      _this.JobType = null
      _this.query = {
        cities: [],
        industries: [],
        kwd: "",
        pageIndex: 1,
        pageSize: 10,
        salaryFrom: "",
        salaryTo: "",
        hasMoneyBack: undefined,//有回款
        hasOrder: undefined,//有成单
        moreCommision: undefined,//提点
        shortWarrantyPeriod: undefined,//保用期短
        hasMoneyBack: undefined,//有回款
        fastFeedback: undefined,//反馈快
      }
    },
    JobScreenSubmitClick() {
      var _this = this
      _this.ilocationTreeData = []
      _this.industryData = []
      _this.query.kwd = _this.kewords
      _this.GoodIndustrysed.forEach(e => {
        _this.industryData.push(e.Id)
      })
      _this.GoodLocationsed.forEach(e => {
        _this.ilocationTreeData.push(e.Id)
      })
      if (_this.JobScreenSaralyVal == 6) {
        _this.query.salaryFrom = _this.JobScreenSaralyFrom
        _this.query.salaryTo = _this.JobScreenSaralyTo
      }
      _this.JobType=1
      

      _this.JobScreenDialog = false
      this.getDeliveryCenterJobList(0)
    },
    JobScreenSaralyChange(val) {
      var _this = this
      _this.JobScreenSaralyVal = val
      switch (val) {
        case 1: {
          _this.JobScreenSaralyFrom = null
          _this.JobScreenSaralyTo = null
          _this.JobScreenSaralyInput = false
          _this.query.salaryFrom = 0
          _this.query.salaryTo = 10
          break;
        }
        case 2: {
          _this.JobScreenSaralyFrom = null
          _this.JobScreenSaralyTo = null
          _this.JobScreenSaralyInput = false
          _this.query.salaryFrom = 11
          _this.query.salaryTo = 20
          break;
        }
        case 3: {
          _this.JobScreenSaralyFrom = null
          _this.JobScreenSaralyTo = null
          _this.JobScreenSaralyInput = false
          _this.query.salaryFrom = 31
          _this.query.salaryTo = 50
          break;
        }
        case 4: {
          _this.JobScreenSaralyFrom = null
          _this.JobScreenSaralyTo = null
          _this.JobScreenSaralyInput = false
          _this.query.salaryFrom = 51
          _this.query.salaryTo = 100
          break;
        }
        case 5: {
          _this.JobScreenSaralyFrom = null
          _this.JobScreenSaralyTo = null
          _this.JobScreenSaralyInput = false
          _this.query.salaryFrom = 101
          _this.query.salaryTo = 0
          break;
        }
        case 6: {
          _this.JobScreenSaralyInput = true
          break;
        }
      }
      if (val == 6) {
        _this.JobScreenSaralyInput = true
      } else {
        _this.JobScreenSaralyInput = false
      }
    },
    //删除行业类别
    subscriptionIndustryDeleteClick(index) {
      var _this = this
      _this.GoodIndustrysed.splice(index, 1)
      _this.GoodIndustrysed = [..._this.GoodIndustrysed]


    },
    //删除工作地点
    subscriptionGoodLocationsDeleteClick(index) {
      var _this = this
      _this.GoodLocationsed.splice(index, 1)
      _this.GoodLocationsed = [..._this.GoodLocationsed]

    },
    editAbilityed: function (type) {
      var _this = this
      if (type == 'industry') {
        _this.helpSelected($(this), _this.pluginSelectorIndustry, 1, 'goodIndustrys', {
          data_target: 'query_criteria',
          target_obj: 'goodIndustrys'
        });
      }

      if (type == 'location') {
        _this.helpSelected($(this), _this.nativeLocation, 5, 'goodLocation', {
          data_target: 'query_criteria',
          target_obj: 'goodLocation'
        });
      }
    },
    helpSelected: function (el, obj, num, valname, o) {
      if (el.find("li").size() == 0) { el.removeData("data"); }
      var _this = this;
      var sd = [];

      if (valname == "goodIndustrys") {
        _this.GoodIndustrysed.forEach(e => {
          sd.push(e.Id)
        })
      } else if (valname == "goodLocation") {
        _this.GoodLocationsed.forEach(e => {
          sd.push(e.Id)
        })
      }
      obj.setCheckedNum(num);
      obj.setSureCallBack(function (result) {
        var curItem = [];
        var vmName = "";

        $("#" + valname).val("");
        if (result.length > 0) {
          result.forEach(function (item, index) {
            curItem.push(item.Id);
          })
          console.log("valname", valname)
          if (valname == 'goodIndustrys') {
            _this.GoodIndustrysed = []
            result.forEach(function (elem) {
              const item = {
                Id: elem.Id,
                Text: elem.Text,
              }
              _this.GoodIndustrysed.push(item)
            });
            console.log("_this.GoodIndustrysed", _this.GoodIndustrysed)
          } else {
            _this.GoodLocationsed = []
            result.forEach(e => {
              const item = {
                Id: e.Id,
                Text: e.Text
              }
              _this.GoodLocationsed.push(item)
            })
          }
          _this[o.target_obj] = curItem;
        } else {
          if (_this[o.data_target]) {
            _this[o.target_obj] = [];
            _this[o.data_target][o.target_obj] = [];
          } else {
            _this.GoodLocationsed = []
            _this[o.target_obj] = [];
          }
        }
        this.hidden();
      });
      if (sd) {
        obj.select(sd);
      }
      obj.show();

    },




  /**23.10.30改版start */
    //数据跳动
    animateCountUp(tag, end, newtag) {
      var $this = this;
      if ($this[tag]) {
        clearInterval($this[tag]);
      }
      if (end > 0) {
        const start = 0;//起始值
        const duration = 500;//持续时间间隔
        const stepTime = 10;//切换时间
        const range = end - start;
        let current = start;
        let increment = range / (duration / stepTime);
        $this[tag] = setInterval(() => {
          current += increment;
          if (current >= end) {
            $this[newtag] = end;
            clearInterval($this[tag]);
          } else {
            $this[newtag] = parseInt(current);
          }
        }, stepTime);
      } else {
        $this[newtag] = end;
      }
    },
    //顾问统计
    GetPerformanceSummaryPersonalStatistics(flg) {
      let $this = this;
      //$this.personalStatisItem.contrastHuikuan = 0;
      //$this.personalStatisItem.contrastTuijianNum = 0;
      //$this.personalStatisItem.contrastMianshiNum = 0;
      //$this.personalStatisItem.contrastOfferNum = 0;
      //$this.personalStatisItem.contrastEntryNum = 0;
      //$this.personalStatisItem.companyPerformanceTarget = 0;
      //$this.personalStatisItem.finishPercent = 0;
            //$.ajax({
      //  url: "/home/<USER>"+flg,
      //  data: {},
      //  dataType: 'json',
      //  contentType: 'application/json',
      //  success: function (res) {
      //    if (res.success) {
      //      $this.personalStatisItem = res.data;
      //    } else {
      //      beautAlert.done(res.message, "hits");
      //    }
      //  },
      //  error: function () { }
      //});
    },
    //是否展示快捷方式
    toggerKuaiJie() {
      if (this.isKuaijie) {
        //添加收起的动效
        let that = this;
        $(".kuaijie-wrap-new1").css({ "animation": "slidehide 0.5s linear 1" });
        setTimeout(function () {
          that.isKuaijie = false;
          $(".kuaijie-wrap-new1").addClass("page-head-height0");
        }, 500)

      } else {
        //添加展开的动效
        this.isKuaijie = true;
        $(".kuaijie-wrap-new1").css({ "animation": "slideshow 0.5s linear 1" });
        setTimeout(function () { $(".kuaijie-wrap-new1").removeClass("page-head-height0") }, 500)
      }
    },
    topChangeTime(v) {//事项提醒切换年月
      let that = this
      if (v != that.timeSel) {
        that.timeSel = v;
        switch (this.timeSel) {
          case 1:
            this.timeTxt = '今日';
            break;
          case 2:
            this.timeTxt = '本周';
            break;
          case 3:
            this.timeTxt = '本月';
            break;
          case 4:
            this.timeTxt = '本年';
            break;
        }
        //调用查询数据的接口
        that.GetPerformanceSummaryPersonalStatistics(that.timeSel);
      }
    },
    //点赞, staffId, id
    zanClick(index) {
      let that = this;
      that.isZanLoading = true;
      that.curIndex = index;
      //调用点赞接口，接口返回后改变值，有用值+1，如果点过踩，无用值就得-1
      setTimeout(function () {
        that.isZanLoading = false;
        that.curIndex = -1;
      }, 1500)
    },
    //踩, staffId, id
    caiClick(index) {
      let that = this;
      that.isCaiLoading = true;
      that.curIndex = index;
      //调用踩接口，接口返回后改变值，有用值+1，如果点过踩，无用值就得-1
      setTimeout(function () {
        that.isCaiLoading = false;
        that.curIndex = -1;
      }, 1500)
    },
    /**23.10.30改版end */

    thisClick() {
      var list = $(".poster-txt>.audiotShow")
      var listed = $(".poster-txt>.audiotHide")
      for (var i = 0; i < list.length; i++) {
        list[i].onclick = (function (index) {
          return function () {
            $($(this).siblings('p.test-p')).attr({ style: '-webkit-line-clamp: unset' })
            $($(this)).hide()
            $($(this).siblings('p.audiotHide')).show()
          }
        })(i)
      }


      for (var i = 0; i < listed.length; i++) {
        listed[i].onclick = (function (index) {
          return function () {
            $($(this).siblings('p.test-p')).attr({ style: '-webkit-line-clamp: 3' })
            $($(this)).hide()
            $($(this).siblings('p.audiotShow')).show()
          }
        })(i)
      }

    },

    divScroll() {
      var _this = this
      let toTop = this.$refs.Box.scrollHeight;
      var wholeHeight = this.$refs.Box.scrollHeight;
      var scrollTop = this.$refs.Box.scrollTop;
      var divHeight = this.$refs.Box.clientHeight;
      if (scrollTop + divHeight >= wholeHeight) {
        //alert('滚动到底部了！');
        _this.moddleListShow = true
        _this.InterDataIndex++
        this.InterDataPage()
      }
    },

    eventDisposalRangeChange() {
      var _this = this
      if (_this.searchKeyInput == "") {
        this.GetPageInter()
      }
    },

    // 贴贴事件
    pasting(e) {
      var _this = this
      let txt = event.clipboardData.getData('Text')
      if (typeof (txt) == 'string') {
        if (_this.TagsVal.Content.length <= 2000) {
          _this.TagsVal.Content += txt
        }
      }
      let file = null
      const items = (event.clipboardData || window.clipboardData).items
      if (items.length) {
        for (let i = 0; i < items.length; i++) {
          if (items[i].type.indexOf('image') !== -1) {
            file = items[i].getAsFile()
            this.handleChange(file)
            break
          }
        }
      }
    },

    //添加标签
    handleClose(tag) {
      var _this = this
      _this.TagsVal.dynamicTags.splice(_this.TagsVal.dynamicTags.indexOf(tag), 1);
    },

    newCardShowInput() {
      var _this = this
      _this.inputVisible = true;
      _this.$nextTick(_ => {
        _this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    newCardHandleInput() {
      var _this = this
      let inputValue = this.inputValue;
      if (inputValue) {
        _this.TagsVal.dynamicTags.push(inputValue);
      }
      _this.inputVisible = false;
      _this.inputValue = '';
    },

    F_Open_dialog() {
      const fileType = ['jpg', 'jpeg', 'png', 'xlsx', 'xls', 'pdf', 'doc', 'docx']
      const inputFile = document.createElement('input')
      inputFile.type = 'file'
      inputFile.style.display = 'none'
      //inputFile.setAttribute('multiple', 'multiple');
      document.body.appendChild(inputFile)
      inputFile.click()
      inputFile.addEventListener('change', () => {
        const file = inputFile.files[0]
        var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
        if (!fileType.includes(testmsg)) {
          this.$message.warning('上传的文件格式只能是,jpg,jpeg,png,xlsx,xls,pdf,doc,docx')
          document.body.removeChild(inputFile)
          return false
        }

        this.handleChange(file)
      })
    },
    sizeTostr(size) {
      var data = "";
      if (size < 0.1 * 1024) { //如果小于0.1KB转化成B  
        data = size.toFixed(2) + "B";
      } else if (size < 0.1 * 1024 * 1024) {//如果小于0.1MB转化成KB  
        data = (size / 1024).toFixed(2) + "KB";
      } else if (size < 0.1 * 1024 * 1024 * 1024) { //如果小于0.1GB转化成MB  
        data = (size / (1024 * 1024)).toFixed(2) + "MB";
      } else { //其他转化成GB  
        data = (size / (1024 * 1024 * 1024)).toFixed(2) + "GB";
      }
      var sizestr = data + "";
      var len = sizestr.indexOf("\.");
      var dec = sizestr.substr(len + 1, 2);
      if (dec == "00") {//当小数点后为00时 去掉小数部分  
        return sizestr.substring(0, len) + sizestr.substr(len + 3, 2);
      }
      return sizestr;
    },
    // 上传
    handleChange(file, filelist) {
      var _this = this
      let formData = new FormData()
      formData.append('file', file.raw || file)
      $.ajax({
        type: 'post',
        data: formData,
        contentType: false,
        processData: false,
        url: "/UploadFile/UploadInteractionFile",
        success: function (data) {
          var a = data.FileName.substr(data.FileName.lastIndexOf("."))
          _this.imgData.push({ "url": data.Url, "FileName": data.FileName, "FileType": a, "FileSize": _this.sizeTostr(file.size) })
        }
      });
    },


    //发布线索
    publisClick(val) {
      var _this = this
      _this.TagsVal.Category = val
    },
    submitFabu() {
      var _this = this
      _this.clueLoading = true
      if (_this.TagsVal.Content == "") {
        r_Layout_BeautAlert.done("请填写线索内容", "hits", 2500);
        _this.clueLoading = false
        return
      }
      if (_this.TagsVal.dynamicTags.length == 0) {
        r_Layout_BeautAlert.done("请添加标签", "hits", 2500);
        _this.clueLoading = false
        return
      }
      ClueImg = [],
        ClueFile = [];
      _this.imgData.forEach(e => {
        if (e.FileType == '.jpg' || e.FileType == '.jpeg' || e.FileType == '.png') {
          ClueImg.push(e.url)
        } else {
          ClueFile.push({ "FileName": e.FileName, "FileUrl": e.url, "FileSize": e.FileSize, "FileType": e.FileType })
        }
      })
      var data = {
        StaffId: _this.staffId,
        Content: _this.TagsVal.Content,//内容
        Category: _this.TagsVal.Category,//标签分类
        ClueImg: ClueImg,
        ClueFileObj: ClueFile,
        ClueLabel: _this.TagsVal.dynamicTags,
      }
      $.post("/Staff/addinteraction", data, function (result) {
        if (result.Status == 1) {
          _this.clueDialog = false
          _this.clueLoading = false
          _this.TagsVal.Content = ""
          _this.imgData = []
          _this.TagsVal.dynamicTags = []
          _this.GetPageInterData = []
          _this.GetPageInter()
        } else {
          r_Layout_BeautAlert.done(result.Message, 'hits');
          _this.clueLoading = false
        }
      }).error(function (er) {
        r_Layout_BlockUI.hide("");
      });
    },

    deleteImg(index) {
      var _this = this
      _this.imgData.splice(index, 1);
    },

    //联合交付------开始
    closeClueDialog() {
      var _this = this
      _this.clueDialog = false
      _this.TagsVal.Content = ""
      _this.imgData = []
      _this.TagsVal.dynamicTags = []

    },
    //发布线索
    publishClick() {
      var _this = this
      _this.clueDialog = true
    },

    //是否弹出偏好设置窗口
    IsEjectPreference() {
      var _this = this
      $.post("/Staff/IsEjectPreference", { staffId: _this.staffId }, function (r) {
        _this.projectDialog = r.Data
      }).error(function (er) {

      })
    },
    //下次设置偏好
    NextSettingPreference() {
      var _this = this
      $.post("/Staff/NextSettingPreference", { staffId: _this.staffId }, function (r) {
        _this.projectDialog = false
      }).error(function (er) {

      })
    },

    lianheClick(val) {
      var _this = this
      _this.lianheListId = val
      if (val == 1) {
        _this.pageCount = 1
        this.jobTags()
      } else if (val == 2) {
        this.resumeTags()
        _this.pageCount = 1

      } else if (val == 3) {
        _this.GetPageInterData = []
        _this.InterDataIndex = 1
        this.GetPageInter()
      }
    },
    changeBatchClick() {
      var _this = this
      var num = _this.ResumeESTotal / 8
      _this.pageCount++
      if (_this.pageCount > num) {
        _this.pageCount = 1
      }
      if (_this.lianheListId == 1) {

        this.lianheOne()
      } else if (_this.lianheListId == 2) {
        this.lianheTwo()
      }
    },
    salaryClick(val) {
      var _this = this
      if (val == 1) {
        _this.PreferenceData.GoodMinSalary = 20
        _this.PreferenceData.GoodMaxSalary = 30
      } else if (val == 2) {
        _this.PreferenceData.GoodMinSalary = 30
        _this.PreferenceData.GoodMaxSalary = 50
      } else if (val == 3) {
        _this.PreferenceData.GoodMinSalary = 50
        _this.PreferenceData.GoodMaxSalary = 100
      }
    },

    AtLinksClick(val) {
      val.IsSelected = !val.IsSelected
    },
    DispositionClick(val) {
      val.IsSelected = !val.IsSelected
    },
    AbilitysClick(val) {
      val.IsSelected = !val.IsSelected
    },
    //职业标签Resume/SearchResumeSignal
    resumeTags() {
      var _this = this
      _this.resumeTagsData = []
      $.get("/Resume/SearchResumeSignal", {}, function (r) {
        if (r.Data.ResumeResourcelist && r.Data.ResumeResourcelist.length > 0) {
          r.Data.ResumeResourcelist.forEach(e => {
            const item = {
              Count: e.Count,
              Name: e.Name,
              check: true
            }
            _this.resumeTagsData.push(item)
            _this.resumeTagsArray.push(item.Name)
            _this.checkList.push(item.Name)
          })
          //_this.resumeTagsData[0].check = true
          //_this.resumeTagsArray = [_this.resumeTagsData[0].Name]
          //_this.checkList = [_this.resumeTagsData[0].Name]
        }
        _this.lianheTwo()
      }).error(function (er) {
      })
    },

    //人选标签 Job/SearchJobSignal
    jobTags() {
      var _this = this
      _this.jobTagsData = []
      $.get("/Job/SearchJobSignal", {}, function (r) {
        if (r.Data.JobResourcelist && r.Data.JobResourcelist.length > 0) {

          r.Data.JobResourcelist.forEach(e => {
            const item = {
              Count: e.Count,
              Name: e.Name,
              check: true
            }
            _this.jobTagsData.push(item)
            _this.jobTagsArray.push(item.Name)
            _this.checkList.push(item.Name)
          })

        }

        _this.lianheOne()
      }).error(function (er) {

      })
    },
    jobTagsClick(val) {
      var _this = this
      _this.jobTagsArray = []
      val.check = !val.check
      var arr = _this.jobTagsData.filter(item => item.check == true);
      arr.forEach(e => {
        _this.jobTagsArray.push(e.Name)
      })
      if (_this.lianheListId == 1) {

        this.lianheOne()
      } else if (_this.lianheListId == 2) {
        this.lianheTwo()
      }
    },
    //我有需求，找人才
    lianheOne() {
      let _this = this
      _this.ESLoading = true
      _this.ResumeESData = []
      _this.ResumeESTotal = 0
      var data = {
        StaffId: _this.staffId,//员工ID
        TagNameList: _this.jobTagsArray,//标签列表
        PageSize: 8,
        PageIndex: _this.pageCount,
      }
      $.post("/Resume/SearchResumeES", data, function (r) {
        _this.ResumeESData = r.Data
        _this.ResumeESTotal = r.Total
        _this.ESLoading = false
        setTimeout(function () {
          rnss_event_usertelphonecall2()
        }, 1000);
      }).error(function (er) {
      })
    },


    //我有人才，找需求
    resumeTagsClick(val) {
      var _this = this
      val.check = !val.check
      _this.resumeTagsArray = []
      var arr = _this.resumeTagsData.filter(item => item.check);
      arr.forEach(e => {
        _this.resumeTagsArray.push(e.Name)
      })
      this.lianheTwo()
    },
    lianheTwo() {
      let _this = this
      _this.ESLoading = true
      _this.ResumeESData = []
      _this.ResumeESTotal = 0
      var data = {
        StaffId: _this.staffId,//员工ID
        TagNameList: _this.resumeTagsArray,//标签列表
        PageSize: 8,
        PageIndex: _this.pageCount,
      }
      $.post("/Job/SearchJobES", data, function (r) {
        _this.ResumeESData = r.Data
        _this.ResumeESTotal = r.Total
        _this.ESLoading = false
        setTimeout(function () {
          rnss_event_usertelphonecall2()
        }, 1000);
      }).error(function (er) {
      })
    },
    //资源大厅

    //tab切换
    tabSearchClick(key) {
      var _this = this
      if (key == 1) {
        _this.searchStaffId = ""
        _this.searchCategroy = ""
      } else if (key == 2) {
        _this.searchStaffId = ""
        _this.searchCategroy = 1
      } else if (key == 3) {
        _this.searchStaffId = ""
        _this.searchCategroy = 2
      } else if (key == 4) {
        _this.searchCategroy = ""
        _this.searchStaffId = _this.staffId
      }
      _this.GetPageInterData = []
      _this.InterDataIndex = 1
      this.GetPageInter()
    },
    searchInput() {
      var _this = this
      _this.GetPageInterData = []
      _this.InterDataIndex = 1
      this.GetPageInter()
    },
    clueDateDialog() {
      var _this = this
      _this.dateDialog = true
    },
    closeDateDialog() {
      var _this = this
      _this.dateDialog = false
      _this.clueDateVal = ""
    },
    submitClueDate() {
      var _this = this
      _this.startFrom = _this.clueDateVal[0]
      _this.startTo = _this.clueDateVal[1]
      _this.dateDialog = false
      _this.clueDateVal = ""
      this.GetPageInter()
    },
    InterDataPage() {
      var _this = this
      /*_this.InterDataCont += 10*/
      this.GetPageInter()
    },
    GetPageInter() {
      var _this = this
      var data = {
        Page: _this.InterDataIndex,
        PageSize: 10,// _this.InterDataCont
        StaffId: _this.searchStaffId,
        UserStaffId: _this.staffId,//员工ID
        Categroy: _this.searchCategroy,
        CompanyId: "",
        From: _this.startFrom,
        To: _this.startTo,
        Keywords: _this.searchKeyInput
      }

      $.post("/Staff/GetPageInteraction", data, function (r) {
        if (r.Success) {
          let list = r.Data
          _this.GetPageInterData = _this.GetPageInterData.concat(list)
          _this.moddleListShow = false


          //setTimeout(function () {
          //  var audiot_style = document.getElementsByClassName("audiot_style");
          //  var GetPageInterData = _this.GetPageInterData;
          //  var inputvalue = _this.searchKeyInput;
          //  // 遍历所有对话文本内容
          //  for (let i = 0; i < GetPageInterData.length; i++) {
          //    // 当对话内容中有包含搜索框中的字符串时
          //    if (GetPageInterData[i].Content.indexOf(inputvalue) >= 0) {
          //      // 先将包含关键字的对话内容拆分为数组
          //      var values = GetPageInterData[i].Content.split(inputvalue);
          //      // 然后再以一段设置了css样式的标签为分隔符,将数组拼接为字符串
          //      // 再赋值给对应的dom,让其节点的innerhtml为这个字符串
          //      audiot_style[i].innerHTML = values.join(
          //        '<span style="color:#EE1414;">' + inputvalue + "</span>"
          //      );
          //    }
          //  }
          //  rnss_event_usertelphonecall2()
          //}, 1000);

        } else {
          r_Layout_BeautAlert.done(r.Message, "hits", 1600);
        }

      }).error(function (err) {
        r_Layout_BeautAlert.done("请求失败", "hits", 1600);
      })

    },


    GetSatffPreference: function (callback) {
      var _this = this
      $.post("/Staff/GetSatffPreference", { staffId: _this.staffId }, function (r) {
        _this.PreferenceData = r.Data
        if (r.Success) {
          _this.PreferenceData = r.Data
        } else {
          r_Layout_BeautAlert.done(r.Message, "hits", 1600);
        }

      }).error(function (err) {
        r_Layout_BeautAlert.done("请求失败", "hits", 1600);
      })
    },

    submitProject() {
      var _this = this
      if (!_this.PreferenceData.GoodIndustrys) {
        r_Layout_BeautAlert.done("请选择偏好行业", "hits", 1600);
        return
      }
      if (!_this.PreferenceData.GoodOccupations) {
        r_Layout_BeautAlert.done("请选择偏好职位", "hits", 1600);
        return
      }
      if (!_this.PreferenceData.GoodLocations) {
        r_Layout_BeautAlert.done("请选择偏好地区", "hits", 1600);
        return
      }
      if (Number(_this.PreferenceData.GoodMinSalary) == 0 && Number(_this.PreferenceData.GoodMaxSalary == 0)) {
        r_Layout_BeautAlert.done("请填写偏好年薪", "hits", 1600);
        return
      }
      var num = _this.PreferenceData.GoodAtLinks.filter(s => s.IsSelected == true)
      var num1 = _this.PreferenceData.GoodDispositions.filter(s => s.IsSelected == true)
      var num2 = _this.PreferenceData.GoodAbilitys.filter(s => s.IsSelected == true)
      var num3 = num.length + num1.length + num2.length
      if (num3 > 8) {
        r_Layout_BeautAlert.done("擅长环节，性格特点，个人能力标签最高不超过八个", "hits", 2500);
        return
      }
      var data = _this.PreferenceData
      $.post("/Staff/SetSatffPreference", { data }, function (r) {
        if (r.Success) {
          r_Layout_BeautAlert.done("设置成功，如果需要再次修改可在系统右上角点击我的头像，进入“个人主页”进行修改。", "hits", 5000);
          _this.projectDialog = false

        } else {
          r_Layout_BeautAlert.done(r.Message, "hits", 1600);
        }

      }).error(function (err) {
        r_Layout_BeautAlert.done("请求失败", "hits", 1600);
      })

    },

    editAbility: function (type) {
      if (type == 'industry') {
        this.helpSelect1(this.pluginSelectorIndustry, 3, {
          targetObj: 'goodIndustrys',
        });
      }
      if (type == 'occupation') {
        this.helpSelect1(new r_AdvancedSelect({
          title: "选择职位",
          data: data_occupation3,
          showmeassage: function (txt) {
            beautAlert.done(setRnssLanguage(txt), "hits");
          }
        }), 3, {
          targetObj: 'goodOccupations'
        });
      }
      if (type == 'location') {
        this.nativeHelpSelect(this.nativeLocation, 3, "NativeLocation");
      }
    },
    // 户籍地址
    nativeHelpSelect(obj, num, valname) {
      var _this = this;
      var nativeLocation_obj = [];
      obj.setCheckedNum(num);
      obj.setSureCallBack(function (result) {
        _this.PreferenceData.GoodLocations = []
        result.forEach(e => {
          const item = {
            LocationsId: e.Id,
            LocationsName: e.Text
          }
          _this.PreferenceData.GoodLocations.push(item)
        })
        this.hidden();
      });
      if (nativeLocation_obj) {
        obj.select(nativeLocation_obj);
      }
      obj.show();
    },

    helpSelect1: function (obj, num, o) {
      var $this = this;
      var sd = o.targetObj;
      obj.setCheckedNum(num);
      obj.setSureCallBack(function (result) {
        var list = result.map(function (elem) {
          return {
            id: elem.Id,
            text: elem.Text,
          }
        });
        var resThis = this;

        $this.ajaxSetStaffAbilityData(o.targetObj, list, function () {
          $this.PreferenceData.GoodIndustrys[o.targetObj] = list;
          resThis.hidden();
        });

      });

      //if (sd.length > 0) {
      //  obj.select(sd.map(function (elem) {
      //    return elem.id;
      //  }));
      //}

      obj.show();
    },

    ajaxSetStaffAbilityData: function (type, selected, successCallback) {
      var _this = this;
      var data = {};
      data[type] = [];
      var url;
      if (type == 'goodIndustrys') {
        _this.PreferenceData.GoodIndustrys = []
        selected.forEach(function (elem) {
          const item = {
            industryId: elem.id,
            industryName: elem.text,
          }
          _this.PreferenceData.GoodIndustrys.push(item)
        });
      } else {
        _this.PreferenceData.GoodOccupations = []
        selected.forEach(function (elem) {
          const item = {
            occupationsId: elem.id,
            occupationsName: elem.text,
          }
          _this.PreferenceData.GoodOccupations.push(item)
        });
      }
      successCallback && successCallback();
    },


    //联合交付------结束


    jumpContactName(jobCandidateId) {//人选详情
      window.open("http://staff.risfond.com/job/viewcandidate?id=" + jobCandidateId)
    },
    jumpjobTitle(jobId) {//职位详情
      window.open("http://staff.risfond.com/apps/viewjob2.aspx?id=" + jobId)
    },
    jumpPartyAName(clientId) {//客户详情
      window.open("http://staff.risfond.com/client/viewclient?id=" + clientId)

    },
    schoolTiao(a, b) {//课堂跳转
      window.open("http://staff.risfond.com/course/CourseDetail?type=1&id=" + a + "&keshiid=" + b)
    },
    openJobDetailPanel: function (jobId, title) {//打开职位弹框
      //if (typeof jobDetailVue == 'undefined') {
      //  this.ajaxGetJobDetailView(function () {
      //    jobDetailVue.openJobDetailPanel(jobId);
      //  });
      //  return
      //}
      //jobDetailVue.openJobDetailPanel(jobId);
      //23.11改成跳转到交互中心，并展开对应详情
      if (!jobId) return;
      let item = {
        jobId: jobId,
        title: title
      }
      localStorage.setItem('jiaohu', JSON.stringify(item));
      window.open('/deliverycenter/index');
    },
    closeJobDetailPanel: function () {//关闭
      jobDetailVue.closeJobDetailPanel();
    },
    ajaxGetJobDetailView: function (callback) {
      if (this.jobDetailLock) {
        return;
      }
      this.jobDetailLock = true;
      $.ajax({
        url: "/deliverycenter/jobdetailpartialview",
        data: {},
        type: "GET",
        dataType: "html",
        success: function (html) {
          $("#jobDetailPanel").html(html);
          callback && callback();
          this.jobDetailLock = false;
        },
        error: function (xhr, status) {
        },
      });
    },
    //getPersonData(){//获取登录人信息
    //    let that = this
    //    let yearTop=new Date().getFullYear()
    //    let yearBottom=0
    //    let monthTop=new Date().getMonth()+1
    //    let monthBottom=0
    //    if(monthTop == 12){
    //        monthBottom = 1
    //        yearBottom = yearTop +1
    //    }else{
    //        monthBottom = monthTop+1
    //        yearBottom = yearTop
    //    }
    //    const data = {
    //        //StartTime:yearTop+"-"+monthTop+"-1",//起始年月 格式2021-7-1
    //        //EndTime:yearBottom+"-"+monthBottom+"-1"//起始年月 格式2021-8-1
    //        StartTime:yearTop+"-1-1",//起始年 格式2021-1-1
    //        EndTime: (yearTop+1)+"-1-1"//起始年 格式2022-1-1
    //    }
    //    $.post("/WorkBench/ConsultantWorkPanelStatistics", data, function(r) {
    //          const a = r.Data
    //          that.personRz = a.EntryNum//入职
    //          that.personHk = a.PaidBackAmount//回款
    //          that.personTj = a.TuijianNum//推荐
    //          that.personMs = a.MianshiNum//面试
    //          that.personOffer = a.OfferNum//offer
    //        }).error(function(er) {
    //    })
    //    },
    getPersonData2() {//获取登录人信息
      let that = this
      $.get("/home/<USER>", function (r) {
        const a = r.data
        const b = r.staffInfo
        if (a.performanceTargetAmount == null) {
          that.personYj = 0//登录人业绩目标
        } else {
          that.personYj = a.performanceTargetAmount//登录人业绩目标
        }
        if (a.percent == null) {
          that.personJd = 0//登录人完成进度
        } else {
          that.personJd = a.percent//登录人完成进度
        }
        if (a.unfinishedPerformanceAmount == null) {
          that.personWwc = 0//登录人未完成
        } else {
          that.personWwc = a.unfinishedPerformanceAmount//登录人未完成
        }

        that.personRz = a.entryNum//入职
        that.personHk = a.paidBackAmount//回款
        that.personTj = a.tuijianNum//推荐
        that.personMs = a.mianshiNum//面试
        that.personOffer = a.offerNum//offer

        that.prsonziliao = b
        //根据登录人的时间反馈内容，后期修改
        var seniority = r.seniority;
        if (seniority <= 2)
          that.userYearTxt = '今日愉快～遇到困难也要勇往直前，克服难关！';//1-2年
        else if (seniority <= 5)
          that.userYearTxt = '每天都是新的开始，愿你积极面对，充满信心！';//2-5年
        else that.userYearTxt = '新的一天，新的挑战，愿你充满乐趣和成就感！';//5年以上
      }).error(function (er) {
      })
    },
    getReliableKAPageList() {//分页获取KA靠谱大客户
      let that = this
      const data = {
        rNSSNoticeQuery:
        {
          "IsPublish": 1,
          "RnssType": 5,
          "PageNum": 1,
          "PageSize": 3
        }
      }
      $.post("/Client/GetReliableKAPageList", data, function (r) {
        that.kpBig = r.data
      }).error(function (er) {
      })
    },
    getKechengList() {//获取达人网校课程推荐模块
      let that = this
      $.get("/home/<USER>", function (r) {
        if (r.success) {
          that.kechengList = r.data
        }
      }).error(function (er) {

      })
    },
    getInteractionList() {//获取实时信息互动

      let that = this
      $.get("/home/<USER>", function (r) {
        that.timeMessage = r.data
        that.gundongBottom()
      }).error(function (er) {
      })
    },
    getDeliveryCenterJobList(v) {//查询交付职位
      let that = this
      that.lhjfisLoading = true;
      let data = {}
      const shortWarrantyPeriod = that.checkeda == false ? null : true
      const moreCommision = that.checkedb == false ? null : true
      const hasOrder = that.checkedc == false ? null : true
      const hasMoneyBack = that.checkedd == false ? null : true
      const fastFeedback = that.checkede == false ? null : true

      if (v == 0) {
        data = {
          industries: that.industryData,
          Occupations: that.occupationData,
          cities: that.ilocationTreeData,
          shortWarrantyPeriod: shortWarrantyPeriod,
          moreCommision: moreCommision,
          salaryFrom: that.query.salaryFrom,
          salaryTo: that.query.salaryTo,
          kwd: that.query.kwd,
          hasOrder: hasOrder,
          hasMoneyBack: hasMoneyBack,
          fastFeedback: fastFeedback,
          pageIndex: 1,
          pageSize: 5,
          JobType: that.JobType
        }
      }
      else {
        data = {
          pageIndex: 1,
          pageSize: 5,
          jobType: v,
        }
      }


      $.ajax({
        type: "post",
        url: "/Home/GetPageDeliveryCenterJobList",
        data: JSON.stringify(data),
        dataType: 'json',
        contentType: 'application/json',
        success: function (r) {
          that.positionList = r.data
          that.lhjfisLoading = false;
        },
        error: function () {
          that.lhjfisLoading = false;
        }
      });
      // $.post("/Home/GetPageDeliveryCenterJobList",JSON.stringify(data),function(r) {
      //     that.positionList = r.data
      // }).error(function(er) {
      // })


    },
    getRankingList() {//查询排行榜
      let that = this
      let abc = 0
      if (that.rankingLeftValue == 200 || that.rankingLeftValue == 400) {
        that.tableThree = "回款金额"
      } else if (that.rankingLeftValue == 100) {
        that.tableThree = "所得业绩"
      } else {
        that.tableThree = "完成数"
      }
      if (that.rankingPearsonValue == 2) {
        abc = "2,3,4,6";
      } else if (that.rankingPearsonValue == 3) {
        abc = "9,12";
      }
      else if (that.rankingPearsonValue == -1) {
        abc = "2,3,4,6,9,12,1";
      }
      else {
        abc = "1";
      }
      let data = {
        "rankingQuery": {
          "position": abc, //顾问=2,3,4,6 AC=9,12 助理=1
          "rankingType": that.rankingLeftValue,//龙虎榜100，推荐2，面试6，入职9，offer8，回款是200，签约300
          "timetype": that.rankingDateValue,//本周1，本月2，本季3，本年4
          "scope": that.rankingRightValue,//全国榜1，大区榜3，本地榜2
          "top": "6"
        }
      }
      $.post("/home/<USER>", data, function (r) {
        that.rankingData = r.data
      }).error(function (er) {
      })
    },
    changeMonth(v) {//排行榜切换年月
      let that = this
      if (v != that.timetype) {
        that.timetype = v
        $("#monthId" + v).addClass("click-divv").siblings().removeClass("click-divv");
      }
    },
    changeJf(v) {//排行榜切换年月
      let that = this
      that.moreData = v
      $("#changeJf" + v).addClass("lhjf-left-center-div1-p2").siblings().removeClass("lhjf-left-center-div1-p2");
      that.getDeliveryCenterJobList(v)

    },
    getGetCompanyNewsList() {//查询公司新闻
      let that = this
      $.get("/home/<USER>", function (r) {
        that.companyNews = r.data
        if (that.companyNews.length != 0) {
          that.companyNews.map((item, index) => {
            if (index % 2 == 0) {
              item.abc = 1
            } else {
              item.abc = 2
            }
          })
        }
      }).error(function (er) {
      })
    },
    topChangeMonth1(v) {//事项提醒切换年月
      let that = this
      if (v != that.topChangeMonth) {
        that.topChangeMonth = v
        $("#topChangeMonth" + v).addClass("sxtj-active").siblings().removeClass("sxtj-active");
        that.getClientRemindMessage()
      }
    },
    getClientRemindMessage() {//查询事项提醒
      let that = this
      $.get("/Home/GetClientRemindMessage?type=" + that.monthChangeData + "&timetype=" + that.topChangeMonth, function (r) {
        that.thingRemind = r.data
      }).error(function (er) {
      })
    },
    addCondition() {//添加订阅条件
      let that = this
      let a = {
        categoryID: 1,
        tjMessage: "",
        keyword: [],
      }
      that.subMessages.push(a)
    },
    addGuanjian(v) {//添加关键词
      let that = this
      if (that.subMessages[v].tjMessage == "") {
        return
      }
      if (that.subMessages[v].keyword.length == 8) {
        that.$message.error('只能添加8个关键词');
        return
      }
      that.subMessages[v].keyword.push(that.subMessages[v].tjMessage)
      that.subMessages[v].tjMessage = ""
    },
    delectDyData(v) {//订阅信息删除一整组数据
      let that = this
      let c = that.subMessages
      c.splice(v, 1)
      that.subMessages = c
    },
    decectkeyWord(a, b) {//订阅信息删除关键词
      let that = this
      let d = that.subMessages[a].keyword
      d.splice(b, 1)
      that.subMessages[a].keyword = d
    },
    submitDyData() {//保存订阅信息
      let that = this
      let data = {
        "staffSubscriptionInteractionDTO": []
      }
      that.subMessages.forEach(item => {
        data.staffSubscriptionInteractionDTO.push(item)
      });
      $.post("/Interaction/SaveStaffSubscriptionInteraction", data, function (r) {
        if (r.success == true) {
          that.$message.success('保存成功');
          that.closeDingyue()
        } else {
          that.$message.error('保存失败,请检查关键词是否为空');
        }

      }).error(function (er) {
        that.$message.error('保存失败,请检查关键词是否为空');
      })
    },
    getStaffSubscriptionInteraction() {//获取订阅信息
      let that = this
      $.get("/Interaction/GetStaffSubscriptionInteraction", function (r) {
        r.data.map(item => {
          item.tjMessage = ""
        })
        that.subMessages = r.data
      }).error(function (er) {
      })
    },
    getSubscriptionResumeDynamic() {//查询员工订阅简历动态
      let that = this
      $.get("/Home/GetSubscriptionResumeDynamic", function (r) {
        if (r.success) {
          that.resumeDynamics = r.data
          that.resumeDynamics.map((item, index) => {
            if (index % 2 == 0) {
              item.abc = 1
            } else {
              item.abc = 2
            }
          })
        }
      }).error(function (er) {
      })
    },
    openDingyue() {//打开订阅信息弹框
      var dingyue = document.querySelector(".dingyue-tankuang");
      dingyue.style.display = "block";
    },
    closeDingyue() {//关闭订阅信息弹框
      var dingyue = document.querySelector(".dingyue-tankuang");
      dingyue.style.display = "none";
    },
    openZhiwei() {//打开职位类型弹框
      var zhilei = document.querySelector(".zhilei-tankuang");
      zhilei.style.display = "block";
    },
    closeZhiwei() {//关闭职位类型弹框
      var zhilei = document.querySelector(".zhilei-tankuang");
      zhilei.style.display = "none";
    },
    clearZhiwei() {
      let that = this
      that.followIndustry = "请选择行业，可选择3个行业"
      that.followOccupation = "请选择职位类别，可选择3个类别"
      that.followIlocationTree = "请选择职位地点，可选择3个地点"
      that.industryData = []
      that.occupationData = []
      that.ilocationTreeData = []
      that.checkeda = false
      that.checkedb = false
      that.checkedc = false
      that.checkedd = false
      that.checkede = false
    },
    getjoblist() {//合作联盟实时播报
      let that = this
      let data = {}
      $.post("/Job/GetCooperationSuccess", data, function (r) {
        that.lianhebobao = r.data;
        let count = that.lianhebobao.length;
        if (4 < count < 12) {
          $(".list .rowup").css("animation-duration", count * 2 + 's');
        } else if (count >= 12) {
          $(".list .rowup").css("animation-duration", count + 's');
        }
      }).error(function (er) {
      })
    },
    getConfigsList() {//实时信息互动类型
      let that = this
      $.get("/Home/GetConfigsList?type=InteractionInfoType", function (r) {
        that.submitLeixing = r.data
      }).error(function (er) {
      })
    },
    getStaffAttentionJob() {//查询用户关注交付中心职位
      let that = this
      $.get("/DeliveryCenter/GetStaffAttentionJob", function (r) {
        const a = r.data
        if (a.industryIDStr) {
          that.followIndustry = a.industryIDStr
        }
        if (a.locationIDStr) {
          that.followIlocationTree = a.locationIDStr
        }
        if (a.occupationIDStr) {
          that.followOccupation = a.occupationIDStr
        }
        that.industryData = a.industryID
        that.occupationData = a.occupationID
        that.ilocationTreeData = a.locationID
        that.checkeda = a.shortWarrantyPeriod == 0 ? false : true
        that.checkedb = a.moreCommision == 0 ? false : true
        that.checkedc = a.hasOrder == 0 ? false : true
        that.checkedd = a.hasMoneyBack == 0 ? false : true
        that.checkede = a.fastFeedback == 0 ? false : true
        that.getDeliveryCenterJobList(0)
      }).error(function (er) {
      })
    },
    saveStaffAttentionJob() {//保存用户关注交付中心职位
      let that = this
      const shortWarrantyPeriod = that.checkeda == false ? 0 : 1
      const moreCommision = that.checkedb == false ? 0 : 1
      const hasOrder = that.checkedc == false ? 0 : 1
      const hasMoneyBack = that.checkedd == false ? 0 : 1
      const fastFeedback = that.checkede == false ? 0 : 1
      let data = {
        "staffAttentionJobDTO": {
          "IndustryID": that.industryData,
          "OccupationID": that.occupationData,
          "LocationID": that.ilocationTreeData,
          "shortWarrantyPeriod": shortWarrantyPeriod,
          "moreCommision": moreCommision,
          "hasOrder": hasOrder,
          "hasMoneyBack": hasMoneyBack,
          "fastFeedback": fastFeedback,
        }
      }
      $.post("/DeliveryCenter/SaveStaffAttentionJob", data, function (r) {
        if (r.success == true) {
          that.$message.success('保存成功');
          that.closeZhiwei()
        }
      }).error(function (er) {
        that.$message.error('保存失败,请稍后再试');
      })
    },
    submitMessage() {//发送互动信息
      let that = this
      let data = {
        Content: that.submitInput,
        category: that.submitValue,
      }
      $.post("/home/<USER>", data, function (r) {
        that.$message.success('发送成功');
        that.submitInput = ""
        that.getInteractionList()
      }).error(function (er) {
      })
    },
    getIndustry() {//获取行业
      let that = this
      $.get("/RnssIndustry/GetIndustry", function (r) {
        that.getIndustryDigui(r.Data)
      }).error(function (er) {
      })
    },
    getIndustryDigui(r) {//获取行业的数据处理
      let that = this
      r.forEach(item => {
        if (item.Childens.length != 0) {
          that.industry.push(item)
          that.getIndustryDigui(item.Childens)
        } else {
          that.industry.push(item)
        }
      })
    },
    getOccupation() {//获取职业类别
      let that = this
      $.post("/Occupation/GetOccupation?ContainsEx=true", function (r) {
        that.getOccupationDigui(r)
      }).error(function (er) {
      })
    },
    getOccupationDigui(r) {//获取职业类别的数据处理
      let that = this
      r.forEach(item => {
        if (item.Childens.length != 0) {
          that.occupation.push(item)
          that.getOccupationDigui(item.Childens)
        } else {
          that.occupation.push(item)
        }
      })
    },
    getILocationTree() {//获取工作地点
      let that = this
      $.get("/RnssIndustry/GetLocationTree", function (r) {
        that.getILocationTreeDigui(r.Data)
      }).error(function (er) {
      })
    },
    getILocationTreeDigui(r) {//获取工作地点的数据处理
      let that = this
      if (r != null) {
        r.forEach(item => {
          if (item.Childens.length != 0) {
            that.ilocationTree.push(item)
            that.getILocationTreeDigui(item.Childens)
          } else {
            that.ilocationTree.push(item)
          }
        })
      }
    },
    selectData(v) {//用系统自带的弹框赋值
      let that = this
      that.selectType = v
      let title = ""
      let data = []
      let dataName = ""
      let dataNameId = {}
      if (v == 1) {
        title = "选择职位行业"
        data = that.industry
        dataName = "Industry"
        dataNameId = {
          IdKey: "IndustryId",
        }
      } else if (v == 2) {
        title = "选择职位类别"
        data = that.occupation
        dataName = "Occupation"
        dataNameId = {
          IdKey: "OccupationId",
        }
      } else {
        title = "请选择职位地点"
        data = that.ilocationTree
        dataName = "locationsWrapper"
        dataNameId = {
          IdKey: "Location",
        }
      }
      if (v != 3) {
        var occupationSelectObj = new r_AdvancedSelect(
          {
            title: title,
            data: data,   // 基础数据源
            showmeassage: function (txt) {
              beautAlert.done(setRnssLanguage(txt), "hits");
            }
          }
        );
      }
      else {
        var occupationSelectObj = new AdvancedSelect2({
          title: "选择地址",
          width: 520,
          data: data,
          showmeassage: function (txt) { beautAlert.done(txt, "hits"); }
        });
      }
      this.helpSelect($(this), occupationSelectObj, 3, dataName, dataNameId
      );
    },
    helpSelect: function (el, obj, num, valname, o) {//用系统自带的弹框赋值之后的回调
      if (el.find("li").size() == 0) { el.removeData("data"); }
      var that = this;
      var sd = o.target_obj;
      let timeMessages = []
      obj.setCheckedNum(num);
      obj.setSureCallBack(function (result) {
        if (result.length != 0) {
          if (that.selectType == 1) {
            that.industryData = []
            result.forEach(item => {
              timeMessages.push(item.Text)
              that.industryData.push(item.Id)
            })
            that.followIndustry = timeMessages.join("，")
          } else if (that.selectType == 2) {
            that.occupationData = []
            result.forEach(item => {
              timeMessages.push(item.Text)
              that.occupationData.push(item.Id)
            })
            that.followOccupation = timeMessages.join("，")
          } else {
            that.ilocationTreeData = []
            result.forEach(item => {
              timeMessages.push(item.Text)
              that.ilocationTreeData.push(item.Id)
            })
            that.followIlocationTree = timeMessages.join("，")
          }
        } else {
          if (that.selectType == 1) {
            that.industryData = result
            that.followIndustry = "请选择行业，可选择3个行业"
          } else if (that.selectType == 2) {
            that.occupationData = result
            that.followOccupation = "请选择职位类别，可选择3个类别"
          } else {
            that.ilocationTreeData = result
            that.followIlocationTree = "请选择职位地点，可选择3个地点"
          }
        }


        this.hidden();
      });
      if (sd) {
        obj.select(sd);
      }
      obj.show();
    },
    quickEntrance() {
      let self = this;
      $.ajax({
        url: "/home/<USER>/",
        method: "get",
        success: function (resp) {
          return new Promise((resolve, reject) => {
            if (resp.success) {
              resolve(resp);
            } else {
              reject(resp)
            }
          }).then(value => {
            let data = value.data;
            self.yesAddDTO = data.yesAddDTO;
            self.editYesAddDto = data.yesAddDTO;
            self.noAddDTO = data.noAddDTO;
            self.isKuaijieFlag = false;
          }, reason => {
            console.error(reason)
          }).then(() => {
            self.getRemainAddDTO();
          })
        }
      });
    },
    getRemainAddDTO() {
      let self = this;
      let yesArr = [];//已经选择的
      let noArr = [];//所有的
      let surplusArr = [];//所有的 - 已经所有的

      self.editYesAddDto.forEach(e => {
        yesArr.push(e.id);
      })
      self.noAddDTO.forEach(e => {
        noArr.push(e.id)
      })
      surplusArr = [...new Set(noArr)].filter(item => !(new Set(yesArr)).has(item));

      self.remainAddDTO = [];
      for (let i = 0; i < surplusArr.length; i++) {

        for (let j = 0; j < self.noAddDTO.length; j++) {

          if (surplusArr[i] == self.noAddDTO[j].id) {

            self.remainAddDTO.push(self.noAddDTO[j])

          }
        }
      }

    },
    //保存快捷方式
    saveShortcut() {
      let self = this;
      let params = {
        addShortcutDTOs: []
      };
      let shortCut = document.getElementsByName("shortCutNames");
      if (shortCut != "" && shortCut != undefined && shortCut != null) {
        shortCut.forEach(e => {
          params.addShortcutDTOs.push({
            Id: e.attributes[1].value
          })
        })
      }
      $.ajax({
        url: "/home/<USER>/",
        method: "POST",
        data: params,
        success: function (resp) {
          if (resp.success) {
            console.log('成功！！')
            self.quickEntrance();
          } else {
            console.error("失败！！")
          }
        }
      });
    },
    restoreDefault() {
      let self = this;
      $.ajax({
        url: "/home/<USER>/",
        method: "get",
        success: function (resp) {
          return new Promise((resolve, reject) => {
            if (resp.success) {
              resolve(resp);
            } else {
              reject(resp)
            }
          }).then(value => {
            let data = value.data;
            self.yesAddDTO = data.yesAddDTO;
            self.editYesAddDto = data.defaultDTO;
            self.noAddDTO = data.noAddDTO;
          }, reason => {
            console.error(reason)
          }).then(value => {
            self.getRemainAddDTO();
          })
        }
      });
    },
    toDetail(item) {
      window.open(item.shortcutUrl)
    },
    addShortcut(e) {
      let _this = this;
      return new Promise((resolve, reject) => {
        if (_this.editYesAddDto.length < 9) {
          _this.editYesAddDto.push(e);
          resolve();
        } else {
          reject();
        }
      }).then(value => {
        _this.getRemainAddDTO();
      }, reason => {
        alert('最多只能添加9个')
      }).then(value => {
      })
    },
    removeShortcut(e) {
      let _this = this;
      return new Promise((resolve, reject) => {
        if (_this.editYesAddDto.length > 1) {
          let index = _this.editYesAddDto.indexOf(e);
          if (index != -1) {
            _this.editYesAddDto.splice(index, 1);
          }
          resolve();
        } else {
          reject();
        }

      }).then(value => {
        _this.getRemainAddDTO();
      }, reason => {
        alert('不能在删除了')
      })
    },
    getMore() {
      let that = this
      if (that.moreData == 0) {
        window.open("http://staff.risfond.com/deliverycenter/index")
      } else {
        window.open("http://staff.risfond.com/DeliveryCenter/CooperationJob?typeId=3")
      }
    },
    getMorse(v) {
      window.open("http://staff.risfond.com" + v)

    },
    onScroll() {//挂载监听滚动
      window.addEventListener('scroll', this.scrollFuction);
    },
    offScroll() {//清除监听滚动
      window.removeEventListener('scroll', this.scrollFuction);
    },
    scrollFuction() {//监听滚动
      let that = this
      var scroll_top = 0;
      if (document.documentElement && document.documentElement.scrollTop) {
        scroll_top = document.documentElement.scrollTop;
      }
      else if (document.body) {
        scroll_top = document.body.scrollTop;
      }

      if (scroll_top > that.scrollNumber) {
        that.scrollNumber = scroll_top
        if (that.scrollOrdera == 0) {
          if (scroll_top >= 100) {
            
            that.getjoblist()//合作联盟实时播报
            that.getIndustry()//获取行业
            that.getOccupation()//获取职业类别
            that.getILocationTree()//获取工作地点
            that.scrollOrdera = 1
          }

        }
        if (that.scrollOrderb == 0) {
          if (scroll_top >= 400) {
            that.getRankingList()//查询排行榜
            that.getInteractionList()//获取实时信息互动
            that.getStaffSubscriptionInteraction()//获取订阅信息
            that.getConfigsList()//实时信息互动类型
            that.getKechengList()//获取达人网校课程推荐模块
            that.scrollOrderb = 1
          }
        }
        if (that.scrollOrderc == 0) {
          if (scroll_top >= 1000) {
            that.getSubscriptionResumeDynamic()//查询员工订阅简历动态
            that.getGetCompanyNewsList()//查询公司新闻
            that.offScroll()//卸载滚动监听
            that.scrollOrderc = 1
          }
        }

      }
    },
    gundongBottom() {//对话框触底
    },
    getRealTimeList() {//获取实时榜推荐
      let self = this;
      $.ajax({
        url: "/home/<USER>/",
        method: "get",
        success: function (resp) {
          if (resp.success) {
            let data = resp.data;
            if (data.length > 0) {
              self.realTimeList = data;
              self.shishituijaingbangIsflag = false;
              self.$nextTick(() => {
                if (self.realTimeList.length > 5) {
                  $('.tuijian-body tbody').html($('.tuijian-body tbody').html() + $('.tuijian-body tbody').html());
                  var outerHeight = $('.tuijian-body tbody').find("tr").outerHeight();
                  let count = self.realTimeList.length*2;
                  let top = (count - 6) * outerHeight;
                  let time = 0.8 * count;
                  $(".r-tb-tuijian-am").css({ "animation": "slideAm " + time +"s linear infinite normal" });
                  self.moveTableNew('r-tb-tuijian-am', time, top)
                }
                
              })

            } else {
              self.shishituijaingbangIsflag = false;
              self.tjDataLenFlag = true;
            }
          } else {
            console.error('error!!');
          }

        }
      });
    },
    //实时推荐、实时回款table滚动
    moveTableNew(tag, time, top) {
      $("." + tag+"").css({ "animation": "slideAm " + time + "s linear infinite normal" });
      var style = document.createElement('style');
      style.type = 'text/css';
      var keyFrames = '\
                    @-webkit-keyframes slideAm{\
                    0% {\
                    top: 0px;\
                    }\
                    100% {\
                    top: -'+ top + 'px;\
                    }\
                  }';
      style.innerHTML = keyFrames;
      document.getElementsByTagName('head')[0].appendChild(style);
    },
    getCollectionList() {//获取实时回款榜
      let arr = [];
      let self = this;
      return new Promise((resolve, reject) => {
        $.ajax({
          url: "/home/<USER>/",
          method: "get",
          success: function (resp) {
            if (resp.success) {
              let data = resp.data;
              if (data.length > 0) {
                data.forEach(e => {
                  arr.push(e);
                })
                resolve(arr);
              } else {
                self.shishihuikuangbangIsflag = false;
                self.hkDataLenFlag = true;
              }
            } else {
              console.error('error')
            }
          },
          error: function (e) {
            reject(e)
          }
        });

      }).then(value => {
        self.collectionList = value;
        self.shishihuikuangbangIsflag = false;
        self.$nextTick(() => {
          if (self.collectionList.length > 5) {
            $('.shishi-body tbody').html($('.shishi-body tbody').html() + $('.shishi-body tbody').html());
            $('.shishi-body').css('top', '0');
            var outerHeight = $('.shishi-body tbody').find("tr").outerHeight();
            //self.moveTable('collectionList', outerHeight, 'shishiTr', 'shishi-body tbody', 'MyMarhq')
            let count = self.collectionList.length * 2;
            let top = (count - 6) * outerHeight;
            let time = 0.8 * count;
            self.moveTableNew('r-tb-shishi-am', time, top)
          }
        })

      }, reason => {
        console.error(reason)
      })
    },

  },
  watch: {
    "personalStatisItem.huikuan": {
      handler(val) {
        this.animateCountUp('timerhuikuan', val, 'huikuan');
      }
    },
    "personalStatisItem.tuijianNum": {
      handler(val) {
        this.animateCountUp('timertuijian', val, 'tuijian');
      }
    },
    "personalStatisItem.mianshiNum": {
      handler(val) {
        this.animateCountUp('timermianshi', val, 'mianshi');
      }
    },
    "personalStatisItem.offerNum": {
      handler(val) {
        this.animateCountUp('timeroffer', val, 'offer');
      }
    },
    "personalStatisItem.entryNum": {
      handler(val) {
        this.animateCountUp('timerruzhi', val, 'ruzhi');
      }
    },
    "personalStatisItem.contrastHuikuan": {
      handler(val) {
        this.animateCountUp('timercomhuikuan', val, 'comhuikuan');
      }
    },
    "personalStatisItem.contrastTuijianNum": {
      handler(val) {
        this.animateCountUp('timercomtuijian', val, 'comtuijian');
      }
    },
    "personalStatisItem.contrastMianshiNum": {
      handler(val) {
        this.animateCountUp('timercommianshi', val, 'commianshi');
      }
    },
    "personalStatisItem.contrastOfferNum": {
      handler(val) {
        this.animateCountUp('timercomoffer', val, 'comoffer');
      }
    },
    "personalStatisItem.contrastEntryNum": {
      handler(val) {
        this.animateCountUp('timercomruzhi', val, 'comruzhi');
      }
    },
  }
})
function allowDrop(ev) {
  ev.preventDefault();
}
var srcdiv = null;
var temp = null;
//当拖动时触发
function drag(ev, divdom) {
  srcdiv = divdom;
  temp = divdom.innerHTML;
}
//当拖动完后触发
function drop(ev, divdom) {
  ev.preventDefault();
  if (srcdiv !== divdom) {
    srcdiv.innerHTML = divdom.innerHTML;
    divdom.innerHTML = temp;
  }
}
laydate.render({
  elem: '#test10',
  range: ['#test-startDate-1', '#test-endDate-1'],
  done: function (value) {
    let dateArr = value.split('-');
    if (dateArr != null && dateArr != "" && dateArr != undefined) {
      if (dateArr.length > 0) {
        VM.from = dateArr[0] + '-' + dateArr[1] + '-' + dateArr[2];
        VM.to = dateArr[3] + '-' + dateArr[4] + '-' + dateArr[5];
        VM.getOperateData();
      }
    }
  }
});
  /*// 右侧轮播动态赋值
var dome;
var timer;
window.onload = function()
{
dome = document.getElementById("dome");//外部盒子的id
var dome1 = document.getElementById("dome1");//内部盒子的id
var objName=document.getElementById("dome1").getElementsByTagName("div");//div是需要滚动的内容
dome1.style.height = objName.length*83 + "px";//number是每个盒子的高度
dome.onmouseover = function(){//鼠标放上暂停
window.clearInterval(timer);
}
dome.onmouseout = function(){
timer = window.setInterval("start2()",40);
}
timer = window.setInterval("start2()",40);
}
function start2()
{
if(dome.scrollTop == dome1.offsetHeight-270)
{
dome.scrollTop = 0;
}else
{
dome.scrollTop++;
}
}*/