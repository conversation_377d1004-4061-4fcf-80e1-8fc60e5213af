body.page-boxed {
  background-color: #e9ecf3;
}

.RsBody * {
  margin: 0;
  padding: 0;
  border: 0;
  text-decoration: none;
  list-style-type: none;
}

.RsBody a {
  text-decoration: none;
  color: #000;
}

  .RsBody img, .RsBody a img {
    border-width: 0;
  }

.RsBody .msg-box {
  height: 170px;
  width: 150px;
  border: 1px solid #d2d2d2;
  background-color: #fff;
  position: absolute;
  z-index: 1;
  cursor: auto;
  overflow: visible;
}

  .RsBody .msg-box .msg-title {
    height: 22px;
    width: 100%;
    background-image: url(images/weixin_title_bg.png);
    background-repeat: repeat-x;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: #dfdfdf;
    font-size: 12px;
    line-height: 22px;
    color: #007acd;
    text-indent: 5px;
  }

    .RsBody .msg-box .msg-title .msg-close {
      height: 10px;
      width: 10px;
      float: right;
      background-image: url(images/weixin_close.png);
      background-repeat: no-repeat;
      margin: 6px;
      display: inline-block;
      cursor: pointer;
    }

  .RsBody .msg-box .msg-content {
    height: 110px;
    width: 110px;
    margin-right: auto;
    margin-left: auto;
    margin-top: 12px;
  }

  .RsBody .msg-box .msg-footer {
    line-height: 24px;
    font-size: 10px;
    color: #5e5b5b;
    text-align: center;
  }

.RsBody .arrows {
  background-image: url(images/weibo_layer_bg.png);
  background-repeat: no-repeat;
  display: block;
  height: 12px;
  width: 16px;
  position: absolute;
  left: 130px;
  top: 170px;
  z-index: 2;
}

.left {
  text-align: left;
}

.center {
  text-align: center;
}

.right {
  text-align: right;
}

.float-right {
  float: right;
}

.red {
  color: red;
}

a.red {
  color: red;
}

.orange {
  color: orange;
}

.green {
  color: green;
}

.yellow {
  color: yellow;
}

.black {
  color: black;
}

.deepyellow {
  color: #f60;
}

.fontbold {
  font-weight: 700;
}

.greenbold {
  font-weight: bold;
  color: green;
}

.redbold {
  font-weight: bold;
  color: red;
}

.orangebold {
  font-weight: bold;
  color: orange;
}

.maxwrap {
  display: block;
  font-weight: lighter;
  list-style-type: none;
  overflow: hidden;
  padding-right: 5px;
  white-space: nowrap;
  width: 90%;
}

.box-shadow {
  -webkit-box-shadow: 0 5px 10px rgba(0,0,0,.2);
  -ms-box-shadow: 0 5px 10px rgba(0,0,0,.2);
  box-shadow: 0 5px 10px rgba(0,0,0,.2);
  margin: 0;
  outline: medium none;
  background: none repeat scroll 0 0 #fff;
  border: 1px solid #ddd;
}
/*公司管理*/
.msg-box-content * {
  box-sizing: content-box;
}

.msg-box-content .select-client {
  width: 370px
}

.msg-box-content .term-list-title {
  float: left;
  font-weight: bold;
  margin-right: 10px;
  padding-left: 15px;
  width: 68px;
  white-space: nowrap;
  overflow: hidden;
}

.msg-box-content .category-list {
  background: none repeat scroll 0 0 #fff;
  border: 1px solid #ccc;
  display: none;
  height: 220px;
  left: 0;
  overflow-y: scroll;
  position: absolute;
  top: 28px;
  width: 100%;
  z-index: 10;
  text-align: left
}

.msg-box-content .selector-list {
  text-align: left
}

.msg-box-content .clientName-list_a {
  left: 649px;
  position: absolute;
  top: 125px;
  width: 172px;
  z-index: 10;
  text-align: left
}
/*公司管理结束*/
/*弹出窗口开始*/
.Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-name {
  background: url("images/list-item-2.gif") repeat-x scroll 0 0 transparent;
  border: 1px solid #d0cecf;
  font-weight: bold;
  height: 31px;
  line-height: 31px;
  padding: 0 10px 0 20px;
  overflow: hidden;
}

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-name span {
    display: inline;
    float: left;
    overflow: hidden;
    text-align: left;
  }

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-name .item-1 {
    width: 78px;
  }

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-name .item-2 {
    width: 90px;
  }

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-name .item-3 {
    width: 120px;
  }

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-name .item-4 {
    width: 200px;
  }

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-name .item-5 {
    width: 110px;
  }

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-name .item-6 {
    width: 60px;
    text-align: center;
  }

.Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-data {
  color: #000;
  display: block;
  overflow: hidden;
}

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-data li {
    float: left;
    height: 29px;
    margin-top: 1px;
    padding: 0 10px 0 20px;
    background: none repeat scroll 0 0 #f8f8f8;
    overflow: hidden;
  }

    .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-data li.even {
      background: none repeat scroll 0 0 #edecec;
      float: left;
      height: 29px;
      margin-top: 1px;
      padding: 0 10px 0 20px;
    }

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-data span {
    display: inline;
    float: left;
    overflow: hidden;
    text-align: left;
    min-height: 29px;
  }

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-data .item-1 {
    width: 80px;
  }

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-data .item-2 {
    width: 90px;
  }

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-data .item-3 {
    width: 120px;
  }

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-data .item-4 {
    width: 200px;
  }

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-data .item-5 {
    width: 110px;
  }

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-data .item-6 {
    width: 60px;
    text-align: center;
  }

  .Rs_MsgBox_Layer_Wrap .msg-box-content .msg-composite-item-data li.hover {
    color: #000;
    background: url(images/saffh2.jpg) 0 -55px repeat-x !important;
  }

.msg-box-layer {
  position: absolute;
  left: -10000px;
  top: -10000px;
}
/*弹出窗口结束*/
/*弹出窗口TABLE*/
.tb-statistics {
  text-align: left;
  width: 100%;
  table-layout: fixed;
}

  .tb-statistics tbody tr td {
    overflow: hidden;
  }

    .tb-statistics tbody tr td b, .tb-statistics tbody tr td span.maxwrap {
      display: block;
      font-weight: lighter;
      list-style-type: none;
      overflow: hidden;
      padding-right: 5px;
      white-space: nowrap;
      width: 90%;
    }

  .tb-statistics .textindent1 {
    text-indent: 10px;
  }
  /*编号*/
  .tb-statistics .item-number {
    width: 80px;
  }
  /*操作用户*/
  .tb-statistics .item-user {
    width: 100px;
  }
  /*时间*/
  .tb-statistics .item-time {
    width: 110px;
  }
  /*查看详情*/
  .tb-statistics .item-look {
    width: 70px;
  }
  /*职位状态*/
  .tb-statistics .item-jobstate {
    width: 80px;
  }
  /*客户状态*/
  .tb-statistics .item-clientstate {
    width: 80px;
  }
  /*人选姓名*/
  .tb-statistics .item-peoplename {
    width: 80px;
  }
  /*推荐状态*/
  .tb-statistics .item-recommend {
    width: 80px;
  }
  /*到岗职位*/
  .tb-statistics .item-injob {
    width: 100px;
  }
  /*成功入职-到岗职位*/
  .tb-statistics .item-seccessjob {
    width: 130px;
  }
  /*人选职务*/
  .tb-statistics .item-peoplejob {
    width: 110px;
  }
  /*面试人选*/
  .tb-statistics .item-see {
    width: 80px;
  }
  /*操作类型*/
  .tb-statistics .item-operatetype {
    width: 80px;
  }
  /*操作类型*/
  .tb-statistics .item-resumestate {
    width: 80px;
  }
  /*职位名称*/
  .tb-statistics .item-jobname {
    width: 120px;
  }
  /*客户号码或者邮件*/
  .tb-statistics .item-clientnum {
    width: 120px;
  }
  /*数据列表*/
  .tb-statistics .statistics-item-data {
    background: none repeat scroll 0 0 #f8f8f8;
    height: 29px;
  }

    .tb-statistics .statistics-item-data.even {
      background: none repeat scroll 0 0 #edecec;
      height: 29px;
      text-align: left;
    }

    .tb-statistics .statistics-item-data.hover {
      color: #000;
      background: url(images/saffh2.jpg) 0 -55px repeat-x !important;
    }

  .tb-statistics .statistics-item-name {
    background: url("images/list-item-2.gif") repeat-x scroll 0 0 transparent;
    border: 1px solid #d0cecf;
    font-weight: bold;
    height: 31px;
    line-height: 31px;
    overflow: hidden;
    text-align: left;
  }

    .tb-statistics .statistics-item-name th {
      text-align: left;
      overflow: hidden;
      height: 29px;
      white-space: nowrap;
      text-overflow: clip;
      padding-right: 3px;
    }

    .tb-statistics .statistics-item-name .center {
      text-align: center;
    }

.unaccurate {
  color: #6A6A6A !important;
}
/*悬赏猎聘-推荐详情*/
.jv-yonghu-panel {
  line-height: 20px;
  margin: 0 0 5px;
}

  .jv-yonghu-panel .title {
    float: left;
    width: 70px;
    padding-left: 5px;
    font-weight: bold;
  }

  .jv-yonghu-panel .swap {
    display: none;
    float: right;
    margin: 3px 3px 0 3px;
    width: 16px;
    height: 16px;
    background-image: url(images/risfondIcon.gif);
    background-repeat: no-repeat;
    background-position: -64px -400px;
    cursor: pointer;
  }

    .jv-yonghu-panel .swap:hover {
      background-position: -64px -432px;
    }

    .jv-yonghu-panel .swap.selected {
      background-position: -64px -416px;
    }

      .jv-yonghu-panel .swap.selected:hover {
        background-position: -64px -416px;
      }

  .jv-yonghu-panel .jv-yonghu-list {
    float: left;
    overflow: hidden;
    width: 584px;
  }

    .jv-yonghu-panel .jv-yonghu-list li {
      float: left;
      overflow: hidden;
      height: 20px;
      line-height: 20px;
    }

      .jv-yonghu-panel .jv-yonghu-list li.title {
        width: 70px;
        padding-left: 5px;
        font-weight: bold;
      }

      .jv-yonghu-panel .jv-yonghu-list li.item { /*width: 50px;*/
        white-space: nowrap;
        margin: 0 5px 0 0;
      }

      .jv-yonghu-panel .jv-yonghu-list li.swap {
        float: left;
        margin: 3px 3px 0 3px;
        width: 16px;
        height: 16px;
        background-image: url(images/risfondIcon.gif);
        background-repeat: no-repeat;
        background-position: -64px -400px;
        cursor: pointer;
      }

        .jv-yonghu-panel .jv-yonghu-list li.swap:hover {
          background-position: -64px -432px;
        }

        .jv-yonghu-panel .jv-yonghu-list li.swap.selected {
          background-position: -64px -416px;
        }

          .jv-yonghu-panel .jv-yonghu-list li.swap.selected:hover {
            background-position: -64px -416px;
          }

.tb-statistics .statistics-item-name .item-jv-tjgw {
  width: 64px;
}

.tb-statistics .statistics-item-name .item-jv-gsgs {
  width: 60px;
}

.tb-statistics .statistics-item-name .item-jv-renxuan {
  width: 64px;
}

.tb-statistics .statistics-item-name .item-jv-suozaigongsi {
  width: 100px;
}

.tb-statistics .statistics-item-name .item-jv-zhiwei {
  width: 100px;
}

.tb-statistics .statistics-item-name .item-jv-tel {
  width: 80px;
}

.tb-statistics .statistics-item-name .item-jv-tuijianstatus {
  width: 60px;
}

.tb-statistics .statistics-item-name .item-jv-tuijiandate {
  width: 70px;
}

.tb-statistics .statistics-item-name .item-jv-caozuo {
  width: 40px;
}

.tb-statistics .statistics-item-name .item-jv-xiangqing {
  width: 40px;
}
/*弹出窗口TABLE结束*/
/*pager3用户分页控件*/
.Rs_MsgBox_Layer_Wrap .msg-box-content .paging {
  background: none repeat scroll 0 0 #fff;
  height: 40px;
  line-height: 40px;
  overflow: hidden;
  padding: 10px 0 10px 0;
}

.paging3 {
  padding: 12px 36px;
  width: 570px;
  display: block;
  overflow: hidden;
}

  .paging3 li.first.current,
  .paging3 li.last.current {
    border: 1px solid #c5c5c5;
    color: #c5c5c5;
    background: #fff;
  }

    .paging3 li.first.current a,
    .paging3 li.last.current a {
      color: #c5c5c5;
    }

  .paging3 li {
    display: inline;
    margin-left: 10px;
    padding: 5px 8px;
    border: 1px solid #026fb2;
    float: left;
    line-height: 13px;
    font-size: 12px;
    cursor: pointer;
    color: #026fb2;
  }

    .paging3 li:hover {
      background: #026fb2;
      color: #fff;
    }

      .paging3 li:hover a {
        color: #fff;
      }

  .paging3 a {
    display: block;
    text-decoration: none;
    font-size: 12px;
    color: #026fb2;
  }

  .paging3 .current {
    background: #026fb2;
    color: #fff;
  }

    .paging3 .current a {
      color: #fff;
    }

.job-pager3 {
  height: 50px;
  width: 100%;
  overflow: hidden;
}

  .job-pager3 .job-pager3-box {
    float: left;
    width: 70%;
    display: inline-block;
  }

    .job-pager3 .job-pager3-box .pager3 {
      margin: 0;
      padding: 12px 36px;
      display: inline-block;
    }

  .job-pager3 .job-pager3-box-dropdownlist {
    height: 50px;
    float: left;
    width: 30%;
    display: inline-block;
  }

    .job-pager3 .job-pager3-box-dropdownlist span {
      display: inline-block;
      margin-top: 10px;
      line-height: 20px;
    }

      .job-pager3 .job-pager3-box-dropdownlist span select {
        width: 50px;
      }

.classified-search .user-joindata {
  width: 540px;
}

.users-list .classified-search .user-joindata .term-list-con {
  width: 430px;
}

.classified-search .user-joinstate {
  width: 385px;
}

.users-list .classified-search .user-joinstate .term-list-con {
  width: 260px;
}

.tb-log {
  text-align: left;
  width: 100%;
  table-layout: fixed;
}

  .tb-log tbody tr th b {
    display: block;
    width: 100%;
  }

  .tb-log tbody tr td b {
    white-space: nowrap;
    overflow: hidden;
    font-weight: lighter;
  }

  .tb-log .log-item-name {
    background: url("images/list-item-2.gif") repeat-x scroll 0 0 transparent;
    border: 1px solid #d0cecf;
    font-weight: bold;
    height: 31px;
    line-height: 31px;
    overflow: hidden;
    text-align: left;
  }

    .tb-log .log-item-name th {
      overflow: hidden;
      height: 29px;
      text-align: left;
      white-space: nowrap;
      text-overflow: clip;
    }

      .tb-log .log-item-name th span {
        display: block;
        margin-right: 3px;
        overflow: hidden;
      }

  .tb-log .log-item-data {
    background: none repeat scroll 0 0 #f8f8f8;
    height: 29px;
  }

    .tb-log .log-item-data.mylog {
      background-color: #fef8d5;
    }

    .tb-log .log-item-data.even {
      background: none repeat scroll 0 0 #edecec;
      height: 29px;
    }

    .tb-log .log-item-data.hover {
      color: #000;
      background: url(images/saffh2.jpg) 0 -55px repeat-x !important;
    }

    .tb-log .log-item-data td {
      border-bottom: solid 1px #fff;
      overflow: hidden;
    }

  .tb-log .log-item-1 {
    width: 9%;
    text-indent: 2em;
  }

  .tb-log .log-item-data .log-item-1 {
    color: #f60;
    font-weight: bold;
  }

  .tb-log .log-item-2 {
    width: 8%;
  }

  .tb-log .log-item-3 {
    width: 10%;
  }

  .tb-log .log-item-4 {
    width: 12%;
  }

  .tb-log .log-item-5 {
    width: 9%;
  }

  .tb-log .log-item-6 {
    width: 12%;
  }

  .tb-log .log-item-7 {
    width: 9%;
  }

  .tb-log .log-item-8 {
    width: 11%;
  }

  .tb-log .log-item-9 {
    width: 20%;
  }

  .tb-log .hits-icon {
    width: 20px;
    display: block;
    height: 29px;
    margin-top: 4px;
    position: absolute;
    left: 0;
  }
/*简历管理*/
.tb-resume {
  text-align: left;
  width: 100%;
  table-layout: fixed;
}

  .tb-resume tbody tr td span.maxwrap {
    display: block;
    font-weight: lighter;
    list-style-type: none;
    overflow: hidden;
    padding-right: 5px;
    white-space: nowrap;
    width: 90%;
  }

  .tb-resume .resume-item-name {
    background: url("images/list-item-2.gif") repeat-x scroll 0 0 transparent;
    border: 1px solid #d0cecf;
    font-weight: bold;
    height: 31px;
    line-height: 31px;
    overflow: hidden;
    text-align: left;
  }

    .tb-resume .resume-item-name th {
      overflow: hidden;
      height: 29px;
      text-align: left;
      white-space: nowrap;
    }

  .tb-resume .resume-item-data {
    background: none repeat scroll 0 0 #f8f8f8;
    height: 29px;
  }

    .tb-resume .resume-item-data.even {
      background: none repeat scroll 0 0 #edecec;
      height: 29px;
    }

    .tb-resume .resume-item-data.hover {
      color: #000;
      background: url(images/saffh2.jpg) 0 -55px repeat-x !important;
    }

    .tb-resume .resume-item-data td {
      border-bottom: solid 1px #fff;
      overflow: hidden;
    }

    .tb-resume .resume-item-data .resume-item-1 {
      color: #016eb1;
      font-weight: bold;
    }

  .tb-resume .resume-item-0 {
    width: 50px;
    text-align: center !important;
  }

  .tb-resume .resume-item-1 {
    width: 75px;
  }

  .tb-resume .resume-item-2 {
    width: 75px;
  }

  .tb-resume .resume-item-3 {
    width: 75px;
  }

  .tb-resume .resume-item-4 {
    width: 55px;
  }

  .tb-resume .resume-item-5 {
    width: 55px;
  }

  .tb-resume .resume-item-6 {
    width: 110px;
  }

  .tb-resume .resume-item-7 {
    width: 140px;
  }

  .tb-resume .resume-item-8 {
    width: 100px;
  }

  .tb-resume .resume-item-9 {
    width: 80px;
  }

  .tb-resume .resume-item-10 {
    width: 40px;
  }

  .tb-resume .resume-item-11 {
    width: 60px;
  }

  .tb-resume .resume-item-12 {
    width: 40px;
  }

  .tb-resume .take-notes {
    background: none repeat scroll 0 0 #fff;
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
    border-top: 1px solid #bfbfbf;
    cursor: pointer;
    height: 18px;
    line-height: 18px;
    text-align: center;
    width: 30px;
    text-indent: 0;
    display: block;
  }

    .tb-resume .take-notes b {
      color: red;
      font-weight: bold;
    }

  .tb-resume .lookresume {
    float: left;
    margin-right: 5px;
    margin-top: 5px;
    margin-left: 10px;
  }

  .tb-resume .lookdetail {
    float: right;
    margin-right: 20px;
  }

  .tb-resume .update {
    margin-top: 5px;
  }

  .tb-resume .othertime {
    float: left;
    margin-right: 5px;
    margin-top: 5px;
  }

.popUpBoxWrpe.login-noticetopwrap {
  padding: 5px 10px 5px 10px;
}

.popUpBoxJianTou.login-noticetopicon {
  left: 85%;
}

.login-noticetopwrap .close {
  cursor: pointer;
  position: absolute;
  right: 10px;
  top: 2px;
  z-index: 100;
}
/*财务模块*/
.incomepay .border-middle {
  width: 956px;
}

.incomepay-record {
  background-color: #eee;
  width: 100%;
}

.incomepay-record-box {
  width: 760px;
  margin-right: auto;
  margin-left: auto;
  padding-top: 10px;
  padding-bottom: 10px;
}

  .incomepay-record-box .disabled {
    background: #d0cecf;
    cursor: no-drop;
  }

.incomepay-record li {
  height: 40px;
  width: 100%;
  list-style-image: none;
  list-style-type: none;
}

  .incomepay-record li.category-item {
    height: auto;
  }

.incomepay-text {
  float: left;
  line-height: 30px;
  width: 75px;
  white-space: nowrap;
  overflow: hidden;
  margin-right: 3px;
}

.incomepay-select-staff .outbox {
  width: 270px;
}

.incomepay-select-staff .icon-135 {
  left: 240px !important;
  top: 8px !important;
}

.incomepay-select-staff .text-5 {
  height: 28px !important;
}

  .incomepay-select-staff .text-5 .outbox-tip {
    line-height: 28px !important;
  }

.incomepay-select-staff .outbox-item-list li {
  height: 21px;
  width: auto;
  margin-top: 3px;
}

.incomepay-select-staff #selector_input_0 {
  height: 24px;
}

.sourcetype {
  padding: 0 15px 0 15px;
  float: left;
}

.incomepay-source-input-company, .incomepay-source-input, .incomepay-money-input {
  background: none repeat scroll 0 0 transparent;
  height: 28px;
  line-height: 28px;
  overflow: hidden;
  border: medium none;
  float: left;
}

.incomepay-money-input {
  ime-mode: disabled;
}

.incomepay-left-box {
  float: left;
  width: 50%;
}

.incomepay-right-type {
  float: left;
  width: 50%;
}

.incomepay-remark-box {
  background-color: #eee;
  width: 100%;
  height: 130px;
}

.incomepay-remark-content {
  width: 760px;
  overflow: hidden;
  padding: 10px 0 10px 0;
  margin: 0 auto 0 auto;
  height: 100px;
}

.label-remark-text {
  display: inline;
  float: left;
  font-weight: bold;
  margin-right: 15px;
  text-align: right;
  width: 60px !important;
}

.incomepay-submit-box {
  background-color: #eee;
  height: 50px;
  text-align: center;
}

.financial-table {
  font-size: 12px;
  width: 100%;
  border-top-width: 1px;
  border-right-width: 1px;
  border-left-width: 1px;
  border-top-style: solid;
  border-right-style: solid;
  border-left-style: solid;
  border-top-color: #d0cecf;
  border-right-color: #d0cecf;
  border-left-color: #d0cecf;
  table-layout: fixed;
}

.financial-item-name {
  background: url("images/list-item-2.jpg") repeat-x scroll 0 0 transparent;
  border: 1px solid #d0cecf;
  font-weight: bold;
  height: 31px;
  line-height: 31px;
  padding: 0 !important;
  position: relative;
}

  .financial-item-name th {
    white-space: nowrap;
    overflow: hidden;
    text-align: center;
  }

.financial-item-data {
  text-align: center;
  height: 29px;
  line-height: 29px;
  padding: 0 !important;
  background: none repeat scroll 0 0 #f8f8f8;
}

  .financial-item-data td {
    border-bottom: 1px solid #fff;
    text-align: center;
  }

  .financial-item-data .moneytext {
    color: #f60;
    font-weight: bold;
    text-align: right;
    padding-right: 15px;
  }

  .financial-item-data.even {
    background: none repeat scroll 0 0 #edecec;
  }

  .financial-item-data.hover {
    color: #000;
    background: url(images/saffh2.jpg) 0 -55px repeat-x !important;
  }

  .financial-item-data .input-radio {
    margin: 0;
  }

.finance-box-search .select-data {
  width: 500px;
}

.name-line {
  background-image: url(images/list-item-name-line.jpg);
  background-repeat: no-repeat;
  background-position: 100% 50%;
  background-color: transparent;
}

.financial-table .leftindent {
  text-align: left;
  text-indent: 10px;
}

.financial-job .widget {
  margin-top: 0;
}

.financial-income-list .financial-item-name .itemname-1 {
  width: 50px;
}

.financial-income-list .financial-item-name .itemname-2 {
  width: 65px;
  padding: 0 !important;
}

.financial-income-list .financial-item-name .itemname-3 {
  width: 100px;
}

.financial-income-list .financial-item-name .itemname-4 {
  width: 80px;
}

.financial-income-list .financial-item-name .itemname-13 {
  width: 34px;
  overflow: initial;
}

.financial-income-list .financial-item-data .itemname-13 {
  padding: 0 0 0 10px;
}

.financial-income-list .financial-item-name .selectfilter-box .selectfilter-tip {
  padding: 0 11px 0 5px;
  border: 1px solid transparent;
}

.financial-income-list .financial-item-name .selectfilter-box.hover .selectfilter-tip {
  border: 1px solid #b7b7b7;
}

.financial-income-list .financial-item-name .selectfilter-box .icon {
  float: left;
  margin: 3px 0 0;
}

.financial-income-list .financial-item-name .itemname-5 {
  width: 200px;
  text-align: left;
}

.financial-income-list .financial-item-data .itemname-5 {
  text-align: left;
}

.financial-income-list .financial-item-name .itemname-6 {
  width: 90px;
}

.financial-income-list .financial-item-name .itemname-7 {
  width: 70px;
}

.financial-income-list .financial-item-name .itemname-8 {
  width: 60px;
}

.financial-income-list .financial-item-name .itemname-9 {
  width: 60px;
}

.financial-income-list .financial-item-name .itemname-10 {
  width: 40px;
}

.financial-income-list .financial-item-name .financial-item-name .itemname-12 {
  width: 70px;
}

.financial-pay-list .financial-item-name .itemname-1 {
  width: 50px;
}

.financial-pay-list .financial-item-name .itemname-2 {
  width: 65px;
  padding: 0 !important;
}

.financial-pay-list .financial-item-name .itemname-3 {
  width: 100px;
}

.financial-pay-list .financial-item-name .itemname-4 {
  width: 80px;
}

.financial-pay-list .financial-item-name .itemname-5 {
  width: 180px;
}

.financial-pay-list .financial-item-name .itemname-6 {
  width: 90px;
}

.financial-pay-list .financial-item-name .itemname-7 {
  width: 70px;
}

.financial-pay-list .financial-item-name .itemname-8 {
  width: 60px;
}

.financial-pay-list .financial-item-name .itemname-9 {
  width: 60px;
}

.financial-pay-list .financial-item-name .itemname-10 {
  width: 70px;
}

.financial-pay-list .financial-item-name .itemname-12 {
  width: 70px;
}

.financial-pay-list .approval-status-box {
  position: relative;
  text-align: left;
}

  .financial-pay-list .approval-status-box.show {
    border: solid 1px #DA6B00;
    background-color: #FFF6E4;
    width: 68px;
    height: 25px;
    margin: 1px 0;
  }

.financial-pay-list .approval-status-dialog {
  display: none;
}

.financial-pay-list .approval-status-box.show .approval-status-dialog {
  display: block;
  position: absolute;
  top: 24px;
  left: -1px;
  line-height: 25px;
  background-color: #FFF6E4;
  width: 90px;
  border: solid 1px #DA6B00;
  z-index: 3;
}

.financial-pay-list .approval-status-box .approval-status-text {
  padding-left: 10px;
  display: block;
  margin: 1px 0 0 1px;
  color: #DF730A;
  font-weight: 700;
  position: relative;
  cursor: pointer;
}

.financial-pay-list .approval-status-box.show .approval-status-text {
  display: block;
  margin: 0;
  height: 25px;
}

.financial-pay-list .approval-status-box .approval-status-text .text {
  display: block;
  margin: 0;
  height: 27px;
  line-height: 27px;
  width: 59px;
}

.financial-pay-list .approval-status-box.show .approval-status-text .text {
  display: block;
  margin: 0;
  height: 26px;
  line-height: 26px;
  width: 59px;
}

.financial-pay-list .approval-status-box .approval-status-icon i {
  background: url("/static/images/financial-distribution-status-btn-icon.gif") no-repeat 0 0;
  height: 9px;
  width: 9px;
  display: block;
  position: absolute;
  top: 10px;
  right: 7px;
  -webkit-transition: transform 0.2s ease-in 0s;
  -moz-transition: transform 0.2s ease-in 0s;
  -ms-transition: transform 0.2s ease-in 0s;
  -o-transition: transform 0.2s ease-in 0s;
  transition: transform 0.2s ease-in 0s;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}

.financial-pay-list .financial-table .take-notes {
  margin: 2px 15px 0 15px;
}

.financial-pay-list .approval-status-box.show .approval-status-icon i {
  background: url("/static/images/financial-distribution-status-btn-icon.gif") no-repeat 0 0;
  height: 9px;
  width: 9px;
  display: block;
  position: absolute;
  top: 8px;
  right: 7px;
  -webkit-transition: transform 0.2s ease-in 0;
  -moz-transition: transform 0.2s ease-in 0;
  -ms-transition: transform 0.2s ease-in 0;
  -o-transition: transform 0.2s ease-in 0;
  transition: transform 0.2s ease-in 0;
  -webkit-transform: rotate(0);
  -moz-transform: rotate(0);
  -ms-transform: rotate(0);
  -o-transform: rotate(0);
  transform: rotate(0);
}

.financial-pay-list .approval-status-box.show .approval-status-item {
  height: 25px;
  padding-left: 10px;
  border-bottom: solid 1px #d0cecf;
  cursor: pointer;
}

  .financial-pay-list .approval-status-box.show .approval-status-item.itemhover {
    background-color: #FFF1CC;
  }

  .financial-pay-list .approval-status-box.show .approval-status-item.current {
    background-color: #FCE9C4;
  }

.financial-pay-list .approval-status-box .approval-btn-list li {
  width: 50%;
  float: left;
  text-align: center;
}

.financial-pay-list .approval-status-box .approval-btn-list {
  margin: 5px 0;
}

  .financial-pay-list .approval-status-box .approval-btn-list li .btn-approval-confim {
    width: 38px;
    cursor: pointer;
  }

  .financial-pay-list .approval-status-box .approval-btn-list li .btn-approval-cancel {
    width: 38px;
    cursor: pointer;
  }

.financial-table .take-notes {
  background: none repeat scroll 0 0 #fff;
  border-left: 1px solid #eee;
  border-right: 1px solid #eee;
  border-top: 1px solid #bfbfbf;
  cursor: pointer;
  height: 18px;
  line-height: 18px;
  margin: 2px 0 0 5px;
  text-align: center;
  width: 30px;
}

  .financial-table .take-notes strong {
    color: red;
  }

.popupTitle-box {
  width: 400px;
  margin-right: 30px;
}

  .popupTitle-box.popup-showtitle {
    width: auto !important;
    margin-right: 40px;
    text-align: left !important;
  }

.incomepay-table-page {
  border-right-width: 1px;
  border-bottom-width: 1px;
  border-left-width: 1px;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-right-color: #d0cecf;
  border-bottom-color: #d0cecf;
  border-left-color: #d0cecf;
}

.incomepay-table-manipalation {
  border-right-width: 1px;
  border-left-width: 1px;
  border-right-style: solid;
  border-left-style: solid;
  border-right-color: #d0cecf;
  border-left-color: #d0cecf;
}

.incomepay-result, .totalTip {
  float: left
}

  .incomepay-result strong, .totalTip strong {
    color: #f8dd68;
  }
/*仿百度关键词模糊搜索*/
.keytb {
  background: none repeat scroll 0 0 #fff;
  cursor: default;
  width: 100%;
  table-layout: fixed;
}

  .keytb .ml {
    background: none repeat scroll 0 0 #fff;
  }

    .keytb .ml td {
      color: #000;
      margin: 0;
    }

  .keytb .mo {
    background: none repeat scroll 0 0 #ebebeb;
  }

  .keytb .cur {
    background: none repeat scroll 0 0 #ebebeb;
  }

  .keytb .job {
    width: 50%;
    text-indent: 10px;
  }

  .keytb .source {
    width: 15%;
  }

  .keytb .name {
    width: 10%;
  }

  .keytb .phone {
    width: 15%;
  }

  .keytb .state {
    width: 10%;
  }

  .keytb .phone-name {
    width: 60%;
    text-indent: 5px;
    text-align: left;
  }

  .keytb .phone-number {
    width: 40%;
    text-align: left;
  }

  .keytb .keyword {
    font-weight: bold;
    float: none;
    height: auto;
    margin-right: 0;
  }

span.maxtext {
  font-weight: lighter;
  overflow: hidden;
  display: block;
  white-space: nowrap;
  width: 95%;
  list-style-type: none;
}
/*日程管理*/
.schedule-box .schedule-data-title {
  background: url("images/list-item-2.gif") repeat-x scroll 0 0 transparent;
  border: 1px solid #d0cecf;
  font-weight: bold;
  height: 31px;
  line-height: 31px;
}

.listdata-name {
  width: 958px;
  overflow: hidden;
}

.listdata-text .read .icon-read {
  background-position: -100px -416px;
  position: relative;
  left: 5px;
  top: -18px;
  width: 7px !important;
  height: 7px !important;
}

.listdata-text .unread .icon-read {
  background-position: -100px -400px;
  position: relative;
  left: 5px;
  top: -18px;
  width: 7px !important;
  height: 7px !important;
}

.listdata-content span {
  display: inline;
  float: left;
  overflow: hidden;
  text-align: center;
}

.schedule-box .item-1 {
  width: 60px;
}

.schedule-box .item-2 {
  width: 70px;
  overflow: visible;
}

.schedule-box .item-3 {
  width: 130px;
  overflow: visible;
}

.schedule-box .item-4 {
  width: 350px;
}

.schedule-box .item-5 {
  width: 70px;
}

.schedule-box .item-6 {
  width: 70px;
}

.schedule-box .item-7 {
  width: 65px;
}

.schedule-box .item-8 {
  width: 45px;
}

.schedule-box .item-9 {
  width: 65px;
}

.schedule-box .item-6-textleft {
  text-align: left;
  text-indent: 15px;
}

.schedule-box .item-4-max {
  width: 330px;
  text-align: left !important;
  height: auto;
  min-height: 29px;
  -moz-min-width: 330px;
  -ms-min-width: 330px;
  -o-min-width: 330px;
  -webkit-min-width: 330px;
  min-width: 330px;
  overflow: visible;
}

.schedule-box .cmmunctn-more-icon {
  float: left;
  margin-bottom: 6px;
  margin-top: 6px;
}

.schedule-box .cmmunctn-more-box {
  width: 20px;
}

.schedule-box .unread, .schedule-box .unread a {
  color: #000;
}

.schedule-box .read, .schedule-box .read a {
  color: #7C7B7B !important;
}

.schedule-box .schedule-data-content {
  overflow: hidden;
  font-size: 12px;
  line-height: 29px;
  margin: 0;
  padding: 0 !important;
  margin-bottom: 1px;
  border-color: transparent transparent -moz-use-text-color;
  -webkit-border-image: none;
  -moz-border-image: none;
  -ms-border-image: none;
  -o-border-image: none;
  border-image: none;
  border-style: none solid;
  border-width: 1px 0 0 1px;
  border-color: transparent transparent;
  -moz-border-bottom-colors: none;
  -moz-border-left-colors: none;
  -moz-border-right-colors: none;
  -moz-border-top-colors: none;
  _border-color: tomato;
  -webkit-filter: chroma(color=tomato);
  -moz-filter: chroma(color=tomato);
  -o-filter: chroma(color=tomato);
  filter: chroma(color=tomato);
}

  .schedule-box .schedule-data-content.odd {
    background: none repeat scroll 0 0 #f8f8f8;
  }

.schedule-box .schedule-open-box {
  background-color: #eef7fd !important;
  border: 1px solid #a3cce7 !important;
  color: #000;
  line-height: 27px;
  width: 958px !important;
  overflow: visible !important;
}

  .schedule-box .schedule-open-box .schedule-data-box {
    height: auto;
    margin: 0;
    padding: 0;
    width: 958px;
  }

  .schedule-box .schedule-open-box.hover {
    background: none repeat scroll 0 0 #eef7fd !important;
  }

.schedule-box .schedule-data-box {
  height: 29px;
  margin: 0;
  overflow: hidden;
  padding: 0;
  width: 960px;
}

  .schedule-box .schedule-data-box span {
    text-align: center;
  }

  .schedule-box .schedule-data-box .item, .schedule-box .schedule-data-title span {
    white-space: nowrap;
    overflow: hidden;
    margin-right: 3px;
  }

    .schedule-box .schedule-data-box .item.item-4 {
      margin-right: 0;
    }

.schedule-box .schedule-content-label {
  white-space: initial;
  overflow: initial;
}

.schedule-box .schedule-data-box .schedule-data-box {
  border-width: 1px 1px 0;
  border-style: solid solid none;
  border-color: transparent transparent -moz-use-text-color;
  -moz-border-bottom-colors: none;
  -moz-border-left-colors: none;
  -moz-border-right-colors: none;
  -moz-border-top-colors: none;
}

.schedule-box .schedule-title {
  max-height: 29px;
  height: 29px;
  overflow: hidden;
}

.schedule-box .schedule-content strong {
  color: #FF6600;
}

.schedule-data-content .icon-show {
  float: left;
  margin-top: 5px;
  margin-bottom: 6px;
  background-position: -64px -656px;
  cursor: pointer;
  overflow: visible;
}

.schedule-open-box .icon-show {
  float: left;
  margin-top: 5px;
  margin-bottom: 6px;
  background-position: -64px -672px;
  cursor: pointer;
}

.schedule-data-content .schedule-content-label {
  float: none;
}
/*搜索框*/
.join-title-box {
  width: 350px;
  margin-top: 5px;
  margin-right: 8px;
}

  .join-title-box .type-input {
    float: left;
    width: 80px !important;
  }

  .join-title-box .title-line {
    border-right: 1px solid #c2c2c3;
    float: left;
    height: 28px;
    width: 10px;
  }

  .join-title-box .my-calendar-title {
    width: 240px !important;
  }
/*首页日程*/
.schedule-task-box li {
  border-bottom: 1px dashed #D2D2D2;
}

.schedule-task-box .schedule-task-time {
  width: 40px;
}

.schedule-task-box .schedule-task-text {
  width: 125px;
}

.schedule-task-box {
  color: #000;
}

  .schedule-task-box a:hover {
    color: #000 !important;
  }

.schedule-task-dialog {
  width: 350px;
  position: absolute;
  z-index: 200;
  background-color: #eef7fd !important;
  border: 1px solid #7FA0CD !important;
  display: none;
}

  .schedule-task-dialog .schedule-task-content-box {
    width: 320px;
    margin: 10px auto;
  }

  .schedule-task-dialog .schedule-title {
    white-space: pre;
  }

  .schedule-task-dialog .schedule-task-dialog-bj {
    background-image: url(images/schedule-task-dialog-bj.png);
    background-position: right top;
    display: block;
    position: absolute;
    width: 8px;
    background-repeat: no-repeat;
    background-attachment: scroll;
    left: 350px;
    top: 0;
    display: inline;
    height: 74px;
    z-index: 202;
  }

.schedule-cancel-link {
  text-align: right;
}

  .schedule-cancel-link a, .schedule-cancel-link a:hover {
    color: #F60 !important;
    width: 60px;
    float: right;
  }

.schedule-task-box .schedule-task-hover {
  -moz-border-bottom-colors: none;
  -moz-border-left-colors: none;
  -moz-border-right-colors: none;
  -moz-border-top-colors: none;
  border-color: transparent;
  -webkit-border-image: none;
  -moz-border-image: none;
  -ms-border-image: none;
  -o-border-image: none;
  border-image: none;
  border-style: none solid;
  border-width: 1px 0 0 1px;
  font-size: 12px;
  line-height: 25px !important;
  margin: 0 0 1px;
  overflow: hidden;
  padding: 0 !important;
  border-bottom: 1px dashed #d2d2d2;
  height: 25px !important;
  _overflow: hidden;
  _position: fixed;
  width: 170px;
}

.schedule-task-box .schedule-open-box {
  background-color: #eef7fd !important;
  border-top: 1px solid #7fa0cd !important;
  border-right: 1px solid #7fa0cd !important;
  border-bottom: 1px solid #7fa0cd !important;
  color: #000;
  line-height: 24px !important;
  height: 24px !important;
  overflow: visible !important;
}

  .schedule-task-box .schedule-open-box .schedule-task-dialog {
    display: block;
  }

.datepickercalendarbytdhover-dialog-box {
  position: absolute;
  width: 22px;
  height: 19px;
  z-index: 99;
  text-align: center;
  overflow: hidden;
  display: none;
}

.datepickercalendarbytdhover-dialog-text {
  color: #830000;
  line-height: 16px;
}

.schedule-task-box .addotherschedule, .schedule-task-box .addotherschedule:hover, .schedule-task-box .addotherschedule:link {
  color: #908255;
  text-decoration: underline;
  text-align: center;
}
/*通用模板样式-查看页*/
.template-background-white {
  background-color: white;
  border: solid 1px #B7B7B7;
  width: 888px;
}

.template-operate .border-middle {
  width: 956px;
}

.template-operate .search-other-function {
  padding-right: 32px;
}

.template-operate .line {
  width: 890px;
}

.template-operate-box .clientname, .template-operate-box .jobname {
  width: 630px;
}

.template-operate .line .row {
  width: 435px;
}

.template-operate .line .r-row {
  width: 435px;
}

.template-edit .line .row {
  width: 405px;
  margin: 0;
}

.template-operate .dateymd-box, .template-operate .datehsm-box {
  float: left;
}

.template-operate .calendar-date {
  margin-right: 10px;
  width: 96px;
  cursor: pointer;
}

.template-operate .dateymd {
  left: 96px;
  position: relative;
  top: 7px;
}

.template-operate .dateymd2 {
  left: 225px;
  position: relative;
  top: 7px;
}

.template-operate .button-box-4 {
  background: none repeat scroll 0 0 #EEEEEE;
  text-align: center;
}

  .template-operate .button-box-4 .button {
    margin: 0 auto;
  }

.template-operate .communication {
  width: 630px;
}

.template-operate .informationsource {
  margin-left: 10px;
  width: 123px;
  display: none;
  float: left;
}

.template-operate .informationother .informationbox {
  width: 90px;
}

.template-operate .informationother .informationselect {
  width: 80px !important;
  float: left;
}

.template-operate .informationother .informationsource {
  display: block;
}

.template-operate .phonearea-select {
  float: left;
  margin: 3px 7px 0 0;
}
/*通用模板样式-编辑页*/
.template-edition .edition-box {
  background-color: #eee;
  width: 100%;
}

.template-edition .edition-content-box {
  margin: 0 auto;
  overflow: hidden;
  padding-top: 10px;
  width: 760px;
}

.template-edition .e-tr {
  height: 30px;
  margin: 5px 0;
  width: 100%;
  float: left;
}

.template-edition .e-td {
  height: 30px;
  float: left;
  width: 50%;
}

.template-edition .e-key {
  float: left;
  line-height: 30px;
  width: 20%;
}

.template-edition .e-value {
  float: left;
  line-height: 30px;
  width: 80%;
}

.template-edition .inputsize-staff-v1 .outbox {
  width: 270px;
}

.template-edition .inputsize-staff-v1 .icon-135 {
  left: 240px !important;
  top: 9px !important;
}

.template-edition .inputsize-staff-v1 .text-5 {
  height: 28px !important;
}

  .template-edition .inputsize-staff-v1 .text-5 .outbox-tip {
    line-height: 28px !important;
  }

.template-edition .inputsize-staff-v1 .outbox-item-list li {
  height: 21px;
  width: auto;
  margin-top: 3px;
}

.template-edition .inputsize-staff-v1 .selector_outerbox {
  height: 24px;
}

.template-edition .inputsize-v1 {
  width: 250px;
}

.template-edition .amount-v1 {
  background: url("/static/images/distribute-money-unit-icon.jpg") no-repeat scroll 95% 50% #fcfcfc;
  ime-mode: disabled;
  overflow: hidden;
}

.template-edition .label-remark-text {
  display: inline;
  float: left;
  font-weight: bold;
  margin-right: 15px;
  text-align: right;
  width: 60px !important;
}

.template-edition .form-operate-box {
  text-align: center;
  padding: 20px 0;
}

.template-edition .outbox input {
  margin-top: 4px;
}

.date-v1 {
  background: url("/static/images/date-icon.gif") no-repeat scroll 95% 50% #fcfcfc;
  ime-mode: disabled;
  overflow: hidden;
}

.salary-box .unit-box {
  margin: 0 4px;
}

.salary-box .qingjiaismonth-box {
  float: left;
  width: 20%;
}

.salary-box .qingjiaqita-box {
  float: left;
  width: 70%;
  margin-left: 10px;
}

  .salary-box .qingjiaqita-box .text-2 {
    width: 55px;
  }

.salary-box .edition-content-box {
  margin-left: 70px;
}

.canditdaterecommend .line .row label {
  width: 70px;
}

.canditdaterecommend {
  background-color: #fff;
  border: solid 1px #B7B7B7;
  width: 888px;
  margin-bottom: 1px;
}

  .canditdaterecommend .phone-number {
    float: left;
  }

  .canditdaterecommend .telphone-box {
    float: left;
    margin: 3px 0 0 5px;
  }

  .canditdaterecommend .telphone-succ {
    background-position: -64px -160px;
    display: block;
  }

  .canditdaterecommend .telphone-hits {
    background-position: -64px -288px;
    display: block;
    cursor: pointer;
  }

  .canditdaterecommend .telphone-none {
    display: none !important;
  }

  .canditdaterecommend .rnss-callphone {
    float: left;
    margin: 3px 0 0 5px;
  }
/*寻访管理*/
.searchrecord-box .searchrecord-data-title {
  background: url("images/list-item-2.gif") repeat-x scroll 0 0 transparent;
  border: 1px solid #d0cecf;
  font-weight: bold;
  height: 31px;
  line-height: 31px;
}

.searchrecord-data-title span, .listdata-text span {
  display: inline;
  overflow: hidden;
  text-align: left;
}

.searchrecord-box .item-1 {
  width: 35px;
  text-align: center;
}

  .searchrecord-box .item-1 .input-radio {
    margin-top: 0;
  }

.searchrecord-box .item-2 {
  width: 185px;
}

.searchrecord-box .item-3 {
  width: 90px;
}

.searchrecord-box .item-4 {
  width: 70px;
}

.searchrecord-box .item-5 {
  width: 40px;
}

.searchrecord-box .item-6 {
  width: 135px;
}

.searchrecord-box .item-7 {
  width: 60px;
}

.searchrecord-box .item-8 {
  width: 65px;
}

.searchrecord-box .item-9 {
  width: 35px;
}

.searchrecord-box .item-10 {
  width: 75px;
}

.searchrecord-box .item-11 {
  width: 92px;
  text-indent: 3px;
}

.searchrecord-box .searchrecord-data-box .item-4 {
  color: #f60;
  font-weight: bold;
}

.searchrecord-box .searchrecord-data-content {
  border-bottom: 1px solid #fff;
  -moz-border-bottom-colors: none;
  -moz-border-left-colors: none;
  -moz-border-right-colors: none;
  -moz-border-top-colors: none;
  border-color: transparent;
  -webkit-border-image: none;
  -moz-border-image: none;
  -ms-border-image: none;
  -o-border-image: none;
  border-image: none;
  border-style: none solid;
  border-width: 1px 0 0 1px;
  font-size: 12px;
  margin: 0 0 1px;
  overflow: hidden;
  padding: 0 !important;
  width: 960px;
  line-height: 31px;
}

.searchrecord-box .searchrecord-data-box .operate {
  float: left;
  margin: 5px 3px 0 3px;
}

.searchrecord-box .searchrecord-data-box {
  height: 29px;
  margin: 0;
  overflow: hidden;
  padding: 0;
  width: 960px;
  cursor: pointer;
}

  .searchrecord-box .searchrecord-data-box.hover {
    color: #000;
    background: url(images/saffh2.jpg) 0 -55px repeat-x !important;
  }

.searchrecord-box .radio-width {
  width: 34px;
}

.searchrecord-box .searchrecord-data-detail {
  background-color: #EEF7FD;
  width: 960px;
  display: none;
}

  .searchrecord-box .searchrecord-data-detail .detail-head {
    height: 50px;
  }

    .searchrecord-box .searchrecord-data-detail .detail-head ul {
      width: 890px;
      margin: 0 0 0 60px;
      height: 50px;
    }

      .searchrecord-box .searchrecord-data-detail .detail-head ul li {
        float: left;
        color: #21639C;
        line-height: 25px;
        height: 25px;
        overflow: hidden;
      }

        .searchrecord-box .searchrecord-data-detail .detail-head ul li a {
          color: #21639C;
        }

        .searchrecord-box .searchrecord-data-detail .detail-head ul li.explain1 {
          width: 35%;
        }

        .searchrecord-box .searchrecord-data-detail .detail-head ul li.explain2 {
          width: 35%;
        }

        .searchrecord-box .searchrecord-data-detail .detail-head ul li.explain3 {
          width: 30%;
        }

        .searchrecord-box .searchrecord-data-detail .detail-head ul li.explain4 {
          width: 35%;
        }

        .searchrecord-box .searchrecord-data-detail .detail-head ul li.explain5 {
          width: 35%;
        }

        .searchrecord-box .searchrecord-data-detail .detail-head ul li.explain6 {
          width: 30%;
        }

  .searchrecord-box .searchrecord-data-detail .communication-data {
    width: 960px;
    line-height: 31px;
    padding-bottom: 15px;
  }

    .searchrecord-box .searchrecord-data-detail .communication-data li {
      width: 960px;
      border-top: dotted 1px #999;
    }

  .searchrecord-box .searchrecord-data-detail .communication-list-box {
    width: 860px;
    margin: 0 0 0 60px;
  }

    .searchrecord-box .searchrecord-data-detail .communication-list-box span {
      color: #D95913;
      width: 115px;
    }

    .searchrecord-box .searchrecord-data-detail .communication-list-box p {
      line-height: 31px;
    }

    .searchrecord-box .searchrecord-data-detail .communication-list-box .staffusername {
      width: 60px;
      overflow: hidden;
      height: 31px;
    }

.searchrecord-box .searchrecord-open-box .searchrecord-data-detail {
  display: block;
}

.searchrecord-box .searchrecord-open-box {
  color: #000;
  background: url(images/saffh2.jpg) 0 -55px repeat-x !important;
  background-color: #eef7fd !important;
  border: 1px solid #a3cce7 !important;
  line-height: 29px;
  width: 958px;
}

.my-searchrecord {
  width: 470px;
  margin: 0 auto;
}

  .my-searchrecord .line {
    width: 100%;
    margin: 10px 0;
  }

    .my-searchrecord .line label {
      float: left;
      margin-right: 0;
      font-weight: bold;
      width: 70px;
      text-align: right;
      padding-right: 10px;
    }

  .my-searchrecord .textarea-1 {
    width: 360px;
  }

  .my-searchrecord .line p {
    text-align: left;
    text-indent: 80px;
  }

.template-operate-box .line p {
  text-align: left;
  text-indent: 100px;
}

  .my-searchrecord .line p span, .template-operate-box .line p span {
    white-space: pre;
  }

.searchrecord-footer {
  background: none repeat scroll 0 0 #eee;
  border-top: 1px solid #a6a5a5;
  float: left;
  height: 40px;
  line-height: 40px;
  width: 100%;
}

  .searchrecord-footer .my-searchrecord-form {
    float: right;
    margin-right: 40px;
    margin-top: 8px;
    width: 120px;
  }

.dialog-footer {
  background: none repeat scroll 0 0 #eee;
  border-top: 1px solid #a6a5a5;
  float: left;
  height: 40px;
  line-height: 40px;
  width: 100%;
  overflow: hidden;
}

  .dialog-footer .my-dialog-form {
    float: right;
    margin-right: 40px;
    margin-top: 8px;
    width: 120px;
  }
/*寻访弹出窗导入EXECL*/
.my-searchrecord .searchrecord-import-link {
  color: #4b72b0;
  text-decoration: underline;
  text-indent: 10px;
  float: left;
}

  .my-searchrecord .searchrecord-import-link:hover {
    color: #4b72b0;
    text-decoration: none;
  }

.my-searchrecord .line .searchrecord-import-label {
  width: 90px;
}

.my-searchrecord .line .searchrecord-import-inputupfile-box {
  width: 290px;
}

.my-searchrecord .line .other-con li {
  float: left;
  height: 25px;
  line-height: 25px;
  overflow: hidden;
  position: relative;
  white-space: nowrap;
  width: 290px;
  text-align: left;
}

.my-searchrecord .line .uploadBtn {
  cursor: pointer;
  display: inline;
  float: left;
  margin-left: 5px;
}

.my-searchrecord .line.searchrecord-box-width {
  width: 540px;
}

.my-searchrecord .searchrecord-tip-box {
  width: 220px;
  margin: 25px auto 10px auto;
  height: 32px;
}

.my-searchrecord .searchrecord-tip-icon {
  float: left;
}

.my-searchrecord .searchrecord-tip-title {
  width: 140px;
  margin: 0 auto;
  line-height: 32px;
  float: left;
}

.my-searchrecord .again-link-box {
  width: 220px;
  margin: 0 auto;
  height: 32px;
  text-align: center;
  padding: 10px 0 30px 0;
}

.my-searchrecord .again-import-xsl {
  font-size: 14px;
  display: block;
  margin: 0 auto;
  color: #4b72b0;
  text-decoration: underline;
}

  .my-searchrecord .again-import-xsl:hover {
    color: #4b72b0;
    text-decoration: none;
  }

.my-searchrecord .searchrecord-tip-msg-text {
  width: 220px;
  margin: 5px auto 5px auto;
  height: auto;
  text-align: left;
}

  .my-searchrecord .searchrecord-tip-msg-text p {
    color: red;
    line-height: 24px;
  }

.my-searchrecord .searchrecord-import-p {
  float: left;
  width: 30px;
  text-indent: 0 !important;
}

.my-searchrecord .searchrecord-import-ljy {
  text-indent: 0 !important;
}
/*对齐INPUT元素*/
.data-check-box {
  float: left;
  height: 14px;
  line-height: 14px;
}

  .data-check-box .check-input {
    height: 13px;
    margin: 0 4px;
    width: 13px;
  }

  .data-check-box .check-label {
    height: 14px;
    line-height: 14px;
  }
/*推荐*/
.candidate-recommend-box .candidate-recommend-data-title {
  background: url("images/list-item-2.gif") repeat-x scroll 0 0 transparent;
  border: 1px solid #d0cecf;
  font-weight: bold;
  height: 31px;
  line-height: 31px;
}

.candidate-recommend-data-title span, .listdata-text span {
  display: inline;
  overflow: hidden;
  text-align: left;
  margin-right: 1px;
}

.candidate-recommend-data-title span {
}

.candidate-recommend-box .item-1 {
  width: 35px;
  text-align: center;
}

.candidate-recommend-box .item-2 {
  width: 130px;
  margin-right: .5em;
}

.candidate-recommend-box .item-3 {
  width: 120px;
}

.candidate-recommend-box .item-4 {
  width: 70px;
}

.candidate-recommend-box .item-5 {
  width: 35px;
}

.candidate-recommend-box .item-6 {
  width: 60px;
  padding-right: 10px;
  text-align: right;
}

.candidate-recommend-box .item-7 {
  width: 80px;
}

.candidate-recommend-box .item-8 {
  width: 65px;
}

.candidate-recommend-box .item-9 {
  width: 35px;
}

.candidate-recommend-box .item-12 {
  width: 70px;
}

  .candidate-recommend-box .item-12 a {
    font-weight: bold;
  }

.candidate-recommend-box .item-13 {
  width: 43px;
  text-align: center;
}

.candidate-recommend-box .listdata-name .item-9.take-notes {
  text-align: left;
  width: 56px;
  margin-left: 3px;
}

.candidate-recommend-box .listdata-text .item-9.take-notes {
  cursor: pointer;
  margin-left: 15px;
  margin-right: 11px;
  width: 30px;
  text-align: center;
  margin-top: 4px;
  border-top: 1px solid #bfbfbf;
  border-left: 1px solid #eeeeee;
  border-right: 1px solid #eeeeee;
  height: 18px;
  line-height: 18px;
  background: #FFF;
}

  .candidate-recommend-box .listdata-text .item-9.take-notes strong {
    color: #FF0000;
  }

.candidate-recommend-box .item-10 {
  width: 75px;
}

.candidate-recommend-box .item-11 {
  width: 90px;
}

.candidate-recommend-box .candidate-recommend-data-box .operate {
  float: left;
  margin: 5px 3px 0 3px;
}

.candidate-recommend-box .candidate-recommend-data-box {
  height: 29px;
  margin: 0;
  overflow: hidden;
  padding: 0;
  width: 960px;
  line-height: 29px;
}

.catalogue .rfrc-1, .catalogue .rfrc-2 {
  float: right;
  margin-right: 20px;
}

.recommendtip .tiptext, .searchrecord .tiptext {
  margin: 12px 0 0 0;
  font-weight: bold;
}

.recommendtip .icon, .searchrecord .icon {
  margin: 11px 3px 0 0;
}
/*创建推荐*/
.my-candidate {
  width: 550px;
  margin: 0 auto;
}

  .my-candidate .line {
    width: 100%;
    margin: 10px 0;
  }

    .my-candidate .line label {
      float: left;
      margin-right: 0;
      font-weight: bold;
      width: 80px;
    }

    .my-candidate .line .button-6 {
      margin-top: 6px;
    }

    .my-candidate .line span {
      overflow: hidden;
    }

    .my-candidate .line.item-name {
      background: none repeat scroll 0 0 #e1e1e1;
      font-weight: bold;
      overflow: hidden;
      width: 100%;
    }

    .my-candidate .line .itme-1 {
      margin: 0 0 0 10px;
      display: block;
      float: left;
      width: 26px;
      text-align: center;
    }

      .my-candidate .line .itme-1 input {
        vertical-align: middle;
      }

    .my-candidate .line .itme-2 {
      width: 122px;
      display: block;
      float: left;
      text-align: left;
    }

    .my-candidate .line .itme-3 {
      width: 253px;
      display: block;
      float: left;
      text-align: left
    }

    .my-candidate .line .itme-4 {
      width: 75px;
      display: block;
      float: left;
    }

    .my-candidate .line .itme-5 {
      width: 55px;
      float: left;
      text-align: right;
      margin-right: 5px;
    }

    .my-candidate .line .popup-list-item li {
      height: 29px;
    }

    .my-candidate .line.name-box {
      margin: 0;
    }

    .my-candidate .line.data-box {
      margin: 0;
    }
/*高亮字体*/
.highfontnormal {
  color: #f60;
  font-weight: normal;
}

.highfont {
  color: #f60;
  font-weight: bold !important;
}

a.highfont {
  color: #f60;
  font-weight: bold !important;
}

.teamlist strong {
  color: #f60;
  font-weight: bold;
}
/*详细页公用板块*/
.template-detiail-box .clientname a {
  color: #f60;
  font-weight: bold;
}

.template-detiail-box .username {
  color: #f60;
  font-size: 18px;
  font-weight: bold;
}

.template-detiail-box .line .row label {
  color: #21639C;
}

.template-detiail-box .line .row span {
  width: 330px;
  float: left;
}

.template-detiail-box .line .row .team {
  width: auto;
  float: none;
}

.template-detiail-box .line .row .filedown {
  padding: 0 5px;
}

.template-operate .recruiting-situation .textarea-1 {
  margin-left: 25px;
  width: 560px;
}

.template-operate .recruiting-situation .mt-none {
  padding-bottom: 50px;
}

.r_toastr_num {
  color: #D91E18;
  font-size: 18px;
  font-weight: bold;
  font-family: SimHei
}
/*短信*/
.short-message-box .short-message-data-title {
  background: url("images/list-item-2.gif") repeat-x scroll 0 0 transparent;
  border: 1px solid #d0cecf;
  font-weight: bold;
  height: 31px;
  line-height: 31px;
}

.short-message-data-title span, .listdata-text span {
  display: inline;
  overflow: hidden;
  text-align: left;
}

.short-message-box .short-message-data-title span, .short-message-box .short-message-data-content .item {
  white-space: nowrap;
  overflow: hidden;
  margin-right: 3px;
  text-overflow: ellipsis;
}

.short-message-box .item-1 {
  width: 85px;
  text-indent: 15px;
}

.short-message-box .item-2 {
  width: 63px;
}

.short-message-box .item-3 {
  width: 132px;
}

.short-message-box .item-4 {
  width: 280px;
}

.short-message-box .item-5 {
  width: 50px;
}

.short-message-box .item-6 {
  width: 65px;
}

.short-message-box .item-7 {
  width: 65px;
}

.short-message-box .item-8 {
  width: 70px;
}

.short-message-box .item-9 {
  width: 120px;
}

.short-message-box .item-5 .state-icon {
  margin: 6px 0 0 10px;
}

.short-message-box .short-message-data-content {
  border-bottom: 1px solid #fff;
  -moz-border-bottom-colors: none;
  -moz-border-left-colors: none;
  -moz-border-right-colors: none;
  -moz-border-top-colors: none;
  border-color: transparent;
  -webkit-border-image: none;
  -moz-border-image: none;
  -ms-border-image: none;
  -o-border-image: none;
  border-image: none;
  border-style: none solid;
  border-width: 1px 0 0 1px;
  font-size: 12px;
  margin: 0 0 1px;
  overflow: hidden;
  padding: 0 !important;
  width: 960px;
  line-height: 31px;
}

.short-message-box .short-message-data-box .operate {
  float: left;
  margin: 5px 5px 0 5px;
}

.short-message-box .short-message-data-box {
  height: 29px;
  margin: 0;
  overflow: hidden;
  padding: 0;
  width: 960px;
  cursor: pointer;
}

  .short-message-box .short-message-data-box.hover {
    color: #000;
    background: url(images/saffh2.jpg) 0 -55px repeat-x !important;
  }

.short-message-box .radio-width {
  width: 59px;
}

.short-message-box .short-message-data-detail {
  background-color: #EEF7FD;
  width: 960px;
  display: none;
}

  .short-message-box .short-message-data-detail .detail-head {
    height: 31px;
    border-top: solid 1px #AAD0E9;
    border-bottom: dotted 1px #AAD0E9;
  }

  .short-message-box .short-message-data-detail .explain1 {
    border-right: dotted 1px #AAD0E9;
  }

  .short-message-box .short-message-data-detail .detail-head ul {
    height: 31px;
    background-color: #DBF0FE;
  }

  .short-message-box .short-message-data-detail .detail-head li {
    height: 31px;
    width: 476px;
    float: left;
    text-indent: 12px;
  }

  .short-message-box .short-message-data-detail .detail-data-box {
    width: 100%;
  }

    .short-message-box .short-message-data-detail .detail-data-box .detail-data-phonenumber {
      float: left;
      width: 476px;
      border-right: dotted 1px #AAD0E9;
    }

    .short-message-box .short-message-data-detail .detail-data-box .etail-data-content {
      float: left;
      width: 476px;
    }

    .short-message-box .short-message-data-detail .detail-data-box .detail-data-p {
      padding-left: 15px;
      padding-top: 10px;
      word-wrap: break-word;
    }

    .short-message-box .short-message-data-detail .detail-data-box .phonenumber-box {
      height: 155px;
      overflow-x: hidden;
      overflow-y: auto;
      padding-left: 15px;
      padding-top: 10px;
    }

    .short-message-box .short-message-data-detail .detail-data-box li.item-number-box {
      width: 50%;
      height: 31px;
      line-height: 31px;
      float: left;
    }

    .short-message-box .short-message-data-detail .detail-data-box .phonefontcolor {
      color: #7B7B7B;
    }

    .short-message-box .short-message-data-detail .detail-data-box .namefontcolor {
      max-width: 100px;
      overflow: hidden;
    }

    .short-message-box .short-message-data-detail .detail-data-box .detail-data-content {
      float: left;
      width: 476px;
    }

.short-message-box .short-message-open-box .short-message-data-detail {
  display: block;
}

.short-message-box .short-message-open-box {
  color: #000;
  background-color: #eef7fd !important;
  border: 1px solid #a3cce7 !important;
  line-height: 29px;
  width: 958px;
}

.my-short-message {
  width: 570px;
  margin: 10px auto;
}

  .my-short-message .line {
    width: 100%;
    margin: 10px 0;
  }

    .my-short-message .line label {
      float: left;
      font-weight: bold;
      margin-right: 0;
      padding-right: 10px;
      text-align: right;
      width: 70px;
      white-space: nowrap;
      overflow: hidden;
    }

  .my-short-message .textarea-1 {
    width: 360px;
  }

  .my-short-message .shortmessage-type-box {
    width: 110px;
  }

    .my-short-message .shortmessage-type-box .shortmessage-type {
      width: 80px;
    }

  .my-short-message .shortmessage-content {
    width: 430px;
    height: 90px;
  }

  .my-short-message .shortmessage-tip-box {
    margin: 0;
    text-align: left;
  }

  .my-short-message .shortmessage-textarea-box {
    margin-bottom: 0;
  }

  .my-short-message .shortmessage-tip {
    text-align: left;
  }

  .my-short-message .shortmessage-example-candidates {
    color: #56ABEE;
    text-align: right;
    cursor: pointer;
    float: right;
    margin-right: 40px;
    padding-top: 15px;
    line-height: 12px;
  }

  .my-short-message .shortmessage-example-client {
    color: #56ABEE;
    text-align: right;
    cursor: pointer;
    float: right;
    margin-right: 5px;
    padding-top: 15px;
    line-height: 12px;
  }

  .my-short-message .shortmessage-sign {
    width: 447px;
    float: left;
    padding: 0;
    text-indent: 5px;
  }

  .my-short-message .modalityboxes .div_txt {
    background: none repeat scroll 0 0 #fff;
    border: 1px solid #d3d3d3;
    -ms-border-radius: 5px 5px 5px 5px;
    border-radius: 5px 5px 5px 5px;
    -webkit-box-shadow: 1px 1px 1px #d3d3d3 inset;
    -ms-box-shadow: 1px 1px 1px #d3d3d3 inset;
    box-shadow: 1px 1px 1px #d3d3d3 inset;
    -webkit-border-image: none;
    -moz-border-image: none;
    -ms-border-image: none;
    -o-border-image: none;
    border-image: none;
    border-right: 1px solid #c3c3c3;
    border-style: solid;
    border-width: 1px;
    font-family: Tahoma;
    height: auto;
    min-height: 25px;
    padding: 1px;
    width: auto;
    float: left;
    width: 437px;
    overflow-x: hidden;
    overflow-y: auto;
    max-height: 100px;
    position: relative;
    padding: 5px;
  }

  .my-short-message .addr_text {
    background: none repeat scroll 0 0 transparent;
    height: 25px;
    margin: 0;
  }

  .my-short-message .addr_inputkey {
    outline: none;
    -webkit-appearance: none;
    width: 100%;
    height: 25px;
    line-height: 25px;
    background: none repeat scroll 0 0 #fff;
    border: medium none;
  }

  .my-short-message .addr_htmlkey {
    height: 1px;
    overflow: visible;
    white-space: nowrap;
    border: medium none;
    margin: 0;
    padding: 0;
    font-family: Tahoma;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    word-spacing: 0;
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  .my-short-message .addr_outlinekey_box {
    clear: both;
    border: none;
    margin: 0;
    padding: 0;
  }

  .my-short-message .addr_outlinekey_input {
    position: absolute;
    border: none;
    padding: 0;
    width: 10px;
    left: -9999px;
    top: -9999px;
  }

  .my-short-message .addr_text_box_outmax {
    float: left;
    border: medium none;
    width: 1px;
  }

  .my-short-message .addr_del {
    width: 1px;
    height: 8px;
    position: relative;
    top: 10px;
    left: -10px;
    float: left;
  }

    .my-short-message .addr_del .addr_del_link {
      background: url("/static/style/118.png") no-repeat scroll center center transparent;
      width: 7px;
      height: 8px;
      display: none;
    }

      .my-short-message .addr_del .addr_del_link.icon-show {
        display: block;
      }

  .my-short-message .item-data {
    color: #a0a0a0;
    float: left;
    padding: 0 5px;
    white-space: nowrap;
    cursor: text;
  }

    .my-short-message .item-data .addr_del_block {
      width: 7px;
    }

  .my-short-message .addr_base {
    float: left;
    white-space: nowrap;
    cursor: default;
    color: #A0A0A0;
  }

    .my-short-message .addr_base b {
      color: #000;
      font-weight: normal;
    }

    .my-short-message .addr_base .addr_del_block {
      -moz-min-width: 5px;
      -ms-min-width: 5px;
      -o-min-width: 5px;
      -webkit-min-width: 5px;
      min-width: 5px;
      display: inline-block;
      line-height: 16px;
    }

  .my-short-message .foucs {
    background-color: #528bcb;
  }

  .my-short-message .short-message-add {
    height: 16px;
    width: 16px;
    position: absolute;
    right: 25px;
    margin-top: 5px;
  }

  .my-short-message .hover span, .my-short-message .hover b {
    background-color: #e0ecf9;
  }

  .my-short-message .current span, .my-short-message .current b {
    background-color: #528bcb;
    color: #fff;
  }

  .my-short-message .addr_error span, .my-short-message .addr_error b {
    color: red;
  }

  .my-short-message .semicolon {
    width: 5px;
  }

  .my-short-message .tips {
    color: #999;
    padding-left: 20px;
  }

.tips strong {
  color: #1e9300;
}

.tips .js_txtFull strong {
  color: red;
}
/*简历页面所有图标*/
.resume-icon {
  display: block;
  width: 16px;
  height: 16px;
  background-image: url(/static/images/resume-source.gif);
  background-repeat: no-repeat;
  float: left;
  margin: 6px 5px 0 0;
}

.resume-icon-1 {
  background-position: 0 0;
}

.resume-icon-2 {
  background-position: 0 -16px;
}

.resume-icon-3 {
  background-position: 0 -32px;
}

.resume-icon-4 {
  background-position: 0 -48px;
}

.resume-icon-5 {
  background-position: 0 -64px;
}

.resume-icon-6 {
  background-position: 0 -80px;
}

.resume-icon-7 {
  background-position: 0 -96px;
}

.resume-icon-8 {
  background-position: 0 -112px;
}

.resume-icon-9 {
  background-position: 0 -128px;
}

.resume-icon-10 {
  background-position: 0 -144px;
}

.resume-icon-11 {
  background-position: 0 -160px;
}

.resume-icon-12 {
  background-position: 0 -176px;
}

.resume-icon-13 {
  background-position: 0 -192px;
}

.resume-detail .resume-icon {
  margin-top: 3px;
}
/*首页所有图标*/
.home-icon {
  display: block;
  width: 16px;
  height: 16px;
  background-image: url(images/home-icon.png);
  background-repeat: no-repeat;
  left: 7px;
  position: absolute;
  top: 11px;
}

  .home-icon.icon-1 {
    background-position: 0 0;
  }

  .home-icon.icon-2 {
    background-position: 0 -16px;
  }

  .home-icon.icon-3 {
    background-position: 0 -32px;
  }

  .home-icon.icon-4 {
    background-position: 0 -48px;
  }

  .home-icon.icon-5 {
    background-position: 0 -64px;
  }

  .home-icon.icon-6 {
    background-position: 0 -80px;
  }

  .home-icon.icon-7 {
    background-position: 0 -96px;
  }

  .home-icon.icon-8 {
    background-position: 0 -112px;
  }

  .home-icon.icon-9 {
    background-position: 0 -128px;
  }

  .home-icon.icon-10 {
    background-position: 0 -144px;
  }

  .home-icon.icon-11 {
    background-position: 0 -160px;
  }

  .home-icon.icon-12 {
    background-position: 0 -176px;
  }

  .home-icon.icon-13 {
    background-position: 0 -192px;
  }

.candidate-highfirstrow, .finance-highfirstrow, .client-highfirstrow {
  background-color: #e8eef7;
  border-top: solid 1px #b4cef2;
  border-bottom: solid 1px #b4cef2 !important;
}
/*内容管理*/
.portal-manage-box .portal-manage-title-box {
  background: url("images/list-item-2.gif") repeat-x scroll 0 0 transparent;
  border: 1px solid #d0cecf;
  font-weight: bold;
  height: 31px;
  line-height: 31px;
}

.portal-manage-title-box span, .portal-manage-title-box span {
  display: inline;
  overflow: hidden;
  text-align: left;
}

.portal-manage-box .item-1 {
  width: 60px;
  text-align: center;
}

.portal-manage-box .item-2 {
  width: 100px;
}

.portal-manage-box .item-3 {
  width: 310px;
}

.portal-manage-box .item-8 {
  width: 80px;
  margin-left: 20px;
}

.portal-manage-box .item-4 {
  width: 80px;
}

.portal-manage-box .item-5 {
  width: 130px;
}

.portal-manage-box .item-6 {
  width: 80px;
}

.portal-manage-box .item-7 {
  width: 90px;
}

.portal-manage-box .portal-manage-data-box .operate {
  float: left;
  margin: 5px 5px 0 5px;
}

.portal-manage-box .portal-manage-data-box {
  cursor: pointer;
  height: 29px;
  margin: 0;
  overflow: hidden;
  padding: 0;
  width: 960px;
}

  .portal-manage-box .portal-manage-data-box .icon {
    float: left;
    margin: 3px 3px 0;
  }

.portal-manage-icon {
  display: block;
  width: 16px;
  height: 16px !important;
  background-image: url(images/portal-manage-icon.png);
  background-repeat: no-repeat;
}

  .portal-manage-icon.portal-icon-1 {
    background-position: 0 0;
    margin: 11px 7px 0 0;
  }

  .portal-manage-icon.portal-icon-2 {
    background-position: 0 -16px;
  }

  .portal-manage-icon.portal-icon-3 {
    background-position: 0 -32px;
  }

  .portal-manage-icon.portal-icon-4 {
    background-position: 0 -48px;
  }

  .portal-manage-icon.portal-icon-5 {
    background-position: 0 -64px;
  }

.portal-manage-box .alink {
  color: #21639c;
}

  .portal-manage-box .alink.no {
    color: #999;
  }

.portal-manage .mc-term-width2 {
  width: 463px;
}

  .portal-manage .mc-term-width2 .term-custom-list-data-box {
    width: 360px;
  }
/*官网的管理新增和编辑操作*/
.operate-data-box {
  background-color: #EEE;
}

  .operate-data-box .content-manage-box {
    padding: 10px 0;
  }

    .operate-data-box .content-manage-box .line {
      width: 950px;
      padding: 0;
      margin: 5px 0;
    }

      .operate-data-box .content-manage-box .line.box {
        height: 30px;
        overflow: hidden;
      }

    .operate-data-box .content-manage-box .content-title {
      width: 810px !important;
      padding: 0;
      text-indent: 8px;
    }

    .operate-data-box .content-manage-box .content-textarea {
      width: 810px;
    }

    .operate-data-box .content-manage-box .textarea-box {
      width: 810px;
      padding: 0;
      float: left;
    }

    .operate-data-box .content-manage-box .characteristic-box {
      padding: 0;
      float: left;
    }

      .operate-data-box .content-manage-box .characteristic-box.index {
        width: 300px;
      }

      .operate-data-box .content-manage-box .characteristic-box.photo {
        width: 430px;
      }

    .operate-data-box .content-manage-box .topindex-check {
      padding: 0;
      vertical-align: middle;
    }

    .operate-data-box .content-manage-box .preview-image {
      width: 120px;
      height: 90px;
      overflow: hidden;
      float: left;
      background: url(images/default-1.png) no-repeat;
      border: solid 1px #C7C7C7;
    }

    .operate-data-box .content-manage-box .preview-upload {
      float: left;
      width: auto;
      height: 29px;
      padding: 61px 0 0 10px;
    }

      .operate-data-box .content-manage-box .preview-upload .button {
        vertical-align: bottom;
      }

    .operate-data-box .content-manage-box .settopindex-box {
      width: 100%;
      padding: 0;
    }

    .operate-data-box .content-manage-box .uploadimage-box {
      width: 100%;
      padding: 8px 0;
    }

    .operate-data-box .content-manage-box .previewing-image {
      width: 120px;
      height: 90px;
    }

    .operate-data-box .content-manage-box .photo .preview-image {
      width: 280px;
      height: 210px;
      overflow: hidden;
      float: left;
      background: url(images/default-3.jpg) no-repeat;
      border: solid 1px #C7C7C7;
    }

      .operate-data-box .content-manage-box .photo .preview-image .album-image {
        width: 280px;
        height: 210px;
      }

    .operate-data-box .content-manage-box .photo .preview-upload {
      float: left;
      width: auto;
      height: 29px;
      padding: 181px 0 0 10px;
    }

    .operate-data-box .content-manage-box .content-article-select-box {
      width: 300px;
      padding: 0;
      height: 30px;
    }

    .operate-data-box .content-manage-box .content-article-check-box {
      width: 810px;
      padding: 8px 0;
    }

    .operate-data-box .content-manage-box .item-check-input {
      float: left;
      height: 12px;
      padding: 8px 12px 8px 0;
      line-height: 12px;
    }

    .operate-data-box .content-manage-box .checklabel {
      padding: 0 5px;
      line-height: 12px;
      height: 12px;
      float: left;
    }

  .operate-data-box .item-operate #allcheck {
    float: left;
    line-height: 12px;
  }

  .operate-data-box .content-manage-box .content-article-check-box ul {
    width: 810px;
    padding: 0;
    float: left;
  }

  .operate-data-box .content-manage-box .item-operate {
    float: left;
  }

  .operate-data-box .content-manage-box .content-manage-submit {
    text-align: center;
    width: 100%;
    height: 60px;
  }

    .operate-data-box .content-manage-box .content-manage-submit input {
      margin: 15px 10px;
    }

  .operate-data-box .content-manage-box .allcheck-box {
    margin: 10px 0;
  }

  .operate-data-box .content-manage-box .line .text-2 {
    width: 275px;
  }

    .operate-data-box .content-manage-box .line .text-2 .select-size-input {
      width: 270px;
    }

  .operate-data-box .content-manage-box .line .row {
    width: 400px;
  }

    .operate-data-box .content-manage-box .line .row.left {
      width: 515px;
    }

  .operate-data-box .content-manage-box .unit-b-box {
    left: 400px;
    position: relative;
    top: -27px;
  }

  .operate-data-box .content-article-preview {
    margin-left: 10px;
    text-decoration: underline;
  }

.characteristic-box .item-check-input input {
  float: left;
  height: 12px;
  line-height: 12px;
}
/*链接管理*/
.links-manage-box .links-manage-title-box {
  background: url("images/list-item-2.gif") repeat-x scroll 0 0 transparent;
  border: 1px solid #d0cecf;
  font-weight: bold;
  height: 31px;
  line-height: 31px;
}

.links-manage-title-box span, .links-manage-title-box span {
  display: inline;
  overflow: hidden;
  text-align: left;
}

.links-manage-box .item-1 {
  width: 60px;
  text-align: center;
}

.links-manage-box .item-2 {
  width: 80px;
}

.links-manage-box .item-3 {
  width: 80px;
}

.links-manage-box .item-4 {
  width: 100px;
}

.links-manage-box .item-5 {
  width: 170px;
}

.links-manage-box .item-6 {
  width: 110px;
}

.links-manage-box .item-7 {
  width: 130px;
}

.links-manage-box .item-8 {
  width: 75px;
}

.links-manage-box .item-9 {
  width: 50px;
}

.links-manage-box .links-manage-data-box .operate {
  float: left;
  margin: 5px 5px 0 5px;
}

.links-manage-box .links-manage-data-box {
  height: 31px;
  cursor: pointer;
  height: 29px;
  margin: 0;
  overflow: hidden;
  padding: 0;
  width: 960px;
}
/*链接编辑*/
.operate-data-box .links-manage-box {
  padding: 10px 0;
}

  .operate-data-box .links-manage-box .line {
    width: 950px;
    padding: 0;
    margin: 5px 0;
    _margin: 0;
  }

    .operate-data-box .links-manage-box .line.box {
      height: 30px;
      overflow: hidden;
    }

  .operate-data-box .links-manage-box .item-operate {
    float: left;
  }

.operate-data-box .links-manage-submit {
  text-align: center;
  width: 100%;
  margin-top: 10px;
  float: left;
}

  .operate-data-box .links-manage-submit input {
    margin: 0 10px;
  }

.operate-data-box .links-manage-box .links-text {
  width: 500px;
  float: left;
}

.operate-data-box .links-manage-box .links-NavigateUrl {
  width: 500px;
  float: left;
}

.operate-data-box .links-manage-box .links-Remark {
  width: 720px;
}

.operate-data-box .links-manage-box .links-sort {
  width: 300px;
  float: left;
}

.operate-data-box .links-manage-box .links-radio-box {
  width: 700px;
  float: left;
}

.operate-data-box .links-manage-box .links-radio-item {
  float: left;
  height: 12px;
  line-height: 12px;
  padding: 7px 20px 6px 0;
}

  .operate-data-box .links-manage-box .links-radio-item input {
    vertical-align: auto;
  }

.operate-data-box .links-manage-box .links-type-box {
  width: 320px !important;
}

.operate-data-box .links-manage-box .links-type-select {
  width: 300px !important;
}

.operate-data-box .links-manage-box .select-popup {
  width: 300px !important;
}
/*上传简历的TABLE*/
.tb-resume-uploadfile {
  margin: 30px auto;
  width: 300px;
}

  .tb-resume-uploadfile .name {
    width: 50%;
    text-align: right;
    line-height: 25px;
    height: 25px;
  }

  .tb-resume-uploadfile .value {
    width: 50%;
    text-align: left;
    text-indent: 10px;
    line-height: 25px;
    height: 25px;
  }
/*首页公告的弹出框*/
.company-notice {
  margin-top: 5px;
  margin-bottom: 5px;
}

.msg-company-notice-box .company-notice-operate-box {
  width: 520px;
  margin: 10px auto;
}

.msg-company-notice-box .notice-add-box {
  width: 100%;
  margin: 10px 0;
  height: 38px;
}

.msg-company-notice-box .notice-add-btn {
  width: 129px;
  height: 38px;
  background: url("images/company-notice-icon-bt.png") no-repeat scroll 0 0 transparent;
  border: medium none;
  cursor: pointer;
  color: #F7FCFF;
  font-size: 14px;
}

.msg-company-notice-box .notice-btn-box {
  width: 129px;
  height: 38px;
  float: left;
}

.msg-company-notice-box .notice-tip-box {
  float: left;
}

  .msg-company-notice-box .notice-tip-box span {
    color: #B2B2B2;
    line-height: 38px;
    padding-left: 10px;
  }

.msg-company-notice-box .notice-list {
  width: 520px;
}

.msg-company-notice-box .notice-list-box {
  width: 100%;
  position: relative;
  display: inline-block;
}

.msg-company-notice-box .notice-item {
  width: 519px;
  height: 50px;
  background: url(images/company-notice-icon-bg.png) no-repeat scroll 0 0 transparent;
  margin: 10px 0;
  position: relative;
}

.msg-company-notice-box .notice-item-inside-box {
  width: 500px;
  height: 30px;
  margin: 9px auto;
  display: inline-block;
}

.msg-company-notice-box .notice-item-inside-icon {
  float: left;
  width: 12px;
  overflow: hidden;
}

.msg-company-notice-box .notice-item-inside-title {
  float: left;
  width: 127px;
  overflow: hidden;
}

.msg-company-notice-box .notice-item-inside-url {
  float: left;
  width: 360px;
  overflow: hidden;
}

.company-notice-icon {
  background-image: url(images/company-notice-icon.png);
  background-repeat: no-repeat;
  display: block;
  overflow: hidden;
}

  .company-notice-icon.icon-1 {
    width: 65px;
    height: 28px;
    background-position: 0 0;
  }

  .company-notice-icon.icon-2 {
    width: 65px;
    height: 28px;
    background-position: 0 -28px;
  }

  .company-notice-icon.icon-3 {
    width: 10px;
    height: 10px;
    background-position: 0 -56px;
  }

  .company-notice-icon.icon-4 {
    width: 8px;
    height: 8px;
    background-position: 0 -66px;
  }

  .company-notice-icon.icon-5 {
    background-position: 0 -76px;
    cursor: pointer;
    height: 15px;
    width: 15px;
  }

.msg-company-notice-box .company-notice-icon.icon-hover {
  margin-top: 11px;
}

.msg-company-notice-box .notice-item-hover .icon-hover {
  background-position: 0 -66px;
}

.msg-company-notice-box .notice-item-inside-title-input {
  width: 120px;
  height: 28px;
  border: solid 1px #DFDFDF;
  line-height: 28px;
  color: #6F6F6F;
  text-indent: 5px;
}

.msg-company-notice-box .notice-item-inside-url-input {
  width: 358px;
  height: 28px;
  border: solid 1px #DFDFDF;
  line-height: 28px;
  color: #6F6F6F;
  text-indent: 5px;
  _width: 350px;
}

.msg-company-notice-box .notice-item-delete {
  display: none;
  height: 14px;
  left: 511px;
  position: absolute;
  top: -7px;
  width: 14px;
}

.msg-company-notice-box .notice-item-hover .notice-item-delete {
  display: block;
}

.msg-company-notice-box .notice-commit-operate-box {
  width: 520px;
  height: 30px;
  margin: 0 auto;
  overflow: hidden;
}

.msg-company-notice-box .notice-commit-operate-location {
  width: 140px;
  float: right;
}

.msg-company-notice-box .notice-commit-confirm-btn {
  width: 65px;
  height: 28px;
  background-position: 0 0;
  float: left;
  border: medium none;
  margin-right: 10px;
  color: #EDFFD2;
  font-weight: 200;
  line-height: 28px;
  font-size: 13px;
}

.msg-company-notice-box .notice-commit-cancel-btn {
  width: 65px;
  height: 28px;
  background-position: 0 -28px;
  float: left;
  border: medium none;
  color: #77787A;
  font-weight: 200;
  line-height: 28px;
  font-size: 13px;
}

.msg-company-notice-box .notice-list-box li.sortable-placeholder {
  border: 1px dashed #ccc;
  background: none;
  height: 50px;
}
/*外出管理*/
.business-activity-table {
  font-size: 12px;
  width: 100%;
  border-top-width: 1px;
  border-right-width: 1px;
  border-left-width: 1px;
  border-top-style: solid;
  border-right-style: solid;
  border-left-style: solid;
  border-top-color: #d0cecf;
  border-right-color: #d0cecf;
  border-left-color: #d0cecf;
  table-layout: fixed;
}

  .business-activity-table .maxwrap span {
    float: none;
  }

.business-activity-item-name {
  background: url("images/list-item-2.jpg") repeat-x scroll 0 0 transparent;
  border: 1px solid #d0cecf;
  font-weight: bold;
  height: 31px;
  line-height: 31px;
  padding: 0 !important;
  position: relative;
}

.business-activity-item-data {
  text-align: center;
  height: 29px;
  line-height: 29px;
  padding: 0 !important;
  background: none repeat scroll 0 0 #f8f8f8;
}

  .business-activity-item-data td {
    border-bottom: 1px solid #fff;
    text-align: center;
  }

  .business-activity-item-data .moneytext {
    color: #f60;
    font-weight: bold;
    text-align: right;
    padding-right: 15px;
  }

  .business-activity-item-data.even {
    background: none repeat scroll 0 0 #edecec;
  }

  .business-activity-item-data.hover {
    color: #000;
    background: url(images/saffh2.jpg) 0 -55px repeat-x !important;
  }

  .business-activity-item-data .input-radio {
    margin: 0;
  }

.business-activity-table .leftindent {
  text-align: left;
  text-indent: 10px;
}

.business-activity-item-name th {
  white-space: nowrap;
  overflow: hidden;
}

.business-activity-item-name .itemname-1 {
  width: 40px;
}

.business-activity-item-name .itemname-2 {
  width: 70px;
  padding: 0 !important;
}

.business-activity-item-name .itemname-3 {
  width: 100px;
}

.business-activity-item-name .itemname-4 {
  width: 80px;
}

.business-activity-item-name .itemname-5 {
  width: 125px;
}

.business-activity-item-name .itemname-6 {
  width: 125px;
}

.business-activity-item-name .itemname-7 {
  width: 110px;
}

.business-activity-item-name .itemname-8 {
  width: 110px;
}

.business-activity-item-name .itemname-9 {
  width: 50px;
}

.business-activity-item-name .itemname-11 {
  width: 70px;
}

.business-activity-item-name .itemname-12 {
  width: 50px;
}

.business-activity-table .maxwrap {
  text-align: left;
}

.business-activity .hits-item {
  color: red;
  font-weight: 700;
}

.business-activity-table .take-notes {
  background: none repeat scroll 0 0 #fff;
  border-left: 1px solid #eee;
  border-right: 1px solid #eee;
  border-top: 1px solid #bfbfbf;
  cursor: pointer;
  height: 18px;
  line-height: 18px;
  margin: 2px 0 0 8px;
  text-align: center;
  width: 30px;
}

  .business-activity-table .take-notes strong {
    color: red;
  }

.business-activity-table .operate-box {
  width: 78px;
  margin: 0 auto;
}

.disabled {
  color: #7C7B7B;
  cursor: no-drop;
}

.business-activity-table .operate {
  float: left;
  margin: 0 5px;
}

.business-activity-table .left {
  text-align: left;
}

.business-activity-operate .border-middle {
  width: 956px;
}

.business-activity-operate .business-activity-box {
  width: 100%;
  padding: 10px 0;
  background-color: #EEE;
}

.business-activity-operate .line {
  margin: 10px auto;
  padding: 0;
  width: 800px;
  height: 30px;
  float: none;
}

  .business-activity-operate .line .row {
    display: inline;
    float: left;
    width: 385px;
  }

.business-activity-operate .business-activity-client {
  width: 610px;
  height: 28px;
  cursor: text;
}

.business-activity-operate .business-activity-type select {
  border: medium none;
  cursor: pointer;
  margin: 5px 0 0 5px;
}

.business-activity-operate .business-activity-address {
  width: 610px;
}

.business-activity-operate .dateymd {
  float: left;
  left: -24px;
  position: relative;
  top: 7px;
}

.business-activity-operate .datehsm {
  float: left;
  left: -24px;
  position: relative;
  top: 7px;
}

.business-activity-operate .business-activity-staff {
  width: 610px;
  height: 28px;
}

.business-activity-operate .business-activity-form-box {
  height: 60px;
  text-align: center;
  width: 100%;
}

.business-activity-operate .business-activity-type {
  position: relative;
}

.business-activity-remark-label {
  cursor: text;
  display: inline;
  float: left;
  font-weight: bold;
  margin-right: 15px;
  text-align: right;
  width: 82px;
  color: #21639c;
}

.business-activity-remark-span {
  float: left;
  width: 780px;
}

.business-activity-details {
  background-color: #fff;
  border: 1px solid #b7b7b7;
  margin-bottom: 1px;
  width: 888px;
}
/*请假管理*/
.absence-table {
  font-size: 12px;
  width: 100%;
  border-top-width: 1px;
  border-right-width: 1px;
  border-left-width: 1px;
  border-top-style: solid;
  border-right-style: solid;
  border-left-style: solid;
  border-top-color: #d0cecf;
  border-right-color: #d0cecf;
  border-left-color: #d0cecf;
  table-layout: fixed;
}

.absence-item-name {
  background: url("images/list-item-2.jpg") repeat-x scroll 0 0 transparent;
  border: 1px solid #d0cecf;
  font-weight: bold;
  height: 31px;
  line-height: 31px;
  padding: 0 !important;
  position: relative;
}

.absence-item-data {
  text-align: center;
  height: 29px;
  line-height: 29px;
  padding: 0 !important;
  background: none repeat scroll 0 0 #f8f8f8;
}

  .absence-item-data td {
    border-bottom: 1px solid #fff;
    text-align: center;
    white-space: nowrap;
  }

  .absence-item-data .moneytext {
    color: #f60;
    font-weight: bold;
    text-align: right;
    padding-right: 15px;
  }

.absence-table .even {
  background: none repeat scroll 0 0 #edecec;
}

.absence-table .hover {
  color: #000;
  background: url(images/saffh2.jpg) 0 -55px repeat-x !important;
}

.absence-item-data .input-radio {
  margin: 0;
}

.absence-table .leftindent {
  text-align: left;
  text-indent: 10px;
}

.absence-item-name th {
  white-space: nowrap;
  overflow: hidden;
}

.absence-item-name .itemname-1 {
  width: 50px;
}

.absence-item-name .itemname-2 {
  width: 60px;
  padding: 0 !important;
}

.absence-item-name .itemname-3 {
  width: 70px;
}

.absence-item-name .itemname-4 {
  width: 50px;
}

.absence-item-name .itemname-5 {
  width: 70px;
}

.absence-item-name .itemname-6 {
  width: 120px;
}

.absence-item-name .itemname-7 {
  width: 80px;
}

.absence-item-name .itemname-8 {
  width: 90px;
}

.absence-item-name .itemname-9 {
  width: 70px;
}

.absence-item-name .itemname-10 {
  width: 190px;
}

.absence-table .maxwrap {
  text-align: left
}

.absence-table .operate-box {
  width: 78px;
  margin: 0 auto;
}

.absence-table .authority-box .disabled {
  color: #7c7b7b;
  cursor: no-drop;
}

.absence-table .operate {
  float: left;
  margin: 0 5px;
}

.absence-table .left {
  text-align: left;
}

.absence-table .approval-status-box {
  position: relative;
  text-align: left;
}

  .absence-table .approval-status-box.show {
    border: solid 1px #DA6B00;
    background-color: #FFF6E4;
    width: 90px;
    height: 25px;
    margin: 1px 0;
  }

.absence-table .approval-status-dialog {
  display: none;
}

.absence-table .approval-status-box.show .approval-status-dialog {
  display: block;
  position: absolute;
  top: 24px;
  left: -1px;
  line-height: 25px;
  background-color: #FFF6E4;
  width: 90px;
  border: solid 1px #DA6B00;
  z-index: 3;
}

.absence-table .approval-status-box .approval-status-text {
  padding-left: 10px;
  display: block;
  margin: 1px 0 0 1px;
  color: #DF730A;
  font-weight: 700;
  position: relative;
  cursor: pointer;
  height: 27px;
}

.absence-table .approval-status-box.show .approval-status-text {
  display: block;
  margin: 0;
  height: 25px;
}

.absence-table .approval-status-box .approval-status-text .text {
  display: block;
  margin: 0;
  height: 27px;
  line-height: 27px;
  width: 59px;
  white-space: nowrap;
}

.absence-table .approval-status-box.show .approval-status-text .text {
  display: block;
  margin: 0;
  height: 26px;
  line-height: 26px;
  width: 59px;
}

.absence-table .approval-status-box .approval-status-icon i {
  background: url("/static/images/financial-distribution-status-btn-icon.gif") no-repeat 0 0;
  height: 9px;
  width: 9px;
  display: none;
  position: absolute;
  top: 10px;
  right: 7px;
  -webkit-transition: transform 0.2s ease-in 0;
  -moz-transition: transform 0.2s ease-in 0;
  -ms-transition: transform 0.2s ease-in 0;
  -o-transition: transform 0.2s ease-in 0;
  transition: transform 0.2s ease-in 0;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}

.absence-table .approval-status-box.allowrole .approval-status-icon i {
  display: block;
}

.absence-table .financial-table .take-notes {
  margin: 2px 15px 0 15px;
}

.absence-table .approval-status-box.show .approval-status-icon i {
  background: url("/static/images/financial-distribution-status-btn-icon.gif") no-repeat 0 0;
  height: 9px;
  width: 9px;
  display: block;
  position: absolute;
  top: 8px;
  right: 7px;
  -webkit-transition: transform 0.2s ease-in 0s;
  -moz-transition: transform 0.2s ease-in 0s;
  -ms-transition: transform 0.2s ease-in 0s;
  -o-transition: transform 0.2s ease-in 0s;
  transition: transform 0.2s ease-in 0s;
  -webkit-transform: rotate(0);
  -moz-transform: rotate(0);
  -ms-transform: rotate(0);
  -o-transform: rotate(0);
  transform: rotate(0);
}

.absence-table .approval-status-box.show .approval-status-item {
  height: 25px;
  padding-left: 10px;
  border-bottom: solid 1px #d0cecf;
  cursor: pointer;
}

  .absence-table .approval-status-box.show .approval-status-item.itemhover {
    background-color: #FFF1CC;
  }

  .absence-table .approval-status-box.show .approval-status-item.current {
    background-color: #FCE9C4;
  }

.absence-table .approval-status-box .approval-btn-list li {
  width: 50%;
  float: left;
  text-align: center;
}

.absence-table .approval-status-box .approval-btn-list {
  margin: 5px 0;
}

  .absence-table .approval-status-box .approval-btn-list li .btn-approval-confim {
    width: 38px;
    cursor: pointer;
  }

  .absence-table .approval-status-box .approval-btn-list li .btn-approval-cancel {
    width: 38px;
    cursor: pointer;
  }

.absence-table .approval-status-box .approval-status-text .red {
  color: red;
}

.absence-table .approval-status-box .approval-status-text .green {
  color: green;
}

.absence-table .approval-status-box .approval-status-text .black {
  color: black;
}

.absence-operate .border-middle {
  width: 956px;
}

.absence-operate .absence-box {
  width: 100%;
  padding: 10px 0;
  background-color: #EEE;
}

.absence-operate .tips-pic-box {
  background-color: #eee;
  width: 100%;
}

.absence-operate .tips1 {
  background: url(/css/images/absence-tips1.png) no-repeat;
  width: 396px;
  height: 178px;
  display: block;
  margin: 0 auto;
  padding: 50px 0;
}

.absence-operate .tips2 {
  background: url(/css/images/absence-tips2.png) no-repeat;
  width: 396px;
  height: 178px;
  display: block;
  margin: 0 auto;
  padding: 50px 0;
}

.absence-operate .absence-type-list {
  float: left;
}

.absence-operate .absence-tip {
  float: left;
  background: url(/Css/images/absence.cion.png) no-repeat 0 center;
  margin-left: 20px;
  padding-left: 20px;
  color: #0E74C5;
}

.absence-operate .line {
  margin-left: 52px;
  padding: 0;
  width: 800px;
  height: 30px;
  float: none;
}

  .absence-operate .line .row {
    display: inline;
    float: left;
    width: 385px;
  }

.absence-operate .dateymd {
  float: left;
  left: -24px;
  position: relative;
  top: 7px;
}

.absence-operate .absence-form-box {
  height: 60px;
  text-align: center;
  width: 100%;
}

.absence-operate .selector_input {
  margin: 2px 0;
}

.radio-list-box {
  float: left;
  margin: 0 5px;
}

  .radio-list-box label {
    height: 12px;
    line-height: 12px;
  }

  .radio-list-box input {
    vertical-align: middle;
    margin: 0 !important;
    position: static !important;
  }

.selectstaff-box {
  float: left;
  width: 630px;
}

.absence-amount {
  margin-left: 10px;
}

.absence-date {
  width: 116px;
}

.teamSelect-box {
  width: 270px;
}

  .teamSelect-box .selector_outerbox {
    width: 245px;
    height: 28px !important;
  }

  .teamSelect-box .addTeam {
    left: 225px !important;
    top: 9px !important;
  }

  .teamSelect-box .outbox-item-list li {
    height: 21px;
    margin-top: 3px;
    width: auto;
  }

.absence-remark-label, .line-title {
  cursor: text;
  display: inline;
  float: left;
  font-weight: bold;
  margin-right: 15px;
  text-align: right;
  width: 82px;
  color: #21639c;
  white-space: nowrap;
  overflow: hidden;
}

.absence-remark-span, .line-text {
  float: left;
  width: 780px;
}

.absence-details, .page-details {
  background-color: #fff;
  border: 1px solid #b7b7b7;
  margin-bottom: 1px;
  width: 888px;
}

.page-details-unborder {
  background-color: #fff;
  border: none;
  margin: 0;
  width: 888px;
}

.rfrc-absence-date {
  right: 20px;
  position: absolute;
}

.pagerborder {
  border-left: solid 1px #D0CECF;
  border-right: solid 1px #D0CECF;
  border-bottom: solid 1px #D0CECF;
}

.operateborder {
  padding-left: 15px;
  border-left: solid 1px #D0CECF;
  border-right: solid 1px #D0CECF;
}
/*收入管理*/
.personal .financial-item-name .itemname-1 {
  width: 50px;
  text-indent: 15px;
}

.personal .financial-item-name .itemname-2 {
  width: 65px;
  padding: 0 !important;
}

.personal .financial-item-name .itemname-3 {
  width: 100px;
}

.personal .financial-item-name .itemname-4 {
  width: 70px;
}

.personal .financial-item-name .itemname-5 {
  width: 200px;
}

.personal .financial-item-name .itemname-6 {
  width: 80px;
}

.personal .financial-item-name .itemname-7 {
  width: 70px;
}

.personal .financial-item-name .itemname-8 {
  width: 60px;
}

.personal .financial-item-name .itemname-9 {
  width: 40px;
}

.personal .financial-item-name .itemname-10 {
  width: auto;
}

.personal .financial-item-name .itemname-12 {
  width: 160px;
}

.personal-details-box {
  display: none;
}
/*实时数据*/
.leftindent-1 {
  text-indent: 5px;
  text-align: left !important;
}

.leftindent-2 {
  text-indent: 20px;
  text-align: left !important;
}

.live-data .border-middle {
  width: 956px;
}

.live-data-table {
  font-size: 12px;
  width: 100%;
  border: solid 1px #d0cecf;
  table-layout: fixed;
}

.live-data-box .live-data-item-name {
  background: url("images/list-item-2.jpg") repeat-x scroll 0 0 transparent;
  border: 1px solid #d0cecf;
  font-weight: bold;
  height: 31px;
  line-height: 31px;
  padding: 0 !important;
  position: relative;
}

.live-data-item-data {
  text-align: center;
  height: 29px;
  line-height: 29px;
  padding: 0 !important;
  background: none repeat scroll 0 0 #f8f8f8;
}

  .live-data-item-data td {
    border-bottom: 1px solid #fff;
    text-align: center;
  }

  .live-data-item-data.hover {
    color: #000;
    background: url(images/saffh2.jpg) 0 -55px repeat-x !important;
  }

  .live-data-item-data .input-radio {
    margin: 0;
  }

.live-data-table .leftindent {
  text-align: left;
  text-indent: 10px;
}

.live-data-box .live-data-item-name .itemname-1 {
  width: 100px;
}

.live-data-box .live-data-item-name .itemname-2 {
  width: 125px;
  padding: 0 !important;
}

.live-data-box .live-data-item-name .itemname-3 {
  width: 85px;
}

.live-data-box .live-data-item-name .itemname-4 {
  width: 65px;
}

.live-data-box .live-data-item-name .itemname-5 {
  width: 90px;
}

.live-data-box .live-data-item-name .itemname-6 {
  width: 175px;
}

.live-data-box .live-data-item-name .itemname-7 {
  width: 60px;
}

.live-data-box .live-data-item-name .itemname-8 {
  width: 75px;
}

.live-data-box .live-data-item-name .itemname-9 {
  width: 70px;
}

.live-data-box .live-data-item-name th {
  white-space: nowrap;
  overflow: hidden;
}

.live-data-table .maxwrap {
  text-align: left;
}
/*考勤管理卡片版*/
.attendance-box .colleague-box {
  background: none repeat scroll 0 0 #eee;
  padding: 5px 10px 0 11px;
  width: 940px;
}

  .attendance-box .colleague-box .colleague-item {
    position: relative;
    overflow: hidden;
  }

  .attendance-box .colleague-box .colleague-row li {
    float: none !important;
    line-height: 23px !important;
    height: 23px;
    overflow: hidden;
    padding: 0;
  }

  .attendance-box .colleague-box .colleague-staffjob-box {
    float: left;
    width: 92px;
    text-align: center;
    height: 20px;
  }

  .attendance-box .colleague-box .colleague-staffjob-text {
    line-height: 18px;
  }

.attendance-box .attendance-businessactivity-box {
  background-color: #7094c5;
  bottom: 0;
  height: 57px;
  position: absolute;
  right: 0;
  width: 20px;
}

.attendance-box .attendance-businessactivity-detail-link {
  color: #fff;
  display: block;
  line-height: 12px;
  margin: 0 auto;
  padding: 5px;
  text-align: center;
  width: 12px;
}

.attendance-box .attendance-gray {
  background: none repeat scroll 0 0 #F7F8F9;
  color: #696969;
  border-color: #B2B4B6;
}

  .attendance-box .attendance-gray .colleague-con a {
    color: #696969;
  }

.attendance-box .olqq-icon {
  width: 39px;
  height: 17px;
  background: url(/css/images/olqq.png) no-repeat;
  display: block;
}

.attendance-box .sms-icon {
  width: 46px;
  height: 17px;
  background: url(/css/images/send_sms.png) no-repeat;
  display: block;
}

.attendance-box .attendance-gray .olqq-icon {
  background: url(/css/images/olqq_gray.png) no-repeat;
}

.attendance-box .attendance-gray .sms-icon {
  background: url(/css/images/send_sms_gray.png) no-repeat;
}

.attendance-box .attendance-gray .attendance-businessactivity-box {
  background-color: #B2B4B6;
}

.attendance-box .attendance-businessactivity-detail-box {
  width: 110px;
  color: #fff;
  position: absolute;
  bottom: 0;
  right: -112px;
  border: 1px solid #8CA9D0;
  z-index: 99;
}

.attendance-box .attendance-gray .attendance-businessactivity-detail-box {
  border: 1px solid #B2B4B6;
}

.attendance-box .attendance-businessactivity-detail-list {
  width: 100%;
  background-color: #fff;
  padding: 5px 0;
}

  .attendance-box .attendance-businessactivity-detail-list li {
    width: 100%;
    height: 22px;
    line-height: 22px;
    color: #000;
    text-align: center;
  }

.attendance-box .colleague-title, .attendance-box .colleague-con {
  line-height: 23px !important;
}

.attendance-box .attendance-item-name th {
  white-space: nowrap;
  overflow: hidden;
}
/*考勤列表*/
.attendance-table {
  font-size: 12px;
  width: 100%;
  border-left: solid 1px #d0cecf;
  border-right: solid 1px #d0cecf;
  table-layout: fixed;
}

.attendance-item-name {
  background: url("images/list-item-2.jpg") repeat-x scroll 0 0 transparent;
  border: 1px solid #d0cecf;
  font-weight: bold;
  height: 31px;
  line-height: 31px;
  padding: 0 !important;
  position: relative;
}

.attendance-item-data {
  text-align: center;
  height: 29px;
  line-height: 29px;
  padding: 0 !important;
  background: none repeat scroll 0 0 #f8f8f8;
}

  .attendance-item-data td {
    border-bottom: 1px solid #fff;
    text-align: center;
  }

  .attendance-item-data .moneytext {
    color: #f60;
    font-weight: bold;
    text-align: right;
    padding-right: 15px;
  }

.attendance-table .even {
  background: none repeat scroll 0 0 #edecec;
}

.attendance-table .hover {
  color: #000;
  background: url(images/saffh2.jpg) 0 -55px repeat-x !important;
}

.attendance-item-data .input-radio {
  margin: 0;
}

.attendance-table .leftindent {
  text-align: left;
  text-indent: 10px;
}

.attendance-item-name .itemname-1 {
  width: 80px;
}

.attendance-item-name .itemname-2 {
  width: 65px;
}

.attendance-item-name .itemname-3 {
  width: 80px;
}

.attendance-item-name .itemname-4 {
  width: 140px;
}

.attendance-item-name .itemname-5 {
  width: 70px;
}

.attendance-item-name .itemname-6 {
  width: 70px;
}

.attendance-item-name .itemname-7 {
  width: 70px;
}

.attendance-item-name .itemname-8 {
  width: 120px;
}

.attendance-item-name .itemname-9 {
  width: 120px;
}

.attendance-item-name .itemname-10 {
  width: 60px;
}

.attendance-table .attendance-item-green {
  color: white;
  background-color: green;
  padding: 1px 3px;
}

.attendance-table .attendance-item-yellow {
  color: white;
  background-color: #f60;
  padding: 1px 3px;
}

.attendance-table .attendance-item-red {
  color: white;
  background-color: red;
  padding: 1px 3px;
}

.attendance-table .attendance-item-black {
  color: white;
  background-color: black;
  padding: 1px 3px;
}

.attendance-icon {
  display: block;
  width: 16px;
  height: 16px;
  background-image: url(images/attendance-icon.png);
  background-repeat: no-repeat;
  float: left;
  margin-top: 6px;
}

.attendance-onguard {
  background-position: 0 0;
}

.attendance-not {
  background-position: 0 -64px;
}

.unattendance .attendance-onguard {
  background-position: 0 -16px;
}

.attendance-oninfo {
  background-position: 0 -32px;
  cursor: pointer;
}

.nohaveattendance .attendance-oninfo {
  background-position: 0 -48px;
}

.datepacker {
  width: 190px;
  height: 25px;
  line-height: 25px;
  margin: 2px 2px 0 0;
}

  .datepacker .time {
    height: 25px;
    line-height: 20px;
    width: 170px;
  }
/*考勤日志*/
.attendance-log-table {
  font-size: 12px;
  width: 100%;
  border-left: solid 1px #d0cecf;
  border-right: solid 1px #d0cecf;
  table-layout: fixed;
}

.attendance-log-item-name {
  background: url("images/list-item-2.jpg") repeat-x scroll 0 0 transparent;
  border: 1px solid #d0cecf;
  font-weight: bold;
  height: 31px;
  line-height: 31px;
  padding: 0 !important;
  position: relative;
}

.attendance-log-item-data {
  text-align: center;
  height: 29px;
  line-height: 29px;
  padding: 0 !important;
  background: none repeat scroll 0 0 #f8f8f8;
}

  .attendance-log-item-data td {
    border-bottom: 1px solid #fff;
    text-align: center;
  }

  .attendance-log-item-data .moneytext {
    color: #f60;
    font-weight: bold;
    text-align: right;
    padding-right: 15px;
  }

.attendance-log-table .even {
  background: none repeat scroll 0 0 #edecec;
}

.attendance-log-table .hover {
  color: #000;
  background: url(images/saffh2.jpg) 0 -55px repeat-x !important;
}

.attendance-log-item-data .input-radio {
  margin: 0;
}

.attendance-log-table .leftindent {
  text-align: left;
  text-indent: 10px;
}

.attendance-log-item-name th {
  white-space: nowrap;
  overflow: hidden;
}

.attendance-log-item-name .itemname-0 {
  width: 50px;
}

.attendance-log-item-name .itemname-1 {
  width: 90px;
}

.attendance-log-item-name .itemname-2 {
  width: 90px;
}

.attendance-log-item-name .itemname-3 {
  width: 90px;
}

.attendance-log-item-name .itemname-4 {
  width: 180px;
}

.attendance-log-item-name .itemname-5 {
  width: 90px;
}

.attendance-log-item-name .itemname-6 {
  width: 150px;
}

.attendance-log-item-name .itemname-7 {
  width: 120px;
}

.attendance-log-operate .border-middle {
  width: 956px;
}

.attendance-log-operate .attendance-log-box {
  width: 100%;
  padding: 10px 0;
  background-color: #EEE;
}

.attendance-log-operate .line {
  margin-left: 52px;
  padding: 0;
  width: 800px;
  height: 30px;
  float: none;
}

  .attendance-log-operate .line .row {
    display: inline;
    float: left;
    width: 385px;
  }

.attendance-log-operate .attendance-log-type select {
  border: medium none;
  cursor: pointer;
  margin: 5px 0 0 5px;
}

.attendance-log-operate .dateymd {
  float: left;
  left: -24px;
  position: relative;
  top: 7px;
}

.attendance-log-operate .datehsm {
  float: left;
  left: -24px;
  position: relative;
  top: 7px;
}

.attendance-log-operate .attendance-log-form-box {
  height: 60px;
  text-align: center;
  width: 100%;
}

.attendance-log-operate .attendance-log-type {
  position: relative;
}

.attendance-log-operate .text-5 input {
  padding-top: 7px !important;
}

.attendance-log-operate .text-5 .outbox-tip {
  line-height: 28px;
}
/*简历浏览记录*/
.resume-row.open .resume-detail {
  display: block;
  height: 300px;
  overflow-y: scroll;
  padding: 5px 0;
}

  .resume-row.open .resume-detail b {
    color: #f00;
  }

.resume-record-list .item-job {
  width: 100px !important;
}

.resume-record-list .item-industry {
  width: 100px !important;
}

.resume-record-list .item-name-2 .time {
  width: 75px !important;
}

.resume-record-list .item-staff {
  width: 60px;
  padding-left: 10px;
}

.resume-record-list .item-company {
  width: 70px;
  text-indent: 5px;
}

.resume-record-list .item-detail {
  width: 45px !important;
  margin: 0 !important;
  padding: 0 !important;
  text-align: right !important;
}

.resume-record-list .item-time {
  width: 100px;
  margin: 0;
}
/*猎头在线资源*/
.headhunter-staff-icon {
  background-image: url(images/headhunter-staff-icon.gif);
  background-repeat: no-repeat;
  display: block;
  overflow: hidden;
  width: 16px;
  height: 16px;
  float: left;
  margin: 6px 4px 0 0;
}

.headhunter-staff-icon-1 {
  background-position: 0 0;
}

.headhunter-staff-icon-2 {
  background-position: 0 -16px;
}

.headhunter-staff-icon-3 {
  background-position: 0 -32px;
}

.headhunter-staff-icon-4 {
  background-position: 0 -48px;
}

.headhunter-staff-icon-5 {
  background-position: 0 -64px;
}

.headhunter-staff-icon-1.headhunter-staff-icon-un {
  background-position: 0 -80px;
}

.headhunter-staff-icon-2.headhunter-staff-icon-un {
  background-position: 0 -96px;
}

.headhunter-staff-icon-3.headhunter-staff-icon-un {
  background-position: 0 -112px;
}

.headhunter-staff-icon-4.headhunter-staff-icon-un {
  background-position: 0 -128px;
}

.headhunter-staff-set {
  background-image: url("images/headhunter-staff-icon.gif");
  background-position: 0 -160px;
  background-repeat: no-repeat;
  display: block;
  float: right;
  height: 16px;
  margin: 4px 10px 4px 0;
  overflow: hidden;
  width: 16px;
  cursor: pointer;
}

.colleague-list .headhunter-staff-name {
  text-align: center;
}

.colleague-list .headhunter-staff-name-link {
  background-image: url(images/headhunter-staff-icon.gif);
  background-repeat: no-repeat;
  background-position: 0 -145px;
  padding-left: 20px;
  vertical-align: middle;
  width: 20px;
  white-space: nowrap;
  overflow: hidden;
  height: 16px;
  line-height: 16px;
}

.even {
  background: none repeat scroll 0 0 #edecec;
}
/*谁看过我*/
.staff-browse-data-box {
  width: 100%;
}

  .staff-browse-data-box .staff-browse-data-item {
    width: 50px;
    float: left;
    margin: 5px 8px;
    height: 86px;
    overflow: hidden;
    _margin: 5px 4px;
  }

  .staff-browse-data-box .staff-browse-data-item-link {
    width: 50px;
    color: #26709A;
    line-height: 16px;
  }

    .staff-browse-data-box .staff-browse-data-item-link img {
      -ms-border-radius: 4px 4px 4px 4px;
      border-radius: 4px 4px 4px 4px;
      display: block;
      background-image: url(/css/images/default_small.gif);
    }

    .staff-browse-data-box .staff-browse-data-item-link:hover .staff-browse-data-item-name {
      text-decoration: underline;
    }

  .staff-browse-data-box .staff-browse-data-item-time {
    color: #9B9B9B;
    text-decoration: none;
    font-size: 11px;
  }

.visitorBtn {
  width: 188px;
  margin-left: 10px;
}

  .visitorBtn a {
    float: none;
    display: inline;
  }

    .visitorBtn a:hover {
      text-decoration: underline;
    }

  .visitorBtn .prevpage, .visitorBtn .currentpage {
    margin-right: 10px;
  }

  .visitorBtn .disable, .visitorBtn .disable:hover, .visitorBtn .currentpage {
    color: #9B9B9B;
    text-decoration: none;
  }

  .visitorBtn .prevpage, .visitorBtn .nextpage {
    cursor: pointer;
  }

    .visitorBtn .prevpage.disable, .visitorBtn .nextpage.disable {
      cursor: default;
    }

.staff-browse-data-show {
  height: 288px;
  overflow: hidden;
}
/*个人主页*/
.attention-box .staff-browse-data-box .staff-browse-data-item {
  margin: 5px 9px;
}

.attention-box .staff-browse-data-box .staff-browse-data-item-name {
  line-height: 16px;
  display: block;
}
/*录入猎聘网简历*/
.add-other-resume-box {
  width: 100%;
  margin: 10px 0;
}

  .add-other-resume-box .add-resume-tip-box {
    margin-right: 30px;
    text-align: right;
  }

  .add-other-resume-box .add-resume-line {
    width: 100%;
    margin: 10px 0;
  }

  .add-other-resume-box .add-resume-tip-box a {
    border-bottom: dashed 1px #7FA0CD;
    color: #21639C;
  }

  .add-other-resume-box .add-resume-content .key {
    width: 60px;
    float: left;
  }

  .add-other-resume-box .add-resume-content .value {
    width: 500px;
    float: left;
  }

  .add-other-resume-box .add-resume-line .textarea {
    background: none repeat scroll 0 0 #fff;
    border: 1px solid #d3d3d3;
    font-size: 12px;
    height: 200px;
    line-height: 18px;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 5px;
    width: 500px;
  }

.focus:focus {
  outline: 1px solid #1FC8F9;
}

.add-other-resume-box .add-resume-line .text {
  background: none repeat scroll 0 0 #fff;
  border: 1px solid #d3d3d3;
  font-size: 12px;
  line-height: 18px;
  padding: 5px;
  width: 205px;
}

.add-other-resume-box .add-resume-line .number-box {
  float: left;
  position: relative;
}

  .add-other-resume-box .add-resume-line .number-box .number-tip {
    position: absolute;
    top: 6px;
    left: 195px;
  }

  .add-other-resume-box .add-resume-line .number-box .number-tip-text {
    color: red;
    margin: 0 5px;
  }

  .add-other-resume-box .add-resume-line .number-box.none a {
    display: none;
  }

.add-other-resume-box .add-resume-line .rows {
  width: 50%;
  float: left;
}

  .add-other-resume-box .add-resume-line .rows .key {
    width: 60px;
    float: left;
  }

  .add-other-resume-box .add-resume-line .rows .value {
    width: 220px;
    float: left;
    text-align: left;
  }

.add-other-resume-box .add-resume-line .valid-box {
  position: relative;
}

  .add-other-resume-box .add-resume-line .valid-box .valid-icon-box {
    top: 6px;
  }

.add-other-resume-box .hits-tip-box {
  text-align: left;
  text-indent: 60px;
}

.add-other-resume-box .hits-tip {
  color: red;
}

.add-other-resume-box .addresume-other-explain-box {
  float: left;
  width: 50%;
}

.add-other-resume-box .addresume-other-shortcut-box {
  float: left;
  width: 50%;
}

.add-other-resume-box .rfrc-1 span {
  float: left;
  line-height: 16px;
  cursor: pointer;
  margin: 0 2px;
}

.add-other-resume-box .rfrc-1 {
  padding-left: 60px;
}
/*弹窗内模块*/
.Rs_MsgBox_Layer_Wrap .DLC-box-1 {
  background: none repeat scroll 0 0 #fff;
  margin: 0 5px;
  overflow: hidden;
}

.Rs_MsgBox_Layer_Wrap .DLC-STATUS-1 {
  background: url(/static/images/dialog-status.icon.gif) no-repeat 10% center;
  background-position: 0 0;
}

.Rs_MsgBox_Layer_Wrap .DLC-STATUS-2 {
  background: url(/static/images/dialog-status.icon.gif) no-repeat 10% center;
  background-position: 0 -32px;
}

.Rs_MsgBox_Layer_Wrap .DLC-STATUS-3 {
  background: url(/static/images/dialog-status.icon.gif) no-repeat 10% center;
  background-position: 0 -64px;
}

.Rs_MsgBox_Layer_Wrap .DLC-SIZE-1 {
  width: 300px;
  margin: 20px auto;
  max-height: 150px;
  overflow-y: auto;
}

.Rs_MsgBox_Layer_Wrap .DLC-SIZE-2 {
  width: 400px;
  margin: 20px auto;
}

.Rs_MsgBox_Layer_Wrap .DLC-SIZE-2_5 {
  width: 450px;
  margin: 20px auto;
}

.Rs_MsgBox_Layer_Wrap .DLC-SIZE-3 {
  width: 500px;
  margin: 20px auto;
}

.Rs_MsgBox_Layer_Wrap .DLC-ITEM-1 {
  float: left;
  width: 33%;
}

  .Rs_MsgBox_Layer_Wrap .DLC-ITEM-1.debt-item-1 {
    float: left;
    width: 50%;
  }

  .Rs_MsgBox_Layer_Wrap .DLC-ITEM-1 a {
    font-weight: 700;
    text-align: center;
  }

.Rs_MsgBox_Layer_Wrap .DLC-box-1 .DLC-box-title {
  height: 32px;
  line-height: 32px;
  margin: 0 auto;
  overflow: hidden;
}

.Rs_MsgBox_Layer_Wrap .DLC-box-1 .DLC-box-title-text {
  color: #3b5998;
  font-size: 18px;
}

.Rs_MsgBox_Layer_Wrap .DLC-box-1 .DLC-box-content-box {
  margin: 20px auto;
}

.Rs_MsgBox_Layer_Wrap .DLC-box-1 .DLC-box-content-box-Tip {
  text-align: left;
}

.Rs_MsgBox_Layer_Wrap .DLC-box-1 .DLC-box-resumeid {
  margin-right: 15px;
}

.Rs_MsgBox_Layer_Wrap .DLC-box-1 .DLC-box-resume-detail {
  text-decoration: underline;
  margin-left: 15px;
}

  .Rs_MsgBox_Layer_Wrap .DLC-box-1 .DLC-box-resume-detail:hover {
    color: #4b72b0;
  }

.Rs_MsgBox_Layer_Wrap .DLC-box-1 .DLC-box-resume-status {
  text-align: center;
}

.Rs_MsgBox_Layer_Wrap .DLC-box-1 .tb-conflictcandidates {
  width: 100%;
  table-layout: fixed;
  text-align: left;
}

  .Rs_MsgBox_Layer_Wrap .DLC-box-1 .tb-conflictcandidates .maxwrap {
    display: block;
    font-weight: lighter;
    list-style-type: none;
    overflow: hidden;
    padding-right: 5px;
    white-space: nowrap;
    width: 90%;
  }

  .Rs_MsgBox_Layer_Wrap .DLC-box-1 .tb-conflictcandidates .tb-candidates-item-1 {
    width: 30%;
  }

  .Rs_MsgBox_Layer_Wrap .DLC-box-1 .tb-conflictcandidates .tb-candidates-item-2 {
    width: 30%;
  }

  .Rs_MsgBox_Layer_Wrap .DLC-box-1 .tb-conflictcandidates .tb-candidates-item-3 {
    width: 40%;
  }
/*提成申请模块弹窗*/
.Rs_MsgBox_Layer_Wrap .DLC-box-search {
  width: 100%;
}

.Rs_MsgBox_Layer_Wrap .search-keyword-box {
  background: none repeat scroll 0 0 #EDECEC;
  height: 30px;
  padding: 10px 30px;
}

.Rs_MsgBox_Layer_Wrap .key-word-v1 {
  background-color: #fcfcfc;
  border: 1px solid #b7b7b7;
  -webkit-box-shadow: 1px 1px 1px #d3d3d3 inset;
  -ms-box-shadow: 1px 1px 1px #d3d3d3 inset;
  box-shadow: 1px 1px 1px #d3d3d3 inset;
  height: 26px;
  line-height: 26px;
  overflow: hidden;
  padding-left: 7px;
  width: 200px;
  float: left;
  margin-right: 10px;
}

.Rs_MsgBox_Layer_Wrap .data-list-title {
  background: none repeat scroll 0 0 #e1e1e1;
  font-weight: bold;
  overflow: hidden;
  width: 100%;
}

.Rs_MsgBox_Layer_Wrap .select-income-money .item-0 {
  width: 50px;
}

.Rs_MsgBox_Layer_Wrap .select-income-money .item-1 {
  width: 110px;
  text-align: left;
  text-indent: 5px;
}

.Rs_MsgBox_Layer_Wrap .select-income-money .item-2 {
  width: 130px;
  text-align: left;
  text-indent: 5px;
}

.Rs_MsgBox_Layer_Wrap .select-income-money .item-3 {
  width: 90px;
  text-align: left;
  text-indent: 5px;
}

.Rs_MsgBox_Layer_Wrap .select-income-money .item-4 {
  width: 70px;
  text-align: left;
  text-indent: 5px;
}

.Rs_MsgBox_Layer_Wrap .select-income-money .item-5 {
  width: 70px;
  text-align: left;
  text-indent: 5px;
}

.Rs_MsgBox_Layer_Wrap .select-income-money .item, .Rs_MsgBox_Layer_Wrap .select-candidate .item {
  line-height: 29px;
}

.Rs_MsgBox_Layer_Wrap .select-collection-details .item-1 {
    width: 110px;
    text-align: left;
    text-indent: 5px;
}

.Rs_MsgBox_Layer_Wrap .select-collection-details .item-2 {
    width: 100px;
    text-align: left;
    text-indent: 5px;
}

.Rs_MsgBox_Layer_Wrap .select-collection-details .item-3 {
    width: 80px;
    text-align: left;
    text-indent: 5px;
}

.Rs_MsgBox_Layer_Wrap .select-collection-details .item-4 {
    width: 85px;
    text-align: left;
    text-indent: 5px;
}
.Rs_MsgBox_Layer_Wrap .select-collection-details .item-5 {
    width: 70px;
    text-align: left;
    text-indent: 5px;
}
.Rs_MsgBox_Layer_Wrap .select-collection-details .item-6 {
    width: 170px;
    text-align: left;
    text-indent: 5px;
}
.Rs_MsgBox_Layer_Wrap .select-candidate .item-0 {
  width: 50px;
}

.Rs_MsgBox_Layer_Wrap .select-candidate .item-1 {
  width: 70px;
  text-align: left;
  text-indent: 5px;
}

.Rs_MsgBox_Layer_Wrap .select-candidate .item-2 {
  width: 70px;
  text-align: left;
  text-indent: 5px;
}

.Rs_MsgBox_Layer_Wrap .select-candidate .item-3 {
  width: 120px;
  text-align: left;
  text-indent: 5px;
}

.Rs_MsgBox_Layer_Wrap .select-candidate .item-4 {
  width: 120px;
  text-align: left;
  text-indent: 5px;
}

.Rs_MsgBox_Layer_Wrap .select-candidate .item-5 {
  width: 90px;
}

.Rs_MsgBox_Layer_Wrap .select-income-money .data-list-content li {
  float: left;
  overflow: hidden;
  width: 100%;
}

.Rs_MsgBox_Layer_Wrap .submit-box {
  width: 100%;
  margin: 10px 0;
}

  .Rs_MsgBox_Layer_Wrap .submit-box .select-candidate-submit, .Rs_MsgBox_Layer_Wrap .submit-box .select-income-submit {
    padding: 0 20px;
    float: left;
    margin-left: 20px;
  }
/*提成申请查看详细弹窗*/
.distribute-dialog-detail-box {
  width: 100%;
  text-align: left;
}

  .distribute-dialog-detail-box .distribute-title-box {
    height: 27px;
    text-indent: 22px;
    background-color: #D8E3F3;
    margin-bottom: 1px;
  }

    .distribute-dialog-detail-box .distribute-title-box .title {
      line-height: 27px;
      color: #2367A1;
      font-weight: 700;
    }

  .distribute-dialog-detail-box .distribute-content-box {
    width: 100%;
  }

  .distribute-dialog-detail-box .distribute-child-title-box {
    background-color: #E7E7E7;
    height: 27px;
    text-indent: 22px;
  }

    .distribute-dialog-detail-box .distribute-child-title-box .title {
      line-height: 27px;
      color: #5A5959;
      font-weight: 700;
    }

  .distribute-dialog-detail-box .distribute-basic-information-box {
    height: 40px;
  }

  .distribute-dialog-detail-box .basic-information-item {
    width: 25%;
    float: left;
    text-indent: 7px;
    line-height: 40px;
  }

    .distribute-dialog-detail-box .basic-information-item b {
      max-width: 70px;
      overflow: hidden;
      white-space: nowrap;
      display: inline-block;
      vertical-align: middle;
    }

    .distribute-dialog-detail-box .basic-information-item span {
      display: inline-block;
      vertical-align: middle;
    }

      .distribute-dialog-detail-box .basic-information-item span.job-candidate {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        width: 68px;
      }

    .distribute-dialog-detail-box .basic-information-item .highfont {
      color: #f60 !important;
    }

    .distribute-dialog-detail-box .basic-information-item a:hover {
      text-decoration: underline;
      color: #21639C;
    }

#Layout_distributeDetailModal .modal-dialog {
  width: 800px;
}

.distribute-dialog-detail-box .tb-detail-distribute {
  table-layout: fixed;
  text-align: left;
  width: 100%;
  border: solid 1px #C2DBFF;
  border-collapse: collapse;
}

  .distribute-dialog-detail-box .tb-detail-distribute td, .distribute-dialog-detail-box .tb-detail-distribute th {
    border-bottom: 1px solid #b3ccf2;
    border-right: 1px solid #b3ccf2;
    height: 29px;
  }

  .distribute-dialog-detail-box .tb-detail-distribute th {
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
  }

  .distribute-dialog-detail-box .tb-detail-distribute td {
    text-indent: 17px;
    white-space: nowrap;
    overflow: hidden;
  }

.select-income-money .data-list-content, .select-candidate .data-list-content {
  width: 520px;
}
/*提成申请2013-06-25 11:50*/
.push-money-box .push-money-required .border-middle {
  width: 956px;
}

.push-money-box .data-content {
  background-color: #EEEEEE;
  width: 100%;
  line-height: 30px;
  padding: 10px 0;
}

.push-money-box .data-pos-box {
  margin: 20px;
  line-height: 30px;
}

.push-money-box .key {
  width: 75px;
  float: left;
  text-align: right;
  font-weight: 700;
  cursor: text;
  white-space: nowrap;
  overflow: hidden;
}

.push-money-box .value {
  width: 160px;
  float: left;
  margin-left: 5px;
  height: 30px;
}

.push-money-box .other {
  width: 140px;
}

.select-box-v1 {
  width: 135px;
  height: 27px;
  border: solid 1px #B7B7B7;
  cursor: pointer;
  -webkit-box-shadow: 1px 1px 1px #D3D3D3 inset;
  -ms-box-shadow: 1px 1px 1px #D3D3D3 inset;
  box-shadow: 1px 1px 1px #D3D3D3 inset;
  background: url(/static/images/select-icon-v1.jpg) no-repeat 95% 50% #FCFCFC;
  padding-left: 7px;
  overflow: hidden;
}

.push-money-box .rows {
  padding: 6px 0;
}

.select-v1-box {
  position: relative;
  width: 120px;
}

.overshow {
  overflow: visible !important;
}

.select-user {
  width: 135px;
  height: 27px;
  border: solid 1px #B7B7B7;
  cursor: pointer;
  -webkit-box-shadow: 1px 1px 1px #D3D3D3 inset;
  -ms-box-shadow: 1px 1px 1px #D3D3D3 inset;
  box-shadow: 1px 1px 1px #D3D3D3 inset;
  background-color: #FCFCFC;
  padding-left: 7px;
  position: relative;
}

.select-icon {
  background: url(/static/images/select-icon-v1.jpg) no-repeat;
  width: 16px;
  height: 13px;
  display: block;
  right: 7px;
  position: absolute;
  top: 7px;
}

.select-user .outbox-item-list li {
  margin: 2px 0 0 0;
}

.distribute-box {
  border: solid 1px #B8CFF1;
  width: 926px;
  margin: 8px auto;
  background-color: #FFF;
}

.distribute-title-box {
  height: 27px;
  width: 100%;
  background-color: #D8E3F3;
  text-indent: 22px;
  color: #2367A1;
  font-weight: 700;
  line-height: 27px;
  position: relative;
  overflow: hidden;
}

  .distribute-title-box .distribute-operate {
    position: absolute;
    right: 30px;
    top: 0;
  }

  .distribute-title-box .distribute-clear, .distribute-title-box .distribute-del {
    color: #2367A1;
    margin: 0 10px;
  }

  .distribute-title-box .distribute-status {
    display: block;
    width: 11px;
    height: 11px;
    position: absolute;
    top: 8px;
    right: 10px;
    overflow: hidden;
  }

  .distribute-title-box .status-hide {
    background: url(/static/images/distribute-status-icon.jpg) no-repeat;
    background-position: 0 0;
  }

  .distribute-title-box .status-show {
    background: url(/static/images/distribute-status-icon.jpg) no-repeat;
    background-position: 0 -11px;
  }

.distribute-box .distribute-content-child-title-box {
  height: 25px;
  line-height: 25px;
  background-color: #E7E7E7;
  width: 100%;
  margin: 1px 0 0 0;
  text-indent: 22px;
  color: #5A5959;
  white-space: nowrap;
  overflow: hidden;
}

.distribute-box .distribute-content-child-content-box {
  background-color: #fff;
  width: 926px;
}

.distribute-box .basics {
  padding: 20px 0;
}

.push-money-box .distribute-money {
  width: 135px;
  height: 26px;
  border: solid 1px #B7B7B7;
  -webkit-box-shadow: 1px 1px 1px #D3D3D3 inset;
  -ms-box-shadow: 1px 1px 1px #D3D3D3 inset;
  box-shadow: 1px 1px 1px #D3D3D3 inset;
  padding-left: 7px;
  overflow: hidden;
  background: url(/static/images/distribute-money-unit-icon.jpg) no-repeat 95% 50% #FCFCFC;
  line-height: 26px;
  ime-mode: disabled;
}

.push-money-box input.distribute-money {
  vertical-align: top;
}

.tb-distribute {
  table-layout: fixed;
  text-align: left;
  width: 100%;
}

  .tb-distribute .maxwrap {
    display: block;
    font-weight: lighter;
    list-style-type: none;
    overflow: hidden;
    padding-right: 5px;
    white-space: nowrap;
    width: 95%;
  }

.tb-distribute-box {
  margin: 10px 20px;
}

.tb-distribute th {
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
}

.tb-distribute .item-1 {
  width: 75px;
}

.tb-distribute .item-2 {
  width: 190px;
}

.tb-distribute .item-3 {
  width: 190px;
}

.tb-distribute .item-4 {
  width: 160px;
}

.tb-distribute .item-5 {
  width: 90px;
}

.tb-distribute .item-6 {
  width: 90px;
}

.tb-distribute .distribute-type-name {
  cursor: text;
  font-weight: 700;
  color: #f60;
  white-space: nowrap;
  overflow: hidden;
  display: block;
  margin-right: 3px;
}

.tb-distribute .distribute-type-staff .select-user {
  width: 160px;
}

.tb-distribute .distribute-type-company .select-v1-box {
  width: 160px;
}

.tb-distribute .distribute-type-ratio .select-v1-box {
  width: 110px;
}

.tb-distribute td {
  padding: 7px 0;
}

.distribute-box .total {
  border-top: dotted 1px #696969;
  height: 50px;
  line-height: 50px;
}

.distribute-box .total-money-box {
  float: right;
}

.distribute-box .total-key {
  color: #707070;
  font-size: 14px;
}

.distribute-box .total-value {
  color: #FF5A00;
  font-size: 14px;
  margin-right: 40px;
  font-weight: 700;
}

.distribute-box .valid-box {
  position: relative;
}

.distribute-box .valid-total-box {
  left: 35px;
  margin-left: 5px;
  position: absolute;
  top: 0;
}

.distribute-box .summary-box {
  margin: 15px 22px;
  line-height: 25px;
  width: 882px;
}

  .distribute-box .summary-box .item-summary {
    width: 145px;
    float: left;
  }

    .distribute-box .summary-box .item-summary a {
      font-weight: 700;
      color: #f60;
    }

  .distribute-box .summary-box .moneyvalue {
    color: #FF5A00;
    font-weight: 700;
  }

.add-distribute-box {
  border: 1px solid #b8cff1;
  margin: 8px auto;
  width: 926px;
  font-weight: 700;
  text-indent: 27px;
  color: #2367a1;
  cursor: pointer;
  background: url("images/plus__orange.png") no-repeat scroll 6px 7px #D8E3F3;
}

  .add-distribute-box .tips {
    color: #f00;
    font-size: 12px;
  }

  .add-distribute-box .tip-add-btn {
    float: right;
    margin-right: 10px;
  }

    .add-distribute-box .tip-add-btn .next-number {
      color: red;
      font-weight: 700;
    }

.remark-content {
  width: 100%;
  width: 760px;
  margin: 0 auto;
}

.distribute-submit-box {
  background-color: #eee;
  height: 50px;
  text-align: center;
}

  .distribute-submit-box .submit, .distribute-submit-box .verify {
    margin: 0 10px;
    font-weight: 700;
  }

.push-money-box .outbox input {
  padding-left: 0;
}

.push-money-box .outbox-tip {
  margin-left: 0;
}
/*v2列表方案*/
.data-list-name {
  overflow: hidden;
  width: 958px;
  background: url("images/list-item-2.gif") repeat-x scroll 0 0 transparent;
  border: 1px solid #d0cecf;
  font-weight: bold;
  height: 29px;
  line-height: 29px;
}

.data-list-content {
  overflow: hidden;
  width: 958px;
  line-height: 29px;
}

.data-list-content-ul {
  width: 958px;
  line-height: 29px;
}

  .data-list-content-ul .odd, .data-list-content-ul .even {
    width: 100%;
    height: 29px;
    line-height: 29px;
    float: left;
  }

.data-list {
  width: 100%;
}
/*提成管理*/
.financial-distribution-my-pushmoney .item-1 {
  width: 70px;
  padding-left: 20px;
}

.financial-distribution-my-pushmoney .item-2 {
  width: 70px;
}

.financial-distribution-my-pushmoney .item-3 {
  width: 70px;
}

.financial-distribution-my-pushmoney .item-4 {
  width: 150px;
}

.financial-distribution-my-pushmoney .item-5 {
  width: 80px;
  text-align: center;
}

.financial-distribution-my-pushmoney .item-6 {
  width: 70px;
}

.financial-distribution-my-pushmoney .item-7 {
  width: 40px;
}

.financial-distribution-my-pushmoney .item-8 {
  width: 40px;
}

.financial-distribution-my-pushmoney .item-9 {
  width: 70px;
}

.financial-distribution-my-pushmoney .item-10 {
  width: 70px;
}

.financial-distribution-my-pushmoney .item-11 {
  width: 70px;
  font-weight: 700;
}

.financial-distribution-my-pushmoney .item-12 {
  width: 60px;
}

.financial-distribution-my-pushmoney .item-13 {
  width: 70px;
}

.financial-distribution-icon-1 {
  background: url("/static/images/financial-distribution-icon.gif") no-repeat scroll 0 0 transparent;
  display: block;
  height: 16px;
  line-height: 16px;
  margin-top: 6px;
  padding-left: 20px;
}

.financial-distribution-icon-2 {
  background: url("/static/images/financial-distribution-icon.gif") no-repeat scroll 0 0 transparent;
  display: block;
  height: 16px;
  width: 16px;
  margin-top: 6px;
}

.financial-distribution-icon-3 {
  background: url("/static/images/financial-distribution-icon.gif") no-repeat scroll 0 -64px transparent;
  display: block;
  height: 16px;
  width: 16px;
  margin-top: 6px;
}

.financial-distribution-performances .item-1 {
  width: 70px;
  padding-left: 20px;
}

.financial-distribution-performances .item-2 {
  width: 70px;
}

.financial-distribution-performances .item-3 {
  width: 150px;
}

.financial-distribution-performances .item-4 {
  width: 70px;
}

.financial-distribution-performances .item-5 {
  width: 70px;
}

.financial-distribution-performances .item-6 {
  width: 70px;
}

.financial-distribution-performances .item-7 {
  width: 70px;
}

.financial-distribution-performances .item-8 {
  width: 70px;
}

.financial-distribution-performances .item-9 {
  width: 70px;
}

.financial-distribution-performances .item-10 {
  width: 70px;
}

.financial-distribution-performances .item-11 {
  width: 70px;
}

.financial-distribution-performances .item-12 {
  width: 80px;
}

.financial-distribution-performances .text {
  font-weight: 700;
}

.financial-managedistributions .data-list-content {
  overflow: visible;
}

.financial-managedistributions .item {
  display: block;
  margin-right: 3px;
}

.financial-managedistributions .item-0 {
  width: 50px;
  text-align: center;
}

.financial-managedistributions .item-1 {
  width: 70px;
}

.financial-managedistributions .item-2 {
  width: 70px;
}

.financial-managedistributions .item-3 {
  width: 60px;
}

.financial-managedistributions .item-4 {
  width: 120px;
}

.financial-managedistributions .item-5 {
  width: 40px;
}

.financial-managedistributions .item-6 {
  width: 70px;
}

.financial-managedistributions .item-7 {
  width: 40px;
}

.financial-managedistributions .item-8 {
  width: 70px;
}

.financial-managedistributions .item-9 {
  width: 70px;
  overflow: visible;
}

.financial-managedistributions .data-list-name .item-9 {
  text-align: center;
}

.financial-managedistributions .item-10 {
  width: 70px;
  margin-left: 10px;
}

.financial-managedistributions .item-11 {
  width: 80px;
}

.financial-managedistributions .item-12 {
  width: 40px;
}

.financial-managedistributions .item-13 {
  width: 40px;
}

.financial-managedistributions .item-14 {
  width: 50px;
}

.financial-managedistributions .take-notes {
  background: none repeat scroll 0 0 #fff;
  border-left: 1px solid #eee;
  border-right: 1px solid #eee;
  border-top: 1px solid #bfbfbf;
  cursor: pointer;
  height: 18px;
  line-height: 18px;
  margin: 2px auto;
  text-align: center;
  width: 30px;
}

  .financial-managedistributions .take-notes strong {
    color: red;
  }

.financial-managedistributions .input-radio {
  margin-top: 0;
}

.financial-managedistributions .approval-status-box {
  position: relative;
}

  .financial-managedistributions .approval-status-box.show {
    border: solid 1px #DA6B00;
    background-color: #FFF6E4;
    width: 68px;
    height: 25px;
    margin: 2px 0;
  }

.financial-managedistributions .approval-status-dialog {
  display: none;
}

.financial-managedistributions .approval-status-box.show .approval-status-dialog {
  display: block;
  position: absolute;
  line-height: 25px;
  background-color: #FFF6E4;
  width: 90px;
  border: solid 1px #DA6B00;
  z-index: 5;
}
.financial-managedistributions .approval-status-box.show .approval-status-dialog2 {
  display: block;
  position: fixed;
  line-height: 25px;
  background-color: #FFF6E4;
  width: 90px;
  border: solid 1px #DA6B00;
  z-index: 5;
}

.financial-managedistributions .approval-status-box .approval-status-text {
  padding-left: 10px;
  display: block;
  margin: 1px 0 0 1px;
  color: #DF730A;
  font-weight: 700;
  cursor: pointer;
}

  .financial-managedistributions .approval-status-box .approval-status-text .approval-status-icon {
    display: none;
  }

.financial-managedistributions .allowrole .approval-status-text .approval-status-icon {
  display: block;
}

.financial-managedistributions .approval-status-box.show .approval-status-text {
  display: block;
  margin: 0;
  height: 25px;
}

.financial-managedistributions .approval-status-box .approval-status-text .text {
  display: block;
  margin: 0;
  height: 36px;
  line-height: 36px;
  width: 59px;
}

.financial-managedistributions .approval-status-box.show .approval-status-text .text {
  display: block;
  margin: 0;
  height: 25px;
  line-height: 24px;
  width: 59px;
}

.financial-managedistributions .approval-status-box .approval-status-icon i {
  background: url("/static/images/financial-distribution-status-btn-icon.gif") no-repeat 0 0;
  height: 9px;
  width: 9px;
  display: block;
  position: absolute;
  top: 14px;
  right: 7px;
  -webkit-transition: transform 0.2s ease-in 0;
  -moz-transition: transform 0.2s ease-in 0;
  -ms-transition: transform 0.2s ease-in 0;
  -o-transition: transform 0.2s ease-in 0;
  transition: transform 0.2s ease-in 0;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}

.financial-managedistributions .approval-status-box.show .approval-status-icon i {
  background: url("/static/images/financial-distribution-status-btn-icon.gif") no-repeat 0 0;
  height: 9px;
  width: 9px;
  display: block;
  position: absolute;
  top: 8px;
  right: 7px;
  -webkit-transition: transform 0.2s ease-in 0;
  -moz-transition: transform 0.2s ease-in 0;
  -ms-transition: transform 0.2s ease-in 0;
  -o-transition: transform 0.2s ease-in 0;
  transition: transform 0.2s ease-in 0;
  -webkit-transform: rotate(0);
  -moz-transform: rotate(0);
  -ms-transform: rotate(0);
  -o-transform: rotate(0);
  transform: rotate(0);
}

.financial-managedistributions .approval-status-box.show .approval-status-item {
  height: 25px;
  padding-left: 10px;
  border-bottom: solid 1px #d0cecf;
  cursor: pointer;
}

  .financial-managedistributions .approval-status-box.show .approval-status-item.itemhover {
    background-color: #FFF1CC;
  }

  .financial-managedistributions .approval-status-box.show .approval-status-item.current {
    background-color: #FCE9C4;
  }

.financial-managedistributions .approval-status-box .approval-btn-list li {
  width: 50%;
  float: left;
  text-align: center;
}

.financial-managedistributions .approval-status-box .approval-btn-list {
  margin: 5px 0;
}

  .financial-managedistributions .approval-status-box .approval-btn-list li .btn-approval-confim {
    width: 38px;
    cursor: pointer;
  }

  .financial-managedistributions .approval-status-box .approval-btn-list li .btn-approval-cancel {
    width: 38px;
    cursor: pointer;
  }

.financial-distribution-performances .classified-search #selTeam {
  width: 233px !important;
}

.hover {
  background: url(images/saffh2.jpg) 0 -55px repeat-x !important;
}
/*首页实时提成 2013-7-16*/
.index .newesttime-distribution-box {
  margin-bottom: 3px;
  width: 548px;
  background-color: #EFF4F8;
  height: 25px;
  border: 1px solid #D2D2D2;
  overflow: hidden;
}

  .index .newesttime-distribution-box .newesttime-distribution-title {
    float: left;
    width: 17%;
    text-indent: 10px;
  }

    .index .newesttime-distribution-box .newesttime-distribution-title span {
      color: #153f7b;
      font-size: 12px;
      font-weight: 700;
      display: block;
      overflow: hidden;
      white-space: nowrap;
    }

  .index .newesttime-distribution-box .newesttime-distribution-more {
    color: #006cae;
    float: left;
    width: 10%;
    text-align: center;
  }

  .index .newesttime-distribution-box .newesttime-distribution-list {
    float: left;
    width: 73%;
    overflow: hidden;
  }

  .index .newesttime-distribution-box .newesttime-item-1 {
    float: left;
    width: 15%;
    overflow: hidden;
    white-space: nowrap;
  }

  .index .newesttime-distribution-box .newesttime-item-2 {
    float: left;
    width: 14%;
    overflow: hidden;
    height: 25px;
    white-space: nowrap;
  }

    .index .newesttime-distribution-box .newesttime-item-2 a {
      color: #f60;
      font-weight: 700;
      display: block;
      margin-right: 3px;
      overflow: hidden;
    }

      .index .newesttime-distribution-box .newesttime-item-2 a:hover {
        text-decoration: underline;
      }

  .index .newesttime-distribution-box .newesttime-item-3 {
    float: left;
    width: 15%;
    overflow: hidden;
    white-space: nowrap;
    margin-right: 1%;
  }

  .index .newesttime-distribution-box .newesttime-item-4 {
    float: left;
    width: 17%;
    overflow: hidden;
    white-space: nowrap;
  }

  .index .newesttime-distribution-box .newesttime-item-5 {
    float: left;
    width: 12%;
    overflow: hidden;
    white-space: nowrap;
  }

  .index .newesttime-distribution-box .newesttime-item-6 {
    float: left;
    width: 26%;
    overflow: hidden;
    white-space: nowrap;
    text-align: center;
  }
/*首页搜索栏*/
.index .data-search-box {
  width: 550px;
  height: 35px;
  margin: 2px 0;
  color: #666;
  font-size: 14px;
}

  .index .data-search-box input {
    color: #666;
    font-size: 14px;
  }

  .index .data-search-box .search-bar-box {
    width: 481px;
    height: 33px;
    border: solid 1px #C1C1C1;
    -ms-border-radius: 3px 0 0 3px;
    border-radius: 3px 0 0 3px;
    float: left;
  }

  .index .data-search-box .search-btn-box {
    width: 66px;
    height: 35px;
    float: left;
  }

    .index .data-search-box .search-btn-box .search-btn {
      background: url(/static/images/search-btn-bg-v1.jpg) no-repeat;
      height: 36px;
      width: 66px;
      border: none 0;
      cursor: pointer;
    }

  .index .data-search-box .search-bar-type-box {
    height: 33px;
    float: left;
    width: 65px;
    position: relative;
    border-right: solid 1px #c1c1c1;
  }

  .index .data-search-box .search-bar-text-box {
    height: 33px;
    float: left;
    width: 400px;
  }

    .index .data-search-box .search-bar-text-box .search-bar-text {
      width: 390px;
      border: none 0;
      line-height: 33px;
      height: 33px;
      font-size: 14px;
      padding-left: 5px;
    }

.search-box-v1 {
  background: url("/static/images/select-icon-v1.jpg") no-repeat scroll 95% 50% #fcfcfc;
  cursor: pointer;
  height: 35px;
  overflow: hidden;
  width: 65px;
}

.index .data-search-box .search-bar-type-box .selectbox-wrapper {
  top: 8px;
  zoom: 1;
}
/*列表的列头筛选*/
.selectfilter-box {
  position: relative;
  white-space: nowrap;
  z-index: 5;
  float: left;
  margin-top: 4px;
}

  .selectfilter-box.hover {
    z-index: 6;
  }

  .selectfilter-box .selectfilter-tip {
    float: left;
    padding: 0 20px 0 5px;
    border: 1px solid transparent;
    border-bottom: 0;
    height: 22px;
    line-height: 21px;
    cursor: pointer;
    background: url(images/close.gif) no-repeat 90% 50%;
    font-weight: 600;
  }

  .selectfilter-box .selectfilter-list {
    display: none;
    overflow: hidden;
    border: 1px solid #b7b7b7;
    padding: 5px 0;
    white-space: nowrap;
    *white-space: pre-wrap;
    *word-wrap: break-word;
    border-top: 0;
    position: absolute;
    left: 0;
    background: #ffffff;
    text-align: left;
    z-index: 6;
  }

  .selectfilter-box.hover .selectfilter-tip {
    background: url("images/open.gif") no-repeat scroll 90% 50% #fff;
    border: 1px solid #b7b7b7;
  }

  .selectfilter-box .selectfilter-ul .hover {
    background: #b7b7b7;
  }

  .selectfilter-box.hover .selectfilter-list {
    display: block;
  }

  .selectfilter-box.hover .selectfilter-li {
    text-indent: 5px;
    cursor: pointer;
    height: 25px;
    line-height: 25px;
  }

    .selectfilter-box.hover .selectfilter-li.itemhover {
      background-color: #EAE8E9;
    }

    .selectfilter-box.hover .selectfilter-li.selected {
      background-color: #d0cecf;
    }

.performance-data-box .live-data-item-name {
  background: url("images/list-item-2.jpg") repeat-x scroll 0 0 transparent;
  border: 1px solid #d0cecf;
  font-weight: bold;
  height: 31px;
  line-height: 31px;
  padding: 0 !important;
  position: relative;
}

.performance-huikuan-box .live-data-item-name .itemname-1 {
  /*width: 80px;*/
  width: 50px;
}

.performance-huikuan-box .live-data-item-name .itemname-2 {
  width: 90px;
}

.performance-huikuan-box .live-data-item-name .itemname-3 {
  width: 90px;
}

.performance-huikuan-box .live-data-item-name .itemname-4 {
  width: 90px;
}

.performance-huikuan-box .live-data-item-name .itemname-5 {
  width: 90px;
}

.performance-huikuan-box .live-data-item-name .itemname-6 {
  width: 90px;
}

.performance-huikuan-box .live-data-item-name .itemname-7 {
  /*width: 260px;*/
  width: 220px;
}

.performance-huikuan-box .live-data-table .amount-box {
  text-align: right;
}

  .performance-huikuan-box .live-data-table .amount-box .align-center {
    float: right;
  }

.performance-huikuan-box .live-data-item-name th, .performance-huikuan-box .live-data-item-data td {
  white-space: nowrap;
  overflow: hidden;
}

.performance-contract-box .live-data-item-name .itemname-1 {
  width: 50px;
  text-align: center;
}

.performance-contract-box .live-data-item-name .itemname-2 {
  width: 80px;
}

.performance-contract-box .live-data-item-name .itemname-3 {
  width: 90px;
}

.performance-contract-box .live-data-item-name .itemname-4 {
  width: 90px;
}

.performance-contract-box .live-data-item-name .itemname-5 {
  width: 90px;
}

.performance-contract-box .live-data-item-name .itemname-6 {
  width: 90px;
}

.performance-contract-box .live-data-item-name .itemname-7 {
  width: 10%;
}

.performance-contract-box .live-data-item-name .itemname-8 {
  width: 10%;
}

.performance-contract-box .live-data-item-name .itemname-9 {
  width: 12%;
}

.performance-contract-box .live-data-table .amount-box {
  text-align: right;
  padding-right: 20px;
}

.performance-contract-box .live-data-item-name th, .performance-huikuan-box .live-data-item-data td {
  white-space: nowrap;
  overflow: hidden;
}

.performance-bonuses-box .live-data-item-name .itemname-1 {
  width: 50px;
  text-align: center;
}

.performance-bonuses-box .live-data-item-name .itemname-2 {
  width: 74px;
}

.performance-bonuses-box .live-data-item-name .itemname-3 {
  width: 90px;
}

.performance-bonuses-box .live-data-item-name .itemname-4 {
  /*width: 90px;*/
  width: 65px;
}

.performance-bonuses-box .live-data-item-name .itemname-5 {
  /*width: 90px;*/
  width: 65px;
}

.performance-bonuses-box .live-data-item-name .itemname-6 {
  /*width: 90px;*/
  width: 65px;
}

.performance-bonuses-box .live-data-item-name .itemname-7 {
  width: 12%;
}

.performance-bonuses-box .live-data-item-name .itemname-8 {
  width: 12%;
}

.performance-bonuses-box .live-data-item-name .itemname-9 {
  /*width: 10%;*/
  width: 6%;
}

.performance-bonuses-box .live-data-table .amount-box {
  text-align: right;
  padding-right: 20px;
}

.performance-bonuses-box .live-data-item-name th, .performance-huikuan-box .live-data-item-data td {
  white-space: nowrap;
  overflow: hidden;
}

.align-center {
  float: none !important;
  text-align: center !important;
}
/*工资管理*/
.title-bar .border-middle {
  width: 956px;
}

.financial-managesalary .item-0 {
  width: 34px;
  text-align: center;
}

.financial-managesalary .item-1 {
  width: 50px;
}

.financial-managesalary .item-2 {
  width: 52px;
}

.financial-managesalary .item-3 {
  width: 88px;
  text-align: right;
}

.financial-managesalary .item-4 {
  width: 60px;
  padding-right: 5px;
  text-align: right;
}

.financial-managesalary .item-5 {
  width: 70px;
  text-align: center;
}

.financial-managesalary .item-6 {
  width: 60px;
  text-align: right;
}

.financial-managesalary .item-7 {
  width: 64px;
  text-align: right;
}

.financial-managesalary .item-8-1 {
  width: 60px;
  text-align: center;
}

.financial-managesalary .item-8-2 {
  width: 60px;
  text-align: center;
}

.financial-managesalary .item-8-3 {
  width: 60px;
  text-align: center;
}

.financial-managesalary .item-8 {
  width: 52px;
  text-align: right;
}

.financial-managesalary .item-9 {
  width: 70px;
  text-align: right;
}

.financial-managesalary .item-10 {
  width: 74px;
  text-align: right;
}

.financial-managesalary .item-11 {
  width: 60px;
  text-align: right;
  padding-right: 10px;
}

.financial-managesalary .item-12 {
  width: 45px;
}

.financial-managesalary .item-13 {
  width: 32px;
  text-align: center;
}

.financial-managesalary .data-list-content-ul .item-7 {
  cursor: pointer;
}

.financial-managesalary .take-notes {
  background: none repeat scroll 0 0 #fff;
  border-left: 1px solid #eee;
  border-right: 1px solid #eee;
  border-top: 1px solid #bfbfbf;
  cursor: pointer;
  height: 18px;
  line-height: 18px;
  margin: 3px auto;
  text-align: center;
  width: 30px;
}

  .financial-managesalary .take-notes strong {
    color: red;
  }

.financial-managesalary .input-radio {
  margin-top: 0;
}

.mtz-monthpicker-month:hover {
  background-color: #fff !important;
}
/*月选择控件样式*/
.monthrangpicker-box {
  float: left;
  margin-top: 1px;
  width: 400px;
}

.monthrangpicker-connect {
  margin: 0 5px;
  float: left;
}

.monthrangpicker-form {
  float: left;
  cursor: pointer;
}

.monthrangpicker-to {
  float: left;
  margin-right: 5px;
  cursor: pointer;
}

.mtz-monthpicker-month {
  cursor: pointer !important;
}
/*客户页显示历史到账信息*/
.data-display-box {
  width: 700px;
}

  .data-display-box .condition-box {
    border: 1px solid #b7b7b7;
    clear: both;
    display: block;
    padding: 5px;
    width: 690px;
    margin: 2px auto;
    background-color: #fff;
  }

  .data-display-box .data-display-prompt {
    background-color: #f3f2f2;
    width: 690px;
    height: 24px;
    overflow: hidden;
  }

  .data-display-box .show .data-display-prompt {
    overflow: visible;
    height: auto;
  }

  .data-display-box .data-display-title {
    color: #ff6a00;
    font-weight: bold;
    width: 80px;
    float: left;
    text-indent: 10px;
    cursor: pointer;
  }

  .data-display-box .data-display-list {
    width: 590px;
    float: left;
    overflow: hidden;
    height: 25px;
  }

  .data-display-box .data-display-item {
    height: 25px;
  }

  .data-display-box .dropdown-icon-box {
    width: 20px;
    float: left;
  }

  .data-display-box .dropdown-icon {
    background-image: url("images/risfondIcon.gif");
    background-repeat: no-repeat;
    display: block;
    height: 16px;
    width: 16px;
    background-position: -64px -400px;
    cursor: pointer;
    display: block;
    margin: 4px auto;
  }

    .data-display-box .dropdown-icon:hover {
      background-position: -64px -432px;
    }

  .data-display-box .show .dropdown-icon {
    background-position: -64px -416px;
  }

  .data-display-box .data-display-item-date {
    background: url("/static/images/date-icon.gif") no-repeat 0 50%;
    padding-left: 20px;
    width: 80px;
    display: block;
    float: left;
  }

  .data-display-box .item {
    margin: 0 3px;
    display: block;
    overflow: hidden;
    height: 25px;
  }

  .data-display-box .data-display-item-invoice {
    width: 70px;
  }

  .data-display-box .data-display-item-payment {
    width: 120px;
  }

  .data-display-box .data-display-item-staff {
    width: 120px;
  }

  .data-display-box .data-display-item-amount {
    width: 140px;
  }

  .data-display-box .data-display-item-jobtitle {
    width: 206px;
  }

  .data-display-box .show .data-display-list {
    overflow: visible;
    height: auto;
  }
/*简历短信发送记录*/
.sms-log-box {
  width: 178px;
  overflow: hidden;
}

  .sms-log-box .sms-log-item {
    height: 25px;
    border-bottom: 1px dashed #D2D2D2;
    overflow: hidden;
    position: relative;
  }

  .sms-log-box .sms-content-box {
    width: 150px;
    padding: 0 5px;
    -moz-word-break: break-all;
    -o-word-break: break-all;
    word-break: break-all;
    word-wrap: break-word;
    float: left;
  }

  .sms-log-box .sms-open-icon-box {
    width: 16px;
    height: 25px;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
    text-align: center;
  }

  .sms-log-box .open {
    height: auto;
    line-height: 18px;
    padding: 5px 0;
    background-color: #fafafa;
    overflow: visible;
    width: 178px;
  }

  .sms-log-box .sms-content-sign {
    color: #2DA2FF;
    text-align: right;
  }

  .sms-log-box .sms-content-date {
    cursor: pointer;
  }
/*ie6提示升级*/
.ie6upgrade-box {
  display: none;
}
/*推荐的年薪*/
.plate-box {
  width: 200px;
  height: 138px;
  background: url(images/plate_bg.gif) no-repeat;
  position: absolute;
  top: 10px;
  right: 30px;
}

  .plate-box .plate-title {
    position: absolute;
    top: 12px;
    left: 8px;
    line-height: 22px;
    color: red;
    font-weight: 700;
  }

  .plate-box .plate-operate {
    position: absolute;
    top: 17px;
    right: 5px;
    line-height: 22px;
  }

  .plate-box .plate-content {
    color: red;
    font-size: 26px;
    font-weight: 700;
    left: 5px;
    overflow: hidden;
    position: absolute;
    text-align: center;
    top: 60px;
    width: 190px;
  }

.resume-sms-box {
  width: 100%;
  height: 14px;
  line-height: 14px;
  margin: 10px 0;
}

  .resume-sms-box .resume-sms-status-box {
    width: 20px;
    float: left;
    padding-left: 30px;
  }

  .resume-sms-box .resume-sms-tips-box {
    width: 400px;
    float: left;
  }

  .resume-sms-box .resume-sms-tips-text {
    cursor: pointer;
  }

  .resume-sms-box .resume-sms-preview-content {
    cursor: pointer;
    margin-left: 10px;
  }

    .resume-sms-box .resume-sms-preview-content:hover {
      text-decoration: underline;
    }
/*客户页改版 2013-10-16*/
.client-information-v2 .client-left .title {
  width: 748px;
}

  .client-information-v2 .client-left .title .border-middle {
    width: 712px;
  }

.client-information-v2 .client-left .information-con {
  width: 684px;
}

.client-information-v2 .client-left .detail-con {
  width: 714px;
  border-top: 0 none;
  padding-left: 32px;
}

.client-information-v2 .client-left .detail {
  width: 696px;
}

  .client-information-v2 .client-left .detail li {
    width: 346px;
    line-height: 26px;
    overflow: hidden;
  }

    .client-information-v2 .client-left .detail li .name {
      width: 62px;
    }

    .client-information-v2 .client-left .detail li .value {
      width: 274px;
    }

    .client-information-v2 .client-left .detail li .f18 {
      font-size: 18px;
    }

    .client-information-v2 .client-left .detail li.maxwidth {
      width: 100%;
      margin: 0;
      padding: 0;
    }

      .client-information-v2 .client-left .detail li.maxwidth .value {
        width: 624px;
        overflow: hidden;
      }
    /*客户信息：多个联系人的qtip容器 start zsl*/
    .client-information-v2 .client-left .detail li .contactitems {
      display: inline-block;
      margin: 0 5px 0 0;
      cursor: default;
    }

.secondarycontactqtippanel {
  width: 360px;
  margin: 0;
  overflow: hidden;
}

  .secondarycontactqtippanel div {
    line-height: 22px;
    overflow: hidden;
  }

    .secondarycontactqtippanel div strong {
      float: left;
      padding: 0 5px 0 0;
      width: 55px;
      text-align: right;
      overflow: hidden;
      white-space: nowrap;
    }

    .secondarycontactqtippanel div span {
      float: left;
      padding: 0;
      width: 114px;
      height: 22px;
      text-align: left;
      overflow: hidden;
      white-space: nowrap;
    }
/*客户信息：多个联系人的qtip容器 end zsl*/
.client-information-v2 .client-left .data-list .listContent li {
  width: 750px;
}

.client-information-v2 .client-left .data-list .list-con-1 {
  width: 717px;
}

.client-information-v2 .client-left .feedback, .client-information-v2 .add-feedback {
  width: 748px;
}

.client-information-v2 .client-left .cooperation {
  width: 748px;
}

.client-information-v2 .client-left {
  width: 750px;
  float: left;
  margin-right: 4px;
}

.client-information-v2 .client-right {
  width: 200px;
  float: left;
}

  .client-information-v2 .client-right .title {
    width: 200px;
  }

    .client-information-v2 .client-right .title .border-middle {
      width: 196px;
    }

  .client-information-v2 .client-right .operate-function {
    background: none repeat scroll 0 0 #fff;
    border: 1px solid #b7b7b7;
    display: block;
    margin-top: 1px;
    overflow: hidden;
    padding-bottom: 7px;
    width: 197px;
  }

    .client-information-v2 .client-right .operate-function a {
      cursor: pointer;
      display: block;
      float: left;
      white-space: nowrap;
      overflow: hidden;
    }

    .client-information-v2 .client-right .operate-function li {
      height: 27px;
    }

    .client-information-v2 .client-right .operate-function .icon {
      float: left;
      margin: 6px 6px 0 0;
    }

.client-information-v2 .communication-scroll {
  font-weight: 700;
  margin-left: 32px;
}

.client-information-v2 .feedback .list-con-3 {
  width: 465px;
}

.client-information-v2 .client-left .cooperation .data-list .list-item-3 li {
  width: 715px;
}

.client-information-v2 .client-income-box .pagination {
  margin: 5px 0 5px 80px;
}

.icon2 {
  display: block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  background-image: url(images/risfondIcon2.gif);
  background-repeat: no-repeat;
}

.icon2-1 {
  background-position: 0 0;
}

.icon2-2 {
  background-position: 0 -16px;
}

.icon2-3 {
  background-position: 0 -32px;
}

.icon2-4 {
  background-position: 0 -48px;
}

.icon2-5 {
  background-position: 0 -64px;
}

.icon2-6 {
  background-position: 0 -80px;
}

.icon2-7 {
  background-position: 0 -96px;
}

.icon2-8 {
  background-position: 0 -112px;
}

.icon2-9 {
  background-position: 0 -128px;
}

.icon2-10 {
  background-position: 0 -144px;
}

.resume-detail .tonghualog-box {
  float: left;
}

  .resume-detail .tonghualog-box .list-item-3 li {
    width: 724px;
    line-height: 26px;
  }

.tonghualog-box .th-datalen-b {
  float: right;
  color: white;
  font-size: 12px;
  margin-left: 12px;
}

  .tonghualog-box .th-datalen-b .th-datalen-con {
    font-size: 14px;
    font-weight: bold;
  }

.tonghualog-box .item.item-xianlu {
  width: 40px;
}

.tonghualog-box .icon2 { /*float:left;margin:5px 0 0 11px;*/
  color: white;
  display: inline-block;
  vertical-align: middle;
  text-align: center;
}

.tonghualog-box .luyinbtn-box {
  display: block;
}

  .tonghualog-box .luyinbtn-box.selected {
    border: 1px solid #D0D0D0;
    border-bottom: 0 none;
    position: relative;
    margin-bottom: 0;
    float: left;
    width: 30px;
    height: 29px;
    margin-top: 0;
    z-index: 28;
    background: #fff;
    margin-left: 5px;
  }

  .tonghualog-box .luyinbtn-box .luyinbtn {
    cursor: pointer;
    transition: transform 0.2s ease-in 0s;
    transform: rotate(0deg);
  }

  .tonghualog-box .luyinbtn-box.selected .luyinbtn {
    transition: transform 0.2s ease-in 0s;
    transform: rotate(90deg);
  }

.tonghualog-box .bofang-box {
  display: block;
  overflow: hidden;
  height: 0;
  clear: both;
  background-color: white;
  margin-left: -32px;
}

.tonghualog-box-mcl .bofang-box {
  float: right;
  margin: 3px 3px 0 !important;
}

.bofang-box li {
  width: auto !important;
}

.tonghualog-box a {
  outline: none;
}

.tonghualog-box .jp-gui {
  float: right;
  position: relative;
  padding: 18px;
  width: 510px;
  float: right;
  border: 1px solid #D0D0D0;
  border-bottom: 2px solid #708FC1;
  border-radius: 0;
  margin-bottom: 3px;
  margin-right: 1px;
  margin-top: -1px;
}

.tonghualog-box-mcl .jp-gui {
  float: right;
  position: relative;
  padding: initial;
  width: auto;
  border: 0 none;
  border-radius: 0;
  margin-bottom: 0;
  margin-right: 0;
  margin-top: 0;
}

.tonghualog-box-mcl .bofang-box,
.tonghualog-box-mcl .bofang-box * {
  box-sizing: content-box;
}

.tonghualog-box-mcl .jp-controls button,
.tonghualog-box-mcl .jp-volume-controls button {
  padding: 0;
}

.tonghualog-box .jp-gui.jp-no-volume {
  width: 432px;
}

.tonghualog-box .jp-gui ul {
  margin: 0;
  padding: 0;
}

  .tonghualog-box .jp-gui ul li {
    position: relative;
    float: left;
    list-style: none;
    margin: 2px;
    padding: 4px 0;
    cursor: pointer;
  }

    .tonghualog-box .jp-gui ul li a {
      margin: 0 4px;
    }

.tonghualog-box .jp-gui li.jp-repeat,
.tonghualog-box .jp-gui li.jp-repeat-off {
  margin-left: 224px;
}

.tonghualog-box .jp-gui li.jp-mute,
.tonghualog-box .jp-gui li.jp-unmute {
  margin-left: 10px;
}

.tonghualog-box .jp-gui li.jp-volume-max {
  margin-left: 91px;
}

.tonghualog-box li.jp-pause,
.tonghualog-box li.jp-repeat-off,
.tonghualog-box li.jp-unmute,
.tonghualog-box .jp-no-solution {
  display: none;
}

.tonghualog-box .jp-progress-slider {
  position: absolute;
  top: 28px;
  left: 90px;
  width: 202px;
}

  .tonghualog-box .jp-progress-slider .ui-slider-handle {
    cursor: pointer;
  }

.tonghualog-box .jp-volume-slider {
  position: absolute;
  top: 31px;
  left: 372px;
  width: 80px;
  height: .4em;
}

  .tonghualog-box .jp-volume-slider .ui-slider-handle {
    height: .8em;
    width: .8em;
    cursor: pointer;
  }

.tonghualog-box .jp-gui.jp-no-volume .jp-volume-slider {
  display: none;
}

.tonghualog-box .jp-current-time,
.tonghualog-box .jp-duration {
  position: absolute;
  top: 38px;
  font-size: 0.8em;
  cursor: default;
}

.tonghualog-box-mcl.tonghualog-box .jp-current-time,
.tonghualog-box-mcl.tonghualog-box .jp-duration {
  position: relative;
  top: auto;
  right: auto;
  left: auto;
}

.tonghualog-box .jp-current-time {
  left: 100px;
}

.tonghualog-box-mcl .jp-duration { /*position: absolute; top: 38px; font-size: 0.8em; cursor: default;*/
}

.tonghualog-box-mcl .jp-current-time { /*left: 100px;*/
}

.tonghualog-box .jp-duration {
  right: 266px;
}

.tonghualog-box .jp-gui.jp-no-volume .jp-duration {
  right: 70px;
}

.tonghualog-box .jp-clearboth {
  clear: both;
}

.view-candidate {
}

  .view-candidate .tabs-link-box {
    float: left;
    margin-top: 6px;
    margin-left: 32px;
    border: 0 none;
    border-left: 1px solid #6E88AD;
    border-right: 1px solid #6E88AD;
  }

    .view-candidate .tabs-link-box .th-tabs-link {
      float: left;
      font-weight: bold;
      height: 25px;
      line-height: 25px;
      padding: 0 8px;
      background: transparent url("images/tonghu-tabs-back.gif") 0 0 repeat-x;
      text-decoration: underline;
    }

      .view-candidate .tabs-link-box .th-tabs-link:hover {
        text-decoration: none;
      }

      .view-candidate .tabs-link-box .th-tabs-link.br {
        border-right: 1px solid #6B85AB;
      }

      .view-candidate .tabs-link-box .th-tabs-link.selected {
        background-position: 0 -25px;
        -webkit-box-shadow: inset 0 0 10px #5D79A8;
        -moz-box-shadow: inset 0 0 10px #5D79A8;
        box-shadow: inset 0 0 10px #5D79A8;
      }

.tonghualog-box .item {
  float: left;
  overflow: hidden;
  text-align: center;
  min-height: 10px;
  height: 29px;
}

  .tonghualog-box .item.item-huchuuser {
    width: 75px;
  }
  /*.tonghualog-box .item.item-huchunum{width:85px;}*/
  .tonghualog-box .item.item-company {
    width: 80px;
  }

  .tonghualog-box .item.item-touser {
    width: 65px;
  }

  .tonghualog-box .item.item-tousernum {
    width: 105px;
  }

  .tonghualog-box .item.item-huchutime {
    width: 155px;
  }

  .tonghualog-box .item.item-time {
    width: 100px;
  }

  .tonghualog-box .item.item-status {
    width: 40px;
  }

  .tonghualog-box .item.item-download {
    width: 40px;
  }

  .tonghualog-box .item.item-luyin {
    width: 40px;
  }

.item-channel .lineNumber {
  margin: 4px 15px;
}

.lineNumber {
  border-radius: 10px !important;
  width: 20px;
  height: 20px;
  display: block;
  background-color: red;
  text-align: center;
  font-size: 12px;
  line-height: 20px;
  color: white;
}

  .lineNumber.l1 {
    background-color: #21b1e4
  }

  .lineNumber.l2 {
    background-color: #f29319
  }

  .lineNumber.l3 {
    background-color: #ea5259
  }

  .lineNumber.l4 {
    background-color: #4f86ea
  }

  .lineNumber.l5 {
    background-color: #009966
  }
/*.client-information-v2*/
.client-information-v2 .tonghualog-box .item {
  float: left;
  overflow: hidden;
  text-align: center;
  min-height: 10px;
  height: 29px;
}

  .client-information-v2 .tonghualog-box .item.item-huchuuser {
    width: 75px;
  }
  /*.client-information-v2 .tonghualog-box .item.item-huchunum{width:85px;}*/
  .client-information-v2 .tonghualog-box .item.item-company {
    width: 70px;
  }

  .client-information-v2 .tonghualog-box .item.item-touser {
    width: 65px;
  }

  .client-information-v2 .tonghualog-box .item.item-tousernum {
    width: 105px;
  }

  .client-information-v2 .tonghualog-box .item.item-huchutime {
    width: 125px;
  }

  .client-information-v2 .tonghualog-box .item.item-time {
    width: 100px;
  }

  .client-information-v2 .tonghualog-box .item.item-status {
    width: 40px;
  }

  .client-information-v2 .tonghualog-box .item.item-download {
    width: 40px;
  }

  .client-information-v2 .tonghualog-box .item.item-luyin {
    width: 40px;
  }
/*.resume-detail*/
.resume-detail .tonghualog-box .item {
  float: left;
  overflow: hidden;
  text-align: center;
  min-height: 10px;
  height: 29px;
}

  .resume-detail .tonghualog-box .item.item-huchuuser {
    width: 95px;
  }
  /*.resume-detail .tonghualog-box .item.item-huchunum{width:85px;}*/
  .resume-detail .tonghualog-box .item.item-company {
    width: 80px;
  }

  .resume-detail .tonghualog-box .item.item-touser {
    width: 65px;
  }

  .resume-detail .tonghualog-box .item.item-tousernum {
    width: 125px;
  }

  .resume-detail .tonghualog-box .item.item-huchutime {
    width: 155px;
  }

  .resume-detail .tonghualog-box .item.item-time {
    width: 100px;
  }

  .resume-detail .tonghualog-box .item.item-status {
    width: 40px;
  }

  .resume-detail .tonghualog-box .item.item-download {
    width: 40px;
  }

  .resume-detail .tonghualog-box .item.item-luyin {
    width: 40px;
  }
/*.view-candidate*/
.view-candidate .tonghualog-box .item {
  float: left;
  overflow: hidden;
  text-align: center;
  min-height: 10px;
  height: 29px;
}

  .view-candidate .tonghualog-box .item.item-huchuuser {
    width: 105px;
  }
  /*.view-candidate .tonghualog-box .item.item-huchunum{width:85px;}*/
  .view-candidate .tonghualog-box .item.item-company {
    width: 140px;
  }

  .view-candidate .tonghualog-box .item.item-touser {
    width: 65px;
  }

  .view-candidate .tonghualog-box .item.item-tousernum {
    width: 165px;
  }

  .view-candidate .tonghualog-box .item.item-huchutime {
    width: 165px;
  }

  .view-candidate .tonghualog-box .item.item-time {
    width: 130px;
  }

  .view-candidate .tonghualog-box .item.item-status {
    width: 40px;
  }

  .view-candidate .tonghualog-box .item.item-download {
    width: 40px;
  }

  .view-candidate .tonghualog-box .item.item-luyin {
    width: 40px;
  }

.assessmentperformance-box .data-list .item {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}

.assessmentperformance-box .item-select {
  width: 40px;
}

  .assessmentperformance-box .item-select .input-radio {
    margin-top: 0;
  }

.assessmentperformance-box .item-staffid {
  width: 59px;
}

.assessmentperformance-box .item-companyid {
  width: 67px;
}

.assessmentperformance-box .item-jobcandidatename {
  width: 67px;
}

.assessmentperformance-box .item-stepstatus {
  width: 62px;
}

.assessmentperformance-box .data-list-content .item-stepstatus {
  color: #f60;
  font-weight: 700;
}

.assessmentperformance-box .item-jobsalary {
  width: 82px;
}

.assessmentperformance-box .item-jobtitle {
  width: 76px;
  text-align: left !important;
}

.assessmentperformance-box .item-checkstatus {
  width: 66px;
}

.assessmentperformance-box .item-bohuireason {
  width: 56px;
  overflow: visible !important;
}

.assessmentperformance-box .item-checkerstaffid {
  width: 60px;
}

.assessmentperformance-box .item-createdtime {
  width: 71px;
}

.assessmentperformance-box .item-checktime {
  width: 71px;
}

.assessmentperformance-box .item-operation {
  width: 89px;
}

  .assessmentperformance-box .item-operation a {
    margin: 0 2px;
  }

.assessmentperformance-box .data-list-item-box {
  width: 100%;
}

.assessmentperformance-box .item-source {
  width: 60px;
}

.assessmentperformance-box .item-position {
  width: 70px;
}

.assessmentperformance-box .item-clientname {
  width: 78px;
  text-align: left !important;
}

.assessmentperformance-box .item-clientstatus {
  width: 60px;
}

.assessmentperformance-box .qpl_box .item-clientname {
  width: 178px;
}

.assessmentperformance-box .data-list-content-ul .recruiting-situation {
  display: none;
}

.assessmentperformance-box .data-list-content-ul .data-list-li {
  height: auto;
}

.assessmentperformance-box .data-list-content-ul .open .recruiting-situation {
  display: block;
}

.assessmentperformance-box .take-notes {
  background: none repeat scroll 0 0 #fff;
  border-left: 1px solid #eee;
  border-right: 1px solid #eee;
  border-top: 1px solid #bfbfbf;
  cursor: pointer;
  height: 18px;
  line-height: 18px;
  margin: 2px auto;
  text-align: center;
  width: 30px;
  text-align: center;
  margin: 5px 3px;
  float: none;
  display: inline-block;
  vertical-align: middle;
}

  .assessmentperformance-box .take-notes strong {
    color: red;
  }

.assessmentperformance-box .unfold-box {
  background: url("/css/images/unfold-icon-v1.gif") no-repeat 0 0;
  width: 10px;
  height: 8px;
  display: inline-block;
  margin-left: 8px;
  cursor: pointer;
}

.assessmentperformance-box .open .unfold-box {
  background-position: 0 -8px;
}

.checkstatus-bohuireason {
  font-size: 12px;
  width: 220px;
}

textarea.checkstatus-bohuireason.textarea-1 {
  width: 201px;
  float: none;
}

.checkstatus-bohuireason-name {
  vertical-align: top;
  max-width: 70px;
  overflow: hidden;
  display: inline-block;
  vertical-align: middle;
  white-space: nowrap;
}

.hiringsteps-date-edit {
  background: url("images/risfondIcon.gif") no-repeat -32px -80px;
  display: block;
  height: 16px;
  width: 16px;
  float: right;
  margin-left: 5px;
  cursor: pointer;
}

.stepsmemos-box .recruiting-situation .allowapply {
  cursor: pointer;
  vertical-align: middle;
  display: inline-block;
}

  .stepsmemos-box .recruiting-situation .allowapply:hover {
    color: red;
    text-decoration: underline;
  }

.stepsmemos-box .recruiting-situation .apm-take-notes {
  float: none;
  _display: inline-block;
  display: inline-block !important;
  vertical-align: middle;
  _margin: 0 0 2px 3px;
  margin: 0 0 2px 3px !important;
  cursor: pointer;
}

.stepsmemos-box .recruiting-situation .hiringsteps-bohuireason {
  display: block;
  background: url("images/risfondIcon.gif") no-repeat -64px -288px;
  height: 16px;
  width: 16px;
  position: absolute;
  top: 11px;
  right: 0px;
}

.stepsmemos-box .recruiting-situation .remark-text-box {
  width: 430px;
  float: left;
  height: 135px;
  margin: 10px 0;
}

.stepsmemos-box .recruiting-situation .remark-operate-box {
  height: 30px;
  width: 420px;
}

.stepsmemos-box .recruiting-situation .remark-textarea {
  width: 410px;
  height: 80px;
  font-size: 12px;
  line-height: 20px;
  padding: 5px;
  resize: none;
}

.stepsmemos-box .recruiting-situation .data-performance-date-box .performance-date {
  float: left;
  border: none 0;
  background: url("/static/images/select-icon-v1.jpg") no-repeat scroll 95% 50% #EEF7FD;
  cursor: pointer;
  width: 100px;
  position: absolute;
  top: 8px;
  left: 592px;
}

.stepsmemos-box .recruiting-situation .data-performance-date-box .performancedate-title {
  left: 528px;
  line-height: 12px;
  position: absolute;
  top: 10px;
  width: 60px;
  white-space: nowrap;
}

.stepsmemos-box .recommend textarea.textarea-1 {
  width: 560px;
  margin-left: 25px;
}

.checkstatus-line {
  width: 95px;
  float: left;
}

.checkstatus-line-bohuireason {
  margin-top: 6px;
  width: 100%;
  float: left;
}

  .checkstatus-line-bohuireason .selectBox-1 {
    width: 220px;
    float: none;
    display: inline-block;
    vertical-align: top;
    margin: 0 0 8px 0;
  }

.item2 {
  margin-right: 20px;
  font-size: 14px;
  letter-spacing: 2px;
  line-height: 30px;
}

.aa {
  padding: 0 20px;
  margin-top: 90px;
}

.back {
  height: 633px;
  background-image: url(../../images/yuangonglogin.png);
  background-size: 100% 100%;
  border: 9px solid #fff;
}

  .back h3 {
    font-size: 22px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: rgba(51,51,51,1);
    line-height: 23px;
    text-align: center;
  }

.back_content {
  width: 501px;
  height: 441px;
  margin: 28px auto;
}

  .back_content::-webkit-scrollbar {
    width: 5px;
  }
  /*滚动条轨道颜色*/
  /*.back_content::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
    background-color: rosybrown;
    border-radius: 3px;
  }*/
  .back_content::-webkit-scrollbar-thumb {
    border-radius: 7px;
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
    background-color: #E8E8E8;
  }

.bot {
  width: 220px;
  height: 34px;
  display: block;
  margin: 0 auto;
  background: #026FB2;
  font-size: 14px;
  margin-top: 20px;
  line-height: 34px;
  color: #FFFFFF;
  border: none;
  border-radius: 4px !important;
}

  .bot span {
    float: left;
  }

    .bot span:nth-of-type(2) {
      margin-left: 8px;
      margin-top: -2px;
    }

.icona {
  width: 16px;
  height: 16px;
  display: inline-block;
  background-image: url(../../images/feiji.png);
  background-size: 100% 100%;
  margin-left: 33px;
  margin-top: 8px;
}

.back_content ul li:nth-of-type(1) {
  font-weight: 500;
  text-align: left;
  font-family: PingFangSC-Medium;
}

.back_content ul li {
  width: 100%;
  padding: 5px;
  border-bottom: 1px solid #ccc;
  font-size: 14px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  color: #333333;
  text-align: left;
  line-height: 20px;
}

.mod-footer {
  text-align: center !important;
  border-top: none !important;
  margin-top: 30px;
}

  .mod-footer .but {
    border-radius: 5px !important;
    background: #ccc;
    border-color: #95A006;
  }

.renew-performance-btn, .cancel-performance-btn {
  margin: 0 5px;
}

.stepsmemos-box .yibohui:hover {
  color: #000 !important;
  text-decoration: underline;
  cursor: pointer;
}

.stepsmemos-box .rfrc-33 {
  float: right !important;
  margin-right: 20px;
}

.stepsmemos-box .pr-time {
  margin-left: 10px;
}

.stepsmemos-box .reference-tips-box {
  float: left;
}

  .stepsmemos-box .reference-tips-box * {
    float: left;
    height: 16px;
    line-height: 16px;
    margin: 0 4px !important;
  }

.data-check-box .sms-content-preview {
  cursor: pointer;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
}

.stepsmemos-box .btntuijiankehumoban {
  float: left;
  background: #ccc;
  border: 1px solid #666;
  border-radius: 2px;
  padding: 1px 2px;
  margin: 8px 0 0 3px;
  cursor: pointer;
  background-color: #dfdfdf;
}

  .stepsmemos-box .btntuijiankehumoban:hover {
    background-color: #efefef;
    border-color: #7b7b7b;
  }

.stepsmemos-box .tip-box {
  float: left;
  width: 168px;
}
/*招聘流程的行高*/
.recruiting-situation .list-con-4 .remark .item-14 {
  line-height: 20px;
  margin: 10px 10px 10px 0;
  white-space: normal;
  text-align: left;
}
/*确定和取消按钮*/
.btn-confirm-v1 {
  border: 1px solid;
  border-radius: 2px 2px 2px 2px;
  cursor: pointer;
  float: right;
  font-family: "Microsoft yahei",Arial;
  font-size: 12px;
  height: 24px;
  line-height: 24px;
  margin-bottom: 10px;
  padding: 0 12px;
  background: -moz-linear-gradient(90deg,#4787ed,#4d90fe) repeat scroll 0 0 transparent;
  border-color: #3079ed;
  color: #fff;
}

.btn-cancel-v1 {
  border: 1px solid;
  border-radius: 2px 2px 2px 2px;
  cursor: pointer;
  float: right;
  font-family: "Microsoft yahei",Arial;
  font-size: 12px;
  height: 24px;
  line-height: 24px;
  margin-bottom: 10px;
  padding: 0 12px;
  background: -moz-linear-gradient(90deg,#ececec,#f4f4f4) repeat scroll 0 0 transparent;
  border-color: #c3c3c3;
  color: #333;
  margin-left: 10px;
}
/*审核*/
.assessment-box .approval-status-box {
  text-align: left;
  width: 68px;
}

  .assessment-box .approval-status-box.show {
    border: solid 1px #DA6B00;
    background-color: #FFF6E4;
    width: 68px;
    height: 25px;
    margin: 1px 0;
  }

  .assessment-box .approval-status-box .approval-status-text {
    display: block;
    margin: 1px 0 0 1px;
    color: #DF730A;
    font-weight: 700;
    position: relative;
    cursor: pointer;
    height: 27px;
  }

  .assessment-box .approval-status-box.show .approval-status-text {
    display: block;
    margin: 0;
    height: 25px;
  }

  .assessment-box .approval-status-box .approval-status-text .text {
    display: block;
    margin: 0;
    height: 27px;
    line-height: 27px;
    width: 70px;
    text-indent: 5px;
  }

  .assessment-box .approval-status-box.show .approval-status-text .text {
    display: block;
    margin: 0;
    height: 26px;
    line-height: 26px;
    width: 59px;
  }

  .assessment-box .approval-status-box .approval-status-icon i {
    background: url("/static/images/financial-distribution-status-btn-icon.gif") no-repeat 0 0;
    height: 9px;
    width: 9px;
    display: none;
    position: absolute;
    top: 10px;
    right: 12px;
    transition: transform 0.2s ease-in 0s;
    transform: rotate(180deg);
  }

  .assessment-box .approval-status-box.allowrole .approval-status-icon i {
    display: block;
  }

  .assessment-box .approval-status-box.show .approval-status-icon i {
    background: url("/static/images/financial-distribution-status-btn-icon.gif") no-repeat 0 0;
    height: 9px;
    width: 9px;
    display: block;
    position: absolute;
    top: 8px;
    right: 12px;
    transition: transform 0.2s ease-in 0s;
    transform: rotate(0deg);
  }

.assessment-box .approval-status-dialog {
  display: none;
}

  .assessment-box .approval-status-dialog.show {
    display: block;
    position: absolute;
    line-height: 25px;
    background-color: #FFF6E4;
    width: 90px;
    border: solid 1px #DA6B00;
    z-index: 3;
    display: block;
  }

.assessment-box .approval-status-item {
  height: 25px;
  padding-left: 10px;
  border-bottom: solid 1px #d0cecf;
  cursor: pointer;
}

  .assessment-box .approval-status-item.itemhover {
    background-color: #FFF1CC;
  }

  .assessment-box .approval-status-item.current {
    background-color: #FCE9C4;
  }

.assessment-box .approval-btn-list li {
  width: 50%;
  float: left;
  text-align: center;
}

.assessment-box .approval-btn-list {
  margin: 5px 0;
}

  .assessment-box .approval-btn-list li .btn-approval-confim {
    width: 38px;
    cursor: pointer;
  }

  .assessment-box .approval-btn-list li .btn-approval-cancel {
    width: 38px;
    cursor: pointer;
  }
/*我的绩效*/
.staffassessment-performance-box .assistant .item-select input {
  margin-top: 0;
}

.staffassessment-performance-box .assistant .item-select {
  text-align: center;
  width: 50px;
}

.staffassessment-performance-box .assistant .item-staffid {
  width: 80px;
}

.staffassessment-performance-box .assistant .item-companyid {
  width: 80px;
}

.staffassessment-performance-box .assistant .item-positionid {
  width: 80px;
}

.staffassessment-performance-box .assistant .item-assessmentdate {
  width: 70px;
}

.staffassessment-performance-box .assistant .item-assessmentfactor {
  width: 40px;
}

.staffassessment-performance-box .assistant .item-incomeamount {
  width: 140px;
}

.staffassessment-performance-box .assistant .item-tuijiantotal {
  width: 100px;
}

.staffassessment-performance-box .assistant .item-kehumianshitotal {
  width: 100px;
}

.staffassessment-performance-box .assistant .item-offertotal {
  width: 100px;
}

.staffassessment-performance-box .assistant .item-ruzhitotal {
  width: 100px;
}

.staffassessment-performance-box .assistant .item-totalscore {
  width: 40px;
}

.staffassessment-performance-box .internconsultant .item-select input {
  margin-top: 0;
}

.staffassessment-performance-box .internconsultant .item-select {
  text-align: center;
  width: 50px;
}

.staffassessment-performance-box .internconsultant .item-staffid {
  width: 80px;
}

.staffassessment-performance-box .internconsultant .item-companyid {
  width: 80px;
}

.staffassessment-performance-box .internconsultant .item-positionid {
  width: 80px;
}

.staffassessment-performance-box .internconsultant .item-assessmentdate {
  width: 70px;
}

.staffassessment-performance-box .internconsultant .item-assessmentfactor {
  width: 40px;
}

.staffassessment-performance-box .internconsultant .item-incomeamount {
  width: 120px;
}

.staffassessment-performance-box .internconsultant .item-kehumianshitotal {
  width: 110px;
}

.staffassessment-performance-box .internconsultant .item-qianyuetotal {
  width: 110px;
}

.staffassessment-performance-box .internconsultant .item-ruzhitotal {
  width: 110px;
}

.staffassessment-performance-box .internconsultant .item-totalscore {
  width: 40px;
}

.staffassessment-performance-box .consultant .item-select input {
  margin-top: 0;
}

.staffassessment-performance-box .consultant .item-select {
  text-align: center;
  width: 50px;
}

.staffassessment-performance-box .consultant .item-staffid {
  width: 80px;
}

.staffassessment-performance-box .consultant .item-companyid {
  width: 80px;
}

.staffassessment-performance-box .consultant .item-positionid {
  width: 80px;
}

.staffassessment-performance-box .consultant .item-assessmentdate {
  width: 70px;
}

.staffassessment-performance-box .consultant .item-assessmentfactor {
  width: 40px;
}

.staffassessment-performance-box .consultant .item-incomeamount {
  width: 120px;
}

.staffassessment-performance-box .consultant .item-zhulijixiao {
  width: 110px;
}

.staffassessment-performance-box .consultant .item-zhuliliushi {
  width: 110px;
}

.staffassessment-performance-box .consultant .item-bdqianyue {
  width: 110px;
}

.staffassessment-performance-box .consultant .item-totalscore {
  width: 40px;
}

.staffassessment-performance-box .reference-con {
  max-height: 300px;
  overflow-y: auto;
}

.staffassessment-performance-box .amount-dialog,
.staffassessment-performance-box .hiring-dialog,
.staffassessment-performance-box .qianyue-dialog,
.staffassessment-performance-box .zhulijixiao-dialog,
.staffassessment-performance-box .zhuliliushi-dialog,
.staffassessment-performance-box .bdqianyue-dialog,
.staffassessment-performance-box .addresume-dialog,
.staffassessment-performance-box .asteriskresume-dialog {
  cursor: pointer;
}

  .staffassessment-performance-box .amount-dialog:hover,
  .staffassessment-performance-box .hiring-dialog:hover,
  .staffassessment-performance-box .qianyue-dialog:hover,
  .staffassessment-performance-box .zhulijixiao-dialog:hover,
  .staffassessment-performance-box .zhuliliushi-dialog:hover,
  .staffassessment-performance-box .bdqianyue-dialog:hover,
  .staffassessment-performance-box .addresume-dialog:hover,
  .staffassessment-performance-box .asteriskresume-dialog:hover {
    text-decoration: underline;
  }

.staffassessment-performance-box .quarter-other-list-box {
  background-color: #fff;
  border: 1px solid #c2c2c2;
  display: none;
  left: 700px;
  max-height: 110px;
  overflow-y: auto;
  padding: 5px;
  position: absolute;
  top: 310px;
  width: 260px;
}

  .staffassessment-performance-box .quarter-other-list-box li {
    float: left;
    line-height: 20px;
    margin: 0 5px;
    width: 50px;
  }

.tb-statistics .item-performance-date {
  width: 15%;
}

.tb-statistics .item-performance-client {
  width: 25%;
}

.tb-statistics .item-performance-people {
  width: 10%;
}

.tb-statistics .item-performance-type {
  width: 10%;
}

.tb-statistics .item-performance-incomeamount {
  width: 10%;
}

.tb-statistics .item-performance-ratio {
  width: 10%;
}

.tb-statistics .item-performance-amount {
  width: 10%;
}

.tb-statistics .item-performance-staff {
  width: 10%;
}

.tb-statistics .item-performance-checker {
  width: 15%;
}

.tb-statistics .statistics-qianyue-item-name .item-performance-date {
  width: 15%;
}

.tb-statistics .statistics-qianyue-item-name .item-performance-client {
  width: 45%;
}

.tb-statistics .statistics-qianyue-item-name .item-performance-clientstatus {
  width: 10%;
}

.tb-statistics .statistics-qianyue-item-name .item-performance-staffname {
  width: 10%;
}

.tb-statistics .statistics-qianyue-item-name .item-performance-checkerstaffname {
  width: 10%;
}

.tb-statistics .statistics-qianyue-item-name .item-performance-checkerstaffstatus {
  width: 10%;
}

.performance-staffassessment-dialog-box .statistics-item-data td {
  border-bottom: dashed 1px #c2c2c2;
}

.performance-staffassessment-dialog-box .statistics-item-data:last-child td {
  border-bottom: 0 none;
}

.performance-staffassessment-dialog-box {
  width: 690px;
  max-height: 400px;
  overflow-y: auto;
  min-height: 100px;
}

.tb-statistics .statistics-zhulijixiao-item .item-zj-name {
  width: 12%;
}

.tb-statistics .statistics-zhulijixiao-item .item-zj-pa {
  width: 18%;
}

.tb-statistics .statistics-zhulijixiao-item .item-zj-tuijian {
  width: 14%;
}

.tb-statistics .statistics-zhulijixiao-item .item-zj-mianshi {
  width: 14%;
}

.tb-statistics .statistics-zhulijixiao-item .item-zj-offer {
  width: 14%;
}

.tb-statistics .statistics-zhulijixiao-item .item-zj-chenggong {
  width: 14%;
}

.tb-statistics .statistics-zhulijixiao-item .item-zj-ts {
  width: 10%;
}

.tb-statistics .statistics-zhuliliushi-item .item-zj-name {
  width: 12%;
}

.tb-statistics .statistics-zhuliliushi-item .item-zj-entrytime {
  width: 28%;
}

.tb-statistics .statistics-zhuliliushi-item .item-zj-leavetime {
  width: 28%;
}

.tb-statistics .statistics-zhuliliushi-item .item-zj-status {
  width: 28%;
}

.tb-statistics .statistics-bdqianyue-item .item-zj-qt {
  width: 13%;
}

.tb-statistics .statistics-bdqianyue-item .item-zj-name {
  width: 44%;
}

.tb-statistics .statistics-bdqianyue-item .item-zj-ks {
  width: 10%;
}

.tb-statistics .statistics-bdqianyue-item .item-zj-qsn {
  width: 10%;
}

.tb-statistics .statistics-bdqianyue-item .item-zj-ssn {
  width: 10%;
}

.tb-statistics .statistics-bdqianyue-item .item-zj-ss {
  width: 10%;
}
/*简历搜索切换高级搜索位置调整 2013-10-18*/
.resume-seek .catalogue .search-other-function a {
  color: #0070B2;
}

.resume-seek .catalogue .search-other-function {
  margin-right: 20px;
}

.filter-bar-sort {
  border-width: 1px;
  border-style: solid;
  display: inline-block;
  vertical-align: middle;
  line-height: 18px;
  padding: 0 5px;
  text-shadow: 1px 1px 1px #fff;
  border-radius: 3px;
  color: #00518c;
  border-color: #9aafe5;
  background: #c9ebfe;
  background-image: -webkit-gradient(linear,left top,left bottom,from(#fff),to(#c9ebfe));
  background-image: -webkit-linear-gradient(#fff,#c9ebfe);
  background-image: -moz-linear-gradient(#fff,#c9ebfe);
  background-image: -ms-linear-gradient(#fff,#c9ebfe);
  background-image: -o-linear-gradient(#fff,#c9ebfe);
  background-image: linear-gradient(#fff,#c9ebfe);
  cursor: pointer;
}

  .filter-bar-sort.current, .filter-bar-sort:hover {
    color: #5e5e5e;
    border-color: #9b9b9b;
    background: #e6e6e6;
    background-image: -webkit-gradient(linear,left top,left bottom,from(#e0e0e0),to(#eeeeef));
    background-image: -webkit-linear-gradient(#e0e0e0,#eeeeef);
    background-image: -moz-linear-gradient(#e0e0e0,#eeeeef);
    background-image: -ms-linear-gradient(#e0e0e0,#eeeeef);
    background-image: -o-linear-gradient(#e0e0e0,#eeeeef);
    background-image: linear-gradient(#e0e0e0,#eeeeef);
    -moz-box-shadow: 0 3px 1px #ccc inset;
    -webkit-box-shadow: 0 3px 1px #ccc inset;
    box-shadow: 0 3px 1px #ccc inset;
    *border-right-color: #dfdfdf;
    *border-bottom-color: #dfdfdf;
    cursor: pointer;
  }

.filter-bar-sort-title {
  line-height: 25px;
}

.resume-seek .keyword-search .filter-bar-sort-box {
  float: right;
}

  .resume-seek .keyword-search .filter-bar-sort-box li {
    float: left;
    margin-left: 10px;
  }

  .resume-seek .keyword-search .filter-bar-sort-box .icon {
    float: left;
    margin: 12px 3px 0 0;
  }

.resume-seek .resume-detail-picture {
  float: left;
  margin-right: 10px;
}

.resume-seek .resume-detail-keywords {
  float: left;
  width: 800px;
}

.resume-seek .rslc-title {
  font-weight: bold;
  width: 70px;
}

.resume-search-panel .keyword-search .filter-bar-sort-box {
  float: right;
}

  .resume-search-panel .keyword-search .filter-bar-sort-box li {
    float: left;
    margin-left: 10px;
  }

  .resume-search-panel .keyword-search .filter-bar-sort-box .icon {
    float: left;
    margin: 12px 3px 0 0;
  }

.resume-search-panel .resume-detail-picture {
  float: left;
  margin-right: 10px;
}

.resume-search-panel .resume-detail-keywords {
  float: left;
  width: 800px;
}

.resume-search-panel .rslc-title {
  font-weight: bold;
  width: 70px;
}

.right-menu-container.loading {
  display: none;
}

.right-collapse-btn {
  position: fixed;
  z-index: 99;
  width: 60px;
  right: 0px;
  bottom: 490px;
  cursor: pointer;
  display: none;
}
.right-expand-btn {
  position: fixed;
  z-index: 99;
  height: 70px;
  right: 0;
  bottom: 300px;
  cursor: pointer;
}

.right-menu-container .right-expand-btn {
  display: none;
}

.right-menu-container .right-menu-panel {
  display: initial;
}

.right-menu-container.is-fold .right-expand-btn {
  display: initial;
}

.right-menu-container.is-fold .right-menu-panel {
  display: none;
}

.right-menu-container.is-fold.show-panel .right-collapse-btn {
  display: initial;
}

.right-menu-container.is-fold.show-panel .right-expand-btn {
  display: none;
}

.right-menu-container.is-fold.show-panel .right-menu-panel {
  display: initial;
}


/*拨号*/
.call-box {
  bottom: 125px;
  right: 0;
  position: fixed;
  z-index: 99;
  width: 60px;
  height: 60px;
  right: 200px;
  border-radius: 50% !important;
  font-family: 'Microsoft YaHei';
}

#bgWrap {
  width: 100%;
  background: #000;
  filter: alpha(opacity=30);
  z-index: 100;
  position: fixed;
  left: 0;
  top: 0;
  display: none;
  overflow: hidden;
  opacity: .3;
  visibility: visible;
}

.callphone-box.index-iconinfo-box {
  display: block;
  width: 60px;
  height: 60px;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 98;
  cursor: pointer;
  text-decoration: none;
  border-radius: 50% !important;
  overflow: hidden;
  background-color: #00c69b;
}

  .callphone-box.index-iconinfo-box:hover {
    background-color: #00ae89;
    transition: all .3s;
  }

/*帮助中心-传送门*/
.help-box {
  bottom: 210px;
  position: fixed;
  z-index: 99;
  width: 60px;
  height: 60px;
  right: 200px;
}

  .help-box .help-box-link {
    display: block;
    width: 60px;
    height: 60px;
    font-size: 12px;
    color: #fff;
    background-color: #629bfb;
    text-align: center;
    overflow: hidden;
    border-radius: 50% !important;
    font-family: 'Microsoft YaHei';
  }

    .help-box .help-box-link:hover {
      background-color: #3d7dea;
      text-decoration: none;
    }

  .help-box img {
    display: block;
    width: 20px;
    height: 20px;
    margin: 10px auto 0;
  }

/*帮助中心-传送门*/
.help-box {
  bottom: 210px;
  position: fixed;
  z-index: 99;
  width: 60px;
  height: 60px;
  right: 200px;
}

  .help-box .help-box-link {
    display: block;
    width: 60px;
    height: 60px;
    font-size: 12px;
    color: #fff;
    background-color: #629bfb;
    text-align: center;
    overflow: hidden;
    border-radius: 50% !important;
    font-family: 'Microsoft YaHei';
  }

    .help-box .help-box-link:hover {
      background-color: #3d7dea;
      text-decoration: none;
    }

  .help-box img {
    display: block;
    width: 20px;
    height: 20px;
    margin: 10px auto 0;
  }


/*意见反馈-传送门*/
.feedback-box {
  bottom: 40px;
  position: fixed;
  z-index: 99;
  width: 60px;
  height: 60px;
  right: 200px;
}

  .feedback-box .help-box-link {
    display: block;
    width: 60px;
    height: 60px;
    font-size: 12px;
    color: #fff;
    background-color: #4389C7;
    text-align: center;
    overflow: hidden;
    border-radius: 50% !important;
    font-family: 'Microsoft YaHei';
  }

.feedback-box .help-box-link:hover {
  background-color: #3d7dea;
  text-decoration: none;
}

  .feedback-box img {
    display: block;
    width: 20px;
    height: 20px;
    margin: 10px auto 0;
  }

/*聊天中心*/
.chat-box {
  bottom: 461px;
  position: fixed;
  z-index: 99;
  width: 60px;
  height: 60px;
  right: 200px;
}

  .chat-box .chat-box-unread {
    /*height: 14px;
    width: 14px;*/
    background-color: #f51c1c;
    border-radius: 10px !important;
    top: -8px;
    right: -2px;
    position: absolute;
    display: initial;
    color: #fff;
    padding: 2px 4px;
    border-radius: 50% !important;
    font-size: 12px;
    display: none;
  }

  .chat-box .chat-box-link {
    display: block;
    width: 60px;
    height: 60px;
    font-size: 12px;
    color: #fff;
    background-color: #31B9D1;
    text-align: center;
    overflow: hidden;
    border-radius: 50% !important;
    font-family: 'Microsoft YaHei';
  }

    .chat-box .chat-box-link:hover {
      background-color: #189ab1;
      text-decoration: none;
    }

    .chat-box .chat-box-link > i {
      display: block;
      vertical-align: middle;
      font-size: 17px;
      text-align: center;
      margin: 12px 0 4px;
    }

  .chat-box img {
    display: block;
    width: 20px;
    height: 20px;
    margin: 10px auto 0;
  }

/*个人邮箱*/
.mail-box {
  bottom: 295px;
  position: fixed;
  z-index: 99;
  width: 60px;
  height: 60px;
  right: 200px;
}

  .mail-box .mail-box-link {
    display: block;
    width: 60px;
    height: 60px;
    font-size: 12px;
    color: #fff;
    background-color: #efae5e;
    text-align: center;
    overflow: hidden;
    border-radius: 50% !important;
    font-family: 'Microsoft YaHei';
  }

    .mail-box .mail-box-link:hover {
      background-color: #dc9c4e;
      text-decoration: none;
    }

    .mail-box .mail-box-link > i {
      display: block;
      vertical-align: middle;
      font-size: 17px;
      text-align: center;
      margin: 12px 0 4px;
    }

  .mail-box .help-num {
    position: absolute;
    left: 36px;
    top: 0;
    background: #f83d3d;
    border-radius: 8px !important;
    padding: 2px 7px !important;
    line-height: 1;
    color: white;
    font-size: 12px;
  }

    .mail-box .help-num.no {
      display: none;
    }

  .mail-box.block {
  }

    .mail-box.block:after {
      content: '';
      position: absolute;
      z-index: 1000;
      border: none;
      margin: 0px;
      padding: 0px;
      width: 100%;
      height: 100%;
      top: 0px;
      left: 0px;
      background-color: rgb(85, 85, 85);
      opacity: 0.05;
      cursor: wait;
      border-radius: 50%;
    }

.mail_popover_box {
  position: relative;
  font-size: 14px;
  padding: 6px;
  width: 246px;
}

  .mail_popover_box .mp_b {
    position: relative;
    margin: 0 0 8px;
  }

    .mail_popover_box .mp_b .mp_icon {
      float: left;
      width: 70px;
      height: 60px;
      border: 0 none !important;
      margin: 3px 0 0 0;
    }

    .mail_popover_box .mp_b .mp_r {
      margin: 0 0 0 82px;
    }

      .mail_popover_box .mp_b .mp_r .mp_t {
        line-height: 1;
        padding: 9px 0;
        margin: 0;
      }

      .mail_popover_box .mp_b .mp_r .mp_mail {
        margin: 0;
        max-width: 152px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

  .mail_popover_box .mp_bp {
    position: relative;
  }

    .mail_popover_box .mp_bp .mp_link {
      float: right;
      margin: 1px 0 0 0;
    }

    .mail_popover_box .mp_bp .mp_num_b {
      color: #666;
    }

      .mail_popover_box .mp_bp .mp_num_b .mp_num {
        color: white;
        background: #F83D3D;
        border-radius: 2px !important;
        padding: 2px 5px;
        line-height: 1;
      }

.mmp_popover {
  position: fixed !important;
  right: 78px !important;
  bottom: 247px !important;
  top: auto !important;
  left: auto !important;
}

@media (min-width:1100px) and (max-width:1200px) {
  .feedback-box {
    right: 0;
    bottom: 50px;
  }
  .call-box {
    right: 0;
    bottom: 125px;
  }

  .help-box {
    right: 0;
    bottom: 200px;
  }

  .mail-box {
    right: 0;
    bottom: 275px;
  }

  .chat-box {
    right: 0;
    bottom: 421px;
  }

  .mmp_popover {
    right: 50px !important;
    position: absolute !important;
    top: 50px !important;
    height: 124px;
  }

  .xiaoxi-box {
    right: 0 !important;
    bottom: 348px !important;
  }

  .zhinengxiaoda {
    right: 0 !important;
    bottom: 496px !important;
  }
}

@media (min-width:1200px) and (max-width:1440px) {

  .call-box {
    right: 0;
    bottom: 125px;
  }
  .feedback-box {
    right: 0;
    bottom: 50px;
  }
  .help-box {
    right: 0;
    bottom: 200px;
  }

  .mail-box {
    right: 0;
    bottom: 275px;
  }

  .chat-box {
    right: 0;
    bottom: 421px;
  }

  .mmp_popover {
    right: 50px !important;
    position: absolute !important;
    top: 50px !important;
    height: 124px;
  }

  .xiaoxi-box {
    right: 0 !important;
    bottom: 348px !important;
  }

  .zhinengxiaoda {
    right: 0 !important;
    bottom: 496px !important;
  }
}

@media (min-width:1440px) and (max-width:1920px) {
  .feedback-box {
    right: 42px;
  }
  .call-box {
    right: 42px;
  }

  .help-box {
    right: 42px;
  }

  .mail-box {
    right: 42px;
  }

  .chat-box {
    right: 42px;
  }


  .xiaoxi-box {
    right: 42px !important;
  }

  .zhinengxiaoda {
    right: 42px !important;
  }

}

@media (min-width:1920px) {
  .feedback-box {
    right: 200px;
  }

  .call-box {
    right: 200px;
  }

  .help-box {
    right: 200px;
  }

  .mail-box {
    right: 200px;
  }

  .chat-box {
    right: 200px;
  }

  .mmp_popover {
    right: 50px !important;
    top: 50px !important;
    height: 124px;
    position: absolute !important;
  }

  .xiaoxi-box {
    right: 200px !important;
  }

  .zhinengxiaoda {
    right: 200px !important;
  }
}

.callphone-box.index-iconinfo-box .if-box {
  display: block;
  vertical-align: middle;
  width: 60px;
  height: 60px;
}

  .callphone-box.index-iconinfo-box .if-box:hover {
    text-decoration: none;
  }

.callphone-box.index-iconinfo-box .if-icon {
  margin: 6px 0 1px;
  font-size: 16px;
}

  .callphone-box.index-iconinfo-box .if-icon img {
    width: 20px;
    height: 20px;
  }

.callphone-box.index-iconinfo-box .index-iconinfo-con {
  font-size: 12px;
  color: white;
  font-weight: normal;
}

.call-box .cp-pho-box {
  width: 382px;
  right: 0;
  bottom: 0;
  background-color: #fefef2;
  border: 1px solid #e7e7e7;
  cursor: default;
  left: 50%;
  margin-left: 150px;
  position: absolute;
  z-index: 99;
  bottom: 10px;
  display: none;
}

  .call-box .cp-pho-box .cp-pho-close {
    background: none;
    border: 0 none;
    margin: 0;
    padding: 0;
    position: absolute;
    width: 100%;
  }

  .call-box .cp-pho-box .cp-close {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 12px;
    height: 12px;
    background: transparent url("/images/close_notice_7a51922.png") center center no-repeat;
    cursor: pointer;
  }

  .call-box .cp-pho-box .cp-pho-title {
    display: block;
    margin: 0 30px 0 0;
    font-size: 16px;
    font-weight: bold;
    height: 20px;
  }

  .call-box .cp-pho-box .cp-con-box {
    display: block;
    margin: 15px 10px 5px;
    padding: 5px;
    background: none;
    overflow: hidden;
  }

  .call-box .cp-pho-box .cp-line {
    display: block;
    margin: 0 0 10px;
    line-height: 30px;
  }

  .call-box .cp-pho-box .cp-title {
    float: left;
    font-size: 14px;
    font-weight: bold;
    width: 80px;
    text-align: right;
  }

  .call-box .cp-pho-box .cp-num {
    float: left;
    font-size: 14px;
    font-weight: bold;
    color: #f90;
  }

  .call-box .cp-pho-box .cp-txt-title {
    float: left;
    margin-left: 8px;
    line-height: 18px;
  }

  .call-box .cp-pho-box .cp-txt-con {
    float: left;
    width: 300px;
    line-height: 18px;
  }

  .call-box .cp-pho-box .cp-phonum {
    float: left;
    margin: 2px 0 0 0;
    padding: 2px 5px;
    width: 200px;
    height: 18px;
    line-height: 18px;
  }

  .call-box .cp-pho-box .cp-tishi {
    float: left;
    margin: 6px 0 0 8px;
    cursor: pointer;
    width: 18px;
    height: 18px;
    background: transparent url("/images/mark_icon.png") center center no-repeat;
    background-size: 18px 18px;
  }

  .call-box .cp-pho-box .cp-btn {
    background: #00b88d;
    color: white;
    float: right;
    padding: 6px 10px;
    width: auto;
    height: auto;
    line-height: initial;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -o-border-radius: 3px;
    -ms-border-radius: 3px;
    border-radius: 3px;
  }

    .call-box .cp-pho-box .cp-btn:hover {
      background: #00a57f;
    }

@keyframes pingjiaAnimatedBackground {
  from {
    background-color: #1FD28E;
  }

  to {
    background-color: #D21F56;
  }
}

@keyframes pingjiaAnimatedBorder {
  from {
    border-color: #1FD28E;
    color: #1ED28B;
  }

  to {
    border-color: #D21F56;
    color: #D21F56;
  }
}

.call-box .callphone-pingjia-box {
  background: #1FD28E url("images/call_pingjia.png") center 8px no-repeat;
  background-size: 22px 22px;
  display: block;
  width: 60px;
  height: 60px;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 98;
  cursor: pointer;
  text-decoration: none;
  color: white;
  font-size: 12px;
  display: none;
  animation: pingjiaAnimatedBackground 2s infinite alternate;
}



@media(max-width:1385px) {
}

.call-box .callphone-pingjia-box .cppj-title {
  display: block;
  position: absolute;
  bottom: 0;
  text-align: center;
  left: 0;
  right: 0;
  line-height: 30px;
  font-weight: bold;
}

.call-box .callphone-pingjia-box:hover .cppj-title {
  display: none;
}

.call-box .callphone-pingjia-box .cppj-line {
  display: none;
  height: 30px;
  line-height: 30px;
  text-align: center;
  background: #00D28B;
}

.call-box .callphone-pingjia-box:hover .cppj-line {
  display: block;
}

.call-box .callphone-pingjia-box .cppj-line:hover {
  background: #00B276;
}

.call-box .callphone-pingjia-box .cppj-zz {
  position: absolute;
  width: 60px;
  padding: 3px 0;
  text-align: center;
  color: #1ED28B;
  border: 1px solid #1FD28E;
  border-radius: 3px;
  left: 0;
  bottom: -28px;
  line-height: 16px;
  font-size: 12px;
  animation: pingjiaAnimatedBorder 2s infinite alternate;
}

.Rs_CallBox_Layer_Wrap {
  margin: 0;
  padding: 0;
  border: 0;
  text-decoration: none;
  list-style-type: none;
}

  .Rs_CallBox_Layer_Wrap .cp-pho-box {
    width: 382px;
    background-color: #fefef2;
    border: 1px solid #e7e7e7;
    cursor: default;
    position: absolute;
    z-index: 2900;
    display: none;
  }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-pho-close {
      background: none;
      border: 0 none;
      margin: 0;
      padding: 0;
      position: absolute;
      width: 100%;
    }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-close {
      position: absolute;
      right: 10px;
      top: 10px;
      width: 12px;
      height: 12px;
      background: transparent url("/images/close_notice_7a51922.png") center center no-repeat;
      cursor: pointer;
    }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-pho-title {
      display: block;
      margin: 0 30px 0 0;
      font-size: 16px;
      font-weight: bold;
      height: 20px;
    }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-con-box {
      display: block;
      margin: 15px 10px 5px;
      padding: 5px;
      background: none;
      overflow: hidden;
    }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-line {
      display: block;
      margin: 0 0 10px;
      line-height: 30px;
    }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-title {
      float: left;
      font-size: 14px;
      font-weight: bold;
      width: 80px;
      text-align: right;
    }

    .Rs_CallBox_Layer_Wrap .cp-pho-box span {
      float: left;
      margin-left: 10px;
    }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-num {
      padding-left: 5px;
      padding-right: 5px;
      font-size: 14px;
      font-weight: bold;
      color: #f90;
    }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-num-on {
      padding-left: 5px;
      padding-right: 5px;
      font-size: 14px;
      font-weight: bold;
      color: #f90;
      background: #00b88d none repeat scroll 0 0;
      color: white;
      -moz-border-radius: 10px !important;
      -webkit-border-radius: 10px !important;
      border-radius: 10px !important;
    }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-txt-title {
      float: left;
      margin-left: 8px;
      line-height: 18px;
    }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-txt-con {
      float: right;
      line-height: 18px;
      font-size: 13px;
      width: 368px;
    }

      .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-txt-con ul {
        list-style: decimal;
        margin-left: 0;
        padding-left: 10px;
      }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-phonum {
      float: left;
      margin: 2px 0 0 0;
      padding: 2px 5px;
      width: 200px;
      line-height: 18px;
    }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-phoarea {
      float: left;
      margin: 2px 0 0 0;
      line-height: 18px;
      max-width: 130px;
      padding: 1px 5px;
      height: 27px;
    }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-tishi {
      float: left;
      margin: 6px 0 0 8px;
      cursor: pointer;
      width: 18px;
      height: 18px;
      background: transparent url("/images/mark_icon.png") center center no-repeat;
      background-size: 18px 18px;
    }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-btn {
      background: #00b88d;
      color: white;
      float: right;
      padding: 6px 10px;
      width: auto;
      height: auto;
      line-height: initial;
      -webkit-border-radius: 3px;
      -moz-border-radius: 3px;
      -o-border-radius: 3px;
      -ms-border-radius: 3px;
      border-radius: 3px;
      border: medium none;
      margin: 0 5px;
      text-align: center;
      font-size: 13px;
    }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .calloperation {
      width: 90%;
      margin: 0 auto;
    }

    .Rs_CallBox_Layer_Wrap .cp-pho-box .cp-btn:hover {
      background: #00a57f;
    }






/*客户合同*/
.input-v1 {
  background-color: #fcfcfc;
  border: 1px solid #b7b7b7;
  box-shadow: 1px 1px 1px #d3d3d3 inset;
  height: 23px;
  overflow: hidden;
  padding-left: 7px;
  font-size: 12px;
  line-height: 23px;
}

.input-v1-money {
  background: url("/static/images/distribute-money-unit-icon.jpg") no-repeat scroll 95% 50% #fcfcfc;
}

.module-1 .module-box {
  width: 960px;
}

.module-1 .left-box {
  float: left;
  margin-right: 4px;
  overflow: hidden;
  width: 756px;
}

.radio-box {
  line-height: 22px;
  float: left;
}

  .radio-box .rb-radio {
    float: left;
    margin: 5px 5px 0 0;
  }

  .radio-box .rb-txt {
    float: left;
    margin: 0 15px 0 0;
    width: auto;
    text-align: left;
    cursor: pointer;
  }

    .radio-box .rb-txt:last-child {
      margin-right: 0;
    }

.zhezhao-box {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ccc;
  -webkit-filter: alpha(opacity=40);
  -moz-filter: alpha(opacity=40);
  -o-filter: alpha(opacity=40);
  filter: alpha(opacity=40);
  -ms-opacity: 0.4;
  opacity: 0.4;
}

.operate-module {
  margin-bottom: 3px;
}

  .operate-module .border-middle {
    width: 196px;
  }

  .operate-module .operate-function {
    background: none repeat scroll 0 0 #fff;
    border: 1px solid #b7b7b7;
    display: block;
    margin-top: 1px;
    overflow: hidden;
    padding-bottom: 7px;
    width: 197px;
  }

  .operate-module .icon {
    float: left;
    margin: 6px 6px 0 0;
  }

  .operate-module a {
    cursor: pointer;
    display: block;
    float: left;
  }

.segment-number {
  float: right;
  margin-right: 20px;
}

.rfrc-clientcontract {
  color: red;
}

.client-contract-operate .client-contract-title {
  width: 40%;
  white-space: nowrap;
  overflow: hidden;
}

.client-contract-operate .client-contract-date {
  width: 58%;
  white-space: nowrap;
  overflow: hidden;
  margin-left: 2%;
}

.client-contract-operate .client-contract-title-link {
  padding: 0;
}

  .client-contract-operate .client-contract-title-link:hover {
    background: none;
    text-decoration: underline;
  }

.client-contract-operate .list-item-2 li {
  height: 25px;
  border-bottom: 1px dashed #999;
}

.clientcontract-box .select-user .outbox-item-list li {
  margin: 0;
}

.clientcontract-box .clientcontract-wan-input {
  width: 40px;
  margin: 0 2px;
}

.clientcontract-manage-box .item-clientname {
  text-indent: 20px;
  width: 20%;
}

.clientcontract-manage-box .item-contractstaff {
  width: 7%;
}

.clientcontract-manage-box .item-company {
  width: 7%;
}

.clientcontract-manage-box .item-initialpaymentamount {
  width: 7%;
}

.clientcontract-manage-box .item-fuwufeiratio {
  width: 12%;
}

.clientcontract-manage-box .item-zhifushixian {
  width: 5%;
}

.clientcontract-manage-box .item-paymentmethod {
  width: 7%;
}

.clientcontract-manage-box .item-contract-date {
  width: 8%;
}

.clientcontract-manage-box .item-baoyongqi {
  width: 5%;
}

.clientcontract-manage-box .item-status {
  width: 7%;
}

.clientcontract-manage-box .item-details {
  width: 6%;
  text-align: center;
}
/*标题栏宽度*/
.title-bar-v1 .border-middle {
  width: 956px;
}
/*发票管理编辑*/
.input-width-v2 {
  width: 580px;
}

.input-width-v3 {
  width: 230px;
}

.label-v1 {
  float: left;
  font-weight: 700;
  text-align: right;
  width: 80px;
  white-space: nowrap;
  overflow: hidden;
  margin-right: 3px;
}

.select-left-box {
  float: left;
}

.select-hasother-box {
  float: left;
}

.select-other-box .select-hasother-box {
  width: 150px;
}

.input-width-v3 select {
  background: none repeat scroll 0 0 #fff;
  border: medium none;
  cursor: pointer;
  margin: 3px 0 0;
  width: 96%;
}

.input-other-v1 {
  float: left;
  width: 60px;
  margin-left: 10px;
  display: none;
}

.select-other-box .input-other-v1 {
  display: block;
}

.invoice-operation-box .special-invoice-box {
  display: none;
}

.invoice-operation-box .select-user .outbox-item-list li {
  margin: 0;
}

.invoice-operation-box .outbox input {
  margin-top: 0;
}

.invoice-operation-box .edition-content-box {
  margin-left: 50px;
}

.invoice-operation-box .bank-invoice-box {
}

  .invoice-operation-box .bank-invoice-box .btnautofull {
    padding: 4px 10px;
    cursor: pointer;
  }
/*发票管理列表*/
.invoice-manage-box .item-select {
  text-align: center;
  width: 5%;
}

.invoice-manage-box .item-staff {
  width: 6%;
}

.invoice-manage-box .item-type {
  width: 6%;
}

.invoice-manage-box .item-amount {
  width: 9%;
  text-align: right;
  margin-right: 1%;
}

.invoice-manage-box .item-clientname {
  width: 17%;
}

.invoice-manage-box .item-applicationdate {
  width: 7%;
}

.invoice-manage-box .item-status {
  width: 9%;
}

.invoice-manage-box .item-incomestatus {
  width: 7%;
}

  .invoice-manage-box .item-incomestatus .select-verify-btn-box {
    text-align: center;
  }

.invoice-manage-box .item-company {
  width: 7%;
}

.invoice-manage-box .item-accountday {
  width: 7%;
  text-align: center;
}

.invoice-manage-box .item-memo {
  width: 4%;
}

.invoice-manage-box .item-operate {
  width: 7%;
}

.invoice-manage-box .item-drawerstaffname {
  width: 6%;
}

.invoice-manage-box .item-operate .operate-edit,
.invoice-manage-box .item-operate .operate-detail {
  margin: 0 3px;
  cursor: pointer;
}

.invoice-manage-box .memo-icon {
  background: url(images/invoice-icon.png) no-repeat 0 0;
  display: block;
  height: 16px;
  width: 16px;
  cursor: pointer;
  margin: 6px 0 0 3px;
}

  .invoice-manage-box .memo-icon.un {
    background-position: 0 -16px;
  }

.invoice-manage-box .detail-box {
  display: none;
}

.invoice-manage-box .open {
  background: url("images/saffh2.jpg") repeat-x scroll 0 -55px #eef7fd !important;
  border: 1px solid #a3cce7 !important;
  color: #000;
  line-height: 29px;
  width: 956px;
  height: auto;
}

  .invoice-manage-box .open .detail-box {
    display: block;
    line-height: 22px;
  }

  .invoice-manage-box .open .detail-text {
    display: inline-block;
    padding: 2px 20px;
  }

.invoice-manage-box .detail-list {
  padding: 2px 20px;
  border-top: 1px dotted #999999;
}

  .invoice-manage-box .detail-list li {
    width: 33%;
    float: left;
  }

.invoice-manage-box .data-list-li {
  border-left: 1px solid transparent;
  border-right: 1px solid transparent;
  border-top: 1px solid #fff;
  width: 956px;
}

.invoice-manage-box .detail-box .detail-label {
  color: #21639C;
  font-weight: 700;
}

  .invoice-manage-box .detail-box .detail-label:not(:first-child) {
    margin-left: 5px;
  }
/*SelectVerify 插件专用*/
/*基本样式*/
.select-verify-btn-box.show {
  background-color: #fff6e4;
  border: 1px solid #da6b00;
  height: 27px;
}

.select-verify-btn-box {
  color: #df730a;
  display: block;
  font-weight: 700;
  position: relative;
  padding-left: 10%;
  width: 70%;
  border: 1px solid transparent;
}

.select-verify-text-box {
  display: block;
  height: 27px;
  line-height: 27px;
  margin: 0;
  display: inline-block;
}

.select-verify-icon-box i {
  background: url("/static/images/financial-distribution-status-btn-icon.gif") no-repeat scroll 0 0 #000;
  display: inline-block;
  height: 9px;
  margin-left: 6px;
  transform: rotate(180deg);
  transition: transform .2s ease-in 0s;
  width: 9px;
}

.show .select-verify-icon-box i {
  background: url("/static/images/financial-distribution-status-btn-icon.gif") no-repeat scroll 0 0 #000;
  display: inline-block;
  height: 9px;
  margin-left: 6px;
  height: 9px;
  transform: rotate(0deg);
  transition: transform .2s ease-in 0s;
  width: 9px;
}
/*弹出样式*/
.select-verify-dialog {
  background-color: #fff6e4;
  border: 1px solid #da6b00;
  display: block;
  line-height: 25px;
  position: absolute;
  width: 90px;
  z-index: 3;
}

  .select-verify-dialog .select-verify-item.itemhover, .select-verify-dialog .select-verify-item:hover {
    background-color: #FFF1CC;
  }

  .select-verify-dialog .select-verify-item.current {
    background-color: #FCE9C4;
  }

  .select-verify-dialog .select-verify-item {
    border-bottom: 1px solid #d0cecf;
    cursor: pointer;
    height: 25px;
    padding-left: 10px;
  }

  .select-verify-dialog .select-verify-btn-list-box li {
    float: left;
    text-align: center;
    width: 50%;
  }

  .select-verify-dialog .select-verify-confim {
    width: 38px;
    cursor: pointer;
  }

  .select-verify-dialog .select-verify-cancel {
    width: 38px;
    cursor: pointer;
  }
/*请款管理*/
.paymentorder-operation-box .left-box {
  background: none repeat scroll 0 0 #eee;
  border: 1px solid #b7b7b7;
  width: 754px;
}

.paymentorder-operation-box .right-box {
  float: left;
}

.paymentorder-operation-box .input-width-v1 {
  width: 230px;
}

.paymentorder-operation-box .clientcontract-form {
  border-left: 0 none;
  border-right: 0 none;
  border-top: 1px solid #b7b7b7;
  clear: both;
  padding: 20px 0;
  text-align: center;
}

.paymentorder-operation-box .party-title {
  border-bottom: 1px solid #B7B7B7;
  margin-bottom: 10px;
  padding: 5px 0;
  width: 100%;
  float: left;
  font-size: 14px;
  font-weight: bold;
}

.paymentorder-operation-box .paymentorder-title {
  line-height: 35px;
  font-size: 14px;
}

.paymentorder-operation-box .paymentorder-content {
  line-height: 35px;
  text-indent: 2em;
  font-size: 14px;
}

.paymentorder-operation-box .paymentorder-partyatitle-nihao {
  margin-left: 10px;
}

.paymentorder-operation-box .partyatitle {
  width: 80px;
  font-weight: 700;
  font-size: 14px;
  line-height: 23px;
}

.paymentorder-operation-box .content-input {
  width: 80px;
  margin: 0 5px;
  font-size: 14px;
  line-height: 23px;
}

.paymentorder-operation-box .select {
  background: none repeat scroll 0 0 #fff;
  border: medium none;
  cursor: pointer;
  margin: 3px 0 0;
  width: 90%;
  float: left;
}

.paymentorder-operation-box .paymentdescription {
  width: 98%;
  height: 70px;
  padding: 5px;
  font-size: 14px;
}

.form-box {
  border-left: 0 none;
  border-right: 0 none;
  border-top: 1px solid #b7b7b7;
  clear: both;
  padding: 20px 0;
  text-align: center;
}

.paymentorder-operation-box .select-user .outbox-item-list li {
  margin: 0;
}
/*请款管理*/
.paymentorder-manage-box .item-select {
  text-align: center;
  width: 50px;
}

.paymentorder-manage-box .item-staff {
  width: 80px;
}

.paymentorder-manage-box .item-company {
  width: 80px;
}

.paymentorder-manage-box .item-fukuan {
  width: 250px;
}

.paymentorder-manage-box .item-qingkuan {
  width: 90px;
}

.paymentorder-manage-box .item-shanggang {
  width: 80px;
}

.paymentorder-manage-box .item-zhaopin {
  width: 100px;
}

.paymentorder-manage-box .item-date {
  width: 100px;
}

.paymentorder-manage-box .item-operation {
  width: 90px;
  text-align: center;
}

  .paymentorder-manage-box .item-operation a {
    margin: 0 3px;
  }
/*daterangepicker*/
.datepicker-range-box {
  float: left;
  margin-right: 5px;
}
/*签约绩效*/
.assessment-performance-status-box {
  float: right;
}

.exclamation {
  background: url("images/risfondIcon.gif") no-repeat scroll -64px -288px #000;
  display: block;
  float: right;
  height: 16px;
  margin: 11px 0 0 5px;
  width: 16px;
}

.qianyue-performance-shenhe {
  color: #FFDF00;
}

.qianyue-performance-tongguo {
  color: #BFFB02;
}

.qianyue-performance-bohui {
  color: red;
}
/*客户转移记录*/
.client-transferlog-operate .list-item-2 li {
  border-bottom: 1px dashed #999;
  height: 25px;
  cursor: default;
}

.client-transferlog-operate .client-transferlog-content {
  width: 58%;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  margin-right: 2%;
}

.client-transferlog-operate .client-transferlog-date {
  width: 40%;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
}

.client-transferlog-operate .client-transferlog-item {
}

.client-transferlog-detail-title {
}

  .client-transferlog-detail-title span {
    float: left;
    text-align: center;
  }

    .client-transferlog-detail-title span strong {
      color: #333 !important;
    }

.client-transferlog-detail-list {
}

  .client-transferlog-detail-list li {
    border: 0 none !important;
  }

  .client-transferlog-detail-list span {
    float: left;
    text-align: center;
    height: 25px;
  }

.client-transferlog-detail-staffname {
  width: 65px;
}

.client-transferlog-detail-companyname {
  width: 65px;
}

.client-transferlog-detail-changereason {
  width: 70px;
}

.client-transferlog-detail-updatetime {
  width: 75px;
}

.client-transferlog-detail-createstaffname {
  width: 65px;
}

.client-transferlog-detail-ispublicclient {
  width: 65px;
}

.client-transferlog-detail-reasonmemo-p {
  border-top: 1px dashed #333;
  line-height: 24px;
  padding-top: 4px;
  padding-left: 8px;
  margin-top: 3px;
}

  .client-transferlog-detail-reasonmemo-p label {
    float: left;
    width: 65px;
    color: #333;
    font-weight: bold;
  }

  .client-transferlog-detail-reasonmemo-p div {
    float: left;
    width: 337px;
    word-break: break-all;
    word-wrap: break-word;
  }
/*催款管理*/
.paymentorder-operation-box .paymentorder-warning {
  font-size: 14px;
  line-height: 35px;
  color: red;
}

.select-list-urge-box {
  background-color: #fefefe;
  border-left: solid 1px #7fcae2;
  border-right: solid 1px #7fcae2;
  border-bottom: solid 1px #7fcae2;
  color: #444;
  font-size: 12px !important;
  line-height: 20px !important;
  position: absolute;
  width: 100px;
}

  .select-list-urge-box a {
    line-height: 24px;
    padding-left: 10px;
    display: block;
  }

    .select-list-urge-box a:hover {
      background-color: #f2f2f2;
    }

.select-list-active-box {
  float: right;
  height: 24px;
  line-height: 24px;
  margin-top: 7px;
  width: 100px;
}

  .select-list-active-box.open {
    border-left: solid 1px #7fcae2;
    border-top: solid 1px #7fcae2;
    border-right: solid 1px #7fcae2;
    background-color: #FEFEFE;
    color: #000;
  }

  .select-list-active-box .select-list-active {
    color: #fff;
    font-weight: 700;
    white-space: nowrap;
  }

  .select-list-active-box.open .select-list-active {
    color: #000;
    font-weight: 700;
  }

.select-list-urge-box ul {
  border-top: solid 1px #d0cecf;
}
/*筛选QueryTermList3资源*/
.term-custom-list {
  float: left;
  width: 926px;
}

.term-custom-list-underline {
  border-bottom: 1px dotted #005f99;
}

.term-custom-list .term-custom-list-title {
  float: left;
  font-weight: bold;
  margin-right: 10px;
  padding-left: 15px;
  width: 68px;
  overflow: hidden;
  white-space: nowrap;
}

.term-custom-list .term-custom-list-data-box {
  width: 833px;
  float: left;
  height: 28px;
  overflow: hidden;
}

  .term-custom-list .term-custom-list-data-box a {
    color: #21639C;
  }

    .term-custom-list .term-custom-list-data-box a:hover {
      color: #FC8B00;
    }

.term-custom-list .term-custom-list-data {
  overflow: hidden;
}

.term-custom-list .term-custom-list-item {
  float: left;
  margin-right: 17px;
  white-space: nowrap;
}

.term-custom-list .term-custom-list-more {
  width: 61px;
  height: auto;
  float: right;
}

.term-custom-list .term-custom-list-more-picture {
  background: url(images/unfold-icon.jpg) no-repeat;
  width: 61px;
  height: 20px;
  background-position: 0 0;
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
}

.term-custom-list .term-open {
  height: auto;
  overflow: visible;
}

  .term-custom-list .term-open .term-custom-list-more-picture {
    background-position: 0 -20px;
  }

.term-custom-list .condition li a {
  color: red;
  display: block;
  padding: 0 38px 0 8px;
}

.term-custom-list .condition li {
  background: url("images/dot.gif") no-repeat scroll right center #f9d2ce;
  border: 1px solid #f46e6e;
  height: 20px;
  line-height: 20px;
  margin: 9px 6px 0 0;
}

.term-custom-list.condition {
  overflow: visible;
}

  .term-custom-list.condition .term-custom-list-data-box {
    height: auto;
    overflow: visible;
    margin-bottom: 8px;
    line-height: 40px;
  }
/*修改简历筛选*/
.resume-desiredsalaryrange-filter {
  width: 720px;
}

  .resume-desiredsalaryrange-filter .term-custom-list-data-box {
    width: 610px;
  }

.resume-photostatus-filter {
  width: 205px;
}

  .resume-photostatus-filter .term-custom-list-data-box {
    width: 70px;
  }

.resume-educationlevel-filter {
  width: 360px;
}

  .resume-educationlevel-filter .term-custom-list-data-box {
    width: 266px;
  }

.resume-gender-filter {
  width: 120px;
}

  .resume-gender-filter .term-custom-list-data-box {
    width: 65px;
  }

  .resume-gender-filter .term-custom-list-title {
    margin: 0;
    padding-left: 10px;
    width: 40px;
  }

.resume-candidatestatus-filter {
  width: 185px;
}

  .resume-candidatestatus-filter .term-custom-list-data-box {
    width: 85px;
  }

  .resume-candidatestatus-filter .term-custom-list-title {
    padding-left: 0;
  }

.resume-lastupdate-filter {
  width: 540px;
}

  .resume-lastupdate-filter .term-custom-list-data-box {
    width: 430px;
  }

.resume-language-filter {
  width: 680px;
}

  .resume-language-filter .term-custom-list-data-box {
    width: 570px;
  }

.resume-proficiency-filter {
  width: 245px;
}

  .resume-proficiency-filter .term-custom-list-data-box {
    width: 150px;
  }

.resume-salaryrange-filter {
  width: 726px;
}

  .resume-salaryrange-filter .term-custom-list-data-box {
    width: 633px;
  }

.resume-tuijianstatus-filter {
  width: 200px;
}

  .resume-tuijianstatus-filter .term-custom-list-data-box {
    width: 107px;
  }
/*邮件*/
.manageemail-settingconfig {
  float: right;
  margin-right: 20px;
  vertical-align: baseline;
  font-weight: 700;
}

.email-icon {
  background: url("images/risfondIcon.gif") no-repeat scroll 0 -752px #000;
  display: inline-block;
  height: 16px;
  margin: 0 5px 0 0;
  vertical-align: middle;
  width: 16px;
}

.email-box {
  margin: 10px auto;
  width: 95%;
}

  .email-box .line {
    margin: 10px 0;
    width: 100%;
  }

    .email-box .line .emailkey {
      float: left;
      font-weight: bold;
      margin-right: 0;
      padding-right: 10px;
      text-align: right;
      width: 70px;
      white-space: nowrap;
      overflow: hidden;
    }

    .email-box .line .emailvalue {
      float: left;
      width: 380px;
      text-align: left;
    }

      .email-box .line .emailvalue .emailaccount, .email-box .line .emailvalue .emailpwd {
        width: 120px;
      }

    .email-box .line .email-change-box {
      width: 430px;
    }

      .email-box .line .email-change-box .btn-setconfig {
        float: right;
        text-decoration: underline;
      }

  .email-box .btn-setsendmail {
    float: right;
    text-decoration: underline;
  }

  .email-box .line .emailconnect {
    vertical-align: baseline;
    margin: 0 3px;
  }

  .email-box .line .emailaccounttype {
    vertical-align: middle;
  }

  .email-box .line .emailsign-config {
    width: 360px;
    resize: none;
    height: 180px;
  }

  .email-box .line .textarea-v1 {
    background-color: #fcfcfc;
    border: 1px solid #b7b7b7;
    box-shadow: 1px 1px 1px #d3d3d3 inset;
    font-size: 12px;
    padding: 7px;
  }
  /*邮件弹出*/
  .email-box .emailcategory {
    width: 120px;
  }

  .email-box .emailcontent {
    width: 420px;
    height: 150px;
    resize: none;
  }

  .email-box .emailtitle {
    width: 428px;
  }

  .email-box .modalityboxes-email .div_txt {
    background-color: #FCFCFC;
    border: 1px solid #B7B7B7;
    box-shadow: 1px 1px 1px #D3D3D3 inset;
    border-image: none;
    border-right: 1px solid #c3c3c3;
    border-style: solid;
    border-width: 1px;
    font-family: Tahoma;
    height: auto;
    min-height: 23px;
    padding: 1px;
    width: auto;
    float: left;
    width: 424px;
    overflow-x: hidden;
    overflow-y: auto;
    max-height: 100px;
    position: relative;
    padding: 0 4px;
  }

  .email-box .addr_text {
    background: none repeat scroll 0 0 transparent;
    height: 25px;
    margin: 0;
  }

  .email-box .addr_inputkey {
    outline: none;
    -webkit-appearance: none;
    width: 100%;
    height: 25px;
    line-height: 25px;
    background: none repeat scroll 0 0 transparent;
    border: medium none;
  }

  .email-box .addr_htmlkey {
    height: 1px;
    overflow: visible;
    white-space: nowrap;
    border: medium none;
    margin: 0;
    padding: 0;
    font-family: Tahoma;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    word-spacing: 0;
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  .email-box .addr_outlinekey_box {
    clear: both;
    border: none;
    margin: 0;
    padding: 0;
  }

  .email-box .addr_outlinekey_input {
    position: absolute;
    border: none;
    padding: 0;
    width: 10px;
    left: -9999px;
    top: -9999px;
  }

  .email-box .addr_text_box_outmax {
    float: left;
    border: medium none;
    width: 1px;
  }

  .email-box .addr_del {
    width: 1px;
    height: 8px;
    position: relative;
    top: 10px;
    left: -10px;
    float: left;
  }

    .email-box .addr_del .addr_del_link {
      background: url("/static/style/118.png") no-repeat scroll center center transparent;
      width: 7px;
      height: 8px;
      display: none;
    }

      .email-box .addr_del .addr_del_link.icon-show {
        display: block;
      }

  .email-box .item-data {
    color: #a0a0a0;
    float: left;
    padding: 0 5px;
    white-space: nowrap;
    cursor: text;
  }

    .email-box .item-data .addr_del_block {
      width: 7px;
    }

  .email-box .addr_base {
    float: left;
    white-space: nowrap;
    cursor: default;
    color: #A0A0A0;
  }

    .email-box .addr_base b {
      color: #000;
      font-weight: normal;
    }

  .email-box .foucs {
    background-color: #528bcb;
  }

  .email-box .hover span, .email-box .hover b {
    background-color: #e0ecf9;
  }

  .email-box .current span, .email-box .current b {
    background-color: #528bcb;
    color: #fff;
  }

  .email-box .addr_error span, .email-box .addr_error b {
    color: red;
  }

  .email-box .semicolon {
    width: 5px;
  }

  .email-box .tips {
    color: #999;
    padding-left: 20px;
  }
/*邮件管理*/
.mail-manage-box .data-list .data-list-name .item, .mail-manage-box .data-list .email-data-box .item {
  white-space: nowrap;
  overflow: hidden;
  margin-right: 0.5%;
}

.mail-manage-box .input-radio {
  margin-top: 0;
}

.mail-manage-box .item-select {
  text-align: center;
  width: 50px;
}

.mail-manage-box .item-category {
  width: 5%;
}

.mail-manage-box .item-name {
  width: 10%;
}

.mail-manage-box .item-address {
  width: 13%
}

.mail-manage-box .item-title {
  width: 23%
}

.mail-manage-box .item-status {
  width: 4%;
}

.mail-manage-box .item-resultstatus {
  width: 8%;
}

.mail-manage-box .item-staff {
  width: 7%;
}

.mail-manage-box .item-company {
  width: 7%;
}

.mail-manage-box .item-date {
  width: 12%;
}

.email-setting-icon {
  background: url("images/risfondIcon.gif") no-repeat scroll 0 -719px #000;
  display: inline-block;
  height: 16px;
  margin: 0 5px 0 0;
  vertical-align: middle;
  width: 16px;
}

.mail-manage-box .data-list-content .email-data-content {
  line-height: 29px;
  width: 958px;
}

.mail-manage-box .email-data-content {
  border-bottom: 1px solid #fff;
  -moz-border-bottom-colors: none;
  -moz-border-left-colors: none;
  -moz-border-right-colors: none;
  -moz-border-top-colors: none;
  border-color: transparent;
  border-image: none;
  border-style: none solid;
  border-width: 1px 0 0 1px;
  font-size: 12px;
  margin: 0 0 1px;
  overflow: hidden;
  padding: 0 !important;
  width: 960px;
  line-height: 31px;
}

.mail-manage-box .email-data-box .operate {
  float: left;
  margin: 5px 5px 0 5px;
}

.mail-manage-box .email-data-box {
  height: 29px;
  margin: 0;
  overflow: hidden;
  padding: 0;
  width: 960px;
  cursor: pointer;
}

  .mail-manage-box .email-data-box.hover {
    color: #000;
    background: url(images/saffh2.jpg) 0 -55px repeat-x !important;
  }

.mail-manage-box .radio-width {
  width: 59px;
}

.mail-manage-box .email-data-detail {
  background-color: #EEF7FD;
  width: 960px;
  display: none;
}

  .mail-manage-box .email-data-detail .detail-head {
    height: 31px;
    border-top: solid 1px #AAD0E9;
    border-bottom: dotted 1px #AAD0E9;
  }

  .mail-manage-box .email-data-detail .explain1 {
    border-right: dotted 1px #AAD0E9;
  }

  .mail-manage-box .email-data-detail .detail-head ul {
    height: 31px;
    background-color: #DBF0FE;
  }

  .mail-manage-box .email-data-detail .detail-head li {
    height: 31px;
    width: 476px;
    float: left;
    text-indent: 12px;
  }

  .mail-manage-box .email-data-detail .detail-data-box {
    width: 100%;
  }

    .mail-manage-box .email-data-detail .detail-data-box .detail-data-number {
      float: left;
      width: 476px;
    }

    .mail-manage-box .email-data-detail .detail-data-box .detail-data-content {
      float: left;
      width: 476px;
      border-left: dotted 1px #AAD0E9;
    }

    .mail-manage-box .email-data-detail .detail-data-box .detail-data-p {
      padding-left: 15px;
      padding-top: 10px;
      word-wrap: break-word;
    }

    .mail-manage-box .email-data-detail .detail-data-box .number-box {
      overflow-x: hidden;
      padding-left: 15px;
      padding-top: 10px;
    }

    .mail-manage-box .email-data-detail .detail-data-box li.item-number-box {
      width: 50%;
      height: 31px;
      line-height: 31px;
      float: left;
    }

    .mail-manage-box .email-data-detail .detail-data-box .fontcolor {
      color: #7B7B7B;
    }

    .mail-manage-box .email-data-detail .detail-data-box .namefontcolor {
      max-width: 100px;
      overflow: hidden;
    }

.mail-manage-box .email-open-box .email-data-detail {
  display: block;
}

.mail-manage-box .email-open-box {
  color: #000;
  background-color: #eef7fd !important;
  border: 1px solid #a3cce7 !important;
  line-height: 29px;
  width: 958px;
}

.mail-manage-box .item-status .state-icon {
  vertical-align: middle;
  display: inline-block !important;
}

.width-left-v1-0 {
  width: 540px;
}

  .width-left-v1-0 .term-custom-list-data-box {
    width: 390px;
  }

.width-right-v1-0 {
  width: 385px;
}

  .width-right-v1-0 .term-custom-list-data-box {
    width: 230px;
  }

.width-left-v1-1 {
  width: 340px;
}

  .width-left-v1-1 .term-custom-list-data-box {
    width: 240px;
  }

.width-center-v1-1 {
  width: 250px;
}

  .width-center-v1-1 .term-custom-list-data-box {
    width: 150px;
  }

.width-right-v1-1 {
  width: 335px;
}

  .width-right-v1-1 .term-custom-list-data-box {
    width: 230px;
  }

.shortcut-send-email {
  background: url("images/send-email-icon.png") no-repeat 50% 50%;
  display: inline-block;
  height: 12px;
  width: 16px;
  margin: 0 4px;
  cursor: pointer;
  vertical-align: middle;
}

  .shortcut-send-email.hide {
    display: none;
  }

.email-number-tip {
  display: inline-block;
  line-height: 20px;
  margin: 0 3px;
  vertical-align: middle;
}

  .email-number-tip .succ {
    background: url("images/risfondIcon.gif") no-repeat;
    display: block;
    height: 16px;
    width: 16px;
    background-position: -64px -160px;
  }

  .email-number-tip .hits {
    background: url("images/risfondIcon.gif") no-repeat;
    display: block;
    height: 16px;
    width: 16px;
    background-position: -64px -288px;
  }
/*发送邮件的同事卡片模式*/
#showCardPanel .colleague-list .send-sms {
  padding: 0 5px 2px 0;
}

#showCardPanel .colleague-list .send-qq {
  padding: 0 0 2px 0;
}

#showCardPanel .colleague-list .send-email {
  margin: 0 auto;
  width: 50px;
}

.users-list .colleague-list .send-sms {
  padding: 0 5px 2px 0;
}

.users-list .colleague-list .send-qq {
  padding: 0 0 2px 0;
}

.users-list .colleague-list .send-email {
  margin: 0 auto;
  width: 50px;
}

.attendance .send-email {
  height: 18px;
  padding: 5px 5px 0 0;
  margin: 0 20px;
  width: 50px;
  float: left;
}

.attendance .colleague-list .send-sms {
  padding: 5px 5px 0 0;
}

.attendance .colleague-list .send-qq {
  padding: 5px 0 0 0;
}
/*禁用的背景色*/
.disabled-bg {
  background-color: #F9F9F9;
}
/*求职意向改进*/
.check-all-box {
  float: left;
}

  .check-all-box .check-all-label {
    font-weight: 700;
  }

  .check-all-box .check-all-checkbox {
    margin: 0 5px;
  }

.resume-view-operation-icon {
  display: inline-block !important;
  margin: 0 5px;
  vertical-align: middle;
}

.baidu-operation-icon {
  background: url(images/baidu-icon.png) no-repeat;
  height: 16px;
  width: 16px;
}

.google-operation-icon {
  background: url(images/google-icon.png) no-repeat;
  height: 16px;
  width: 16px;
}

.resume-view-search-iframe {
  display: none;
  width: 0;
  height: 0;
}
/*身份证*/
.diploma-search-box {
  background-color: #eee;
  border: 1px solid #b7b7b7;
  width: 100%;
}

  .diploma-search-box .border-middle {
    width: 956px;
  }

  .diploma-search-box .diploma-search-baseinfo-box {
    width: 60%;
    margin: 20px 20%;
    font-size: 14px;
    font-weight: 700;
  }

  .diploma-search-box .diploma-search-line {
    margin: 5px 0;
    float: left;
  }

  .diploma-search-box .diploma-search-lb {
    white-space: pre;
    float: left;
    width: 100px;
    text-align: right;
    font-weight: bold;
  }

  .diploma-search-box .diploma-search-text {
    width: 400px;
  }

  .diploma-search-box .diploma-search-submit-box {
    width: 80%;
    float: left;
    padding: 10px 30px;
    text-align: center;
  }

  .diploma-search-box .diploma-search-details-box {
    width: 70%;
    padding: 20px 15%;
    background-color: #fff;
  }

  .diploma-search-box .diploma-tips {
    font-size: 12px;
    color: red;
  }

.diploma-search-result-box {
  text-align: center;
  color: red;
}

.diploma-search-box .diploma-search-result-details-box {
  width: 100%;
  overflow: hidden;
  margin-top: 20px;
}

.diploma-table {
  border-left: 1px solid #aaced8;
  border-top: 1px solid #aaced8;
  margin: 0 auto;
  word-wrap: break-word;
  border: 1px solid #c2dbff;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
  line-height: 43px;
}

  .diploma-table td {
    border-bottom: 1px solid #b3ccf2;
    border-right: 1px solid #b3ccf2;
    height: 29px;
  }

  .diploma-table .font1 {
    font-family: "黑体";
    font-size: 16px;
    padding-left: 8px;
  }

  .diploma-table .font2 {
    font-family: "楷体_GB2312";
    font-size: 16px;
    padding-left: 6px;
  }

  .diploma-table .font615 {
    font-family: "宋体";
    font-size: 12px;
    padding-left: 6px;
  }

  .diploma-table .font3 {
    font-size: 12px;
    line-height: 20px;
    padding-left: 6px;
  }

  .diploma-table .font4 {
    font-family: "黑体";
    font-size: 16px;
    line-height: 36px;
  }

  .diploma-table .font5 {
    font-family: "黑体";
    letter-spacing: 4px;
    padding-left: 8px;
  }

  .diploma-table .font6 {
    font-family: "Times New Roman",Times,serif;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 1px;
    padding-left: 8px;
  }

  .diploma-table .tb2 td, .diploma-table .tb2 tr {
    border-bottom: 0 none;
  }

  .diploma-table .td1 {
    border-right: 0 none;
  }

  .diploma-table .td2 {
    border-bottom: 1px solid #aaced8;
    border-right: 1px solid #aaced8;
    line-height: 25px;
    padding-bottom: 10px;
    padding-left: 15px;
  }

  .diploma-table .logoBot {
    margin-right: 20px;
    text-align: right;
  }

.diploma-search-box .diploma-search-textarea {
  height: 80px;
  padding: 8px;
  width: 390px;
}

.diploma-search-box .diploma-search-submit-box .diploma-search-submit-btn {
  width: 100px;
  line-height: initial;
}

.diploma-manage-box .input-radio {
  margin-top: 0;
}

.diploma-manage-box .item-select {
  text-align: center;
  width: 50px;
}

.diploma-manage-box .item-staff {
  width: 70px;
}

.diploma-manage-box .item-company {
  width: 80px;
}

.diploma-manage-box .item-name {
  width: 80px;
}

.diploma-manage-box .item-sex {
  width: 40px;
}

.diploma-manage-box .item-number {
  width: 200px;
}

.diploma-manage-box .item-year {
  width: 60px;
}

.diploma-manage-box .item-result {
  width: 80px;
}

.diploma-manage-box .item-status {
  width: 80px;
}

.diploma-manage-box .item-date {
  width: 90px;
}

.diploma-manage-box .item-notes {
  width: 50px;
}

.diploma-manage-box .item-operation {
  width: 100px;
}

.diploma-manage-box .base-info-box {
  float: left;
  width: 100%;
}

.diploma-manage-box .detail-info-box {
  display: none;
  width: 700px;
  margin: 0 auto;
}

.diploma-manage-box .open .detail-info-box {
  display: block;
  margin: 0;
  padding: 0 129px 0 129px;
  background-color: #fff;
  float: left;
}

.diploma-manage-box .take-notes {
  background: none repeat scroll 0 0 #fff;
  border-left: 1px solid #eee;
  border-right: 1px solid #eee;
  border-top: 1px solid #bfbfbf;
  cursor: pointer;
  height: 18px;
  line-height: 18px;
  margin: 2px auto;
  text-align: center;
  width: 30px;
}

  .diploma-manage-box .take-notes strong {
    color: red;
  }

.diploma-manage-box .select-result-ok {
  color: green;
  font-weight: 700;
}

.diploma-manage-box .select-result-no {
  color: red;
  font-weight: 700;
}

.diploma-manage-box .select-status-ok {
  color: green;
  font-weight: 700;
}

.diploma-manage-box .select-status-no {
  color: red;
  font-weight: 700;
}
/*COMMON*/
.data-isopen-btn {
  background-image: url("images/spread-icon.gif");
  background-position: 0 0;
  background-repeat: no-repeat;
  cursor: pointer;
  display: inline-block;
  height: 16px;
  line-height: 16px;
  text-indent: 20px;
  vertical-align: middle;
  width: 80px;
  white-space: nowrap;
  overflow: hidden;
}

.open .data-isopen-btn {
  background-position: 0 -16px;
}
/*员工发送短信通知*/
.staff-status-sms-label {
  margin-right: 0 !important;
  text-align: center !important;
  font-weight: 100 !important;
}

.staff-status-sms-text {
  margin-left: 3px;
  cursor: default;
}
/*身份证查询*/
.pin-search-box {
  width: 100%;
  background-color: #EEEEEE;
  border: 1px solid #B7B7B7;
}

.pin-search-input {
  width: 95%;
}

.pin-search-box .border-middle {
  width: 956px;
}

.pin-search-input-box {
  width: 85%;
  float: left;
}

  .pin-search-input-box .pin-search-input {
    float: right;
  }

.pin-search-easy-btn-box {
  width: 15%;
  float: left;
}

  .pin-search-easy-btn-box .pin-search-easy-btn {
    margin: 0 0 0 10px;
  }

.pin-search-box .search-box {
  width: 55%;
  margin: 20px auto;
}

.pin-details-box {
  background: url("/css/images/pin-icon.gif") no-repeat;
  width: 523px;
  height: 298px;
  position: relative;
  margin: 10px auto 10px auto;
  font-size: 16px;
  font-weight: 700;
}

  .pin-details-box .lb-key {
    color: #1388B6;
    position: absolute;
    white-space: pre;
  }

  .pin-details-box .sp-val {
    color: #2f2f2f;
    position: absolute;
  }

  .pin-details-box .lb-pin-name {
    top: 40px;
    left: 40px;
  }

  .pin-details-box .pin-name {
    top: 40px;
    left: 110px;
  }

  .pin-details-box .lb-pin-sex {
    top: 80px;
    left: 40px;
  }

  .pin-details-box .pin-sex {
    top: 80px;
    left: 110px;
  }

  .pin-details-box .lb-pin-nation {
    top: 80px;
    left: 180px;
  }

  .pin-details-box .pin-nation {
    top: 80px;
    left: 240px;
  }

  .pin-details-box .lb-pin-birth {
    top: 120px;
    left: 40px;
  }

  .pin-details-box .pin-birth {
    top: 120px;
    left: 110px;
    white-space: pre;
  }

  .pin-details-box .lb-pin-certificate {
    top: 160px;
    left: 40px;
  }

  .pin-details-box .pin-certificate {
    top: 160px;
    left: 110px;
    width: 220px;
  }

  .pin-details-box .lb-pin-address {
    top: 200px;
    left: 40px;
  }

  .pin-details-box .pin-address {
    top: 200px;
    left: 110px;
    width: 220px;
  }

  .pin-details-box .lb-pin-number {
    top: 260px;
    left: 40px;
  }

  .pin-details-box .pin-number {
    top: 260px;
    left: 160px;
    letter-spacing: 6px;
  }

  .pin-details-box .pin-apply-pay-btn {
    top: 210px;
    left: 365px;
    position: absolute;
    font-size: 14px;
    text-decoration: underline;
  }

  .pin-details-box .pin-pictrue {
    height: 152px;
    width: 124px;
    top: 40px;
    left: 350px;
    position: absolute; /* background: url(/css/images/default_pin_big.gif) no-repeat;*/
    border: 0 none;
  }

.pin-search-box .pin-verify-tip-box {
  width: 100%;
  margin: 10px 0 40px 0;
}

.pin-search-box .pin-verify-tip {
  margin: 0 20%;
  color: #f60;
}

.pin-manage-box .input-radio {
  margin-top: 0;
}

.pin-manage-box .item-select {
  text-align: center;
  width: 50px;
}

.pin-manage-box .item-staff {
  width: 70px;
}

.pin-manage-box .item-company {
  width: 80px;
}

.pin-manage-box .item-name {
  width: 80px;
}

.pin-manage-box .item-sex {
  width: 40px;
}

.pin-manage-box .item-number {
  width: 150px;
}

.pin-manage-box .item-certificate {
  width: 130px;
}

.pin-manage-box .item-result {
  width: 70px;
}

.pin-manage-box .item-status {
  width: 70px;
}

.pin-manage-box .item-picture {
  width: 30px;
}

.pin-manage-box .item-date {
  width: 80px;
}

.pin-manage-box .item-notes {
  width: 50px;
}

.pin-manage-box .take-notes {
  background: none repeat scroll 0 0 #fff;
  border-left: 1px solid #eee;
  border-right: 1px solid #eee;
  border-top: 1px solid #bfbfbf;
  cursor: pointer;
  height: 18px;
  line-height: 18px;
  margin: 2px auto;
  text-align: center;
  width: 30px;
}

  .pin-manage-box .take-notes strong {
    color: red;
  }

.pin-manage-box .item-operation {
  width: 70px;
}

.pin-manage-box .base-info-box {
  float: left;
  width: 100%;
}

.pin-manage-box .detail-info-box {
  float: left;
  width: 100%;
  display: none;
}

.pin-manage-box .open .detail-info-box {
  display: block;
}

.pin-manage-box .select-result-ok {
  color: green;
  font-weight: 700;
}

.pin-manage-box .select-result-no {
  color: red;
  font-weight: 700;
}

.pin-manage-box .select-status-ok {
  color: green;
  font-weight: 700;
}

.pin-manage-box .select-status-no {
  color: red;
  font-weight: 700;
}
/*填写身份证姓名和理由*/
.pin-dialog-box {
  margin: 10px auto;
  width: 95%;
}

  .pin-dialog-box .line {
    margin: 10px 0;
    width: 100%;
  }

    .pin-dialog-box .line .d-key {
      float: left;
      font-weight: bold;
      margin-right: 0;
      padding-right: 10px;
      text-align: right;
      width: 70px;
      white-space: nowrap;
      overflow: hidden;
    }

    .pin-dialog-box .line .d-value {
      float: left;
      width: 380px;
      text-align: left;
    }

    .pin-dialog-box .line .textarea-v1 {
      background-color: #fcfcfc;
      border: 1px solid #b7b7b7;
      box-shadow: 1px 1px 1px #d3d3d3 inset;
      font-size: 12px;
      padding: 7px;
    }

  .pin-dialog-box .pin-name {
    width: 428px;
  }

  .pin-dialog-box .pin-reason {
    height: 60px;
    resize: none;
    width: 420px;
  }
/*推荐通知短信预览*/
.msg-box-layer .change-tabs-list {
  width: 90%;
  margin-left: 10%;
}

.msg-box-layer .change-tabs-item {
  width: 70px;
  float: left;
}

.msg-box-layer .change-tabs-btn {
  cursor: pointer;
  color: #0a6995;
}

  .msg-box-layer .change-tabs-btn.current {
    color: #f60;
    font-weight: 700;
    text-decoration: underline;
  }

.issendsms-tips-box {
  float: left;
  margin: 5px 0;
}

  .issendsms-tips-box * {
    float: left;
    height: 16px;
    line-height: 16px;
    margin: 0 4px !important;
  }
/*简历详情工作经历支持搜索相关公司人选*/
.resume-detail .experience .user-details .resume-jobtitle-box {
  width: 34%;
}

.resume-detail .resume-correlation-candidates {
  float: left;
  width: 55px;
  color: #6481B4;
}

  .resume-detail .resume-correlation-candidates:hover {
    text-decoration: underline;
  }
/*QQ聊天*/
.qqchitchat2 {
  float: right;
  width: 32px;
  height: 32px;
  margin: 27px 0 2px 0;
}
/*简历收藏*/
.resume-favorites-dialog-box {
  margin: 10px auto;
  width: 95%;
  text-align: left;
}

  .resume-favorites-dialog-box .resume-favorites-keywords-box {
    width: 100%;
  }

  .resume-favorites-dialog-box .resume-favorites-keywords-tips {
    font-weight: 700;
  }

  .resume-favorites-dialog-box .resume-favorites-keywords-list {
    width: 100%;
    margin: 10px 0;
  }

  .resume-favorites-dialog-box .resume-favorites-keywords-item {
    float: left;
    height: 27px;
    margin: 5px;
  }

  .resume-favorites-dialog-box .resume-favorites-keywords-text {
    border: solid #f60 1px;
    background-color: #FFCB99;
    color: #010000;
    height: 25px;
    line-height: 25px;
    display: block;
    font-weight: 700;
    padding: 0 5px;
    cursor: pointer;
  }

  .resume-favorites-dialog-box .resume-favorites-keywords-create-box {
    width: 100%;
  }

  .resume-favorites-dialog-box .resume-favorites-keywords-create-label {
    font-weight: 700;
    padding: 2px;
  }

  .resume-favorites-dialog-box .resume-favorites-keywords-create-import {
    padding: 2px;
    width: 240px;
  }

  .resume-favorites-dialog-box .resume-favorites-keywords-create-btn {
    width: 60px;
    margin: 2px 5px;
  }

  .resume-favorites-dialog-box .resume-favorites-keywords-strips {
    height: 1px;
    background-color: #696969;
    display: block;
    margin: 10px 0;
  }

  .resume-favorites-dialog-box .resume-favorites-keywords-assign-box {
    width: 100%;
    margin-bottom: 30px;
  }

  .resume-favorites-dialog-box .resume-favorites-keywords-assign-label {
    font-weight: 700;
    float: left;
    margin: 0 5px 0 0;
  }

  .resume-favorites-dialog-box .resume-favorites-keywords-assign-text-box {
    float: left;
  }

  .resume-favorites-dialog-box .resume-favorites-keywords-del-box {
    width: 100%;
  }

  .resume-favorites-dialog-box .resume-favorites-keywords-error {
    font-weight: 700;
    color: red;
  }

  .resume-favorites-dialog-box .deleting-btn {
    font-weight: 700;
    padding: 2px;
  }

  .resume-favorites-dialog-box .deleted-btn {
    font-weight: 700;
    padding: 2px;
  }

    .resume-favorites-dialog-box .deleting-btn:hover, .resume-favorites-dialog-box .deleted-btn:hover {
      text-decoration: underline;
    }

  .resume-favorites-dialog-box .delete-icon {
    background: url("images/error.gif") no-repeat scroll 0 0 #000;
    cursor: pointer;
    display: inline-block;
    height: 12px;
    line-height: 25px;
    vertical-align: middle;
    width: 11px;
    margin: 0 5px;
  }

  .resume-favorites-dialog-box .hide {
    display: none;
  }
/*背景调查*/
.backgroundreport-manage-box .item-select {
  text-align: center;
  width: 50px;
}

.backgroundreport-manage-box .item-renxuan {
  width: 80px;
  padding-left: 5px;
}

.backgroundreport-manage-box .item-zhiwei {
  width: 120px;
  padding-left: 5px;
}

.backgroundreport-manage-box .item-ruzhi {
  width: 220px;
  padding-left: 5px;
}

.backgroundreport-manage-box .item-guwen {
  width: 120px;
  padding-left: 5px;
}

.backgroundreport-manage-box .item-guishu {
  width: 80px;
  padding-left: 5px;
}

.backgroundreport-manage-box .item-chuangjian {
  width: 80px;
}

.backgroundreport-manage-box .item-baogao {
  width: 80px;
}

.backgroundreport-manage-box .item-pinggu {
  width: 40px;
  text-align: center;
}

  .backgroundreport-manage-box .item-pinggu .take-notes {
    margin-top: 5px;
    width: 40px;
    color: red;
    background: none repeat scroll 0 0 #fff;
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
    border-top: 1px solid #bfbfbf;
    cursor: pointer;
    height: 18px;
    line-height: 18px;
    display: block;
    float: left;
  }

.backgroundreport-manage-box .item-baogao a {
  margin: 0 3px;
}
/**/
.rnss-callphone {
  display: inline-block;
  vertical-align: middle;
  width: 16px;
  height: 16px;
  margin-left: 5px;
}

.rnss-callphone-icon {
  background: transparent url(/images/phone_ico.png) center center no-repeat;
}
/*首页-搜索用户*/
.index-searchgroup-staff {
  margin: 0 10px;
}

  .index-searchgroup-staff .form-control {
    background: #38454E;
    color: #BFC8D0;
    border: 1px solid #38454E;
    font-size: 14px;
  }

  .index-searchgroup-staff .btn {
    background: #38454E;
    border: 1px solid #38454E;
  }
/*盈利中心*/
.proft-manage-list .item-10 a {
  float: left;
  display: block;
  margin-left: 10px;
}
/*工资详情*/
#finalsalaryApp .salary_detail li {
  float: left;
  width: 25%;
  margin-bottom: 10px;
}

#finalsalary_page {
  margin-top: 0px !important;
  margin: 0 auto;
}

#finalsalaryApp .finalsalary_id {
  width: 50px !important;
}

#finalsalaryApp .finalsalary_name {
  width: 68px;
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}

#finalsalaryApp .finalsalary_date {
  width: 76px;
}

#finalsalaryApp .finalsalary_company {
  width: 94px;
}

#finalsalaryApp .finalsalary_basesalary {
  width: 90px;
}

#finalsalaryApp .finalsalary_performancesalary {
  width: 100px;
}

#finalsalaryApp .finalsalary_bonussalary {
  width: 95px;
}

#finalsalaryApp .finalsalary_add {
  width: 70px;
}

#finalsalaryApp .finalsalary_sub {
  width: 100px;
}

#finalsalaryApp .finalsalary_real {
  width: 108px;
}

#finalsalaryApp .finalsalary_op {
  width: 92px;
}

#adjustsalary .model-msg {
  z-index: 100;
  position: absolute;
  width: 70%;
  margin: 0 auto;
  left: 38%;
}

#finalsalaryApp .salary_adjustment {
  width: 100%;
  float: left;
  border-top: 1px solid #BFBFBF;
  padding-top: 5px;
  margin-left: 50px;
}

#finalsalaryApp .salary_adjustment_reason {
  width: 780px;
  display: inline;
}

#finalsalaryApp [v-cloak] {
  display: none !important;
}

.page-header.navbar.navbar-fixed-top {
  z-index: 1000 !important;
}
/*费用预算*/
#comapnyBudgetManage th {
  height: 40px !important;
}

#comapnyBudgetManage .budget_category_name {
  height: 47px;
  width: 15%;
  padding-top: 12px;
}

#comapnyBudgetManage .budget_category_amount {
  height: 47px;
  width: 15%;
  padding-top: 12px;
}

#comapnyBudgetManage .budget_op {
  text-align: center;
  width: 15%;
}

#comapnyBudgetManage .budget_ratio_th {
  width: 25%;
}

#comapnyBudgetManage .budget_ratio {
  padding: 1px 0px 1px 0px;
  height: 40px;
  width: 25%;
}

#comapnyBudgetManage .btn-icon-only {
  height: 26px !important;
  width: 26px !important;
  padding-top: 1px !important;
}

#comapnyBudgetManage .table {
  margin-bottom: 0px !important;
}

  #comapnyBudgetManage .table .btn {
    margin-right: 0px !important;
  }

#comapnyBudgetManage .show_ratio {
  width: 100%; /*height: 100%;*/
  height: 44px;
  text-align: center;
  float: left;
  position: relative;
  z-index: 100;
}

  #comapnyBudgetManage .show_ratio .show_balance_ratio_str {
    position: absolute;
    z-index: 101;
    width: 100%;
    height: 100%;
    padding: 12px 0px 12px 0px;
  }

    #comapnyBudgetManage .show_ratio .show_balance_ratio_str span {
      float: right;
      margin-right: 3px;
    }

#comapnyBudgetManage .show_ratio_left {
  width: 50%;
  height: 100%;
  text-align: right;
  float: left;
  border-right-style: dashed;
  border-right-color: #bfbfbf;
  border-right-width: thin;
}

#comapnyBudgetManage .show_ratio_right {
  width: 50%;
  height: 100%;
  text-align: right;
  float: left;
}

#comapnyBudgetManage .show_ratio_left div {
  height: 100%;
  text-align: center;
  float: right;
  padding: 12px 0 12px 0;
  background-color: orangered;
  background: linear-gradient(orangered, #f7efc8,orangered);
}

#comapnyBudgetManage .show_ratio_left .lActive {
  height: 100%;
  text-align: center;
  float: right;
  padding: 12px 0 12px 0;
  background-color: #be0606;
  background: linear-gradient(#be0606, #f3f3f3,#be0606);
}

#comapnyBudgetManage .show_ratio_left .lActive1 {
  box-shadow: -8px 5px 20px rgba(190, 6, 6, 0.6),-8px -5px 20px rgba(190, 6, 6, 0.6);
}
/*#comapnyBudgetManage .show_ratio_right .near_overstep_budget {height: 100%;background-color: #ffdd41;text-align: left;padding: 12px 0px 12px ;background: linear-gradient(#ffdd41, #fff,#ffdd41)}*/
#comapnyBudgetManage .show_ratio_right .near_overstep_budget {
  height: 100%;
  background-color: #ffd200;
  text-align: left;
  padding: 12px 0px 12px;
  background: linear-gradient(#ffd200, #fbffc6,#ffd200)
}

#comapnyBudgetManage .show_ratio_right .normal_budget {
  height: 100%;
  text-align: left;
  padding: 12px 0px 12px;
  background-color: #36D7B7;
  background: linear-gradient(#36D7B7, #fafffd,#36D7B7)
}
/* 谷歌浏览器 滚动条定义 */
/*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
#div_self_compnay_category_container::-webkit-scrollbar {
  width: 2px;
  height: 16px;
  background-color: #f5f5f5;
}
/*定义滚动条的轨道，内阴影及圆角*/
#div_self_compnay_category_container::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
  border-radius: 10px;
  background-color: #f5f5f5;
}
/*定义滑块，内阴影及圆角*/
#div_self_compnay_category_container::-webkit-scrollbar-thumb { /*width: 10px;*/
  height: 20px;
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
  background-color: #bfbfbf;
}

#div_region_container::-webkit-scrollbar {
  width: 2px;
  height: 16px;
  background-color: #f5f5f5;
}
/*定义滚动条的轨道，内阴影及圆角*/
#div_region_container::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
  border-radius: 10px;
  background-color: #f5f5f5;
}
/*定义滑块，内阴影及圆角*/
#div_region_container::-webkit-scrollbar-thumb { /*width: 10px;*/
  height: 20px;
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
  background-color: #bfbfbf;
}

#modal_region_company_list th {
  height: 40px !important;
}

#modal_region_company_list .budget_category_name {
  height: 47px;
  width: 80px;
  padding-top: 12px;
}

#modal_region_company_list .budget_category_amount {
  height: 47px;
  width: 80px;
  padding-top: 12px;
}

#modal_region_company_list .budget_ratio {
  padding: 2.5px 0px 1px 0px;
  height: 40px;
  width: 100px;
}

#modal_region_company_list .budget_op {
  width: 50px;
  text-align: center;
}

#modal_region_company_list .btn-icon-only {
  height: 26px !important;
  width: 26px !important;
  padding-top: 1px !important;
}

#modal_region_company_list .show_ratio {
  width: 100%; /*height: 100%;*/
  height: 44px;
  text-align: center;
  float: left;
  position: relative;
  z-index: 100;
}

  #modal_region_company_list .show_ratio .show_balance_ratio_str {
    position: absolute;
    z-index: 101;
    width: 100%;
    height: 100%;
    padding: 12px 0px 12px 0px;
  }

    #modal_region_company_list .show_ratio .show_balance_ratio_str span {
      float: right;
      margin-right: 3px;
    }

#modal_region_company_list .show_ratio_left {
  width: 50%;
  height: 100%;
  text-align: right;
  float: left;
  border-right-style: dashed;
  border-right-color: #bfbfbf;
  border-right-width: thin;
}

#modal_region_company_list .show_ratio_right {
  width: 50%;
  height: 100%;
  text-align: right;
  float: left;
}

#modal_region_company_list .show_ratio_left div {
  height: 100%;
  background-color: orangered;
  text-align: center;
  float: left;
  padding: 12px 0 12px 0;
  background: linear-gradient(orangered, #f7efc8,orangered)
}

#modal_region_company_list .show_ratio_left .left {
  height: 100%;
  background-color: orangered;
  text-align: center;
  float: right;
  padding: 12px 0 12px 0;
  background: linear-gradient(orangered, #f7efc8,orangered)
}

#modal_region_company_list .show_ratio_left .lActive {
  height: 100%;
  text-align: center;
  float: right;
  padding: 12px 0 12px 0;
  background-color: #be0606;
  background: linear-gradient(#be0606, #f3f3f3,#be0606);
}

#modal_region_company_list .show_ratio_left .lActive1 {
  box-shadow: -8px 5px 20px rgba(190, 6, 6, 0.6),-8px -5px 20px rgba(190, 6, 6, 0.6) !important;
}

.al-num {
  position: absolute;
  top: -12px;
  right: 0;
  text-align: center;
  color: #fff;
  line-height: 16px;
  width: 28px;
  height: 25px;
  border-radius: 50%;
  font-family: arial;
}

#modal_region_company_list .show_ratio_right .near_overstep_budget {
  height: 100%;
  background-color: #ffdd41;
  text-align: left;
  padding: 12px 0px 12px;
  background: linear-gradient(#ffdd41, #fff,#ffdd41)
}

#modal_region_company_list .show_ratio_right .normal_budget {
  height: 100%;
  background-color: #36D7B7;
  text-align: left;
  padding: 12px 0px 12px;
  background: linear-gradient(#36D7B7, #fafffd,#36D7B7)
}
/*个人名片--达人之星*/
.tstar-boxwrap {
  margin-top: 10px;
  width: 100%;
}

.tstar-maincontent {
  margin: 15px auto 0 auto;
}

.tstar-boxwrap {
  position: relative;
  display: none;
}
  /* 企业价值观变更 - 达人之星暂时隐藏 */
  .tstar-boxwrap .tstar-title {
    margin-bottom: 10px;
    border-left: 3px solid #56ABEE;
    padding-left: 8px;
  }

.tstar-title .tstar-rules {
  float: right;
  font-size: 12px;
  margin-top: 1px;
}

.rules-box {
  position: absolute;
  display: none;
  width: 410px;
  padding: 0 15px 15px 15px;
  top: -100px;
  right: -420px;
  z-index: 8;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 5px !important;
}

.closeTips {
  position: absolute;
  top: 3px;
  right: 8px;
  color: #999;
}

  .closeTips:hover {
    color: #888;
    cursor: pointer;
  }

.tstar-box {
  width: 20%;
  float: left;
}

  .tstar-box dt {
    position: relative;
    z-index: 1;
  }

    .tstar-box dt .hit {
      position: absolute;
      display: none;
      top: 4px;
      right: -2px;
      color: #f60;
      font-size: 12px;
    }

  .tstar-box .hit.active {
    animation: slideInUp 1s;
    -webkit-animation: slideInUp 1s;
    -moz-animation: slideInUp 1s;
  }

@-webkit-keyframes slideInUp {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }

  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInUp {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }

  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.slideInUp {
  -webkit-animation-name: slideInUp;
  animation-name: slideInUp;
}

.tstar-box img {
  display: block;
  margin: 0 auto;
  cursor: pointer;
}

.tstar-box dd {
  text-align: center;
}

  .tstar-box dd.star-tag {
    color: #666;
    font-size: 16px;
    font-weight: bold;
    margin: 10px 0 6px 0
  }
/*全系统电话拨号*/
.bg-cover {
  background: rgba(0, 0, 0, .4);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1002;
  display: none;
}

.tel-modal {
  display: none;
  width: 500px; /*height: 430px;*/
  height: auto; /*box-shadow: 0 0 5px #fff;*/
  position: absolute;
  top: 150px;
  left: 0;
  right: 0; /*bottom: 0;*/
  margin: 0 auto;
  background: #fff;
  border-radius: 8px !important;
  z-index: 10051;
  font-family: "Microsoft YaHei"
}

  .tel-modal a:hover {
    text-decoration: none;
  }

.tel-modal-header {
  position: relative;
  margin-bottom: 30px;
  text-align: center;
  overflow: hidden;
}

.tel-modal-header-bg {
  max-width: 500px;
  border-radius: 8px 8px 0 0 !important;
}

.tel-modal-close {
  position: absolute;
  top: -10px;
  right: 10px;
  font-size: 48px;
  color: #818282;
  font-weight: 100;
  opacity: .3;
  cursor: pointer;
}

  .tel-modal-close:hover {
    transition: all .3s;
    opacity: .6;
  }

.tel-modal .tel-line-box {
  position: absolute;
  height: 97px;
  width: 97px;
  top: 117px;
  right: 20px;
  cursor: pointer;
  z-index: 3;
}

  .tel-modal .tel-line-box .tel-line-box-icon {
    position: relative;
    z-index: 6;
  }

  .tel-modal .tel-line-box .line-box {
    position: absolute;
    top: 4px;
    left: 32px;
    height: 72px;
    width: 0;
    background-color: #EBEBEB;
    border-radius: 37px !important;
    z-index: 4;
    overflow: hidden;
  }

.tel-line-box .lines {
  padding: 4px 0;
  padding-left: 85px;
}

  .tel-line-box .lines li {
    line-height: 32px;
    padding-left: 14px
  }

.tel-line-box .call_line {
  position: relative;
  font-size: 16px;
  color: #309fff;
  overflow: hidden;
  white-space: nowrap;
}


  .tel-line-box .call_line:hover {
    text-decoration: none;
  }

.tel-line-box .call_line_disabled:hover {
  text-decoration: line-through;
}

.tel-line-box .call_line::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto 0;
  left: -14px;
  width: 6px;
  height: 6px;
  background-color: #309fff;
  border-radius: 50% !important;
}

.tel-line-box .call_line_disabled {
  position: relative;
  font-size: 16px;
  overflow: hidden;
  white-space: nowrap;
  color: #999;
  text-decoration: line-through;
  cursor: text;
}

  .tel-line-box .call_line_disabled::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto 0;
    left: -14px;
    width: 6px;
    height: 6px;
    background-color: #999;
    border-radius: 50% !important;
  }

.tel-line-box:hover .line-box {
  transition: all .4s;
  -webkit-transition: all .4s;
  -moz-transition: all .4s;
  width: 208px;
}

.tel-modal-body {
  position: relative;
  padding-left: 30px;
}

.tel-modal .tm-box {
  margin-bottom: 36px;
}

.tel-modal .tm-label {
  position: relative;
  padding-left: 20px;
  margin-bottom: 18px;
  font-size: 20px;
  color: #323232;
  font-family: "Microsoft YaHei"
}

  .tel-modal .tm-label::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto 0;
    left: 0;
    width: 4px;
    height: 18px;
    background-color: #0E97FF;
  }

.tm-op-box {
  width: 396px;
  margin-left: 20px;
  border-radius: 8px !important;
  background-color: #f5f5f5;
}

  .tm-op-box .i-tips {
    position: relative;
    padding-left: 14px;
    width: 143px;
    height: 40px;
    border: none;
    font-size: 18px;
    color: #323232;
    background-color: transparent;
    outline: none;
  }

  .tm-op-box .i-divider {
    display: inline-block;
    width: 1px;
    height: 20px;
    margin: auto 0;
    vertical-align: middle;
    background-color: #dcdcdc;
    font-size: 0;
    -webkit-text-size-adjust: none;
  }

  .tm-op-box .i-phone {
    width: 224px;
    padding-left: 12px;
    height: 40px;
    font-size: 20px;
    color: #323232;
    background-color: transparent;
    border: none;
    outline: none;
  }

.tel-modal .tm-notice-btn {
  position: absolute;
  top: 90px;
  right: 54px;
  font-size: 16px;
  color: #0e97ff;
}

  .tel-modal .tm-notice-btn::before {
    content: "";
    position: absolute;
    left: -12px;
    top: 0;
    bottom: 0;
    margin: auto 0;
    width: 6px;
    height: 6px;
    border-radius: 3px !important;
    background-color: #0E97FF;
  }

.tm-notice-box {
  display: none;
  position: absolute;
  top: -189px;
  left: -166px;
  border: 1px solid #f00;
  color: #818282;
}

.tel-modal .tm-notice-btn:hover .tm-notice-box {
  display: block;
}

.tm-notice-box .popover {
  display: block;
  max-width: 396px;
  width: 396px;
  padding-bottom: 10px;
}

.tm-notice-box .tm-notice-close {
  font-size: 20px;
  cursor: pointer;
  color: #818282;
  opacity: .6;
}

  .tm-notice-box .tm-notice-close:hover {
    transition: all .3s;
    opacity: .9;
  }

.tm-om-box {
  margin-bottom: 30px;
}

  .tm-om-box .tm_num {
    display: inline-block;
    margin-left: 20px;
    width: 158px;
    height: 40px;
    line-height: 40px;
    font-size: 20px;
    font-weight: lighter;
    color: #969696;
    float: left;
    text-align: center;
    cursor: pointer;
  }

    .tm-om-box .tm_num.selected {
      color: #0e97ff;
      background-color: #f1f9ff;
      border-radius: 10px !important;
    }

.tm-rd-box {
  margin-bottom: 10px;
  display: none;
}

.tm-rd-list {
  margin-left: 20px;
}

.tm-rd-box .tm-rd-list li {
  margin: 8px 0;
  font-size: 16px;
  color: #787878;
}

.tm-rd-box .tm-rd-seemore {
  float: right;
  margin-right: 54px;
  margin-top: 2px;
  color: #b4b4b4;
  font-size: 18px;
}

  .tm-rd-box .tm-rd-seemore i {
    font-size: 28px;
    margin-left: 8px;
    display: inline;
    vertical-align: sub;
  }

.tm-rd-list .rdType {
  width: 16%;
}

.tm-rd-list .rdName {
  width: 16%;
}

.tm-rd-list .rdCompany {
  width: 20%
}

.tm-rd-list .rdClient {
  width: 30%;
}

.tm-rd-list .rdTime {
  width: 25%;
  color: #969696;
}

.tm-rd-list .rdNoData {
  width: 80%;
  color: #969696;
}

.tm-rd-list li span {
  display: inline-block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 3px;
}

.tm-rd-list li a {
  display: inline-block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 3px;
}

  .tm-rd-list li a:hover {
    text-decoration: underline
  }

.tm-rd-box .tm_wj {
  border: 0 none !important;
  padding-left: 25px;
}

  .tm-rd-box .tm_wj:before {
    background-color: transparent;
  }

  .tm-rd-box .tm_wj > i {
    position: absolute;
    left: 0;
    top: 6px;
    font-size: 18px;
  }
  /*.tm-rd-box .tm_wj>i:hover .tm-notice-box{display:block;}*/
  .tm-rd-box .tm_wj .tm-notice-box {
    left: -190px;
    top: -85px !important;
  }

  .tm-rd-box .tm_wj .xs_tl1 {
    font-size: 14px;
    margin: 0 0 0 6px;
    vertical-align: bottom;
  }
/*R币转账弹窗*/
.r__rb_modal {
}

  .r__rb_modal .r_label {
    margin-top: 6px;
    /*width: 61px;*/
    padding: 0;
    text-align: right;
  }

  .r__rb_modal .r_show {
    padding-left: 16px;
    padding-bottom: 16px;
  }

  .r__rb_modal .add-btn.addTeam.R_addTeam {
    right: -80px;
    top: -2px;
    background: #62a3d3;
    display: inline-block;
    width: 80px;
    height: 34px;
    text-align: center;
    line-height: 34px;
    color: #fff;
  }

  .r__rb_modal .add-btn.R_addTeam.addTeam:hover {
    color: #fff;
    background-color: #558eb8;
  }


#chat-modal .modal-dialog {
  width: 80%;
  height: 90%;
}

#chat-modal .modal-content {
  height: inherit;
}

#toast-container .received-notice-panel {
  opacity: 1;
  background-image: url(/images/icon-received-notice-panel.png) !important;
  background-size: 52px;
  width: 340px;
  margin: 0;
  background-color: #fff;
  color: #000;
  padding-left: 76px;
  margin-top: 10px;
  box-shadow: 0 0 12px #C1CADF;
}

  #toast-container .received-notice-panel:hover {
    box-shadow: 0 0 12px #999;
    cursor: initial;
  }

  #toast-container .received-notice-panel a {
    color: #2A7FCC;
  }

    #toast-container .received-notice-panel a:hover {
      color: #337ab7;
      text-decoration: underline;
    }

  #toast-container .received-notice-panel .toast-title {
    font-size: 16px;
    margin-bottom: 5px;
    font-weight: bold;
  }

  #toast-container .received-notice-panel .toast-close-button {
    color: #000;
    position: relative;
    top: -16px;
    font-weight: lighter;
    font-size: 24px;
  }

#toast-container .toast-message {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/*系统提醒*/
.xiaoxi-tips {
  width: auto;
  padding: 0px 7px;
  background: #F83D3D;
  border-radius: 8px !important;
  margin-right: 5px;
  height: 16px;
  line-height: 16px;
  color: #FFF;
  font-size: 12px;
  position: absolute;
  top: 0px;
  right: -5px;
}

.xiaoxi-box {
  bottom: 380px;
  position: fixed;
  z-index: 99;
  width: 60px;
  height: 60px;
  right: 200px;
}

  .xiaoxi-box .xiaoxi-box-link {
    display: block;
    width: 60px;
    height: 60px;
    font-size: 12px;
    color: #fff;
    background-color: #B054D7;
    text-align: center;
    overflow: hidden;
    border-radius: 50% !important;
    font-family: 'Microsoft YaHei';
  }

    .xiaoxi-box .xiaoxi-box-link:hover {
      background-color: #B054D7;
      text-decoration: none;
    }

    .xiaoxi-box .xiaoxi-box-link > i {
      display: block;
      vertical-align: middle;
      font-size: 17px;
      text-align: center;
      margin: 12px 0 4px;
    }

  .xiaoxi-box .help-num {
    position: absolute;
    left: 36px;
    top: 0;
    background: #f83d3d;
    border-radius: 8px !important;
    padding: 2px 7px !important;
    line-height: 1;
    color: white;
    font-size: 12px;
  }

    .xiaoxi-box .help-num.no {
      display: none;
    }

  .xiaoxi-box img {
    display: block;
    width: 20px;
    height: 20px;
    margin: 10px auto 0;
  }



  .xiaoxi-box.block {
  }

    .xiaoxi-box.block:after {
      content: '';
      position: absolute;
      z-index: 1000;
      border: none;
      margin: 0px;
      padding: 0px;
      width: 100%;
      height: 100%;
      top: 0px;
      left: 0px;
      background-color: rgb(85, 85, 85);
      opacity: 0.05;
      cursor: wait;
      border-radius: 50%;
    }



/*小达*/

.zhinengxiaodaimg {
  position: relative;
  display: inline-block;
  width: 58px;
  height: 88px;
  background-image: url('../../images/opt/xiaoda_shen.png');
  background-size: 58px 88px;
  -webkit-animation: zhinengxiaodaimg 2s infinite;
  -moz-animation: zhinengxiaodaimg 2s infinite;
  -o-animation: zhinengxiaodaimg 2s infinite;
  -ms-animation: zhinengxiaodaimg 2s infinite;
  animation: zhinengxiaodaimg 2s infinite;
  border: none;
}

.xiaoda_yanjin {
  position: absolute;
  top: 23px;
  left: 14px;
  display: inline-block;
  width: 30px;
  height: 11px;
  background-image: url('../../images/opt/xiaoda_yanjin.png');
  background-size: 30px 11px;
  -webkit-animation: xiaoda_yanjin 1s infinite;
  -moz-animation: xiaoda_yanjin 1s infinite;
  -o-animation: xiaoda_yanjin 1s infinite;
  -ms-animation: xiaoda_yanjin 1s infinite;
  animation: xiaoda_yanjin 1s infinite;
  border: none;
}

.xiaoda_left {
  position: absolute;
  top: 46px;
  left: 0px;
  display: inline-block;
  width: 18px;
  height: 30px;
  background-image: url('../../images/opt/xiaoda_left.png');
  background-size: 18px 30px;
  -webkit-animation: xiaoda_left 1s infinite;
  -moz-animation: xiaoda_left 1s infinite;
  -o-animation: xiaoda_left 1s infinite;
  -ms-animation: xiaoda_left 1s infinite;
  animation: xiaoda_left 1s infinite;
  border: none;
}

.xiaoda_right {
  position: absolute;
  top: 46px;
  right: 0px;
  display: inline-block;
  width: 18px;
  height: 30px;
  background-image: url('../../images/opt/xiaoda_right.png');
  background-size: 18px 30px;
  -webkit-animation: xiaoda_right 1s infinite;
  -moz-animation: xiaoda_right 1s infinite;
  -o-animation: xiaoda_right 1s infinite;
  -ms-animation: xiaoda_right 1s infinite;
  animation: xiaoda_right 1s infinite;
  border: none;
}

.xiaoda_bottom {
  display: inline-block;
  width: 58px;
  height: 17px;
  background-image: url('../../images/opt/xiaoda_bottom.png');
  background-repeat: no-repeat;
  background-size: 58px 17px;
  /*  -webkit-animation: xiaoda_left 4s infinite;
    -moz-animation: xiaoda_left 4s infinite;
    -o-animation: xiaoda_left 4s infinite;
    -ms-animation: xiaoda_left 4s infinite;
    animation: xiaoda_left 4s infinite;
    border: none;*/
}

@keyframes xiaoda_right {
  0% {
    transform: rotate(16deg);
    transform-origin: left;
  }

  50% {
    transform: rotate(-6deg);
    transform-origin: left;
  }

  100% {
    transform: rotate(16deg);
    transform-origin: left;
  }
}

@keyframes xiaoda_left {
  0% {
    transform: rotate(-16deg);
    transform-origin: right;
  }

  50% {
    transform: rotate(6deg);
    transform-origin: right;
  }

  100% {
    transform: rotate(-16deg);
    transform-origin: right;
  }
}

@keyframes xiaoda_yanjin {
  0% {
    opacity: 0
  }

  25% {
    opacity: 0.5
  }

  50% {
    opacity: 1
  }

  75% {
    opacity: 0.5
  }

  100% {
    opacity: 0
  }
}

@keyframes zhinengxiaodaimg {
  0% {
    margin-bottom: 0px;
  }

  50% {
    margin-bottom: 14px;
  }

  100% {
    margin-bottom: 0px;
  }
}

.zhinengxiaoda-tips {
  width: auto;
  padding: 0px 7px;
  background: #F83D3D;
  border-radius: 8px !important;
  margin-right: 5px;
  height: 16px;
  line-height: 16px;
  color: #FFF;
  font-size: 12px;
  position: absolute;
  top: 0px;
  right: -5px;
}

.zhinengxiaoda {
  bottom: 536px;
  position: fixed;
  z-index: 99;
  width: 60px;
  right: 200px;
}

  .zhinengxiaoda .zhinengxiaoda-link {
    display: block;
    width: 60px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    overflow: hidden;
    /*  border-radius: 50% !important;*/
    font-family: 'Microsoft YaHei';
  }

    /* .zhinengxiaoda .zhinengxiaoda-link:hover {
            background-color: #B054D7;
            text-decoration: none;
        }*/

    .zhinengxiaoda .zhinengxiaoda-link > i {
      display: block;
      vertical-align: middle;
      font-size: 17px;
      text-align: center;
      margin: 12px 0 4px;
    }

  .zhinengxiaoda .help-num {
    position: absolute;
    left: 36px;
    top: 0;
    background: #f83d3d;
    border-radius: 8px !important;
    padding: 2px 7px !important;
    line-height: 1;
    color: white;
    font-size: 12px;
  }

    .zhinengxiaoda .help-num.no {
      display: none;
    }

  .zhinengxiaoda img {
    display: block;
    width: 60px;
  }



  .zhinengxiaoda.block {
  }

    .zhinengxiaoda.block:after {
      content: '';
      position: absolute;
      z-index: 1000;
      border: none;
      margin: 0px;
      padding: 0px;
      width: 100%;
      height: 100%;
      top: 0px;
      left: 0px;
      background-color: rgb(85, 85, 85);
      opacity: 0.05;
      cursor: wait;
      border-radius: 50%;
    }
.circle-container {
  position: absolute;
  top: 0px;
  right: 0px;
  height: 100%;
  width: 51px;
  display: flex;
  justify-content: center;
}

.circle {
  font-size: 12px;
  text-align: center;
  color: #fff;
  background: #f83d3d;
  position: relative;
  height: 18px;
  line-height: 18px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 20px !important;
  padding: 0px 7px !important;
}


/**24.12.30AI助理右侧栏*/
.right_menu_container_new {
  position: fixed;
  right: 24px;
  bottom: 162px;
  z-index: 100;
}
.right_menu_list_box {
  background: #FFFFFF;
  border-radius: 8px !important;
  padding: 10px 0;
  margin-top: 16px;
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.14);
  position: relative;
}
.right_menu_item {
  display: flex;
  align-items: center;
  position: relative
}
.right_menu_tips {
  background: #333333;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 13px;
  color: #FFFFFF;
  line-height: 18px;
  padding: 7px 12px;
  border-radius: 4px !important;
  margin-right: 6px;
  position: absolute;
  right: 40px;
  top: calc(50% - 16px);
  width: max-content;
  display: none;
}
.popper_arrow_icon,
.popper_arrow_icon::after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.popper_arrow_icon {
  top: 50%;
  right: -12px;
  margin-bottom: -10px;
  border-left-color: #ebeef5;
  border-right-width: 0;
  border-width: 6px;
  filter: drop-shadow(0 2px 12px rgba(0, 0, 0, .03));
}
.popper_arrow_icon::after {
  content: " ";
  border-width: 6px;
  bottom: 0px;
  right: 1px;
  border-left-color: #333;
  border-right-width: 0;
}
.right_menu_icon_box {
  width: 40px;
  height: 40px;
  cursor: pointer;
}
.right_menu_img {
  width: 100%;
  height: 100%;
}
.right_menu_icon_box1 {
  border-radius: 50% !important;
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.14);
}
.right_menu_icon_box1 .right_menu_img {
  border-radius: 50% !important;
}
.right_menu_item:hover .svg_icon_hover {
  fill: #1C68AE;
}
.right_menu_item:hover .svg_path_hover {
  stroke: #1C68AE;
}
.right_menu_item:hover .right_menu_tips {
  display: inline-block;
  cursor: pointer;
}


.right_menu_phone .callphone-pingjia-box {
  background: #1FD28E url("images/call_pingjia.png") center 8px no-repeat;
  background-size: 22px 22px;
  display: block;
  width: 60px;
  height: 60px;
  position: absolute;
  left: -8px;
  top: 0;
  z-index: 98;
  cursor: pointer;
  text-decoration: none;
  color: white;
  font-size: 12px;
  display: none;
  animation: pingjiaAnimatedBackground 2s infinite alternate;
}

.right_menu_phone .callphone-pingjia-box .cppj-title {
  display: block;
  position: absolute;
  bottom: 0;
  text-align: center;
  left: 0;
  right: 0;
  line-height: 30px;
  font-weight: bold;
}

.right_menu_phone .callphone-pingjia-box:hover .cppj-title {
  display: none;
}

.right_menu_phone .callphone-pingjia-box .cppj-line {
  display: none;
  height: 30px;
  line-height: 30px;
  text-align: center;
  background: #00D28B;
}

.right_menu_phone .callphone-pingjia-box:hover .cppj-line {
  display: block;
}

.right_menu_phone .callphone-pingjia-box .cppj-line:hover {
  background: #00B276;
}

.right_menu_phone .callphone-pingjia-box .cppj-zz {
  position: absolute;
  width: 60px;
  padding: 3px 0;
  text-align: center;
  color: #1ED28B;
  border: 1px solid #1FD28E;
  border-radius: 3px;
  left: 0;
  bottom: -24px;
  line-height: 16px;
  font-size: 12px;
  animation: pingjiaAnimatedBorder 2s infinite alternate;
  background: #fff;
}
.call-phone-top{
  display:block !important;
}
.call-phone {
  justify-content: end !important;
}
.my-callpho-commentbox .call-phone input {
  justify-content: end !important;
}
.right_menu_icon_box circle {
  fill: dodgerblue;
  fill-opacity: 0;
  -webkit-animation: opacity 1.2s linear infinite;
  animation: opacity 1.2s linear infinite;
}

  circle:nth-child(12n + 1) {
    -webkit-animation-delay: -0.1s;
    animation-delay: -0.1s;
  }

  circle:nth-child(12n + 2) {
    -webkit-animation-delay: -0.2s;
    animation-delay: -0.2s;
  }

  circle:nth-child(12n + 3) {
    -webkit-animation-delay: -0.3s;
    animation-delay: -0.3s;
  }

  circle:nth-child(12n + 4) {
    -webkit-animation-delay: -0.4s;
    animation-delay: -0.4s;
  }

  circle:nth-child(12n + 5) {
    -webkit-animation-delay: -0.5s;
    animation-delay: -0.5s;
  }

  circle:nth-child(12n + 6) {
    -webkit-animation-delay: -0.6s;
    animation-delay: -0.6s;
  }

  circle:nth-child(12n + 7) {
    -webkit-animation-delay: -0.7s;
    animation-delay: -0.7s;
  }

  circle:nth-child(12n + 8) {
    -webkit-animation-delay: -0.8s;
    animation-delay: -0.8s;
  }

  circle:nth-child(12n + 9) {
    -webkit-animation-delay: -0.9s;
    animation-delay: -0.9s;
  }

  circle:nth-child(12n + 10) {
    -webkit-animation-delay: -1s;
    animation-delay: -1s;
  }

  circle:nth-child(12n + 11) {
    -webkit-animation-delay: -1.1s;
    animation-delay: -1.1s;
  }

  circle:nth-child(12n + 12) {
    -webkit-animation-delay: -1.2s;
    animation-delay: -1.2s;
  }

@keyframes opacity {
  3% {
    fill-opacity: 1;
  }

  75% {
    fill-opacity: 0;
  }
}

/**2025.AI助理入口 start*/
.ai_chat_right_box {
  cursor: pointer;
  position: fixed;
  right: 24px;
  bottom: 40px;
}
.right_menu_ai_item {
  width: 40px;
  height: 106px;
  background: #FFFFFF;
  border-radius: 20px !important;
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.14);
}
.right_menu_ai_img {
  width: 28px;
  height: 28px;
  margin-bottom: 4px;
}
.right_menu_ai_icon_box {
  padding: 6px;
}
.right_menu_ai_txt {
  display: flex;
  flex-flow: column;
  align-items: center;
}
.right_menu_ai_t {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #333333;
  line-height: 20px;
}
.menu_a:hover,
.menu_a:focus {
  text-decoration: none;
}
/**2025.AI助理入口 end*/