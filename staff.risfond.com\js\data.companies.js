/**
 * 功能：获取公司数据
 * 用途：为公司选择插件(raSelect.js)提供数据源
 * 依赖： [jQuery.js]
 * 请求接口地址：/Company/GetCompaniesData
 * 返回数据格式：json
 */
var data_companies = undefined;
$.ajax({
  type: "POST",
  url: "/Company/GetCompaniesData",
  data: {},
  dataType: "json",
  async: false,
    success: function (req) {
    if (!req) {
      r_Layout_BeautAlert.done("获取公司数据失败，请刷新页面重试..", "", 1500, function () {
        window.location.reload();
      });
    }
    data_companies = req;
  }
});