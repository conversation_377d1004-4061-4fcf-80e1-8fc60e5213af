/*美化选择*/
.qz_msgbox_layer,
.qz_msgbox_layer .gtl_ico_succ,
.qz_msgbox_layer .gtl_ico_fail,
.qz_msgbox_layer .gtl_ico_hits,
.qz_msgbox_layer .gtl_ico_clear,
.qz_msgbox_layer .gtl_end, .gtl_ico_wait { display: inline-block; height: 51px; line-height: 51px; font-weight: bold; font-size: 14px; color: #606060; background-image: url(images/tip_layer_small.png); background-repeat: no-repeat; }
.qz_msgbox_layer_wrap { width: 100%; position: fixed; _position: absolute; top: 40%; left: 0; text-align: center; z-index: 65533; }
.qz_msgbox_layer { background-image: url(images/tip_layer_bj.png); background-repeat: repeat-x; margin: 0 auto; position: relative; -ms-border-radius: 5px 5px 5px 5px; border-radius: 5px 5px 5px 5px; padding-right: 40px; _display: inline; }
.qz_msgbox_layer .gtl_ico_succ { background-position: 0 0; top: 0; width: 30px; overflow: hidden; height: 29px; float: left; display: block; margin: 12px 12px 20px 10px; }
.qz_msgbox_layer .gtl_ico_fail { background-position: 0 -58px; top: 0; width: 30px; overflow: hidden; height: 29px; float: left; display: block; margin: 12px 12px 20px 10px; }
.qz_msgbox_layer .gtl_ico_hits, .qz_msgbox_layer .gtl_ico_wait { background-position: 0 -29px; top: 0; width: 30px; overflow: hidden; height: 29px; float: left; display: block; margin: 12px 12px 20px 10px; }
.qz_msgbox_layer img { float: left; margin: 19px 10px 0 5px; }
.qz_msgbox_layer .img { float: left; margin: 11px 11px 20px 20px; }
.qz_msgbox_layer .gtl_ico_load { width: 70px; top: 0; height: 51px; }
/*头像上传*/
.dialog-footer{
  margin-top:15px;
}
.companies_s{
  height: 16px !important;
  line-height: 16px !important;
}   
#editPhotoWrap { -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; -o-user-select: none; user-select: none; }
.imgareaselect-outer { background-color: #000; -webkit-filter: alpha(opacity=40); -moz-filter: alpha(opacity=40); -o-filter: alpha(opacity=40); filter: alpha(opacity=40); -ms-opacity: .4; opacity: .4; position: absolute; z-index: 10; }
#srcImgWrap { font-size: 0; position: relative; margin-bottom: 10px; overflow: hidden; float: left; margin-right: 40px; }
.preImgWrap { border: 1px solid #ccc; margin-bottom: 20px; overflow: hidden; }
.move-resize { position: absolute; cursor: se-resize; width: 13px; height: 13px; right: 0; bottom: 0; background: url(images/tuoFang.gif) no-repeat; }
/*日期选择*/
.pc_caldr { border: 1px solid #ccc; background-color: #fff; z-index: 10; width: 175px; height: auto; position: absolute; color: #000; padding: 5px; }
.pc_caldr .selector { height: 24px; _padding: 2px 0 2px; padding: 2px 0 0; }
.pc_caldr .selector .month, .pc_caldr .selector .year { float: left; font-size: 12px; width: 80px; border: 1px solid #CCC; height: 19px; }
.pc_caldr .selector .year { width: 84px; margin-left: 10px; }
.pc_caldr .weeks, .pc_caldr .days { list-style: none; width: 100% !important; margin: 0; padding: 0; }
.pc_caldr .weeks { height: 18px; margin-bottom: 2px; background: #b6d1f9; color: #fff; font-size: 12px; }
.pc_caldr .days { height: auto; font-size: 12px; font-family: Arial; }
.pc_caldr .weeks li, .pc_caldr .days li { float: left; height: 20px; line-height: 20px; text-align: center; width: 25px; }
.pc_caldr .days li { background-color: #FFF; }
.pc_caldr .days li a { display: block; text-decoration: none; height: 100%; color: #43609c; -webkit-transition: transform .5s; -moz-transition: transform .5s; -ms-transition: transform .5s; -o-transition: transform .5s; transition: transform .5s; padding: 1px; }
.pc_caldr .days li a:link, .pc_caldr .days li a:visited, .pc_caldr .days li a:hover { text-decoration: none; }
.pc_caldr .days li a strong { font-weight: 400; }
.pc_caldr .days li a:hover { background-color: #5d94e6; color: #fff; -webkit-transform: scale(1.3); -moz-transform: scale(1.3); -ms-transform: scale(1.3); -o-transform: scale(1.3); transform: scale(1.3); }
.pc_caldr .weeks li, .pc_caldr .days li, .pc_caldr .days li a { text-align: center; }
.ie6iframe { background: none repeat scroll 0 0 #FFFFFF; left: -1px; -webkit-filter: alpha(opacity=0); -moz-filter: alpha(opacity=0); -o-filter: alpha(opacity=0); filter: alpha(opacity=0); position: absolute; top: 0; z-index: -1; width: 100%; height: 150px; }
/*输入建议*/
#selTeam, #selClient { line-height: 24px; position: relative; float: left; }
.selTeam, .selClient { float: left; line-height: 24px; position: relative; padding: 0; }
.selTeam .icon-135 { cursor: pointer; left: 152px; position: absolute; top: 6px; }
.selTeam .buttom-2 { margin-left: 1px; margin-top: 1px; }
.outbox-item-list { float: left; }
.outbox-item-list li { display: inline; float: left; margin: 3px 0 0 10px; padding: 0 0 0 5px; border: 1px solid #f3bc19; line-height: 13px; color: #646464; cursor: pointer; background-color: #faf4e2; height: 21px; }
.outbox-item-list .people { height: 21px; line-height: 21px; margin-right: 5px; float: left; }
.outbox-item-list .cancel { cursor: pointer; margin-right: 0; float: left; background: url("/css/images/select-user.gif") no-repeat; height: 21px; width: 21px; }
.outbox input { border: 0 none; width: 14px; float: left; height: 18px; padding: 6px 2px 0 0; font-size: 12px; font-family: Verdana,Geneva,Arial,Helvetica,sans-serif; display: none; background: none; padding-left: 10px; }
.outbox input:focus { outline: none; }
.selector-list { border: 1px solid #ccc; display: none; line-height: 0; font-size: 0; overflow-y: scroll; max-height: 268px; position: absolute; left: 0; /*top: 27px;*/ top: 36px; width: 100%; background: #fff; z-index: 8; }
.selector-list .hint { text-indent: 10px; padding-left: 0 !important; background-color: #EAF9FF; border-bottom: 1px solid #39CBFF; color: #00678C; }
.selector-list li { float: left; width: 100%; background: #ffffff; height: 24px; line-height: 24px; cursor: pointer; font-size: 12px; }
.selector-list li div { padding-left: 10px; }
.result-item .name-follow { float: left; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; max-width: 32%; }
.selector-list .selected { background-color: #EDFFB9; }
.highlight { color: #f00; font-weight: bold; }
.selector_hintbox { display: none; border: 1px solid #ccc; position: absolute; left: 0; /*top: 27px;*/ top: 36px; color: #555; background: #fff; text-indent: 12px; width: 100%; }
.result-item div { float: left; width: 92px; }
#selector_list_0 { border: 1px solid #ccc; display: none; background: #fff; }
.outbox { float: left; height: 26px; background-color: #FFF; }
.add-btn { cursor: pointer; position: absolute; right: -82px; top: 1px; }
.category-list { display: none; background: #fff; left: 13px; z-index: 10; position: absolute; top: 28px; border: 1px solid #ccc; height: 220px; overflow-y: scroll; width: 100%; font-size: 12px; }
.category-list .category-each { margin: 0; }
.category-each dt { background: url(images/selteam.png) no-repeat 8px -31px; text-indent: 26px; height: 18px; line-height: 20px; overflow: hidden; cursor: pointer; font-weight: normal; }
.category-each dt:hover { background: url(images/selteam.png) no-repeat 8px -13px #FAF4E2; cursor: pointer; }
.category-item { float: left; display: inline; width: 113px; margin-left: 5px !important; height: 20px; line-height: 20px; border: 1px solid #fff; cursor: pointer; padding-left: 10px !important; white-space: nowrap; }
.category-item span { background: url(images/selteam.png) no-repeat 0 4px; padding: 0 0 0 16px; }
.category-item:hover { background-color: #FAF4E2; border: 1px solid #F3BC19; }
.ct-selected { background: url("images/selteam.png") no-repeat 96% -121px; }
.category-each dd { display: none; }
.ts-unfold dd { display: block; }
.ts-unfold dt { background-position: 8px -67px; }
.ts-unfold dt:hover { background-position: 8px -49px; }
#clientList .category-item { width: 245px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.outbox-tip { float: left; color: #999; margin: 2px 0 0 9px; }
.r-v-title { width: 415px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
/* 集团公司和子公司样式 */
.child-company-detail .r-tb-vc-td th, .child-company-detail .r-tb-vc-td td { text-align: center !important; }
.relate { float: right; color: #32C6D2; cursor: pointer; margin-top: 4px; }
.r-company-child { width: 140px; }
.r-cids { width: 72px; }
.child-company-detail .r-tb-vc-td th.r-clientnames, .child-company-detail .r-tb-vc-td td.r-clientnames { width: 260px; text-align: left !important; }
.r-sources { width: 46px; }
.r-memo .record-num { display: inline-block; vertical-align: middle; background: #4496E0; color: white; border-radius: 50% !important; overflow: hidden; width: 23px; height: 23px; text-align: center; line-height: 23px; }
.r-memo { width: 50px; position: relative; overflow: initial !important; }
.search-company { width: 500px; height: 38px; box-shadow: 0 0 0 #fff; outline: 0; border: 1px solid #ddd; padding-left: 6px; }
.input-box { text-align: center; }
.search-company:focus { border: 1px solid #32c5d2; box-shadow: 0 0 2px #32c5d2; }
.search-btn { width: 85px; display: inline-block; height: 38px; background: #26a1ab; text-align: center; line-height: 38px; color: #fff; cursor: pointer; }
.red-num { color: #cc1818; }
.r-reference .modal-body table tbody td.child-td { padding: 0 !important; /* height: 40px; */ line-height: 40px; }
.tip-record { position: absolute; top: 23px; right: 38px; background: #f3bc19; width: 600px; min-height: 100px; z-index: 2; }
.r-sources-m, .r-sel-m, .r-jobcount-m, .r-status-m { width: 50px; }
.r-clientnames-m { width: 300px; text-align: left !important; padding-left: 8px !important; }
.index-staffcard { /*margin-top: -300px;*/ }
.staffcard_aspx { margin-top: 20px; }
.modal.fade.in { /*top: 50%;*/ /*margin-top: -300px;*/ padding-right: 0 !important; }
.staff_cards.modal.fade.in { top: 8%; }
.modal.fade.in { padding-right: 0 !important; }
.showCard-items-m { padding: 4px !important; }
.apply_reason { text-align: center; position: relative; }
.reson-box { position: absolute; top: 10px; left: -439px; padding: 2px 6px 10px; width: 450px; background: #eee; border-radius: 2px !important; border: 1px solid #ccc; }
.reson-box h4 { text-align: left; }
.reson-box p { display: block; text-align: left; white-space: normal; width: 440px; }
.icon_close { color: #d83737; }
.icon_check { color: #26C281; }
.icon_close, .icon_check { font-size: 18px; padding: 0 2px; }
.infos { font-size: 18px; /* text-align: center; */ color: #f90; cursor: pointer; }
/*分页*/
.hiddenPager { visibility: hidden; }
.yb-pager { position: relative; width: 100%; margin-top: 28px; padding-bottom: 20px; background-color: #fff; }
.yb-pager .pagination { display: block; margin: 0 auto; width: 555px; float: initial; max-width: 666px;text-align:center; }
.yb-pager .pagination li { display: inline-block; }
.new_modal_show.fade.in { /*margin-top: -300px;*/ }
#selectStaffR_k_n .teamName-list {
  top: 265px !important;
  left: 776px !important;
  width: 172px;
  position: absolute;
  z-index: 10000 !important;
}
@media screen and (max-width: 1366px) {
  #selectStaffR_k_n .teamName-list {
    top: 265px !important;
    left: 493px !important;
    width: 172px;
    position: absolute;
    z-index: 10000 !important;
  }
}
@media screen and (max-width: 1600px) {
  #selectStaffR_k_n .teamName-list {
    top: 265px !important;
    left: 617px !important;
    width: 172px;
    position: absolute;
    z-index: 10000 !important;
  }
}
@media screen and (min-width: 1980px) {
  #selectStaffR_k_n .teamName-list {
    top: 265px !important;
    left: 1007px !important;
    width: 172px;
    position: absolute;
    z-index: 10000 !important;
  }
}
/*顾问驾驶舱样式*/
.bg-orange-studio {
  background: #1bbc9b;
  color: #fff;
}
.bg-orange-studio:hover {
  color: #fff;
  background: #11a788;

}
.bg-orange-studio:focus {
  color: #fff;
}

/* letter  确认函的弹窗*/
.letter-model {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 111;
  background: rgba(0,0,0, .5);
}

.letter-content {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -280px;
  margin-top: -220px;
  width: 560px;
  background: rgba(255,255,255,1);
  font-family: "微软雅黑";
}

  .letter-content .con-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    border-bottom: 1px solid #eee;
    padding: 0 20px;
  }

    .letter-content .con-header .name {
      font-size: 14px;
    }

    .letter-content .con-header .letter-close {
      font-size: 24px;
      cursor: pointer;
    }

  .letter-content .con-main {
    padding: 20px;
  }

  .letter-content .info {
    border-bottom: 1px solid #eee;
  }

    .letter-content .info .one {
      font-size: 14px;
      display: flex;
      justify-content: flex-start;
      padding-bottom: 8px;
    }

      .letter-content .info .one .box {
        width: 270px;
        display: flex;
      }

        .letter-content .info .one .box .con {
          width: 180px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          display: inline-block;
        }

        .letter-content .info .one .box .l-label {
          color: #999999;
        }

  .letter-content .evaluation {
    font-size: 14px;
    display: flex;
    flex-direction: column;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
  }

    .letter-content .evaluation .star .imgs {
      width: 16px;
      height: 16px;
      margin: 0 5px;
    }

    .letter-content .evaluation .evaluation-star {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
    }

    .letter-content .evaluation .star {
      display: flex;
      align-items: center;
      margin-right: 6px;
    }

    .letter-content .evaluation .title {
      margin-right: 12px;
    }

    .letter-content .evaluation .tip {
      color: #FF6600;
    }

  .letter-content .advice {
    font-size: 14px;
  }

  .letter-content .advice-title {
    padding-top: 12px;
    padding-bottom: 6px;
    display: block;
  }

  .letter-content .advice p {
    line-height: 1.5em;
    overflow: hidden;
    height: 104px;
  }

  .letter-content .adminconfirm {
    font-size: 14px;
  }

  .letter-content .remark-title {
    padding-top: 12px;
    padding-bottom: 6px;
    display: block;
  }

  .letter-content .adminconfirm p {
    line-height: 1.5em;
    overflow: hidden;
    height: 104px;
  }

.ellipsis{
   white-space: nowrap;
   text-overflow: ellipsis;
  overflow: hidden;
}
.bubbleStyl {
  position:absolute;
  top:50%;
  right:0;
  z-index:10050;
  padding:5px;
  border:1px solid #8CB7DE;
  background:#fff;
  max-width:300px;
  border-radius:3px !important;
  box-shadow:5px 5px rgba(140,178,198,.2);
  transition:  all .3s cubic-bezier(.25,-0.75,.39,1.75);
}
.bubble-arrow{
       position: absolute;
    content: "";
    width: 0;
    height: 0;
    left: 10px;
    top: -13px;
    border-top: 9px double  transparent;
    border-right: 7px solid #8CB7DE;
    border-bottom: 9px double  transparent;
    transform: rotate(90deg);
         }

/*人才银行导航NEW*/
.personnelBank__new {
  position: absolute;
  top: -7px;
  right: -4px;
  background: #ff3333;
  color: #fff;
  width: 31px;
  height: 15px;
  line-height: 14px;
  text-align: center;
  font-size: 12px;
  border-radius: 2px !important;
}
.personnelBank__new_tip {
  position: absolute;
  top: -182px;
  left: 59px;
  width: 310px;
  height: 172px;
  z-index: 1;
  background-image: url(../public/personnelBank/bg__tips.png);
  background-size: 100% 100%;
  font-size: 12px;
  padding: 30px 30px 0 33px;
}
