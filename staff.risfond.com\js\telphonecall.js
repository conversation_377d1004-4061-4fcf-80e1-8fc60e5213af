var rnss_telpho_data = {
  ceid: 0,
  phoneareas: []
};

//全局方法--播放录音---多处调用
function rnss_event_player(panel) {
  panel = $(panel);
  //#tonghuaLogList 
  var jplaylen = panel.find(".bofang-box").length,
    bofangh = 71;//播放容器的统一高度
  for (var i = 0; i < jplaylen; i++) {
    (function (i) {
      var playitem = panel.find(".bofang-box").eq(i),
        calllogsitem = playitem.closest(".calllogsitem"),
        tindex = playitem.data("index"),
        container = $("#jp_container_" + tindex),
        containerId = "#jp_container_" + tindex,
        myControl = null,
        luyinbtnbox = calllogsitem.find(".luyinbtn-box"),
        luyinbtn = calllogsitem.find(".luyinbtn"),
        recordurl = playitem.data("url");
      var myPlayer = $("#jquery_jplayer_" + tindex),
        myPlayerData,
        fixFlash_mp4, // Flag: The m4a and m4v Flash player gives some old currentTime values when changed.
        fixFlash_mp4_id, // Timeout ID used with fixFlash_mp4
        ignore_timeupdate, // Flag used with fixFlash_mp4
        options = {
          ready: function (event) {
            //var _myPlayer = $(this).closest(".jp-container").siblings(".jp-jplayer"), _myControl = _myPlayer.data("myControl");
            var _myPlayer = $(this), _myControl = _myPlayer.data("myControl");
            if (!_myControl || _myControl == undefined) _myControl = myControl;
            if (event.jPlayer.status.noVolume) {
              $(this).siblings(".jp-container").find(".jp-gui").addClass("jp-no-volume");
            }
            fixFlash_mp4 = event.jPlayer.flash.used && /mp3|wav/.test(event.jPlayer.options.supplied);
            _myPlayer.data("fixFlash_mp4", event.jPlayer.flash.used && /mp3|wav/.test(event.jPlayer.options.supplied));
            $(this).jPlayer("setMedia", {
              //mp3: "/audio/xywm.mp3",
              //m4a: "http://www.jplayer.org/audio/m4a/Miaow-07-Bubble.m4a",
              //oga: "http://www.jplayer.org/audio/ogg/Miaow-07-Bubble.ogg",
              mp3: $(this).closest(".bofang-box").data("url"),
              wav: $(this).closest(".bofang-box").data("url"),
            });
          },
          timeupdate: function (event) {
            var _myPlayer = $(this), _myControl = _myPlayer.data("myControl");
            if (!_myControl || _myControl == undefined) _myControl = myControl;
            if (!_myPlayer.data("ignore_timeupdate")) {
              _myControl.progress.slider("value", event.jPlayer.status.currentPercentAbsolute);
            }
          },
          volumechange: function (event) {
            var _myPlayer = $(this), _myControl = _myPlayer.data("myControl");
            if (!_myControl || _myControl == undefined) _myControl = myControl;
            if (event.jPlayer.options.muted) {
              _myControl.volume.slider("value", 0);
            } else {
              _myControl.volume.slider("value", event.jPlayer.options.volume);
            }
          },
          swfPath: "/Scripts/jplayer",
          supplied: "mp3, wav",//"m4a, oga",//mp3, 
          cssSelectorAncestor: "#jp_container_" + tindex,
          wmode: "window",
          keyEnabled: true,
          cssSelectorAncestor: containerId,
          cssSelector: {
            videoPlay: containerId + " .jp-video-play",
            play: containerId + " .jp-play",
          }
        },
        myControl = {
          progress: $(options.cssSelectorAncestor + " .jp-progress-slider"),
          volume: $(options.cssSelectorAncestor + " .jp-volume-slider")
        };
      myPlayer.jPlayer(options);
      myPlayer.data("myControl", myControl);
      myPlayerData = myPlayer.data("jPlayer");
      luyinbtn.click(function () {
        var _pi = $(this).closest(".item-luyin").siblings(".bofang-box"), ti = _pi.data("index"), _player = $("#jquery_jplayer_" + ti), _lybb = $(this).closest(".luyinbtn-box");
        if (_lybb.hasClass("selected")) {
          _lybb.removeClass("selected");
          _pi.stop(1, 0).css("overflow", "hidden").animate({ height: 0 }, 300);
          _player.jPlayer("stop");
          _player.data("myControl").progress.slider("value", 0);
        }
        else {
          panel.find(".luyinbtn-box.selected .luyinbtn").click();
          _lybb.addClass("selected");
          _pi.stop(1, 0).css("overflow", "visible").animate({ height: bofangh }, 300);
          _player.jPlayer("play", 0);
        }
      });
      container.find('.jp-gui ul li').hover(
        function () { $(this).addClass('ui-state-hover'); },
        function () { $(this).removeClass('ui-state-hover'); }
      );
      myControl.progress.slider({
        animate: "fast",
        max: 100,
        range: "min",
        step: 0.1,
        value: 0,
        slide: function (event, ui) {
          var _myPlayer = $(this).closest(".jp-container").siblings(".jp-jplayer"), _myPlayerData = _myPlayer.data("jPlayer"), _myControl = _myPlayer.data("myControl");
          if (!_myControl || _myControl == undefined) _myControl = myControl;
          if (!_myPlayerData || _myPlayerData == undefined) _myPlayerData = myPlayerData;
          var sp = _myPlayerData.status.seekPercent;
          if (sp > 0) {
            if (_myPlayer.data("fixFlash_mp4")) {
              ignore_timeupdate = true;
              _myPlayer.data("ignore_timeupdate", true);
              clearTimeout(fixFlash_mp4_id);
              fixFlash_mp4_id = setTimeout(function () {
                ignore_timeupdate = false;
                _myPlayer.data("ignore_timeupdate", false);
              }, 1000);
            }
            _myPlayer.jPlayer("playHead", ui.value * (100 / sp));
          } else {
            setTimeout(function () {
              _myControl.progress.slider("value", 0);
            }, 0);
          }
        }
      });
      myControl.volume.slider({
        animate: "fast",
        max: 1,
        range: "min",
        step: 0.01,
        value: $.jPlayer.prototype.options.volume,
        slide: function (event, ui) {
          var _myPlayer = $(this).closest(".jp-container").siblings(".jp-jplayer");
          _myPlayer.jPlayer("option", "muted", false);
          _myPlayer.jPlayer("option", "volume", ui.value);
        }
      });
    })(i);
  }
}
function rnss_event_player_mcl(panel) {
  panel = $(panel);
  //#tonghuaLogList 
  var jplaylen = panel.find(".bofang-box").length,
    bofangh = 86;//播放容器的统一高度
  for (var i = 0; i < jplaylen; i++) {
    (function (i) {
      var playitem = panel.find(".bofang-box").eq(i),
        calllogsitem = playitem.closest(".calllogsitem"),
        tindex = playitem.data("index"),
        container = $("#jp_container_" + tindex),
        containerId = "#jp_container_" + tindex,
        myControl = null,
        luyinbtnbox = calllogsitem.find(".luyinbtn-box"),
        luyinbtn = calllogsitem.find(".luyinbtn"),
        recordurl = playitem.data("url");
      var myPlayer = $("#jquery_jplayer_" + tindex);
      $("#jquery_jplayer_" + tindex).jPlayer({
        ready: function (event) {
          $(this).jPlayer("setMedia", {
            //title: "Bubble",
            mp3: $(this).closest(".bofang-box").data("url"),
            wav: $(this).closest(".bofang-box").data("url")
          });
        },
        swfPath: "/scripts/jPlayer-2.9.2/jplayer",
        //supplied: "m4a, oga",
        supplied: "mp3, wav",
        cssSelectorAncestor: '#jp_container_' + tindex,
        cssSelector: {
          videoPlay: '.jp-video-play',
          play: '.jp-play',
          pause: '.jp-pause',
          stop: '.jp-stop',
          seekBar: '.jp-seek-bar',
          playBar: '.jp-play-bar',
          mute: '.jp-mute',
          unmute: '.jp-unmute',
          volumeBar: '.jp-volume-bar',
          volumeBarValue: '.jp-volume-bar-value',
          volumeMax: '.jp-volume-max',
          playbackRateBar: '.jp-playback-rate-bar',
          playbackRateBarValue: '.jp-playback-rate-bar-value',
          currentTime: '.jp-current-time',
          duration: '.jp-duration',
          title: '.jp-title',
          fullScreen: '.jp-full-screen',
          restoreScreen: '.jp-restore-screen',
          repeat: '.jp-repeat',
          repeatOff: '.jp-repeat-off',
          gui: '.jp-gui',
          noSolution: '.jp-no-solution'
        },
        wmode: "window",
        useStateClassSkin: true,
        autoBlur: false,
        smoothPlayBar: true,
        keyEnabled: true,
        remainingDuration: true,
        toggleDuration: true
      });
      luyinbtn.click(function () {
        var _pi = $(this).closest(".item-luyin").siblings(".bofang-box"),// 当前播放器的盒子
          ti = _pi.data("index"), // 当前行索引
          _player = $("#jquery_jplayer_" + ti), // 当前 jPlayer 播放器
          _lybb = $(this).closest(".luyinbtn-box"); // 当前行的录音（播放）按钮
        if (_lybb.hasClass("selected")) {
          _lybb.removeClass("selected");
          _pi.stop(1, 0).css("overflow", "hidden").animate({ height: 0 }, 300);
          _player.jPlayer("stop");
        }
        else {
          panel.find(".luyinbtn-box.selected .luyinbtn").click();
          _lybb.addClass("selected");
          _pi.stop(1, 0).css("overflow", "visible").animate({ height: bofangh }, 300);
          _player.jPlayer("play", 0);
        }
      });
    })(i);
  }
}

function rnss_event_usertalphonecall2Init() {
  var iscallevent = $(this).data("iscallevent");

  if (!iscallevent) {
    $(this).data("iscallevent", 1);

    $(this).click(function (e) {
      setTimeout(function () {
        rnss_callFun.getTelRecordList2($('#CallNumInput').val());
      }, 1000)
      var eve = e || event;
      eve.stopPropagation();
      var outnum = this.dataset.transfernumber;//要拨打的电话号码
      var mycellphone = $(this).data("usnumber");//当前登录用户的移动电话
      var mytelephone = $(this).data("ustel");//当前登录人的固定电话
      var areacode = $(this).data("areacode");
      var areacodeid = $(this).data("areacodeid");
      var type = $(this).data("type") || "";
      var rid = $(this).data("rid");
      var areas = [{ Code: 86, Id: 1, Name: "中国大陆" }];//目前只有国内线路可用-传送门

      if ($("#staffSmallCard").length > 0) {
        $("#staffSmallCard").trigger("click");
      }

      $("#staffSmallCard").modal("hide");
      //console.log(type, rid);
      rnss_callFun.showTelModal({ outnum: outnum, mycellphone: mycellphone, mytelephone: mytelephone, phoneareas: areas, areacode: areacode }, type, rid);
      return false;
    });
    $(this).qtip({
      id: false,
      overwrite: true,
      suppress: true,
      content: {
        attr: 'title'
      },
      position: {
        my: 'top left',
        target: 'mouse',
        viewport: $(window),
        adjust: {
          x: 10, y: 10
        }
      },
      style: {
        classes: 'qtip-shadow qtip-optimize qtip-optimize-home'
      },
      show: {
        delay: 200,
        solo: false
      }
    });
  }
}

//全局方法--个人名片电话按钮--多处调用
function rnss_event_usertelphonecall2() {

    $.each($(".rnss-callphone"), function (i, n) {
    rnss_event_usertalphonecall2Init.call(this);
  });
}


var tPanel = $("#telModal");          //---电话模态窗口
var tPanelCover = $("#telModalCover");//---电话模态窗遮罩
var tOutnum = $("#CallNumInput");      //---要拨打的电话号码输入框

var rnss_callFun = {

  params: {
    msgShowOverlay: true,
    msgWidth: 450,
    msgPosition: true,
    msgDragDrop: true,
    msgVertical: false,
    msgRCallBtn: false,//是否为右侧按钮
    msgPnum: "",//对方号码
    msgUsernum: "", //我方号码
    msgUsertelnum: "",
    phoneareas: [{ Code: 86, Id: 1, Name: "中国大陆" }],
    areacode: '86',
    areacodeId: '1',
    outnum: "",//对方号码
  },
  callPanel: $("<div>", { "id": "Rs_CallBox", "class": "Rs_CallBox_Layer_Wrap" }),
  pingjiaanimate: null,
  line: 1,
  ceid: 0,
  evaluateType: "0", //评价类型-0其他 1.客户 2.简历 3.人选
  resoureid: 0,

  commonFun: {
    //替换所有空格
    replaceSpace: function (str) {
      if (!str || str.length == 0) {
        return "";
      }
      return str.replace(/\s+/g, "");
    },
  },

  //点击小图标打开拨号模态窗- 将打电话需要传给后台的数据绑定在某个元素上
  showTelModal: function (params, type, rid) {
    var opts =
    {
      msgShowOverlay: true,
      msgWidth: 450,
      msgPosition: true,
      msgDragDrop: true,
      msgVertical: false,
      msgRCallBtn: false,//是否为右侧按钮
      outnum: "",       //对方号码
      mytelephone: "", //登录人的固话
      mycellphone: "",//登录人的手机
      phoneareas: [{ Code: 86, Id: 1, Name: "中国大陆" }],
      areacode: '86',
      areacodeId: '1'
    };

    this.params = $.extend({}, opts, params || {});

    if (type) {
      $("#telModalData").data("type", type);
    } else {
      $("#telModalData").data("type", "0");
    }

    if (rid) {
      $("#telModalData").data("rid", rid);
    } else {
      $("#telModalData").data("rid", 0);
    }

      if (!this.params.mytelephone && !this.params.mycellphone) {
      r_Layout_BeautAlert.done(setRnssLanguage("请先设置员工信息中的个人手机号码，可联系分公司HR完成这一操作"), "hits")
      return;
    }

    //打开拨号窗口-赋值上要拨打的号码-剩下交给用户
    var panel = $("#telModal");
    var panelBg = $("#telModalCover");

    var outcallNum = this.params.outnum || '';

    panelBg.fadeIn('fast', function () {
      rnss_callFun.centerModals();
      panel.fadeIn('fast', function () {
        panel.find("#CallNumInput").val(outcallNum).focus();
      });
    });

  },

  //打开拨号窗口
  openTelModal: function () {
    tPanelCover.fadeIn('fast', function () {
      tPanel.fadeIn('fast');
      tOutnum.focus();
    });
  },

  //关闭拨号窗口-把保存数据的节点清空-防止生成错误类型的通话日志
  closeTelModal: function () {
    tPanel.fadeOut('fast', function () {
      tPanelCover.fadeOut(function () {
        $(this).find("#telModalData").data("type", 0).data("rid", 0);
        $("#telRecordList").html("");
        $("#telModalRecordBox").hide();
        $(this).find("#telModalData2").data("type", 0).data("rid", 0);
        $("#telRecordList2").html("");
        $("#telModalRecordBox2").hide();
      });
    });
  },
  //清空防挖角列表和沟通记录
  closeTelModal2: function () {
    $(this).find("#telModalData").data("type", 0).data("rid", 0);
    $("#telRecordList").html("");
    $("#telModalRecordBox").hide();
    $(this).find("#telModalData2").data("type", 0).data("rid", 0);
    $("#telRecordList2").html("");
    $("#telModalRecordBox2").hide();
  },

  //关闭所有面板
  closeCallPanel: function () {
    $("#Rs_CallBox").fadeOut(400, function () {
      $("#Rs_CallBox").remove();
    });
  },

  //显示评分按钮-隐藏拨号按钮
  showRatingBtn: function () {
    rnss_callFun.closeTelModal();
    $("#showTelModalBtn").hide();
    $("#CallBox .callphone-pingjia-box").show();
  },

  //隐藏评分按钮-显示拨打电话按钮
  hideRatingBtn: function () {
    $("#CallBox .callphone-pingjia-box").hide();
    $("#showTelModalBtn").show();
  },

  //构建评价面板
  callGetPingjiaHtml: function (outnum, line) {
    var html = '<div class="cp-pho-box">';
    html += '<div class="cp-pho-close">';
    html += '<div class="cp-pho-title"></div>';
    html += '<div class="cp-close" title="' + setRnssLanguage("点击关闭") + '"></div>';
    html += '</div>';
    html += '<div class="my-callpho-commentbox"><div class="my-callpho-commentbox-con">';
    html += '<div class="line cf" style="margin-bottom:8px;font-size:14px;border-bottom: 1px solid #ccc;padding-bottom: 7px;"><span style="color:blue;font-weight:bold;">' + setRnssLanguage("线路") + '' + this.line + '</span> ' + setRnssLanguage("正在呼叫") + ' <span style="color:red;font-weight:bold;">' + outnum + '</span>，' + setRnssLanguage("请准备接听电话。") + '</div>';
    html += '<div class="line cf call-phone-top"><div style="color: red;font-weight: bold;">' + setRnssLanguage("通话结束后可对本次电话的语音质量做出评价：") + '</div>';
    html += '<div class="line cf" style="margin-bottom:8px;">';
    html += '<span class="mcp-zlcomment" title="' + setRnssLanguage("好评") + '"><input type="radio" checked="checked" value="1" name="callphozlcomment" id="callphozlcomment1" /><label class="cp-zl-icon cp-zl-icon1" for="callphozlcomment1">' + setRnssLanguage("语音质量") + '<span style="color:#22B14C;font-weight:bold;">' + setRnssLanguage("完美") + '</span></label></span>';
    html += '<span class="mcp-zlcomment" title="' + setRnssLanguage("中评") + '"><input type="radio" value="2" name="callphozlcomment" id="callphozlcomment2" /><label class="cp-zl-icon cp-zl-icon2" for="callphozlcomment2">' + setRnssLanguage("语音质量") + '<span style="font-weight:bold;">' + setRnssLanguage("一般") + '</span></label></span>';
    html += '<span class="mcp-zlcomment" title="' + setRnssLanguage("差评") + '"><input type="radio" value="3" name="callphozlcomment" id="callphozlcomment3" /><label class="cp-zl-icon cp-zl-icon3" for="callphozlcomment3">' + setRnssLanguage("语音质量") + '<span style="color:red;font-weight:bold;">' + setRnssLanguage("差劲") + '<span></label></span></div></div>';
    html += '<div class="line cf call-phone" style="font-weight: bold;">' + setRnssLanguage("主要问题（可多选）：") + '</div>';
    html += '<div class="line mcp-wtcomment cf call-phone"><input type="checkbox" name="callphowtcomment" value="1" id="callphowtcomment1" /><label for="callphowtcomment1">' + setRnssLanguage("语音有延迟，彼此通话会互相干扰插话") + '</label></div>';
    html += '<div class="line mcp-wtcomment cf call-phone"><input type="checkbox" name="callphowtcomment" value="2" id="callphowtcomment2" /><label for="callphowtcomment2">' + setRnssLanguage("语音不清晰，音量小，听不清说什么") + '</label></div>';
    html += '<div class="line mcp-wtcomment cf call-phone" style="margin-bottom:8px;"><input type="checkbox" name="callphowtcomment" value="3" id="callphowtcomment3" /><label for="callphowtcomment3">' + setRnssLanguage("未接通，原因不明") + '</label></div>';
    html += '<div class="line cf" style="font-weight: bold;">' + setRnssLanguage("其他问题描述：") + '</div>';
    html += '<div class="line cf"><textarea rows="3" cols="38" class="mcp-qtcomment"></textarea></div>';
    html += '<div class="line mcp-submit-box cf" style="text-align:center;"><input type="button" class="whitebtn btn-submit0" value="' + setRnssLanguage("提交") + '" /></div>';
    html += '</div></div>';
    html += '</div>';

    return html;
  },

  //显示评价面板
  ShowPingjiaPanel: function () {
    var html = this.callGetPingjiaHtml(this.outnum, this.line);
    this.callPanel = $("<div>", { "id": "Rs_CallBox", "class": "Rs_CallBox_Layer_Wrap" });
    this.closeTelModal();

    this.callPanel.html(html);
    this.callPanel.appendTo(document.body);

    this.executeRatingEvent();//执行评价事件

    var panel = "#Rs_CallBox .cp-pho-box";
    //样式控制
    rnss_callFun.msgConter(panel, this.params.msgWidth, this.params.msgPosition, this.params.msgDragDrop, this.params.msgVertical);

    if (this.params.msgShowOverlay) { this.msgShowOverlay(); }

    if (this.params.msgDragDrop) {
      $(panel).css({ position: "absolute" }).find(".cp-pho-title").css("cursor", "move");
      new dragDrop($(panel), { trigger: "#Rs_CallBox .cp-pho-title" });
    }
  },

  //提交评价到后台
  executeRatingEvent: function () {

    $(".my-callpho-commentbox .btn-submit0").click(function () {
      var data = rnss_callFun.getcommentdata(1);

      if (data === false) {
        return;
      }

      if (!data.evaluationlevel) {
        r_Layout_BeautAlert.done(setRnssLanguage("请选择您对通话质量的评价"), "hits")
        return;
      }

      waitingLayer2.show();
      //调用创建通话日志接口
      $.ajaxPost("/services/staffactionhandler.ashx?action=callevaluation", data, function (result) {
        waitingLayer2.hide();
        if (result.Status == "1") {
          r_Layout_BeautAlert.done(setRnssLanguage("评价成功"), "hits", 1600);

          rnss_callFun.hideRatingBtn();//隐藏评价按钮
          rnss_callFun.closeCallPanel();//关闭所有面板

        } else {
          r_Layout_BeautAlert.done(result.Data, "hits")
          return;
        }
      });
    });

    $("#Rs_CallBox .cp-close").click(function () {
      rnss_callFun.submitcancelcomment();
    });
  },

  //组装评价数据给executeRatingEvent
  getcommentdata: function (feedbstatus) {
    var data = {};
    data.evaluationlevel = $(".my-callpho-commentbox input[name=callphozlcomment]:checked").val();
    var questiontypes = [];
    $.each($(".my-callpho-commentbox input[name=callphowtcomment]:checked"), function (i, n) {
      questiontypes.push($(this).val());
    });
    data.questiontype = questiontypes.length > 0 ? questiontypes.join(",") : "";
    data.remark = $.trim($(".my-callpho-commentbox .mcp-qtcomment").val());
    data.feedbstatus = feedbstatus;
    data.ceid = this.ceid;
    return data;
  },

  //拒绝反馈
  submitcancelcomment: function () {
    var data = {};
    if (!data.evaluationlevel) {
      data.evaluationlevel = "";
    }
    data.feedbstatus = 2;
    data.ceid = this.ceid;

    rnss_callFun.hideRatingBtn();

    $.ajaxPost("/services/staffactionhandler.ashx?action=callevaluation", data, function (result) {
      //console.log(result);
      if (result.Status == "1") {
        rnss_callFun.closeCallPanel();//关闭所有面板
      } else {
        r_Layout_BeautAlert.done(result.Data, "hits")
        rnss_callFun.closeCallPanel();//关闭所有面板
      }
    });
  },

  //再打一个
  callreload: function () {
    this.openTelModal({ msgPnum: "", msgUsernum: this.params.msgUsernum, msgUsertelnum: this.params.msgUsertelnum, phoneareas: rnss_telpho_data.phoneareas });
  },

  //背景遮罩
  msgShowOverlay: function () {
    var maxH = document.documentElement.scrollHeight;
    if ($("#bgWrap").length) {
      $("#bgWrap").css({
        "display": "block",
        "opacity": .4
      });
    } else {
      $("<div>", { id: "bgWrap" }).css({ "display": "block", "height": maxH }).appendTo($("#Rs_CallBox"));
      /msie 6\.0/i.test(navigator.userAgent) && $("<iframe>").css({
        "background": "#fff",
        "left": 0,
        "top": 0,
        "opacity": .3,
        "position": "absolute",
        "zIndex": -1,
        "width": "100%",
        "height": maxH
      }).appendTo($("#Rs_CallBox"));
    }
  },

  //大概是拖拽功能
  msgConter: function ($el, msgWidth, msgPosition, msgDragDrop, msgVertical) {
    el = $($el);
    if (!msgDragDrop) {
      if (msgPosition) {
        //相对定位
        el.css({
          top: "50%",
          "margin-top": ($.browser.msie && parseInt($.browser.version) <= 6) ? -(el.outerHeight() / 2) + $(document).scrollTop() : -(el.outerHeight() / 2),
          left: "50%",
          display: "block",
          "margin-left": -(msgWidth / 2),
          position: ($.browser.msie && parseInt($.browser.version) <= 6) ? "absolute" : "fixed",
          width: msgWidth
        });
      }
      else {
        $(window).resize(function () {
          DragDropPsoition();
        });
        function DragDropPsoition() {
          //绝对定位
          if ($.browser.msie && parseInt($.browser.version) <= 6) {
            //如果是IE6及以下版本
            var left = ($(window).width() - msgWidth) / 2;
            var h = window.outerHeight || document.documentElement.clientHeight;
            var stlye;
            $(window).scroll(stlye = function () {
              el.css({
                top: $(document).scrollTop() + (h - el.outerHeight()) / 2 - 5,
                left: left,
                width: msgWidth
              });
            });
            stlye();
          }
          else {
            //如果是IE6以上版本
            var left = ($(window).width() - msgWidth) / 2;
            var center = ((msgVertical ? document.body.scrollHeight : $(window).height()) - el.outerHeight()) / 2;
            topHeight = Math.abs(center > 0 ? center : 0) + $(document).scrollTop() || 0;
            el.css({
              "top": topHeight,
              "left": left,
              width: msgWidth,
              position: "absolute"
            });
          }
        }
        DragDropPsoition();
        el.css("display", "block").show();
      }
    }
    else {
      //拖拽时不兼容定位属性，所以当使用拖拽时，取消相对定位，使用绝对定位
      var left, top;
      var w = window.outerWidth || document.documentElement.clientWidth;
      var h = window.outerHeight || document.documentElement.clientHeight;
      left = ($(window).width() - msgWidth) / 2;
      var center = ((msgVertical ? document.body.scrollHeight : $(window).height()) - el.outerHeight()) / 2;
      top = Math.abs(center > 0 ? center : 0) + $(document).scrollTop() || 0;
      el.css({ left: left, top: top, width: msgWidth, "display": "block" });
    }
  },

  /*
  * 模态框位置设定 
  */
  centerModals: function () {
    var oBody = document.body || document.documentElement;
    var sTop = oBody.scrollTop || window.pageYOffset || 0;//兼容火狐
    var modalTop = Number(sTop + 100) + "px" || 120;
    $('#telModal').css("top", modalTop);
  },

  //调取最近通话记录
  getTelRecordList: function (outcallnum) {
    var container = $("#telModalRecordBox");
    var rList = $("#telRecordList");
    //var aBtn = $("#telRecordSeemore");

    var data = {
      phonenumber: outcallnum//outcallnum
    };

    //$.post("/CallLog/CallLogs", data, function (r) {
    $.post("/resume/getclientinfo", data, function (r) {
      //console.log(r);
      var html = "";

      if (r.Status) {
        //return;
        var list = r.Data || [], len = list.length || 0;

        if (len == 0) {
          return;
          html += "<li><span class='rdNoData'>系统没有查到与" + data.phonenumber + "的相关记录</span></li>";
        } else {
          for (var i = 0; i < len; i++) {
            //html += "<li><span class='rdType'>" + list[i].Type + "</span><span class='rdName'>" + (list[i].Name||"--") + "</span><span class='rdCompany'>" + (list[i].CompanyName||"--") + "</span><span class='rdTime'>" + list[i].Time + "</span></li>"
            html += '<li>';//start
            html += '<a target="_blank" href="/resume/viewresume?id=' + list[i].ResumeId + '" class="rdName">' + list[i].ResumeName + '</a>';
            html += '<a target="_blank" href="/client/viewclient?id=' + list[i].ClientId + '" class="rdClient" title="' + list[i].ClientName + '">' + list[i].ClientName + '</a>';
            html += '<span class="rdCompany" title="' + list[i].CompanyName + '">' + list[i].CompanyName + '</span>';
            html += '<span class="rdTime">' + list[i].ContactDate + '</span>';
            html += '</li>';//end
          }
        }

      } else {
        return;
        html += "<li class=''>系统没有查到与" + data.phonenumber + "的通话记录</li>";
      }

      rList.html(html);
      //aBtn.attr("href", '/apps/managecalllogs.aspx?keywords=' + r.Message);

      $("#telModalRecordBox").show(function () {
        container.show();
      })


    }).error(function (er) {
      console.log(er);
    })
  },
  //调取最近通话记录
  getTelRecordList2: function (outcallnum) {
    var container = $("#telModalRecordBox2");
    var rList = $("#telRecordList2");
    var aBtn = $("#telRecordSeemore2");
    var data = {
      phonenumber: outcallnum//outcallnum
    };

    $.post("/CallLog/CallLogs", data, function (r) {
      //console.log(r);
      var html = "";

      if (r.Status) {
        var list = r.Data || [], len = list.length || 0;

        if (len == 0) {
          html += "<li><span class='rdNoData'>系统没有查到与" + r.Message + "的通话记录</span></li>";
        } else {
          for (var i = 0; i < len; i++) {
            html += "<li><span class='rdType'>" + list[i].Type + "</span><span class='rdName'>" + (list[i].Name || "--") + "</span><span class='rdCompany'>" + (list[i].CompanyName || "--") + "</span><span class='rdTime'>" + list[i].Time + "</span></li>"
          }
        }

      } else {
        html += "<li class='text-center'>系统没有查到与" + r.Message + "的通话记录</li>";
      }

      rList.html(html);
      aBtn.attr("href", '/apps/managecalllogs.aspx?keywords=' + r.Message);

      $("#telModalRecordBox2").show(function () {
        container.show();
      })


    }).error(function (er) {
      console.log(er);
    })
  }
}

var rnss_phoneCall = (function () {
  var panel = $("#telModal");
  var panelBg = $("#telModalCover");

  var commonFun = {
    //替换掉所有空格
    replaceSpace: function (str) {
      if (!str || str.length == 0) {
        return "";
      }
      return str.replace(/\s+/g, "");
    },

    //自动聚焦
    autofocus: function (target) {
      target.focus();
    },
  };

  var controlCallPanel = function () {
    //悬浮按钮
    $("#CallBox .callphone-box").click(function () {
      panelBg.fadeIn('fast', function () {
        rnss_callFun.centerModals();//确定显示位置
        panel.fadeIn('fast');//面板显示之前如果需要可以getPrefix()-拿取前缀
        $("#CallNumInput").val("");//通过悬浮按钮打开时清除值
        commonFun.autofocus($("#CallNumInput"));
      });
    });

    $(".tel-modal-close").click(function () {
      rnss_callFun.closeTelModal();
    });

    //关闭提示
    $("#telModal .tm-notice-close").click(function () {
      $("#telModal .tm-notice-box").hide();
    });
    $("#telModalRecordBox .tm-notice-close").click(function () {
      $("#telModalRecordBox .tm-notice-box").hide();
    });
  };

  /**
   * 获取拨号前缀
   * 现在系统只允许拨打国内电话+86
   */
  var getPrefix = function () {
    $.get("/Services/StaffActionHandler.ashx?action=getphonearea", {}, function (r) {
      console.log(r);
    })
  }

  //监听输入对方号码的输入框-执行相关事件
  var callNumInputWatcher = function () {
    var callNum = $("#CallNumInput");//要拨打的电话号码

    var timer = null;

    callNum.keydown(function (e) {
      e = e || event;
      var num = $(this).val(),
        dnum = $(this).data("num");
      if (num == dnum) return;
      var len = num.length;
      if (len == 0) {
        $("#telModalRecordBox").slideUp();
      }
      if (len < 8 || len > 11) {
        rnss_callFun.closeTelModal2();
        callNum.data("num", "");
        return;
      }
      //clearTimeout(timer);
      //timer = setTimeout(function () {
      //  if (num < 8 || num > 11) {
      //    return;
      //  }
      //  rnss_callFun.getTelRecordList(num);
      //  rnss_callFun.getTelRecordList2(num);
      //}, 600);
      if (e.keyCode == 13) {

        callNum.data("num", num);
        rnss_callFun.closeTelModal2();
        rnss_callFun.getTelRecordList(num);
        rnss_callFun.getTelRecordList2(num);
      }
    })
    //获得焦点查询数据
    callNum.blur(function () {
      var num = $.trim(callNum.val()), len = num.length,
        dnum = $(this).data("num");
      if (num == dnum) return;
      if (len == 0) {
        $("#telModalRecordBox").hide();
      }
      if (len < 8 || len > 11) {
        rnss_callFun.closeTelModal2();
        callNum.data("num", "");
        return;
      }
      if ($("#telRecordList li").length > 1) {
        return;
      }

      if (num && len >= 8 && len <= 11) {

        callNum.data("num", num);
        rnss_callFun.closeTelModal2();
        rnss_callFun.getTelRecordList(num);
        rnss_callFun.getTelRecordList2(num);
      }
    })
    //失去焦点查询数据
    //callNum.blur(function () {
    //  var num = $.trim(callNum.val()), len = num.length;

    //  if (num && len >= 8) {
    //    rnss_callFun.getTelRecordList(num);
    //    rnss_callFun.getTelRecordList2(num);
    //  }
    //})
  }

  /*
   * 号码类型联动可以使用的线路
   * calltype=1  用员工手机呼叫，线路1和2都可以使用
   * calltype=2  是用座机呼叫，只能使用线路1
   * 有座机的话默认选中
   */
  var setCallMethods = function () {
    var numBtn = $(".tm_num");//我的号码
    var len = numBtn.length || 0;

    $(".tm_num").click(function () {
      if (len > 1) {//有选择的时候再执行选中事件
        $(".tm_num").removeClass("selected");
        $(this).addClass("selected");
      }

      var calltype = $(this).data("calltype");

      if (calltype === 1) {
        //线路1，2均可使用
        $(".call_line").removeClass("call_line_disabled");
      }

      if (calltype === 2) {
        //禁用线路2
        panel.find("#callLine2").addClass("call_line_disabled");
      }
    })
  };

  //点击线路打电话
  var makeACall = function () {
    //线路1
    $("#callLine1").click(function () {
      var isInputEmpty = testTargetNumberIsEmpty();
      if (isInputEmpty) return;
      //被叫号码
      var targetNum = $("#CallNumInput").val()
      targetNum = commonFun.replaceSpace(targetNum);//替换空格
      //我的号码
      var usernumber = panel.find(".tm_num.selected").text();
      usernumber = commonFun.replaceSpace(usernumber);

      var type = $("#telModalData").data("type") || 0;  //从系统何处打电话过来
      var rid = $("#telModalData").data("rid") || 0;    //???客户id，用来创建通话日志时保存

      rnss_callFun.line = 1;
      rnss_callFun.outnum = targetNum;

      var senData = { transfernumber: targetNum, usnumber: usernumber, type: type, rid: rid }
      waitingLayer2.show();
      $.ajaxPost2("/services/staffactionhandler.ashx?action=telphonecall", "POST", senData, function (result) {
        waitingLayer2.hide();

        // 由于登录状态失效 所以重新加载...
        if (!result) {
          location.reload();
          return;
        }

        if (result.Status == "1") {

          rnss_callFun.ceid = result.Data;
          rnss_callFun.showRatingBtn();//显示评价按钮

          r_Layout_BeautAlert.done(setRnssLanguage("正在呼叫") + targetNum + setRnssLanguage("，请准备接听电话"), "hits", 3000);

          // 寻访管理需要增加的地方 线路一
          // 需要弹出 model 
          //if (window.searchManagementVue) {
          //  r_Layout_BeautAlert.done(setRnssLanguage("正在呼叫") + targetNum + setRnssLanguage("，请准备接听电话"), "hits", 3000, function () {
          //    searchManagementVue.showModelBody('viewresume', searchManagementVueQuery)
          //  });
          //} else {
          //  r_Layout_BeautAlert.done(setRnssLanguage("正在呼叫") + targetNum + setRnssLanguage("，请准备接听电话"), "hits", 3000);
          //}

        } else {
          r_Layout_BeautAlert.done(result.Data, "hits");
          return;
        }

      });

    });

    //2方法和1基本一样
    $("#callLine2").click(function () {

      if ($("#callLine2").hasClass("call_line_disabled")) {
        return;
      }

      var isInputEmpty = testTargetNumberIsEmpty();
      if (isInputEmpty) return;

      var targetNum = $("#CallNumInput").val()
      targetNum = commonFun.replaceSpace(targetNum);

      var usernumber = panel.find(".tm_num.selected").text();
      usernumber = commonFun.replaceSpace(usernumber);

      var type = $("#telModalData").data("type") || 0;  //从系统何处打电话过来
      var rid = $("#telModalData").data("rid") || 0;    //???客户id，用来创建通话日志时保存

      rnss_callFun.line = 2;
      rnss_callFun.outnum = targetNum;

      var senData = { transfernumber: targetNum, usnumber: usernumber, type: type, rid: rid }

      waitingLayer2.show();
      $.ajaxPost("/services/staffactionhandler.ashx?action=telphonecall1", senData, function (result) {
        waitingLayer2.hide();

        // 由于登录状态失效 所以重新加载...
        if (!result) {
          location.reload();
          return;
        }

        if (result.Status == "1") {

          rnss_callFun.ceid = result.Data;
          rnss_callFun.showRatingBtn();//显示评价按钮

          r_Layout_BeautAlert.done(setRnssLanguage("正在呼叫") + targetNum + setRnssLanguage("，请准备接听电话"), "hits", 3000);

          // 寻访管理需要增加的地方 线路二
          // 需要弹出 model 
          //if (window.searchManagementVue) {
          //  r_Layout_BeautAlert.done(setRnssLanguage("正在呼叫") + targetNum + setRnssLanguage("，请准备接听电话"), "hits", 3000, function () {
          //    searchManagementVue.showModelBody('viewresume', searchManagementVueQuery)
          //  });
          //} else {
          //  r_Layout_BeautAlert.done(setRnssLanguage("正在呼叫") + targetNum + setRnssLanguage("，请准备接听电话"), "hits", 3000);
          //}

        } else {
          r_Layout_BeautAlert.done(result.Data, "hits")
          return;
        }
      });

    });
    //2方法和1基本一样
    $("#callLine3").click(function () {

      if ($("#callLine3").hasClass("call_line_disabled")) {
        return;
      }

      var isInputEmpty = testTargetNumberIsEmpty();
      if (isInputEmpty) return;

      var targetNum = $("#CallNumInput").val()
      targetNum = commonFun.replaceSpace(targetNum);

      var usernumber = panel.find(".tm_num.selected").text();
      usernumber = commonFun.replaceSpace(usernumber);

      var type = $("#telModalData").data("type") || 0;  //从系统何处打电话过来
      var rid = $("#telModalData").data("rid") || 0;    //???客户id，用来创建通话日志时保存

      rnss_callFun.line = 3;
      rnss_callFun.outnum = targetNum;

      var senData = { transfernumber: targetNum, usnumber: usernumber, type: type, rid: rid }


      waitingLayer2.show();
      $.ajaxPost("/services/staffactionhandler.ashx?action=telphonecall2", senData, function (result) {
        waitingLayer2.hide();
        if (result.Status == "1") {

          rnss_callFun.ceid = result.Data;
          rnss_callFun.showRatingBtn();//显示评价按钮

          r_Layout_BeautAlert.done(setRnssLanguage("正在呼叫") + targetNum + setRnssLanguage("，请准备接听电话"), "hits", 3000);

        } else {
          r_Layout_BeautAlert.done(result.Data, "hits")
          return;
        }
      });

    });
    //判断对方号码是否为空
    function testTargetNumberIsEmpty() {
      var targetNum = $.trim($("#CallNumInput").val());
      if (!targetNum) {
        r_Layout_BeautAlert.done(setRnssLanguage("请输入您要拨打的手机号码"), "hits");
        return true;
      }
      return false;
    }
  };

  //通话反馈
  var feedBack = function () {

    //打开面板
    $(".callphone-pingjia-box .cppj-fankui").click(function () {
      rnss_callFun.ShowPingjiaPanel();
    });

    //再打一个
    $(".callphone-pingjia-box .cppj-callreload").click(function () {
      rnss_callFun.callreload();
    });
  };

  return {
    init: function () {
      controlCallPanel();
      setCallMethods();
      makeACall();
      feedBack();
      callNumInputWatcher();
    }
  }

})();

$().ready(function () {
  rnss_phoneCall.init();
  setTimeout(function () {
    rnss_event_usertelphonecall2();
  })

})

