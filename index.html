<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="utf-8" />
  <title>RISFOND RNSS V3.0 - 瑞丰德永才华</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta content="width=device-width, initial-scale=1" name="viewport" />
  <meta content="瑞丰德永才华招聘管理系统" name="description" />
  <meta content="RISFOND" name="author" />
  <link rel="shortcut icon" href="/favicon.ico" />
  
  <!-- CSS Files -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://unpkg.com/element-ui/lib/theme-chalk/index.css" rel="stylesheet">
  <link href="assets/css/main.css" rel="stylesheet">
  <link href="assets/css/indexbase.css" rel="stylesheet">
  
  <style>
    [v-cloak] { display: none !important; }
    body { font-family: "微软雅黑", "Microsoft YaHei", Arial, sans-serif !important; }
    
    /* 四季换肤样式 */
    .content-wrap-chun { background: url('assets/images/chun_bg.png') no-repeat left 75px; background-size: 100%; }
    .content-wrap-xia { background: url('assets/images/xia_bg.png') no-repeat left 75px; background-size: 100%; }
    .content-wrap-qiu { background: url('assets/images/qiu_bg.png') no-repeat left 75px; background-size: 100%; }
    .content-wrap-dong { background: url('assets/images/dong_bg.png') no-repeat left 75px; background-size: 100%; }
    
    .page-header-fixed .page-container,
    .index-paihangbang { background-color: transparent !important; }
  </style>
</head>

<body class="page-container-bg-solid page-sidebar-closed-hide-logo page-boxed page-header-fixed">
  
  <!-- 顶部导航栏 -->
  <div class="page-header navbar navbar-fixed-top" id="headHuanfu">
    <div class="page-header-inner container">
      <!-- Logo区域 -->
      <div class="page-logo com-flex">
        <a href="/">
          <div class="logo-placeholder">
            <i class="fas fa-building"></i>
          </div>
        </a>
        <div class="menu-toggler sidebar-toggler"></div>
      </div>

      <!-- 换肤功能 -->
      <div class="menu-icon-box">
        <div class="huanfu-box">
          <el-dropdown trigger="click">
            <span class="huanfu-dropdown-link">
              <i class="fas fa-palette"></i> 换肤
            </span>
            <el-dropdown-menu class="dropeown-huanfu" slot="dropdown">
              <el-dropdown-item>
                <el-radio @input="changeSelVal" v-model="selFuVal" label="0">默认</el-radio>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-radio @input="changeSelVal" v-model="selFuVal" label="1">春</el-radio>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-radio @input="changeSelVal" v-model="selFuVal" label="2">夏</el-radio>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-radio @input="changeSelVal" v-model="selFuVal" label="3">秋</el-radio>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-radio @input="changeSelVal" v-model="selFuVal" label="4">冬</el-radio>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>

      <!-- 顶部搜索和用户菜单 -->
      <div class="page-top">
        <!-- 搜索表单 -->
        <form class="search-form2" id="r-search" method="GET" action="">
          <div class="input-group">
            <div class="input-group-btn">
              <button type="button" class="btn white dropdown-toggle search-form2-selected" data-toggle="dropdown">
                <span class="js-currentOption">客户</span>
                <i class="fa fa-angle-down"></i>
              </button>
              <ul class="dropdown-menu js-options">
                <li><a href="javascript:;" data-value="1">客户</a></li>
                <li><a href="javascript:;" data-value="2">职位</a></li>
                <li><a href="javascript:;" data-value="3">简历</a></li>
              </ul>
            </div>
            <input type="text" class="form-control" name="keywords" placeholder="请输入搜索关键词">
            <span class="input-group-btn">
              <a href="javascript:;" class="btn submit js-submit">
                <i class="fas fa-search"></i>
              </a>
            </span>
          </div>
        </form>

        <!-- 顶部菜单 -->
        <div class="top-menu">
          <ul class="nav navbar-nav pull-right">
            <!-- 审批中心 -->
            <li class="dropdown dropdown-extended dropdown-inbox dropdown-dark">
              <a href="javascript:;" class="Approval" @click="ApprovalDialogOpenClick">
                <i class="fas fa-clipboard-check"></i>
                <span class="badge badge-danger2" v-if="WorkflowCountNum==0">0</span>
                <span class="badge badge-danger1" v-else-if="WorkflowCountNum<99">{{WorkflowCountNum}}</span>
                <span class="badge badge-danger1" v-else>99+</span>
              </a>
            </li>

            <!-- 消息中心 -->
            <li class="dropdown dropdown-extended dropdown-inbox dropdown-dark" id="header_inbox_bar">
              <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown">
                <i class="fas fa-envelope"></i>
                <span class="badge badge-danger">0</span>
              </a>
            </li>

            <!-- 日程提醒 -->
            <li class="dropdown dropdown-extended dropdown-tasks dropdown-dark" id="header_task_bar">
              <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown">
                <i class="fas fa-calendar"></i>
                <span class="badge badge-primary">0</span>
              </a>
            </li>

            <!-- 用户菜单 -->
            <li class="dropdown dropdown-user dropdown-dark">
              <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown">
                <ul>
                  <li class="username">{{userName}}</li>
                  <li class="btn-sm show-score">
                    <i class="fa fa-money"></i>
                    <span id="head_show_score">{{userScore}}</span> R币
                  </li>
                </ul>
                <div class="user-avatar-placeholder">
                  <i class="fas fa-user"></i>
                </div>
              </a>
              <ul class="dropdown-menu dropdown-menu-default">
                <li><a href="/staff/PersonalHomepage"><i class="fas fa-user"></i> 个人主页</a></li>
                <li><a href="/company/CompanyMainPage"><i class="fas fa-building"></i> 公司主页</a></li>
                <li><a href="/profile/taskmanage.aspx"><i class="fas fa-calendar"></i> 我的日程</a></li>
                <li><a href="javascript:void(0)" onclick="LoginOut()"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <!-- 主要内容区域 -->
  <div class="" id="huanfuBG">
    <div class="container">
      <div class="page-container" id="page-container">
        
        <!-- 侧边栏 -->
        <div class="page-sidebar-wrapper">
          <div class="page-sidebar navbar-collapse collapse border-ra">
            <ul class="page-sidebar-menu page-sidebar-menu-hover-submenu" data-keep-expanded="false" data-auto-scroll="true" data-slide-speed="200">
              
              <!-- 首页 -->
              <li data-Id="1" class="nav-item active open">
                <a href="/" class="nav-link nav-toggle">
                  <i class="fas fa-home"></i>
                  <span class="title">首页</span>
                  <span class="selected"></span>
                </a>
              </li>

              <!-- 客户管理 -->
              <li data-Id="2" class="nav-item">
                <a href="/client/manage" class="nav-link nav-toggle">
                  <i class="fas fa-users"></i>
                  <span class="title">客户管理</span>
                  <span class="arrow"></span>
                </a>
                <ul class="sub-menu" style="display: none;">
                  <li><a href="/client/manage?guishu=1" class="nav-link"><span class="title">我的客户</span></a></li>
                  <li><a href="/client/manage?guishu=3" class="nav-link"><span class="title">合作客户</span></a></li>
                  <li><a href="/client/manage?guishu=2" class="nav-link"><span class="title">其他客户</span></a></li>
                  <li><a href="/client/manage" class="nav-link"><span class="title">客户管理</span></a></li>
                  <li><a href="/client/editclient" class="nav-link"><span class="title">录入客户</span></a></li>
                  <li><a href="/apps/publicclients2.aspx" class="nav-link"><span class="title">公共客户</span></a></li>
                </ul>
              </li>

              <!-- 职位管理 -->
              <li data-Id="3" class="nav-item">
                <a href="/job/managejob" class="nav-link nav-toggle">
                  <i class="fas fa-briefcase"></i>
                  <span class="title">职位管理</span>
                  <span class="arrow"></span>
                </a>
                <ul class="sub-menu" style="display: none;">
                  <li><a href="/job/managejob?rt=1" class="nav-link"><span class="title">我的职位</span></a></li>
                  <li><a href="/job/managejob?rt=2" class="nav-link"><span class="title">合作职位</span></a></li>
                  <li><a href="/job/managejob?rt=3" class="nav-link"><span class="title">其他职位</span></a></li>
                  <li><a href="/job/managejob" class="nav-link"><span class="title">职位管理</span></a></li>
                  <li><a href="/Fastjob" class="nav-link"><span class="title">快速录入</span></a></li>
                  <li><a href="/deliverycenter/editjobinfo" class="nav-link"><span class="title">完整录入</span></a></li>
                </ul>
              </li>

              <!-- 人选管理 -->
              <li data-Id="4" class="nav-item">
                <a href="/resume/nsearchresume" class="nav-link nav-toggle">
                  <i class="fas fa-user-tie"></i>
                  <span class="title">人选管理</span>
                  <span class="arrow"></span>
                </a>
                <ul class="sub-menu" style="display: none;">
                  <li><a href="/profile/myrecommendmanage.aspx" class="nav-link"><span class="title">推荐管理</span></a></li>
                  <li><a href="/profile/myrecommendmanage.aspx?status=8" class="nav-link"><span class="title">我的offer</span></a></li>
                  <li><a href="/PersonnelBank/Index" class="nav-link"><span class="title">人才银行</span></a></li>
                  <li><a href="/resume/nsearchresume" class="nav-link"><span class="title">搜索简历</span></a></li>
                  <li><a href="/apps/editresume.aspx" class="nav-link"><span class="title">录入简历</span></a></li>
                </ul>
              </li>

              <!-- 其他菜单项... -->
              <li data-Id="5" class="nav-item">
                <a href="/message/managemessages.aspx" class="nav-link nav-toggle">
                  <i class="fas fa-envelope"></i>
                  <span class="title">信息管理</span>
                </a>
              </li>

              <li data-Id="6" class="nav-item">
                <a href="/performance/index" class="nav-link nav-toggle">
                  <i class="fas fa-chart-line"></i>
                  <span class="title">绩效管理</span>
                </a>
              </li>

              <li data-Id="7" class="nav-item">
                <a href="/finance/index" class="nav-link nav-toggle">
                  <i class="fas fa-calculator"></i>
                  <span class="title">财务管理</span>
                </a>
              </li>

              <li data-Id="8" class="nav-item">
                <a href="/admin/index" class="nav-link nav-toggle">
                  <i class="fas fa-cog"></i>
                  <span class="title">行政管理</span>
                </a>
              </li>

            </ul>
          </div>
        </div>

        <!-- 主内容区域 -->
        <div class="page-content-wrapper">
          <div class="page-content body" id="page-content">
            <!-- 四季顶部装饰 -->
            <div class="huanfu-base-head-img"></div>
            
            <!-- Vue应用主容器 -->
            <div id="concenter" class="huanfu-po-box" v-cloak>
              <!-- 内容将通过Vue.js动态加载 -->
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>

  <!-- JavaScript Files -->
  <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
  <script src="https://unpkg.com/element-ui/lib/index.js"></script>
  <script src="assets/js/main.js"></script>

</body>
</html>
