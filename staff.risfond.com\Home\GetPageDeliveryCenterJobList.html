{"page": 1, "pageSize": 5, "recordCount": 4, "pageCount": 0, "data": [{"isJoin": false, "isLike": false, "jiaruxiangmuCount": 0, "tuijianGeiHekuQuery": 0, "yuyuemianshiCount": 0, "renxuanlizhiCount": 0, "keHuQueRenCount": 0, "allCount": 0, "jiaruxiangmuTabCount": 0, "tuijiangeiguwenTabCount": 0, "tuijiangeiguwenCount": 0, "tuijiangeikehuTabCount": 0, "tuijiangeikehuCount": 1, "yuyuemianshiTabCount": 0, "kehumianshiTabCount": 0, "kehumianshiCount": 0, "yifaOfferTabCount": 0, "yifaOfferCount": 0, "yijieOfferTabCount": 0, "yijieOfferCount": 0, "ruzhiTabCount": 0, "ruzhiCount": 0, "keHuQueRenTabCount": 0, "renxuanlizhiTagCount": 0, "renxuanFangqiCount": 0, "renxuanFangqiTagCount": 0, "score": null, "esScore": 0.0, "pubilcText": null, "keHuFouJueCount": 0, "keHuFouJueTagCount": 0, "paymentCycle": "24.9", "hasAttention": false, "jobId": 459087, "title": "销售总经理（香港）", "publicTime": "2024-08-02 13:52", "lastUpdated": "2024-08-06 18:49", "publicTimeMonthDay": "8月6日", "clientName": "某知名农业科技股份有限公司", "clientLocationId": null, "clientLocation": "北京", "jobLocationId": null, "jobLocation": "北京", "clietnIndustryId": null, "clietnIndustry": "全部农林牧渔", "clientLevel": 2, "shortWarrantyPeriod": false, "warrantyPeriod": 3, "rewardRatio": "9%", "rewardStatus": 1, "isMoreCommision": false, "hasOrder": true, "hasMoneyBack": true, "jobTypeText": null, "fastFeedback": false, "staffPhoto": "http://static2.risfond.com/photos/16c5f8a471ee48908b975dfc641bd88d.jpg?x-oss-process=image/resize,m_fixed,h_92,w_92", "staffId": 26356, "staffLevel": "5", "salaryText": "50-80万", "educationLevel": "本科以上", "yearsExperience": "10年", "numberOfHiring": 1, "reckonIncomeFrom": 9900.0, "reckonIncomeTo": 15840.0, "autoKillTime": "2024/9/5 18:49:39", "releaseTime": "2024/8/2 13:52:14", "deliveryRate": "0.00", "bottomText": null, "staffName": "马未未", "companyName": "廊坊公司", "wechatQrCode": "https://wework.qpic.cn/wwpic/653949_xfh2RcbKTSS40-I_1681992281/0", "isCanCooper": true, "isCooperated": false, "isLimit": false, "limitWorkYear": 0, "limitOrderCount": 0, "limitPerformance": 0, "isFollow": false, "isDeliverCenter": false}, {"isJoin": false, "isLike": false, "jiaruxiangmuCount": 0, "tuijianGeiHekuQuery": 0, "yuyuemianshiCount": 0, "renxuanlizhiCount": 0, "keHuQueRenCount": 0, "allCount": 2, "jiaruxiangmuTabCount": 0, "tuijiangeiguwenTabCount": 0, "tuijiangeiguwenCount": 0, "tuijiangeikehuTabCount": 0, "tuijiangeikehuCount": 3, "yuyuemianshiTabCount": 0, "kehumianshiTabCount": 0, "kehumianshiCount": 0, "yifaOfferTabCount": 0, "yifaOfferCount": 0, "yijieOfferTabCount": 0, "yijieOfferCount": 0, "ruzhiTabCount": 0, "ruzhiCount": 0, "keHuQueRenTabCount": 0, "renxuanlizhiTagCount": 0, "renxuanFangqiCount": 0, "renxuanFangqiTagCount": 0, "score": null, "esScore": 0.0, "pubilcText": null, "keHuFouJueCount": 0, "keHuFouJueTagCount": 0, "paymentCycle": "8.1", "hasAttention": false, "jobId": 419533, "title": "集团人资总经理", "publicTime": "2023-10-31 18:52", "lastUpdated": "2023-11-07 13:43", "publicTimeMonthDay": "11月7日", "clientName": "江苏知名食品公司", "clientLocationId": null, "clientLocation": "宿迁", "jobLocationId": null, "jobLocation": "北京", "clietnIndustryId": null, "clietnIndustry": "食品饮料", "clientLevel": 2, "shortWarrantyPeriod": false, "warrantyPeriod": 3, "rewardRatio": "8%", "rewardStatus": 1, "isMoreCommision": false, "hasOrder": true, "hasMoneyBack": true, "jobTypeText": null, "fastFeedback": false, "staffPhoto": "http://static2.risfond.com/photos/0a6fd620c89d4005bfb3f10b5403c15a.jpg?x-oss-process=image/resize,m_fixed,h_92,w_92", "staffId": 34052, "staffLevel": "4", "salaryText": "150-300万", "educationLevel": "本科以上", "yearsExperience": "10年", "numberOfHiring": 1, "reckonIncomeFrom": 24000.0, "reckonIncomeTo": 48000.0, "autoKillTime": null, "releaseTime": "2023/10/31 18:52:21", "deliveryRate": "0.00", "bottomText": null, "staffName": "方媛", "companyName": "合肥二分", "wechatQrCode": "https://wework.qpic.cn/wwpic/963892_FTu37gZTSdCFoum_1681993043/0", "isCanCooper": true, "isCooperated": false, "isLimit": false, "limitWorkYear": 0, "limitOrderCount": 0, "limitPerformance": 0, "isFollow": false, "isDeliverCenter": false}, {"isJoin": false, "isLike": false, "jiaruxiangmuCount": 0, "tuijianGeiHekuQuery": 0, "yuyuemianshiCount": 0, "renxuanlizhiCount": 0, "keHuQueRenCount": 0, "allCount": 0, "jiaruxiangmuTabCount": 0, "tuijiangeiguwenTabCount": 0, "tuijiangeiguwenCount": 0, "tuijiangeikehuTabCount": 0, "tuijiangeikehuCount": 2, "yuyuemianshiTabCount": 0, "kehumianshiTabCount": 0, "kehumianshiCount": 0, "yifaOfferTabCount": 0, "yifaOfferCount": 0, "yijieOfferTabCount": 0, "yijieOfferCount": 0, "ruzhiTabCount": 0, "ruzhiCount": 0, "keHuQueRenTabCount": 0, "renxuanlizhiTagCount": 0, "renxuanFangqiCount": 0, "renxuanFangqiTagCount": 0, "score": null, "esScore": 0.0, "pubilcText": null, "keHuFouJueCount": 0, "keHuFouJueTagCount": 0, "paymentCycle": "25.3", "hasAttention": false, "jobId": 411672, "title": "总经理", "publicTime": "2023-08-15 13:46", "lastUpdated": "2023-08-16 11:34", "publicTimeMonthDay": "8月16日", "clientName": "某知名酒类平台", "clientLocationId": null, "clientLocation": "北京", "jobLocationId": null, "jobLocation": "北京", "clietnIndustryId": null, "clietnIndustry": "全部中介服务", "clientLevel": 0, "shortWarrantyPeriod": false, "warrantyPeriod": 3, "rewardRatio": "10%", "rewardStatus": 1, "isMoreCommision": true, "hasOrder": true, "hasMoneyBack": true, "jobTypeText": null, "fastFeedback": false, "staffPhoto": "http://static2.risfond.com/photos/86a7030bd27d4d74858619f03bf858a2.png?x-oss-process=image/resize,m_fixed,h_92,w_92", "staffId": 9799, "staffLevel": "5", "salaryText": "100-150万", "educationLevel": "大专以上", "yearsExperience": "5年", "numberOfHiring": 1, "reckonIncomeFrom": 20000.0, "reckonIncomeTo": 30000.0, "autoKillTime": null, "releaseTime": "2023/8/15 13:46:32", "deliveryRate": "0.00", "bottomText": null, "staffName": "郎林甜", "companyName": "杭州一分", "wechatQrCode": "https://wework.qpic.cn/wwpic/770710_T4J3R7nOSW63yF5_1681991447/0", "isCanCooper": true, "isCooperated": false, "isLimit": false, "limitWorkYear": 0, "limitOrderCount": 0, "limitPerformance": 0, "isFollow": false, "isDeliverCenter": false}, {"isJoin": false, "isLike": false, "jiaruxiangmuCount": 0, "tuijianGeiHekuQuery": 0, "yuyuemianshiCount": 4, "renxuanlizhiCount": 0, "keHuQueRenCount": 0, "allCount": 2, "jiaruxiangmuTabCount": 0, "tuijiangeiguwenTabCount": 0, "tuijiangeiguwenCount": 0, "tuijiangeikehuTabCount": 0, "tuijiangeikehuCount": 7, "yuyuemianshiTabCount": 0, "kehumianshiTabCount": 0, "kehumianshiCount": 0, "yifaOfferTabCount": 0, "yifaOfferCount": 0, "yijieOfferTabCount": 0, "yijieOfferCount": 0, "ruzhiTabCount": 0, "ruzhiCount": 0, "keHuQueRenTabCount": 0, "renxuanlizhiTagCount": 0, "renxuanFangqiCount": 0, "renxuanFangqiTagCount": 0, "score": null, "esScore": 0.0, "pubilcText": null, "keHuFouJueCount": 0, "keHuFouJueTagCount": 0, "paymentCycle": "8.1", "hasAttention": false, "jobId": 395247, "title": "战略规划本部总经理", "publicTime": "2023-06-19 11:35", "lastUpdated": "2023-07-06 18:39", "publicTimeMonthDay": "7月6日", "clientName": "江苏知名食品公司", "clientLocationId": null, "clientLocation": "宿迁", "jobLocationId": null, "jobLocation": "北京", "clietnIndustryId": null, "clietnIndustry": "食品饮料", "clientLevel": 2, "shortWarrantyPeriod": false, "warrantyPeriod": 3, "rewardRatio": "10%", "rewardStatus": 3, "isMoreCommision": true, "hasOrder": true, "hasMoneyBack": true, "jobTypeText": null, "fastFeedback": false, "staffPhoto": "http://static2.risfond.com/photos/0a6fd620c89d4005bfb3f10b5403c15a.jpg?x-oss-process=image/resize,m_fixed,h_92,w_92", "staffId": 34052, "staffLevel": "4", "salaryText": "80-150万", "educationLevel": "不限", "yearsExperience": "15年", "numberOfHiring": 1, "reckonIncomeFrom": 16000.0, "reckonIncomeTo": 30000.0, "autoKillTime": null, "releaseTime": "2023/6/19 11:35:25", "deliveryRate": "0.00", "bottomText": null, "staffName": "方媛", "companyName": "合肥二分", "wechatQrCode": "https://wework.qpic.cn/wwpic/963892_FTu37gZTSdCFoum_1681993043/0", "isCanCooper": true, "isCooperated": false, "isLimit": false, "limitWorkYear": 0, "limitOrderCount": 0, "limitPerformance": 0, "isFollow": false, "isDeliverCenter": false}], "success": true, "message": null, "statusCode": 0}