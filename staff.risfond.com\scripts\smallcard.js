var newCardId = ""
var layout_SiteReadyNew = function () {
  function layout_ShowStaffNewCard(staffMobileNum, staffTelNum) {
    $(document).on("click", ".showCard-item", function (event) {
      if (!this.dataset.id || this.dataset.id == 0) return
      var staffId = this.dataset.id, isclick = $(this).data("isclick");
      resumeApp.staffId = staffId
      $("#NewCard").data("model", null).modal('show');
      newCardId = staffId
      resumeApp.newCardDialog = true
      resumeApp.GetStaffInfo()

      //resumeApp.successAnli()
      })

  }

  function layout_ShowStaffAddTeam(staffMobileNum, staffTelNum) {
    $(document).on("click", ".showTeam-item", function (event) {
      $("#addTeamShow").modal('show');
      addTeamApp.projectPosition()
      var staffId = this.dataset.id, isclick = $(this).data("isclick");
      resumeApp.staffId = staffId
      addTeamApp.teamStaffId = staffId

      addTeamApp.addTeamShowIndustry = new r_AdvancedSelect({
        title: "选择行业",
        initType: "interface",
        requestOpts: { url: "/RnssIndustry/GetIndustry", requestType: "get", responseDataStructure: "tree" },
        showmeassage: function (txt) {
          beautAlert.done(setRnssLanguage(txt), "hits");
        }
      })


      // 选择员工
      $("#selTeam5").completeTeam({
        type: 0,
        userKey: "newteam",
        maxCount: 1,
        width: 200,
        append: $("#selTeam5"),
        cancelcallback: function (obj) {
          addTeamApp.addQueryConditionData.TargetStaffs = [];
          // 根据返回的员工id显示相应的名称
          $.each($("#selTeam5").find("li"), function () {
            var input = $(this).find("input"), span = $(this).find("span");
            addTeamApp.addQueryConditionData.TargetStaffs.push({
              StaffId: input.val(),
              AuthorityType: 1
            })

          });
        },
        selectcallback: function (id) {
          addTeamApp.addQueryConditionData.TargetStaffs = [];
          $.each($("#selTeam5").find("li"), function () {
            var input = $(this).find("input"), span = $(this).find("span");
            //data.push({ "StaffId": input.val(), "StaffName": span.text(), "StaffKey": input.attr("name") });
            addTeamApp.addQueryConditionData.TargetStaffs.push({
              StaffId: input.val(),
              AuthorityType: 1
            })
          });
        }
      });
      $("#selTeam5").completeTeamEmpty();

      $("#selTeam5").completeTeamSetValue(resumeApp.staffId);
      //newCardId = staffId
      //resumeApp.staffId = staffId
      //resumeApp.newCardDialog = true
      //resumeApp.GetStaffInfo()

    })
  }

  return {
    init: function (_staffId, _staffTelNum, _companyId, _stafflist_el, _stafflist_panel, _staffMobileNum) {
      staffId = _staffId;
      staffTelNum = _staffTelNum;
      companyId = _companyId;
      stafflist_el = _stafflist_el;
      stafflist_panel = _stafflist_panel;
      staffMobileNum = _staffMobileNum;
      layout_ShowStaffNewCard(staffMobileNum, staffTelNum);
      layout_ShowStaffAddTeam(staffMobileNum, staffTelNum)
    },
  };
}();

var resumeApp = new Vue({
  el: "#NewCard",
  data: {
    tableDataa: "",
    tableDataId: "",
    newCardDialog: false,
    activeName: 'first',
    tableData: [],
    staffId: staffIdp,
    basic: {},
    dataggg: {},
    IndustryData: [],
    OccupationData: [],
    LocationData: [],
    SalaryData: [],
    AnliTablData: [],
    anliPageIndex: 1,
    anliPageSize: 10,
    anliPageTotal: 10,
    //职位需求
    ClientNatureData: [],
    ClientLevelData: [],
    jobTable: [],
    jobPageIndex: 1,
    jobPageSize: 10,
    jobTotal: 0,
    //人选资源
    peoplePageIndex: 1,
    peoplePageSize: 10,
    peopleTotal: 0,
    peopleData: [],
    colors: [],
    jobTagsData: [],
    resumeTagsData: [],
    echartsColor: ["#008DFF", "#FF7D56", "#0FB83B", "#FFE324", "#FF9756", "#7C6DFB", "#FF800A", "#FFCD24", "#377CFF", "#F05654", "#3EBF00", "#FFF224", "#379CFF", "#B56DFB", "#FB6D9D", "#00AAEA", "#FFB61E", "#00BF6F", "#FB6D6D", "#4437FF", "#00CFEA", "#FF6D56", "#3748FF", "#FFE128", "#0FB83B", "#936DFB", "#002EFF", "#FFC64B", "#D76DFB", "#3EFF84"],
    echartsNoData: true,
    echartsNoData2: true,
    NewCardLoading: false,
  },
  created() {

  },
  mounted() {
    var _this = this

  },

  methods: {
    jumpZhuye(val) {
      window.open("/staff/PersonalHomepage?id=" + val, '_blank')
    },
    cardJobClick(val) {
      window.open("/apps/viewjob2.aspx?id=" + val.jobId)

    },
    cardJobClick1(val) {
      window.open("/apps/viewjob2.aspx?id=" + val.ResumeId)
    },
    cardNameClick(val) {
      window.open("/resume/viewresume?id=" + val.ResumeId)
    },
    closeNewCard() {
      $("#NewCard").modal("hide");
      var _this = this
      _this.activeName = 'first'
      $(".next-phone").hide();
      $(".next-phone").next().show();
    },
    handleClick(tab, event) {
      if (tab.label == "成功案例") {
        this.successAnli()
        this.successAnliTable()
      } else if (tab.label == "职位需求") {
        this.jobTags()
        this.GetMyJobData()
        this.successJob()
      } else if (tab.label == "人选资源") {
        this.resumeTags()
        this.GetMyResumeData()
      }
    },
    GetStaffInfo() {
      var _this = this;
      _this.NewCardLoading = true
      $.ajax({
        type: "post",
        url: "/cooperation/GetStaffMainInfo?staffId=" + _this.staffId,
        data: {},
        dataType: "json",
        success: function (res) {
          if (res.success) {
            _this.basic = res.data
            _this.dataggg = res.data

            $(".down-name").html(_this.dataggg.staffName)
            $(".down-enName").html("(" + _this.dataggg.enName + ")")
            $(".newCard-position").html(_this.dataggg.positionName + " |")
            $(".newCard-company").html(" " + _this.dataggg.companyName)
            $(".span-mobilePhone").html(_this.dataggg.mobilePhone)
            $(".span-email").html(_this.dataggg.email)
            $(".v-level").html(_this.dataggg.businessLevel)
            $(".fankui").html(_this.dataggg.recommendInterviewDay)
            $(".offerLv").html(Number(_this.dataggg.recommendOffer).toFixed(2))
            var bbb = _this.dataggg.pictureUrl.split("?x-oss")[0]
            $(".imgclass").attr('src', bbb)
            $(".download-img").attr('src', _this.dataggg.weComQrCode)
            _this.NewCardLoading = false
          }
        }
      });
    },


    getBase642(imgUrl) {
      window.URL = window.URL || window.webkitURL;
      var xhr = new XMLHttpRequest();
      xhr.open("get", imgUrl, true);
      // 至关重要
      xhr.responseType = "blob";
      xhr.onload = function () {
        if (this.status == 200) {
          //得到一个blob对象
          var blob = this.response;
          // 至关重要
          let oFileReader = new FileReader();
          oFileReader.onloadend = function (e) {
            // 此处拿到的已经是 base64的图片了
            let base64 = e.target.result;
            $(".imgclass").attr('src', base64)
            //$(".download-img").attr('src', base64)
          };
          oFileReader.readAsDataURL(blob);
          //====为了在页面显示图片，可以删除====
          var img = document.createElement("img");
          img.onload = function (e) {
            window.URL.revokeObjectURL(img.src); // 清除释放
          };
          let src = window.URL.createObjectURL(blob);
          img.src = src
          //document.getElementById("container1").appendChild(img);
          //====为了在页面显示图片，可以删除====

        }
      }
      xhr.send();
    },
    getBase64(imgUrl) {
      window.URL = window.URL || window.webkitURL;
      var xhr = new XMLHttpRequest();
      xhr.open("get", imgUrl, true);
      xhr.responseType = "blob";
      xhr.onload = function () {
        if (this.status == 200) {
          //得到一个blob对象
          var blob = this.response;
          // 至关重要
          let oFileReader = new FileReader();
          oFileReader.onloadend = function (e) {
            // 此处拿到的已经是base64的图片了,可以赋值做相应的处理
          }
          oFileReader.readAsDataURL(blob);
        }
      }
      xhr.send();
    },



    //成功案例Staff/SearchSuccessDistribution
    successAnli() {
      var _this = this;
      _this.SalaryData=[]
      $.post("/Staff/SearchSuccessDistribution", { staffId: _this.staffId }, function (r) {
        if (r.Success) {

          if (r.Data.IndustryDistribution.length > 0 || r.Data.LocationDistribution.length > 0 || r.Data.OccupationDistribution.length > 0) {
            _this.IndustryData = r.Data.IndustryDistribution
            _this.OccupationData = r.Data.OccupationDistribution
            _this.LocationData = r.Data.LocationDistribution
            //_this.SalaryData = r.Data.SalaryDistribution
            r.Data.SalaryDistribution.forEach(e => {
              if (e.Count != 0) {
                const item = {
                  Count: e.Count,
                  Key: e.Key
                }
                _this.SalaryData.push(item)
              }
            })

            _this.renderChartBar01()
            _this.renderChartBar02()
            _this.renderChartBar03()
            _this.renderChartBar04()
            _this.downloadChartBar01()
            _this.downloadChartBar02()
            _this.downloadChartBar03()
            _this.downloadChartBar04()
            _this.echartsNoData = true
          } else {
            _this.echartsNoData = false
          }


        } else {
          r_Layout_BeautAlert.done(r.Message, "hits", 1600);
        }

      }).error(function (err) {
        r_Layout_BeautAlert.done("请求失败", "hits", 1600);
      })
    },
    successAnliTable() {
      var _this = this;
      var data = {
        staffId: _this.staffId,
        companyId: 0,
        months: "",
        keyword: "",
        pageIndex: _this.anliPageIndex,
        pageSize: _this.anliPageSizee,
      }
      $.post("/Cooperation/SearchSuccessCase", data, function (r) {
        if (r.success) {
          _this.AnliTablData = r.data
          _this.anliPageTotal = r.pageCount
        } else {
          r_Layout_BeautAlert.done(r.Message, "hits", 1600);
        }

      }).error(function (err) {
        r_Layout_BeautAlert.done("请求失败", "hits", 1600);
      })
    },
    anliPageChange(val) {
      this.anliPageIndex = val
      this.successAnliTable()
    },



    //需求职位Staff/GetMyJobData
    GetMyJobData() {
      var _this = this;
      var data = {
        staffId: _this.staffId,
        companyId: "",
        keyword: "",
        months: "",
        pageIndex: _this.jobPageIndex,
        pageSize: _this.jobPageSize
      }
      $.post("/Staff/GetMyJobData", data, function (r) {
        if (r.Status) {
          _this.jobTable = r.Data
          _this.jobTotal = r.Total
        } else {
          r_Layout_BeautAlert.done(r.Message, "hits", 1600);
        }

      }).error(function (err) {
        r_Layout_BeautAlert.done("请求失败", "hits", 1600);
      })
    },
    jobPageChange(val) {
      this.jobPageIndex = val
      this.GetMyJobData()
    },
    //列表分页
    peoplePageChange(val) {
      this.peoplePageIndex = val
      this.GetMyResumeData()
    },
    jobTags() {
      var _this = this
      _this.jobTagsData = []
      $.get("/Job/SearchJobSignal", {}, function (r) {
        _this.jobTagsData = r.Data.JobResourcelist
      }).error(function (er) {

      })
    },
    resumeTags() {
      var _this = this
      $.get("/Resume/SearchResumeSignal", {}, function (r) {
        _this.resumeTagsData = r.Data.ResumeResourcelist
      }).error(function (er) {
      })
    },
    successJob() {
      var _this = this;
      $.post("/Staff/GetMyJobDataDistribution", { staffId: _this.staffId }, function (r) {
        if (r.Success) {
          if (r.Data.ClientNatureDistribution.length > 0 || r.Data.ClientLevelDistribution.length > 0) {
            _this.echartsNoData2 = true
            _this.ClientNatureData = r.Data.ClientNatureDistribution //性质
            _this.ClientLevelData = r.Data.ClientLevelDistribution //等级
            _this.renderChartBar05()
            _this.renderChartBar06()
          } else {
            _this.echartsNoData2 = false
          }
        } else {
          r_Layout_BeautAlert.done(r.Message, "hits", 1600);
        }

      }).error(function (err) {
        r_Layout_BeautAlert.done("请求失败", "hits", 1600);
      })
    },

    //人选需求Staff/GetMyResumeData
    GetMyResumeData() {
      var _this = this;
      var data = {
        StaffId: _this.staffId,
        CreaterStaffName: "",
        Page: _this.peoplePageIndex,
        Size: _this.peoplePageSize,
        Companies: [],
        DesiredIndustries: [],
        DesiredSalaryFrom: null,
        DesiredSalaryTo: null,
        Desiredlocations: [],
        Desiredoccupations: [],
        EduSchool: "",
        EduStudyField: "",
        EducationLevelFrom: null,
        EducationLevelTo: null,
        EndCreaterdTime: "",
        ExcludeResumeIds: [],
        ExpCompanyFilter: null,
        ExpCompanyFilter_ExpEnum: false,
        ExpCompanyFilter_ExpEnumTxt: "",
        ExpCompanyFilter_KeyWords: "",
        ExpDescriptionFilter: null,
        ExpDescriptionFilter_ExpEnum: false,
        ExpDescriptionFilter_ExpEnumTxt: "",
        ExpDescriptionFilter_KeyWords: "",
        ExpJobTitleFilter: null,
        ExpJobTitleFilter_ExpEnum: false,
        ExpJobTitleFilter_ExpEnumTxt: "",
        ExpJobTitleFilter_KeyWords: "",
        Gender: null,
        IncludeCreaterStaffIds: [],
        IncludeResumeIds: [],
        Industries: [],
        KeyUniversity: 0,
        KeyWord: "",
        KeyWords: [],
        Languages: [],
        LastUpdatedTimeFrom: "2020-03-24",
        LastUpdatedTimeText: "最近三年",
        LastUpdatedTimeTo: "2023-03-24",
        LiveLocation: [],
        MatchAnyKeyword: false,
        MatchAnyKeywordTxt: "全部匹配",
        MatchAnyKeyword_Enum: "",
        NativeLocationId: [],
        NotResumeIds: [],
        OpenCompanyId: "",
        OrderBy: null,
        PhotoCompleted: null,
        ResumeId: "",
        ResumeTuijianStatus: null,
        Source: null,
        StaffName: null,
        StartCreaterdTime: "",
        TuijianStatus: null,
        WorkStatus: "",
        YearFrom: "",
        YearTo: "",
      }
      $.post("/staff/GetMyResumeData", data, function (r) {
        if (r.Status) {
          _this.peopleData = r.Results
          _this.peopleTotal = r.TotalCount

        }

      }).error(function (err) {
        r_Layout_BeautAlert.done("请求失败", "hits", 1600);
      })
    },
    //列表分页
    peoplePageChange(val) {
      this.peoplePageIndex = val
      this.GetMyResumeData()
    },

    //人选需求



    //echarts图表

    renderChartBar01() {
      var _this = this
      var arr = [],
        arr1 = [];
      _this.IndustryData.forEach(e => {
        arr.push(e.Key)
        const item = {
          value: e.Count,
          name: e.Key
        }
        arr1.push(item)
      })
      var myChart = echarts.init(document.getElementById("newCardEcharts"));
      myChart.setOption(
        {
          title: {
            text: '交付行业分布',
            textStyle: {
              fontWeight: 'normal',
              fontSize: 14
            },
            left: "0",
            top: "-5",
            subtext: '',
            x: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: "{b} : {c}",
            backgroundColor: '#fff',  // 修改背景颜色
            extraCssText: 'background-color:#FBFBFB;color:#333',
            borderRadius: '4px',
            boxShadow: '0px 2px 8px 0px rgba(0,0,0,0.5)'
          },
          legend: {
            show: false,
            x: 'center',
            y: 'bottom',
            data: arr
          },
          toolbox: {
          },
          label: {
            normal: {
              show: true,
              formatter: "{b}"
            }
          },
          calculable: true,
          color: _this.echartsColor,
          series: [
            {
              name: '',
              type: 'pie',
              radius: [30, 50],
              center: ['50%', '50%'],
              //roseType : 'area',
              itemStyle: {
                borderColor: '#fff', // 白边
                borderWidth: 1
              },
              emphasis: { // 高亮item的样式
                disabled: false
              },
              label: {
                normal: {
                  show: true,
                  color: "#333",
                  fontSize: 10,
                  position: 'outside',
                  borderWidth: 1,
                  padding: [-15, -15, -15, -15],
                  borderRadius: 4
                }




              },
              labelLine: {
                normal: {
                  show: false
                }
              },
              data: arr1,
            }
          ]
        }
      );

    },
    renderChartBar02() {
      var _this = this
      var arr = [],
        arr1 = [];
      _this.OccupationData.forEach(e => {
        arr.push(e.Key)
        const item = {
          value: e.Count,
          name: e.Key
        }
        arr1.push(item)
      })
      var myChart = echarts.init(document.getElementById("newCardEcharts2"));
      myChart.setOption(
        {
          title: {
            text: '交付职类分布',
            textStyle: {
              fontWeight: 'normal',
              fontSize: 14
            },
            left: "0",
            top: "-5",
            subtext: '',
            x: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: "{b} : {c}",
            backgroundColor: '#fff',  // 修改背景颜色
            extraCssText: 'background-color:#FBFBFB;color:#333',
            borderRadius: '4px',
            boxShadow: '0px 2px 8px 0px rgba(0,0,0,0.5)'
          },
          legend: {
            show: false,
            x: 'center',
            y: 'bottom',
            data: arr
          },
          toolbox: {
          },
          label: {
            normal: {
              show: true,
              formatter: "{b}"
            }
          },
          calculable: true,
          color: _this.echartsColor,
          series: [
            {
              name: '',
              type: 'pie',
              radius: [30, 50],
              center: ['50%', '50%'],
              //roseType : 'area',
              itemStyle: {
                borderColor: '#fff', // 白边
                borderWidth: 1
              },
              emphasis: { // 高亮item的样式
                disabled: false
              },
              label: {
                normal: {
                  show: true,
                  color: "#333",
                  fontSize: 10,
                  position: 'outside',
                  borderWidth: 1,
                  padding: [-15, -15, -15, -15],
                  borderRadius: 4
                }
              },
              labelLine: {
                normal: {
                  show: false
                }
              },
              data: arr1,
            }
          ]
        }
      );

    },
    renderChartBar03() {
      var _this = this
      var arr = [],
        arr1 = [];

      _this.LocationData.forEach(e => {
        arr.push(e.Key)
        const item = {
          value: e.Count,
          name: e.Key
        }
        arr1.push(item)
      })
      var myChart = echarts.init(document.getElementById("newCardEcharts3"));
      myChart.setOption(
        {
          title: {
            text: '交付地区分布',
            top: '15',
            textStyle: {
              fontWeight: 'normal',
              fontSize: 14
            },
            left: "0",
            subtext: '',
            x: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: "{b} : {c}",
            backgroundColor: '#fff',  // 修改背景颜色
            extraCssText: 'background-color:#FBFBFB;color:#333',
            borderRadius: '4px',
            boxShadow: '0px 2px 8px 0px rgba(0,0,0,0.5)'
          },
          legend: {
            show: false,
            x: 'center',
            y: 'bottom',
            data: arr
          },
          toolbox: {
          },
          label: {
            normal: {
              show: false,
              formatter: "{b}"
            }
          },
          calculable: true,
          color: _this.echartsColor,
          series: [
            {
              name: '',
              type: 'pie',
              radius: [30, 50],
              center: ['50%', '60%'],
              //roseType : 'area',
              itemStyle: {
                borderColor: '#fff', // 白边
                borderWidth: 1
              },
              emphasis: { // 高亮item的样式
                disabled: false
              },
              label: {
                normal: {
                  show: true,
                  color: "#333",
                  fontSize: 10,
                  position: 'outside',
                  borderWidth: 1,
                  padding: [-20, 10, 10, -10],
                  borderRadius: 4
                }
              },
              labelLine: {
                normal: {
                  show: false
                }
              },
              data: arr1,
            }
          ]
        }
      );

    },
    renderChartBar04() {
      var _this = this
      var arr = [],
        arr1 = [];
      _this.SalaryData.forEach(e => {
        arr.push(e.Key)
        const item = {
          value: e.Count,
          name: e.Key
        }
        arr1.push(item)
      })
      var myChart = echarts.init(document.getElementById("newCardEcharts4"));
      myChart.setOption(
        {
          title: {
            text: '交付薪资区间分布',
            textStyle: {
              fontWeight: 'normal',
              fontSize: 14
            },
            top: '15',
            left: "0",
            subtext: '',
            x: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: "{b} : {c}",
            backgroundColor: '#fff',  // 修改背景颜色
            extraCssText: 'background-color:#FBFBFB;color:#333',
            borderRadius: '4px',
            boxShadow: '0px 2px 8px 0px rgba(0,0,0,0.5)'
          },
          legend: {
            show: false,
            x: 'center',
            y: 'bottom',
            data: arr
          },
          toolbox: {
          },
          label: {
            normal: {
              show: true,
              formatter: "{b}"
            }
          },
          calculable: true,
          color: _this.echartsColor,
          series: [
            {
              name: '',
              type: 'pie',
              radius: [30, 50],
              center: ['50%', '60%'],
              //roseType : 'area',
              itemStyle: {
                borderColor: '#fff', // 白边
                borderWidth: 1
              },
              emphasis: { // 高亮item的样式
                disabled: false
              },
              label: {
                normal: {
                  show: true,
                  color: "#333",
                  fontSize: 10,
                  position: 'outside',
                  borderWidth: 1,
                  padding: [20, 5, 15,-20],
                  borderRadius: 4
                }
              },
              labelLine: {
                normal: {
                  show: false
                }
              },
              data: arr1,
            }
          ]
        }
      );

    },
    renderChartBar05() {
      var _this = this
      var arr = [],
        arr1 = [];
      _this.ClientNatureData.forEach(e => {
        arr.push(e.Key)
        const item = {
          value: e.Count,
          name: e.Key
        }
        arr1.push(item)
      })
      var myChart = echarts.init(document.getElementById("newCardEcharts5"));
      myChart.setOption(
        {
          title: {
            text: '需求来源客户性质分类',
            textStyle: {
              fontWeight: 'normal',
              fontSize: 14
            },
            left: "0",
            subtext: '',
            x: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: "{b} : {c}",
            backgroundColor: '#fff',  // 修改背景颜色
            extraCssText: 'background-color:#FBFBFB;color:#333',
            borderRadius: '4px',
            boxShadow: '0px 2px 8px 0px rgba(0,0,0,0.5)'
          },
          legend: {
            show: false,
            x: 'center',
            y: 'bottom',
            data: arr
          },
          toolbox: {
          },
          label: {
            normal: {
              show: true,
              formatter: "{b} \n{d}%"
            }
          },
          calculable: true,
          color: _this.echartsColor,
          series: [
            {
              name: '',
              type: 'pie',
              radius: [35, 60],
              center: ['55%', '60%'],
              //roseType : 'area',
              data: arr1,
              itemStyle: {
                borderColor: '#fff', // 白边
                borderWidth: 1
              },
              emphasis: { // 高亮item的样式
                disabled: false
              },
              label: {
                normal: {
                  show: true,
                  color: "#333",
                  fontSize: 10,
                  padding: [-20, -25, -20, -25],
                }
              },
              labelLine: {
                show: false
              },
            }
          ]
        }
      );

    },
    renderChartBar06() {
      var _this = this
      var arr = [],
        arr1 = [];
      _this.ClientLevelData.forEach(e => {
        arr.push(e.Key)
        const item = {
          value: e.Count,
          name: e.Key
        }
        arr1.push(item)
      })
      var myChart = echarts.init(document.getElementById("newCardEcharts6"));
      myChart.setOption(
        {
          title: {
            text: '需求来源客户等级分类',
            textStyle: {
              fontWeight: 'normal',
              fontSize: 14
            },
            left: "0",
            subtext: '',
            x: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: "{b} : {c}",
            backgroundColor: '#fff',  // 修改背景颜色
            extraCssText: 'background-color:#FBFBFB;color:#333',
            borderRadius: '4px',
            boxShadow: '0px 2px 8px 0px rgba(0,0,0,0.5)'
          },
          legend: {
            show: false,
            x: 'center',
            y: 'bottom',
            data: arr
          },
          toolbox: {
          },
          label: {
            normal: {
              show: true,
              formatter: "{b}"
            }
          },
          calculable: true,
          color: _this.echartsColor,
          series: [
            {
              name: '',
              type: 'pie',
              radius: [35, 60],
              center: ['50%', '60%'],
              //roseType : 'area',
              data: arr1,
              itemStyle: {
                borderColor: '#fff', // 白边
                borderWidth: 1
              },
              emphasis: { // 高亮item的样式
                disabled: false
              },
              label: {
                normal: {
                  show: true,
                  color: "#333",
                  fontSize: 10,
                  padding: [-20, -25, -20, -25],
                }
              },
              labelLine: {
                show: false
              },
            }
          ]
        }
      );

    },

    downloadChartBar01() {
      var _this = this
      var arr = [],
        arr1 = [];
      _this.IndustryData.forEach(e => {
        arr.push(e.Key)
        const item = {
          value: e.Count,
          name: e.Key
        }
        arr1.push(item)
      })
      var myChart = echarts.init(document.getElementById("downloadCardEcharts1"));
      myChart.setOption(
        {
          title: {
            text: '交付行业分布',
            textStyle: {
              fontWeight: 'normal',
              fontSize: 14
            },
            left: "0",
            top: '5',
            subtext: '',
            x: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: "{b} : {c}"
          },
          legend: {
            show: false,
            x: 'center',
            y: 'bottom',
            data: arr
          },
          toolbox: {
          },
          label: {
            normal: {
              show: true,
              formatter: "{b}"
            }
          },
          calculable: true,
          color: _this.echartsColor,
          series: [
            {
              name: '',
              type: 'pie',
              radius: [30, 55],
              center: ['45%', '57%'],
              //roseType : 'area',
              data: arr1,
              itemStyle: {
                borderColor: '#fff', // 白边
                borderWidth: 1
              },
              emphasis: { // 高亮item的样式
                disabled: false
              },
              label: {
                normal: {
                  formatter: '{b|{b}}{a|{d}%}\n\n',
                  borderWidth: 20,
                  borderRadius: 4,
                  padding: [0, -70],
                  rich: {
                    a: {
                      color: '#333',
                      fontSize: 12,
                      lineHeight: 20
                    },
                    b: {
                      fontSize: 12,
                      lineHeight: 20,
                      color: '#333'
                    }
                  }
                }
              },
              labelLine: {
                show: true,
                length: 10
              },
            }
          ]
        }
      );

    },
    downloadChartBar02() {
      var _this = this
      var arr = [],
        arr1 = [];
      _this.OccupationData.forEach(e => {
        arr.push(e.Key)
        const item = {
          value: e.Count,
          name: e.Key
        }
        arr1.push(item)
      })
      var myChart = echarts.init(document.getElementById("downloadCardEcharts2"));
      myChart.setOption(
        {
          title: {
            text: '交付职类分布',
            textStyle: {
              fontWeight: 'normal',
              fontSize: 14
            },
            left: "0",
            top: '5',
            subtext: '',
            x: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: "{b} : {c}"
          },
          legend: {
            show: false,
            x: 'center',
            y: 'bottom',
            data: arr
          },
          toolbox: {
          },
          label: {
            normal: {
              show: true,
              formatter: "{b}"
            }
          },
          calculable: true,
          color: _this.echartsColor,
          series: [
            {
              name: '',
              type: 'pie',
              radius: [30, 55],
              center: ['50%', '57%'],
              //roseType : 'area',
              data: arr1,
              itemStyle: {
                borderColor: '#fff', // 白边
                borderWidth: 1
              },
              emphasis: { // 高亮item的样式
                disabled: false
              },
              label: {
                normal: {
                  show: true,
                  color: "#333",
                  fontSize: 10,
                  padding: [-10, -15, -10, -15],
                }
              },
              labelLine: {
                show: true,
                length: 10
              },
            }
          ]
        }
      );

    },
    downloadChartBar03() {
      var _this = this
      var arr = [],
        arr1 = [];

      _this.LocationData.forEach(e => {
        arr.push(e.Key)
        const item = {
          value: e.Count,
          name: e.Key
        }
        arr1.push(item)
      })
      var myChart = echarts.init(document.getElementById("downloadCardEcharts3"));
      myChart.setOption(
        {
          title: {
            text: '交付地区分布',
            textStyle: {
              fontWeight: 'normal',
              fontSize: 14
            },
            left: "0",
            top: '10',
            subtext: '',
            x: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: "{b} : {c}"
          },
          legend: {
            show: false,
            x: 'center',
            y: 'bottom',
            data: arr
          },
          toolbox: {
          },
          label: {
            normal: {
              show: true,
              formatter: "{b}"
            }
          },
          calculable: true,
          color: _this.echartsColor,
          series: [
            {
              name: '',
              type: 'pie',
              radius: [30, 55],
              center: ['45%', '57%'],
              //roseType : 'area',
              data: arr1,
              itemStyle: {
                borderColor: '#fff', // 白边
                borderWidth: 1
              },
              emphasis: { // 高亮item的样式
                disabled: false
              },
              label: {
                normal: {
                  show: true,
                  color: "#333",
                  fontSize: 10,
                  padding: [-10, -15, -10, -15],
                }
              },
              labelLine: {
                show: true,
                length: 10
              },
            }
          ]
        }
      );

    },
    downloadChartBar04() {
      var _this = this
      var arr = [],
        arr1 = [];
      _this.SalaryData.forEach(e => {
        arr.push(e.Key)
        const item = {
          value: e.Count,
          name: e.Key
        }
        arr1.push(item)
      })
      var myChart = echarts.init(document.getElementById("downloadCardEcharts4"));
      myChart.setOption(
        {
          title: {
            text: '交付薪资区间分布',
            textStyle: {
              fontWeight: 'normal',
              fontSize: 14
            },
            left: "0",
            top: '10',
            subtext: '',
            x: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: "{b} : {c}"
          },
          legend: {
            show: false,
            x: 'center',
            y: 'bottom',
            data: arr
          },
          toolbox: {
          },
          label: {
            normal: {
              show: true,
              formatter: "{b}"
            }
          },
          calculable: true,
          color: _this.echartsColor,
          series: [
            {
              name: '',
              type: 'pie',
              radius: [30, 55],
              center: ['45%', '57%'],
              //roseType : 'area',
              data: arr1,
              itemStyle: {
                borderColor: '#fff', // 白边
                borderWidth: 1
              },
              emphasis: { // 高亮item的样式
                disabled: false
              },
              label: {
                normal: {
                  show: true,
                  color: "#333",
                  fontSize: 10,
                  padding: [-10, -15, -10, -15],
                }
              },
              labelLine: {
                show: true,
                length: 10
              },
            }
          ]
        }
      );

    },
    //沟通记录
    setMouseOut() {
      var _this = this
      var data = {
        count: 10,
        resumeid: _this.tableDataId
      }
      $.post("/resume/ResumeActionHandler.ashx?action=getcommunications2", data, function (r) {
        _this.tableDataa = r.Data
      }).error(function (er) {
      })


    },

  },



});


//添加团队
var addTeamApp = new Vue({
  el: "#addTeamShow",
  data: {
    addTeamShowIndustry: null,
    AddOccupations: [],
    addQueryConditionData: {
      Name: '',
      ConditionJson: '',
      TargetStaffs: [], // 被加入者者
      teamStaffId: "",
      teamJobIds: [],
    },
    projectVlu: "",
    projectSelect: [],
    projectStaffId: staffIdp,
    options: [],
    value: "",
  },

  mounted() {

  },

  methods: {
    projectPosition() {
      var _this = this
      $.post("/Staff/GetMyJobDataAll", { StaffId: staffIdp }, function (r) {
        _this.projectSelect = r.Data
        if (r.Success) {
        }
      }).error(function (er) {

      })
    },
    projectClick() {
      var _this = this
      console.log(_this.projectVlu)
    },

    resumeTags() {
      var _this = this
      var data = {
        KeyWord: "",
        PageSize: 9999,
        PageIndex: 1
      }
      $.post("/Staff/GetSelectStaffs", data, function (r) {
      }).error(function (er) {
      })
    },

    addTeamClick() {
      var _this = this

      if (_this.projectVlu == 0) {
        r_Layout_BeautAlert.done(setRnssLanguage('请选择一个职位'), 'hits');
        return;
      }


      var data = {
        jobIds: _this.projectVlu,
        staffId: _this.teamStaffId,
      }
      $.post("/Job/SetJobStaff", data, function (r) {
        if (r.Success) {
          _this.projectVlu = ""
          _this.projectSelect = []
          $("#addTeamShow").modal('hide');
          r_Layout_BeautAlert.done(setRnssLanguage('添加成功'), 'hits');
        }
      }).error(function (er) {

      })
    },

    closeAddTeam() {
      var _this = this
      _this.AddOccupations = []
      $("#addTeamShow").modal('hide');
    },
    editAbility: function (type) {
      if (type == 'occupation') {
        this.helpSelect1(new r_AdvancedSelect({
          title: "选择职位",
          data: data_occupation3,
          showmeassage: function (txt) {
            beautAlert.done(setRnssLanguage(txt), "hits");
          }
        }), 1, {
          targetObj: 'goodOccupations'
        });
      }
    },
    ajaxSetStaffAbilityData: function (type, selected, successCallback) {
      var _this = this;
      var data = {};
      data[type] = [];
      var url;
      if (type == 'goodOccupations') {
        _this.AddOccupations = []
        selected.forEach(function (elem) {
          const item = {
            occupationsId: elem.id,
            occupationsName: elem.text,
          }
          _this.AddOccupations.push(item)
        });
      }
      successCallback && successCallback();
    },

    helpSelect1: function (obj, num, o) {
      var $this = this;
      var sd = o.targetObj;
      obj.setCheckedNum(num);
      obj.setSureCallBack(function (result) {
        var list = result.map(function (elem) {
          return {
            id: elem.Id,
            text: elem.Text,
          }
        });
        var resThis = this;

        $this.ajaxSetStaffAbilityData(o.targetObj, list, function () {
          $this.AddOccupations[o.targetObj] = list;
          resThis.hidden();
        });

      });

      //if (sd.length > 0) {
      //  obj.select(sd.map(function (elem) {
      //    return elem.id;
      //  }));
      //}

      obj.show();
    },




  }


})









