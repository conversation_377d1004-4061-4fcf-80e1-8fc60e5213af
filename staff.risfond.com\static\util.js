/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
/**
 * 功能：公用函数
 * 用途：弹出框逻辑处理 时间格式转换 全角转半角
 * 依赖：[jquery.js]
 */
/* 帮助函数，此项目的所有代码应该基于它使用*/
if (!Function.prototype.bind) { Function.prototype.bind = function () { var self = this, args = [].slice.call(arguments), target = args.shift(); return function () { self.apply(target, args.concat([].slice.call(arguments))); } }; }

/*jQuery selector cache or cache or delete status*/
var $j = (function () {
	var cache = {};
	return function (key) {
		return cache[key] ? cache[key] : cache[key] = $(key);
	}
})(jQuery);
var $m = (function () {
	var cache = {};
	return function (key, value) {
		if (typeof value == "undefined") {
			return cache[key];
		} else {
			if (value) {
				return cache[key] = value;
			} else {
				delete cache[key];
			}
		}
	}
})(jQuery);

//弹出层默认居中
function centerEl(el, divTop) {
	el = typeof el == "string" ? $("#" + el) : el;
	el.hide();
	$(window).resize(function () { position(); });
	function position() {
		var left, top;
		var w = window.outerWidth || document.documentElement.clientWidth;
		var h = window.outerHeight || document.documentElement.clientHeight;
		left = ($(window).width() - el.width()) / 2;
		top = Math.abs(($(window).height() - el.outerHeight()) / 2) + document.documentElement.scrollTop || 0;
		if (top < 75) {
		  top = 75;
		}
		el.css({ left: left, top: top, width: el.width() });
	}
	position();
	el.show();
	showOverlay();
}

//弹出层默认居中
function centerEl2(el, divTop) {
	el = typeof el == "string" ? $("#" + el) : el;
	el.hide();
	$(window).resize(function () { position(); });
	function position() {
		var left, top;
		var w = window.outerWidth || document.documentElement.clientWidth;
		var h = window.outerHeight || document.documentElement.clientHeight;
		left = ($(window).width() - el.width()) / 2;
		//top = Math.abs(($(window).height() - el.outerHeight()) / 2) + document.body.scrollTop || 0;
		top = Math.abs(($(window).height() - el.outerHeight()) / 2) + $(document).scrollTop() || 0;
		if (top < 75) {
		  top = 75;
		}
		el.css({ left: left, top: top, width: el.width() });
	}
	position();
	el.show();
	showOverlay();
}
function centerEl3(el, panel) {
  el = typeof el == "string" ? $("#" + el) : el;
  el.hide();
  $(window).resize(function () { position(); });
  function position() {
    var left, top;
    var w = window.outerWidth || document.documentElement.clientWidth;
    var h = window.outerHeight || document.documentElement.clientHeight;
    //left = ($(window).width() - el.width()) / 2;
    left = panel ? (panel.width() - el.width()) / 2 : ($(window).width() - el.width()) / 2;
    top = Math.abs(($(window).height() - el.outerHeight()) / 2) + $(document).scrollTop() || 0;
    if (top < 75) {
      top = 75;
    }
    el.css({ left: left, top: top, width: el.width() });
  }
  position();
  el.show();
  showOverlay();
}

function removeEl(el) {
	el = typeof el == "string" ? $("#" + el) : el;
	if ($("#bgWrap").length) {
		removeOverlay();
	}
	if (el) {
		el.css("display", "none");
	}
}

//开始
function hideElAndShowLoad(el) {
	//隐藏弹出层
	el = typeof el == "string" ? $("#" + el) : el;
	if (el) {
		el.css("display", "none");
	}

	//生成并显示加载层
	beautAlert.load(setRnssLanguage("正在处理，请稍后…"), 'load');
}
function hideLoad() {
	beautAlert.hidden();
}
//结束

function showOverlay() {
	var maxH = document.documentElement.scrollHeight;
	if ($("#bgWrap").length) {
		$("#bgWrap").css({ "display": "block", "opacity": .4 });
	} else {
		$("<div>", { id: "bgWrap" }).css({ "display": "block", "height": maxH }).appendTo(document.body);
		/msie 6\.0/i.test(navigator.userAgent) && $("<iframe>").css({ "background": "#fff", "left": -1,top:0, opacity: .3, "position": "absolute", "zIndex": -1, "width": "100%", "height": maxH }).appendTo($("#bgWrap"));
	}
}

function removeOverlay() {
	$("#bgWrap").css("display", "none");
}

Math.randomId = function () {
	return Math.floor(Math.random() * 0x100000).toString(16);
};

function trim_s(target) {
	target.val(target.val().replace(/\s/g, ''));
}

function CheckBoxManager(collection) {
	this.collection = collection;
}

CheckBoxManager.prototype = {
	selectAll: function (select) {
		this.collection.prop('checked', select ? true : false);
	},
	values: function () {
		var selectedValues = [];
		this.collection.filter(':checked').each(function (a, b, c) {
			selectedValues.push($(b).val());
		});
		return selectedValues;
	},
	datas: function () {
	  var selectedDatas = [];
	  this.collection.filter(':checked').each(function (a, b, c) {
	    selectedDatas.push($(b).data("status"));
	  });
	  return selectedDatas;
	}
}
var waitingLayer = {
	msgBox: $("<div>", { "id": "Rs_MsgBox_Wait", "class": "Rs_MsgBox_Layer_Wrap_Wait" }),
	show: function () {
		if ($(".Rs_MsgBox_Layer_Wrap_Wait").length) {
			$(".Rs_MsgBox_Layer_Wrap_Wait").remove();
		}
		else { this.msgBox.appendTo(document.body); }
		var maxH = document.documentElement.scrollHeight;
		$("<div>", { id: "bgWrapWait" }).css({ "display": "block", "height": maxH }).appendTo($(".Rs_MsgBox_Layer_Wrap_Wait"));
		/msie 6\.0/i.test(navigator.userAgent) && $("<iframe>").css({ "background": "#fff", "left": -1, opacity: .0, "position": "absolute", "zIndex": -1, "width": "100%", "height": maxH }).appendTo($(".Rs_MsgBox_Layer_Wrap_Wait"));

	},
	hide: function () { $(".Rs_MsgBox_Layer_Wrap_Wait").remove(); }
}
var waitingLayer2 = {
  msgBox: $("<div>", { "id": "Rs_MsgBox_Wait", "class": "Rs_MsgBox_Layer_Wrap_Wait" }),
  show: function () {
    if ($(".Rs_MsgBox_Layer_Wrap_Wait").length) {
      $(".Rs_MsgBox_Layer_Wrap_Wait").remove();
    }
    else { this.msgBox.appendTo(document.body); }
    var maxH = document.documentElement.scrollHeight;
    $("<div>", { id: "bgWrapWait" }).css({ "display": "block", "height": maxH }).appendTo($(".Rs_MsgBox_Layer_Wrap_Wait"));
    /msie 6\.0/i.test(navigator.userAgent) && $("<iframe>").css({ "background": "#fff", "left": -1, opacity: .0, "position": "absolute", "zIndex": -1, "width": "100%", "height": maxH }).appendTo($(".Rs_MsgBox_Layer_Wrap_Wait"));
    $("#Rs_MsgBox_Wait").append('<div class="" id="bgWrapLoading"><img alt="" src="/images/loading.gif" />' + setRnssLanguage('正在提交...') + '</div>');
  },
  hide: function () { $(".Rs_MsgBox_Layer_Wrap_Wait").remove(); }
}
var limit = {
	init: function (params) {
		var opts =
				{
					txtNote: $(".company").find("#description"),
					txtLimit: $(".maxtext>p"),
					limitCount: 1800,
					isbyte: false
				};
		var params = this.params = $.extend({}, opts, params || {});
		this.params.txtNote.bind("keyup", function () {
			var noteCount = 0;
			if (params.isbyte) { noteCount = params.txtNote.val().replace(/[^\x00-\xff]/ig, "xx").length } else { noteCount = params.txtNote.val().length };
			var txtlength = params.limitCount - noteCount;
			if (txtlength >= 0) {
			  params.txtLimit.html("<p>" + setRnssLanguage("还可以输入{0}个字").format(" <b>" + txtlength + "</b> ") + "</p>");
			}
			else {
			  params.txtLimit.html("<p>" + setRnssLanguage("已经超出{0}个字").format(" <b class='exceed'>" + -txtlength + "</b> ") + "</p>");
			}
			$maxlength = txtlength;
		});
	},
	maxLength: function (params) {
		var opts =
				{
					txtNote: $(".company").find("#description"),
					limitCount: 1800,
					minCount: 0,
					isbyte: false
				};
		this.params = $.extend({}, opts, params || {});
		if (this.params.isbyte) {
			if (this.params.limitCount - this.params.txtNote.val().replace(/[^\x00-\xff]/ig, "xx").length < 0) {
				return false;
			}
			else { return true; }
		}
		else {
			if (this.params.limitCount - this.params.txtNote.val().length < 0) {
				return false;
			}
			else if (this.params.txtNote.val().length < this.params.minCount) {
				return false;
			}
			else { return true; }
		}
	}
}
//全角转半角
function DBC2SBC(str) {
	var result = "";
	for (var i = 0; i < str.length; i++) {
		code = str.charCodeAt(i);//获取当前字符的unicode编码
		if (code >= 65281 && code <= 65373)//在这个unicode编码范围中的是所有的英文字母已经各种字符
		{
			var d = str.charCodeAt(i) - 65248;
			result += String.fromCharCode(d);//把全角字符的unicode编码转换为对应半角字符的unicode码
		}
		else if (code == 12288)//空格
		{
			var d = str.charCodeAt(i) - 12288 + 32;
			result += String.fromCharCode(d);
		}
		else {
			result += str.charAt(i);
		}
	}
	return result;
}
//JS操作时间和日期
Date.prototype.format = function (format) {
	var o = {
		"M+": this.getMonth() + 1,  //month
		"d+": this.getDate(),     //day
		"h+": this.getHours(),    //hour
		"m+": this.getMinutes(),  //minute
		"s+": this.getSeconds(), //second
		"q+": Math.floor((this.getMonth() + 3) / 3),  //quarter
		"S": this.getMilliseconds() //millisecond
	}
	if (typeof this == "string") { return this; }
	if (format == null || format == "undefined") { format = "yyyy-MM-dd"; }

	if (/(y+)/.test(format)) {
		format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
	}

	for (var k in o) {
		if (new RegExp("(" + k + ")").test(format)) {
			format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
		}
	}
	return format;
}
//部分低版本不支持时间转换
function formatDate(str) {
	str = str.split('T');
	str1 = str[0].split('-');
	str2 = str[1].split(':');
	var date = new Date();
	date.setUTCFullYear(str1[0], str1[1] - 1, str1[2]);
	date.setUTCHours(str2[0], str2[1], str2[2], 0);
	return date;
}
