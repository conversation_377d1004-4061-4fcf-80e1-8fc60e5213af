/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

/* 顶部导航栏 */
.top-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 60px;
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1400px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffd700;
    font-size: 20px;
}

.logo-text {
    font-size: 20px;
    font-weight: bold;
    color: #ffd700;
}

.top-nav {
    display: flex;
    gap: 30px;
}

.nav-item {
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    transition: background-color 0.3s;
}

.nav-item:hover {
    background-color: rgba(255,255,255,0.2);
}

.user-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-avatar-placeholder {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.notifications {
    position: relative;
    cursor: pointer;
}

.notification-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 主容器 */
.main-container {
    display: flex;
    margin-top: 60px;
    min-height: calc(100vh - 60px);
}

/* 侧边栏 */
.sidebar {
    width: 250px;
    background: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    overflow-y: auto;
    position: fixed;
    left: 0;
    top: 60px;
    bottom: 0;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-group {
    margin-bottom: 5px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s;
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background-color: #f8f9fa;
    border-left-color: #667eea;
}

.nav-item.active {
    background-color: #e3f2fd;
    border-left-color: #667eea;
    color: #667eea;
}

.nav-item i {
    width: 20px;
    text-align: center;
    font-size: 16px;
}

.nav-submenu {
    background-color: #f8f9fa;
    display: none;
}

.nav-group:hover .nav-submenu {
    display: block;
}

.nav-subitem {
    padding: 8px 20px 8px 52px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    transition: all 0.3s;
}

.nav-subitem:hover {
    background-color: #e9ecef;
    color: #333;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: 250px;
    padding: 20px;
    background-color: #f5f7fa;
}

.page-content {
    display: none;
}

.page-content.active {
    display: block;
}

/* 开业喜报横幅 */
.announcement-banner {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 25px rgba(255,107,107,0.3);
}

.banner-content h2 {
    font-size: 32px;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.banner-content p {
    font-size: 16px;
    margin-bottom: 5px;
    opacity: 0.9;
}

.qr-code {
    text-align: center;
}

.qr-code-placeholder {
    width: 80px;
    height: 80px;
    background: white;
    padding: 8px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 32px;
}

.qr-code p {
    margin-top: 8px;
    font-size: 14px;
}

/* 快速入口 */
.quick-access {
    background: white;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.quick-access h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 20px;
}

.access-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
}

.access-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border-radius: 10px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    cursor: pointer;
    transition: all 0.3s;
    border: 2px solid transparent;
}

.access-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.access-item i {
    font-size: 24px;
    color: #667eea;
    margin-bottom: 10px;
}

.access-item span {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

/* 数据统计卡片 */
.dashboard-stats {
    margin-bottom: 30px;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-3px);
}

.stat-number {
    font-size: 28px;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: #666;
}

/* 图表区域 */
.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.chart-container, .ranking-container {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.chart-container h4, .ranking-container h4 {
    margin-bottom: 20px;
    color: #333;
    font-size: 18px;
}

.chart-placeholder {
    height: 300px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
}

.ranking-list {
    height: 300px;
    overflow-y: auto;
}

/* 搜索表单样式 */
.search-form-container {
    background: white;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.search-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.search-actions-row {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.search-actions {
    display: flex;
    gap: 15px;
}

/* 搜索结果样式 */
.search-results-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.results-info {
    color: #666;
}

.results-actions {
    display: flex;
    gap: 10px;
}

.results-table-container {
    overflow-x: auto;
}

/* 客户、职位、人选管理页面样式 */
.customer-management,
.position-management,
.candidate-management {
    padding: 20px;
}

.customer-stats,
.position-stats,
.candidate-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .main-content {
        padding: 15px;
    }

    .announcement-banner {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .charts-section {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .top-header {
        padding: 0 15px;
    }

    .header-container {
        flex-wrap: wrap;
        gap: 10px;
    }

    .top-nav {
        display: none;
    }

    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s;
        z-index: 1500;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        padding: 15px;
    }

    .charts-section {
        grid-template-columns: 1fr;
    }

    .access-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 15px;
    }

    .stats-row {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }

    .search-row {
        grid-template-columns: 1fr;
    }

    .search-actions-row {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .search-actions-row .btn {
        width: 100%;
        max-width: 200px;
    }

    .search-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .results-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .announcement-banner {
        padding: 20px;
        margin-bottom: 20px;
    }

    .banner-content h2 {
        font-size: 24px;
    }

    .quick-access {
        padding: 20px;
    }

    .access-item {
        padding: 15px;
    }

    .stat-card {
        padding: 20px;
    }

    .chart-container, .ranking-container {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .logo-text {
        display: none;
    }

    .user-name {
        display: none;
    }

    .access-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .stats-row {
        grid-template-columns: 1fr;
    }

    .announcement-banner {
        padding: 15px;
    }

    .banner-content h2 {
        font-size: 20px;
    }

    .banner-content p {
        font-size: 14px;
    }

    .main-content {
        padding: 10px;
    }

    .quick-access, .search-form-container, .search-results-container {
        margin-bottom: 20px;
    }
}

/* 移动端侧边栏遮罩 */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1400;
}

.mobile-view .sidebar-overlay.show {
    display: block;
}

/* 打印样式 */
@media print {
    .top-header,
    .sidebar,
    .search-actions-row,
    .results-actions {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    .page-content {
        box-shadow: none !important;
    }
}
