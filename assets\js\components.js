// 组件相关功能

// 简历搜索页面
function loadResumeSearchPage() {
    const resumeSearchPage = document.getElementById('resume-search-page');
    if (resumeSearchPage) {
        resumeSearchPage.innerHTML = `
            <div class="resume-search-container">
                <div class="search-header">
                    <h2>搜索简历</h2>
                    <div class="search-actions">
                        <button class="btn btn-primary" onclick="showAdvancedSearch()">高级搜索</button>
                        <button class="btn btn-success" onclick="uploadResume()">上传简历</button>
                    </div>
                </div>
                
                <div class="search-form-container">
                    <form class="search-form" onsubmit="performSearch(event)">
                        <div class="search-row">
                            <div class="form-group">
                                <label class="form-label">关键字</label>
                                <input type="text" class="form-control" placeholder="请输入关键字搜索简历">
                            </div>
                            <div class="form-group">
                                <label class="form-label">更新时间</label>
                                <select class="form-control form-select">
                                    <option value="">请选择</option>
                                    <option value="recent">最近三年</option>
                                    <option value="week">一周内</option>
                                    <option value="month">一个月内</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">排序方式</label>
                                <select class="form-control form-select">
                                    <option value="relevance">相关度排序</option>
                                    <option value="time">时间排序</option>
                                    <option value="salary">薪资排序</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="search-row">
                            <div class="form-group">
                                <label class="form-label">职位名称</label>
                                <input type="text" class="form-control" placeholder="请输入职位名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">公司名称</label>
                                <input type="text" class="form-control" placeholder="请输入公司名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">当前地点</label>
                                <select class="form-control form-select">
                                    <option value="">请选择地点</option>
                                    <option value="beijing">北京</option>
                                    <option value="shanghai">上海</option>
                                    <option value="guangzhou">广州</option>
                                    <option value="shenzhen">深圳</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="search-row">
                            <div class="form-group">
                                <label class="form-label">当前行业</label>
                                <select class="form-control form-select">
                                    <option value="">请选择行业</option>
                                    <option value="it">IT互联网</option>
                                    <option value="finance">金融</option>
                                    <option value="education">教育</option>
                                    <option value="healthcare">医疗</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">期望年薪</label>
                                <select class="form-control form-select">
                                    <option value="">不限</option>
                                    <option value="0-5">5万以下</option>
                                    <option value="5-10">5-10万</option>
                                    <option value="10-20">10-20万</option>
                                    <option value="20+">20万以上</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">期望年龄</label>
                                <select class="form-control form-select">
                                    <option value="">不限</option>
                                    <option value="20-25">20-25岁</option>
                                    <option value="25-30">25-30岁</option>
                                    <option value="30-35">30-35岁</option>
                                    <option value="35+">35岁以上</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="search-actions-row">
                            <button type="button" class="btn btn-outline" onclick="resetSearch()">重置</button>
                            <button type="button" class="btn btn-secondary" onclick="saveSearchCondition()">保存搜索条件</button>
                            <button type="submit" class="btn btn-primary">搜索</button>
                            <button type="button" class="btn btn-success" onclick="exportSearchResults()">搜索并导出</button>
                        </div>
                    </form>
                </div>
                
                <div class="search-results-container">
                    <div class="results-header">
                        <div class="results-info">
                            <span>共搜索到 <strong>3000+</strong> 份简历 您可查看前 <strong>900</strong> 份</span>
                        </div>
                        <div class="results-actions">
                            <button class="btn btn-outline" onclick="batchSelect()">批量选择简历</button>
                            <button class="btn btn-primary" onclick="sortResults()">列表排序</button>
                        </div>
                    </div>
                    
                    <div class="results-table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>姓名</th>
                                    <th>年龄</th>
                                    <th>学历</th>
                                    <th>工作年限</th>
                                    <th>居住地</th>
                                    <th>职位</th>
                                    <th>公司</th>
                                    <th>更新时间</th>
                                    <th>推荐</th>
                                    <th>记录</th>
                                    <th>评价</th>
                                </tr>
                            </thead>
                            <tbody id="search-results-tbody">
                                <!-- 搜索结果将在这里动态加载 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="pagination">
                        <a href="#" class="pagination-item disabled">上一页</a>
                        <a href="#" class="pagination-item active">1</a>
                        <a href="#" class="pagination-item">2</a>
                        <a href="#" class="pagination-item">3</a>
                        <a href="#" class="pagination-item">下一页</a>
                    </div>
                </div>
            </div>
        `;
        
        // 加载搜索结果
        loadSearchResults();
    }
}

// 加载搜索结果
function loadSearchResults() {
    const tbody = document.getElementById('search-results-tbody');
    if (tbody) {
        const mockResults = [
            {
                id: 1,
                name: '刘晓明',
                age: 27,
                education: '硕士',
                experience: 2,
                location: '闵行区',
                position: '光学工程师',
                company: '上海朗研科技有限公司',
                updateTime: '2025-06-25',
                recommend: 7,
                records: 0
            },
            {
                id: 2,
                name: '侯宜君',
                age: 34,
                education: '硕士',
                experience: 0,
                location: '北京',
                position: '机械研发工程师',
                company: '北京起重运输机械设计研究院',
                updateTime: '2025-06-25',
                recommend: 8,
                records: 8
            },
            {
                id: 3,
                name: '徐先生',
                age: 35,
                education: '本科',
                experience: 8,
                location: '深圳',
                position: '审计经理',
                company: '瀚德医疗用品制造有限公司',
                updateTime: '2025-06-25',
                recommend: 16,
                records: 16
            }
        ];
        
        let html = '';
        mockResults.forEach(result => {
            html += `
                <tr>
                    <td>
                        <input type="checkbox" class="result-checkbox" value="${result.id}">
                        ${result.id}
                    </td>
                    <td>
                        <a href="#" onclick="viewResumeDetail(${result.id})" style="color: #667eea; text-decoration: none;">
                            ${result.name}
                        </a>
                        ${result.id <= 2 ? '<span class="badge badge-success" style="margin-left: 5px;">新</span>' : ''}
                    </td>
                    <td>${result.age}</td>
                    <td>${result.education}</td>
                    <td>${result.experience}</td>
                    <td>${result.location}</td>
                    <td>${result.position}</td>
                    <td>${result.company}</td>
                    <td>${result.updateTime}</td>
                    <td>
                        <span class="badge badge-info">${result.recommend}</span>
                    </td>
                    <td>
                        <span class="badge badge-warning">${result.records}</span>
                    </td>
                    <td>
                        <button class="btn btn-outline" style="padding: 4px 8px; font-size: 12px;" onclick="addEvaluation(${result.id})">
                            评价
                        </button>
                    </td>
                </tr>
            `;
        });
        
        tbody.innerHTML = html;
    }
}

// 执行搜索
function performSearch(event) {
    event.preventDefault();
    showNotification('正在搜索简历...', 'info');
    
    // 模拟搜索延迟
    setTimeout(() => {
        loadSearchResults();
        showNotification('搜索完成！', 'success');
    }, 1000);
}

// 重置搜索
function resetSearch() {
    const form = document.querySelector('.search-form');
    if (form) {
        form.reset();
        showNotification('搜索条件已重置', 'info');
    }
}

// 显示高级搜索
function showAdvancedSearch() {
    showNotification('高级搜索功能开发中...', 'info');
}

// 上传简历
function uploadResume() {
    showNotification('简历上传功能开发中...', 'info');
}

// 保存搜索条件
function saveSearchCondition() {
    showNotification('搜索条件已保存', 'success');
}

// 导出搜索结果
function exportSearchResults() {
    showNotification('正在导出搜索结果...', 'info');
}

// 批量选择
function batchSelect() {
    const checkboxes = document.querySelectorAll('.result-checkbox');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    
    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
    });
    
    showNotification(allChecked ? '已取消全选' : '已全选', 'info');
}

// 排序结果
function sortResults() {
    showNotification('排序功能开发中...', 'info');
}

// 查看简历详情
function viewResumeDetail(id) {
    showNotification(`正在查看简历 ID: ${id}`, 'info');
}

// 添加评价
function addEvaluation(id) {
    showNotification(`为简历 ID: ${id} 添加评价功能开发中...`, 'info');
}

// 加载客户数据
function loadCustomerData() {
    const customerPage = document.getElementById('customer-page');
    if (customerPage) {
        customerPage.innerHTML = `
            <div class="customer-management">
                <h2>客户管理</h2>
                <div class="customer-stats">
                    <div class="stat-card">
                        <div class="stat-number">156</div>
                        <div class="stat-label">总客户数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">89</div>
                        <div class="stat-label">活跃客户</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">23</div>
                        <div class="stat-label">新增客户</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">67</div>
                        <div class="stat-label">合作客户</div>
                    </div>
                </div>
                <p style="margin-top: 20px; color: #666;">客户管理详细功能正在开发中...</p>
            </div>
        `;
    }
}

// 加载职位数据
function loadPositionData() {
    const positionPage = document.getElementById('position-page');
    if (positionPage) {
        positionPage.innerHTML = `
            <div class="position-management">
                <h2>职位管理</h2>
                <div class="position-stats">
                    <div class="stat-card">
                        <div class="stat-number">234</div>
                        <div class="stat-label">总职位数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">145</div>
                        <div class="stat-label">活跃职位</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">34</div>
                        <div class="stat-label">新增职位</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">89</div>
                        <div class="stat-label">已完成</div>
                    </div>
                </div>
                <p style="margin-top: 20px; color: #666;">职位管理详细功能正在开发中...</p>
            </div>
        `;
    }
}

// 加载人选数据
function loadCandidateData() {
    const candidatePage = document.getElementById('candidate-page');
    if (candidatePage) {
        candidatePage.innerHTML = `
            <div class="candidate-management">
                <h2>人选管理</h2>
                <div class="candidate-stats">
                    <div class="stat-card">
                        <div class="stat-number">1,234</div>
                        <div class="stat-label">简历总数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">456</div>
                        <div class="stat-label">活跃人选</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">78</div>
                        <div class="stat-label">面试中</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">23</div>
                        <div class="stat-label">已入职</div>
                    </div>
                </div>
                <p style="margin-top: 20px; color: #666;">人选管理详细功能正在开发中...</p>
            </div>
        `;
    }
}

// 显示快速录入模态框
function showQuickEntryModal() {
    const modal = createModal('快速录入', `
        <form class="quick-entry-form">
            <div class="form-group">
                <label class="form-label">姓名</label>
                <input type="text" class="form-control" placeholder="请输入姓名" required>
            </div>
            <div class="form-group">
                <label class="form-label">电话</label>
                <input type="tel" class="form-control" placeholder="请输入电话号码" required>
            </div>
            <div class="form-group">
                <label class="form-label">邮箱</label>
                <input type="email" class="form-control" placeholder="请输入邮箱地址">
            </div>
            <div class="form-group">
                <label class="form-label">职位</label>
                <input type="text" class="form-control" placeholder="请输入期望职位">
            </div>
            <div class="form-group">
                <label class="form-label">备注</label>
                <textarea class="form-control" rows="3" placeholder="请输入备注信息"></textarea>
            </div>
        </form>
    `, [
        { text: '取消', class: 'btn-secondary', onclick: 'closeModal()' },
        { text: '保存', class: 'btn-primary', onclick: 'saveQuickEntry()' }
    ]);
    
    showModal(modal);
}

// 创建模态框
function createModal(title, content, buttons = []) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">${title}</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            <div class="modal-footer">
                ${buttons.map(btn => `<button class="btn ${btn.class}" onclick="${btn.onclick}">${btn.text}</button>`).join('')}
            </div>
        </div>
    `;
    
    return modal;
}

// 显示模态框
function showModal(modal) {
    document.body.appendChild(modal);
    setTimeout(() => modal.classList.add('show'), 10);
    
    // 点击背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
}

// 关闭模态框
function closeModal() {
    const modal = document.querySelector('.modal.show');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(modal);
        }, 300);
    }
}

// 保存快速录入
function saveQuickEntry() {
    const form = document.querySelector('.quick-entry-form');
    const formData = new FormData(form);
    
    // 这里可以添加表单验证和数据保存逻辑
    showNotification('信息保存成功！', 'success');
    closeModal();
}
