//周年祝福框
var r_anniversary_notice = function () {
    var page = 0, pagesize = 1,
        recordcount = 0,
        pagedata = null,
        listData = [{
            year: 9,
            name: '赵冰',
            level: 3,
            imgUrl: '',
            detailPage: 'xxxxxxxxxxx.html'
        }],
        dataId = 0,
        outerCb = "",
        isShowQw = false/*是否显示全文按钮*/,
        tId = 0/*记录当前Id*/;
    var mask = $("#home_notice_mask"),
        panel = $("#home_notice_panel"),
        box = $("#home_notice_box");
    //格式化发布时间
    Date.prototype.Format = function (fmt) { //author: meizz 
        var o = {
            "M+": this.getMonth() + 1, //月份 
            "d+": this.getDate(), //日 
            "h+": this.getHours(), //小时 
            "m+": this.getMinutes(), //分 
            "s+": this.getSeconds(), //秒 
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度 
            "S": this.getMilliseconds() //毫秒 
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }
    function setPublishTimeTxt(date) {
        if (!date) return "";
        var $date = "";
        if (date && date !== "0001/1/1 0:00:00" && date !== "0001-01-01T12:00:00") {
            if (date.indexOf("Date") !== -1) {
                $date = eval('new ' + eval(date).source);
            }
            else {
                $date = new Date(date);
            }
            $date = $date.Format("yyyy-MM-dd");
        }
        return $date;
    }
    function setMask() {//向页面中添加遮罩
        if ($("#home_notice_mask").length > 0) return;
        var html = '<div id="home_notice_mask" class="home_notice_mask"></div>';
        $("body").append(html);
    }

    function buildBoxHtml() {
        //生成基础结构
        var box = $('<div id="home_notice_box" class="home_notice_box" style="transform:scale(0.7) translateY(-150px);"></div>');
        box.append(
            //  '<a href="javascript:;" class="hn_page_btn hn_prev"><img src="/content/images/notice/left.png" /></a>' +
            //   '<a href="javascript:;" class="hn_page_btn hn_next"><img src="/content/images/notice/right.png" /></a>' +
            //   '<div class="hn_pager"><span class="hn_p_yx"></span>&frasl;<span class="hn_p_ym"></span></div>' +
            '<div id="home_notice_panel" class="home_notice_panel home_notice_panel1"><img class="hnp_close hnp_close_notice" src= "/css/images/close_notice.png"/><div class="hnp_con"><img src="/css/images/headerImg.png" class="notice-headerImg"></div>' +
            '<div class="hn_footer"><textarea class="hn_Txt_Pl" placeholder="' + setRnssLanguage("最多输入200字~") + '"></textarea><input type="button" class="hn_Btn_Sub " style="border-radius:2px" value="' + setRnssLanguage("发送") + '" /></div>' +
            '</div>'
            //   '<img src="/css/images/earning-shayu1.png" alt="" class="hep_con_img1" />' +
            //   '<img src="/css/images/earning-shayu2.png" alt="" class="hep_con_img2" />'
        );
        return box;
    }
    var handlerEvents = {
        buildDetailList: function (arr, styleMap) {
            var jumpUrl = "/staff/personalhomepage?id=";
            var len = JSON.parse(arr.LogContent).length;
            var tar = {};
            var sty = "";
            var typeOuter = "";
            var html = "";
            if (len < 3 && len > 0) {
                tar = styleMap[len];
                sty = tar.type;
                typeOuter = tar.typeOuter;
            } else {
                tar = styleMap['3'];
                sty = tar.type;
                typeOuter = tar.typeOuter;
            };
            JSON.parse(arr.LogContent).forEach(function (item) {
                var content = item;
                html += "<div class='" + sty + "'>" +
                    " <img src='/css/images/listheaderimg.png' class='notice-list-header-img' />" +
                    "<p class='notice-year notice-year'>入职<span class='font'>" + item.Years + "</span>周年</p>" +
                    "<div class='header-pro-wrap'><a href='" + jumpUrl + item.StaffId + "' target='_blank'><img src=" + content.PicUrl + " class='notice-touxiang'></a></div>" +
                    "<div class='notice-name-w'><a href='" + jumpUrl + item.StaffId + "' target='_blank'>" + content.StaffName + "</a>";
                if (content.Level > 0) {
                    html += "<span class='level'>Lv" + content.Level + "</span>";
                }
                html += "</div ></div > ";
            })
            return {
                html: html,
                typeOuter: typeOuter,
            };
        }
    }
    //生成公告的Html内容部分
    function buildGGHtml() {
        var styleMap = {
            1: {
                typeOuter: "listHtml_b",
                type: "notice-list-detail-big",
            },
            2: {
                typeOuter: "listHtml_m",
                type: "notice-list-detail-midum",
            },
            3: {
                typeOuter: "listHtml_s",
                type: "notice-list-detail-small",
            }
        };
        var h_c = handlerEvents.buildDetailList(pagedata[0], styleMap);
        var html = '',
            d = pagedata[page];
        //内容区域 start
        html += "<div class='notice-list-wrap " + h_c.typeOuter + "' >" +
            h_c.html +
            "</div > "
        return html;
    }
    //生成评论的Html
    function buildPLHtml() {
        var html = '',
            data = pagedata[page],
            dl = data.length,
            dfl = data.FabulousList.length,
            dcl = data.CommentsList.length;
        //传送门
        //console.log(data.CommentsList);

        html += '<div class="pl_box cf">';
        if (isShowQw) {
            html += '<a href="javascript:;" class="pl_qw">' + setRnssLanguage("全文") + '</a>';
        }
        html += '<div class="pl_info">';//评论点赞 start
        html += '<img src="/content/images/notice/iconmore.png" alt="" />';
        html += '<div class="pl_info_b">';
        html += '<a href="javascript:;" class="pl_info_btn pl_info_zan"><img src="/Content/images/notice/iIconzan.png" alt=""/>' + setRnssLanguage("赞") + '</a><span class="pl_info_line"></span><a href="javascript:;" class="pl_info_btn pl_info_pl"><img src="/Content/images/notice/iconcomment.png" alt=""/>' + setRnssLanguage("评论") + '</a>';
        html += '</div>';
        html += '</div>';//评论点赞 end
        html += '</div>';
        //新加外壳
        html += '<div class="wrapperCot">';
        html += buildPLConHtml();
        html += '</div>';
        return html;
    }
    function buildPLConHtml() {
        var html = '',
            data = pagedata[page],
            dl = data.length,
            dfl = data.FabulousList.length,
            dcl = data.CommentsList.length;
        html += '<div class="dz_box">';//点赞列表 start
        html += '<span class="dz_sl"><img src="/Content/images/notice/iconzanblack.png" alt="" /><span class="dz_num">' + dfl + '</span></span>';
        html += '<div class="dz_list cf">';
        for (var i = 0; i < dfl; i++) {
            var item = data.FabulousList[i];

            html += '<a class="dz_item ' + (data.FabulousList.length > 20 ? (i % 10 == 0 || i % 10 == 1) ? '' : 'dz_item_r' : (i % 11 == 0 || i % 11 == 1) ? '' : 'dz_item_r') + '" href="/staff/personalhomepage?id=' + item.StaffId + '" target="_blank">';
            html += '<img src="' + item.StaffPhoto + '" alt="' + item.StaffName + '" />';
            html += '<div class="dz_con">';
            html += '<p class="dz_title">' + item.CompanyName + '</p>';
            html += '<p class="dz_uname">' + item.StaffName + '</p>';
            html += '</div>';
            html += '</a>';
        }
        if (dfl == 0) {
            html += '<div class="dz_nodata">' + setRnssLanguage("暂时还没有人点赞...") + '</div>';
        }

        html += '</div>';
        html += '</div>';//点赞列表 end
        html += '<div class="p_box">';//评论列表 start
        html += '<span class="p_sl"><img src="/Content/images/notice/iconcommentblack.png" alt="" /><span class="p_num">' + dcl + '</span></span>';
        html += '<div class="p_list cf">';
        for (var i = 0; i < dcl; i++) {
            var item = data.CommentsList[i];
            html += '<div class="p_item" href="/staff/personalhomepage?id=' + item.StaffId + '" target="_blank">';
            html += '<img src="' + item.StaffPhoto + '" alt="' + item.StaffName + '" />';
            html += '<div class="p_con">';
            html += '<div class="p_title cf">' + item.StaffName + '<span class="p_time">' + setPublishTimeTxt(item.CreatedTime) + '</span></div>';
            html += '<p class="p_content">' + item.Content + '</p>';
            html += '</div>';
            html += '</div>';
        }
        if (dcl == 0) {
            html += '<div class="p_nodata">' + setRnssLanguage("暂时还没有人评论...") + '</div>';
        }
        html += '</div>';
        html += '</div>';//评论列表 end
        return html;
    }
    //重新计算内容区域高度
    function resetConHeight() {

        var b_con = panel.find(".hnp_c"),
            b_con_b = b_con.find(".hnp_c_b");
        //setTimeout(function () {
        var bHeight = b_con.outerHeight(true) + 10;
        //if (bHeight < 500) b_con.height(bHeight);
        //else {
        //  b_con.height(500);
        //}
        if (b_con_b.height() > 230) {
            b_con_b.addClass("hcon");
            if (pagedata[page].RnssType != 2) {
                isShowQw = true;
                if (box.find(".pl_box .pl_qw").length > 0) return;
                box.find(".pl_box").prepend('<a href="javascript:;" class="pl_qw">全文</a>');
            }
        } else {
            b_con_b.removeClass("hcon");
            isShowQw = false;
        }
        //}, 350);
        bindEvent();
    }

    //
    function setPageData() {
        var data = pagedata[page],
            b_con = box.find(".hnp_con"),
            p = page + 1;
        var img1 = box.find(".hep_con_img1"), img2 = box.find(".hep_con_img2");
        setConOverflow();
        var html = getHtml();
        panel.stop(true, false).fadeOut(300, function () {
            //resetForm();
            img1.css({ left: 0, top: 0, height: 0 }).hide();
            img2.css({ right: 0, bottom: 0, height: 0 }).hide();
            panel.find(".hnp_con").html(html);
            panel.stop(true, false).fadeIn(200, function () {
                e_ConHeight();
            });
        });
        setPager();
    }

    //根据不同的类型（大鲨鱼或系统公告）设置内容区域的overflow属性
    function setConOverflow() {
        var data = pagedata[page],
            b_con = box.find(".hnp_con");
        if (data.RnssType == 2) {
            b_con.addClass("hnp_con_dsy");
        }
        else {
            b_con.removeClass("hnp_con_dsy");
        }
    }

    //翻页后的事件绑定
    function bindEvent() {
        var data = pagedata[page];
        if (data.RnssType == 2) {//如果是大鲨鱼
            //鲨鱼图片的动画
            var img1 = box.find(".hep_con_img1"), img2 = box.find(".hep_con_img2");
            img1.stop(true, false).show().animate({ height: 200, top: 0, left: 0 }, 1500);
            img2.stop(true, false).show().animate({ height: 200, top: 52, right: -80 }, 1500);
        }
        //评论、点赞等的事件
        box.find(".pl_info_pl").off("click").click(function () {
            box.find(".hn_Txt_Pl").focus();
        });
        box.find(".pl_info_zan").off("click").click(function () {
            //tId--当前Id
            AddRNSSNoticeComment(1, "");
        });
        box.find(".pl_qw").off("click").click(function () {
            var con = box.find(".hnp_c_b");
            if (con.hasClass("hcon")) {
                con.removeClass("hcon");
                $(this).text(setRnssLanguage("收起"));
            } else {
                con.addClass("hcon");
                $(this).text(setRnssLanguage("全文"));
            }
        });
    }

    //点赞或评论后，更新点赞（或评论）列表的Dom信息
    function changeCommentDom(type) {
        ////type: 1点赞 2评论
        //var list = box.find(".dz_list"),
        //  nodata = box.find(".dz_nodata"),
        //  html = '';
        //if (type == 2) {
        //  list = box.find(".p_list");
        //  nodata = box.find(".p_nodata");
        //  //-----------添加一条评论信息

        //  buildPLHtml();

        //} else {
    //  //-----------注意：这里需替换成当前登录用户的信息
        //  html += '<a class="dz_item" href="/staff/personalhomepage?id=' + 11 + '" target="_blank">';
        //  html += '<img src="' + fsdfs.png + '" alt="' + 发生的 + '" />';
        //  html += '<div class="dz_con cf">';
        //  html += '<p class="dz_title">' + dd + '</p>';
        //  html += '<p class="dz_uname">' + fff + '</div>';
        //  html += '</div>';
        //  html += '</a>';
        //  list.prepend(html);
        //}
        //nodata.remove();
        var conHtml = buildPLConHtml();
        box.find(".wrapperCot").html(conHtml);
    }

    //评论、点赞的提交处理
    function AddRNSSNoticeComment(type, content, callback) {
        tId = pagedata[page].RNSSNoticeId;
        //r_Layout_BlockUI.show();
        $.post('/notice/AddRNSSNoticeComment', { NoticeId: tId, StaffId: 0, CommentType: type, Content: content }, function (result) {
            if (result.Status == "1") {
                r_Layout_BeautAlert.done(setRnssLanguage('操作成功！'), 'hits', 1300);

                //成功了在拿一次数据
                //showNoticeList(true);
                reloadNoticeData(pagedata[page].RNSSNoticeId);
                callback && typeof callback == "function" && callback();
            }
            else {
                r_Layout_BeautAlert.done(result.Data, 'hits');
            }
        }).error(function (er) {
            r_Layout_BlockUI.hide();
            console.log(er.status + ": " + er.responseText);
        });
    }

    function setPager() {
        //翻页设置
        var btnprev = box.find(".hn_prev"),
            btnnext = box.find(".hn_next"),
            p = page + 1;
        box.find(".hn_p_ym").html(recordcount);
        box.find(".hn_p_yx").html(p);
        if (p >= recordcount && !btnnext.hasClass("nogo")) {
            btnnext.addClass("nogo");
        }
        else if (p < recordcount) {
            btnnext.removeClass("nogo");
        }
        if (page <= 0 && !btnprev.hasClass("nogo")) {
            btnprev.addClass("nogo");
        }
        else if (page > 0) {
            btnprev.removeClass("nogo");
        }
    }

    function resetForm() {
        //重置表单值
        box.find(".hn_Txt_Pl").val("").blur();
        tId = 0;
        page = 0;
        pagesize = 1;
        recordcount = 0;
        pagedata = null;
        isShowQw = false;
    }

    //翻页、关闭等非内容区域的事件的注册
    function setEvent() {
        box.find(".hn_page_btn").click(function () {
            isShowQw = false;
            if ($(this).hasClass("nogo")) return;
            if ($(this).hasClass("hn_prev")) {//前一条
                page -= 1;
            }
            else if ($(this).hasClass("hn_next")) {//后一条
                page += 1;
            }
            setPageData();
        });
      box.find(".hnp_close").click(function () {
            mask.fadeOut("slow", function () {
                mask.hide();
            });
            box.fadeOut("slow", function () {
                box.hide();
            });
            page = 0;
            pagesize = 1;
            recordcount = 0;
            pagedata = null;
            outerCb();
        });
        box.find(".hn_Txt_Pl").focus(function () {
            if (!$(this).hasClass("hn_Txt_Open")) {
                $(this).addClass("hn_Txt_Open");
            }
        }).blur(function () {
            if (!$(this).val()) {
                $(this).removeClass("hn_Txt_Open");
            }
        });
        box.find(".hn_Btn_Sub").click(function () {
            var txt = box.find(".hn_Txt_Pl").val().trim();
            if (!txt) {
                r_Layout_BeautAlert.done(setRnssLanguage('请填写评论内容！'), 'hits');
                return;
            }
            AddRNSSNoticeComment(2, txt, function () {
                box.find(".hn_Txt_Pl").val("").blur();
            });
        });
        e_ConHeight();
        bindEvent();
        setConOverflow();
    }
    function e_ConHeight() {
        //resetConHeight();
        var b_con = panel.find(".hnp_c"),
            b_con_b = b_con.find(".hnp_c_b"),
            imgs = b_con_b.find("img");
        var imgLen = imgs.length;
        if (imgLen > 0) {
            var m = 1;
            imgs.each(function (i, n) {
                (function () {
                    var nImg = new Image();
                    nImg.src = n.src;
                    nImg.onload = function () {
                        m++;
                        m >= imgLen && resetConHeight();
                    };
                    nImg.onerror = function () {
                        imgLen--;
                        m >= imgLen && resetConHeight();
                    };
                })(i);
            });
        }
        else {
            resetConHeight();
        }
    }
    //根据类型获取不同的内容部分Html机构
    function getHtml() {
        var html = "";
        html = buildGGHtml();
        html += buildPLHtml();
        return html;
    }

    //初始化数据和结构
    function event_Show() {

        if (!pagedata || pagedata.length <= 0) return;
        recordcount = pagedata.length;
        $("#home_notice_box").remove();
        var boxHtml = buildBoxHtml();
        var html = getHtml();
        boxHtml.find(".hnp_con").append(html);
        $("body").append(boxHtml);
        setMask();
        mask = $("#home_notice_mask");
        panel = $("#home_notice_panel");
        box = $("#home_notice_box");
        mask.fadeIn("slow");
        //panel.fadeIn("slow");
        box.fadeIn("slow", function () {
            setEvent();
            setPager();
        });
    }

    //根据Id获取一个--打开窗体的时候获取评论点赞数据
    function showNoticePreview(rsid) {
        $.post("/notice/getrnssnoticepreview", { id: rsid }, function (result) {
            if (result.Status == "1") {
                pagedata = [result.Data];
                event_Show();
            }
        }).error(function (er) {
            console.log(er.status + ": " + er.responseText);
        });
    }
    function reloadNoticeData(id) {
        $.post("/notice/getrnssnoticepreview", { id: id }, function (result) {
            if (result.Status == "1") {
                pagedata[page] = result.Data;
                changeCommentDom();
            }
        }).error(function (er) {
            console.log(er.status + ": " + er.responseText);
        });
    }
    //获取评论列表--这个不是首次获取
    //isReload: 是否为重新加载
  function showNoticeList(isReload, cb) {
        outerCb = cb;
        var url = "/notice/getrnssnoticepreview",
            data = {};
        if (!dataId || isNaN(Number(dataId))) {
            url = "/Notice/GetAnniversaryNotice";
            //   url = "Notice/GetNotice";
        }
        else {
            data.id = dataId;
        }
        //获取多个
        $.post(url, data, function (result) {
            if (result.Status == "1") {
                pagedata = result.Data.constructor == Array ? [result.Data[0]] : [result.Data];
                //console.log(pagedata);
                if (!pagedata[0]) {
                    cb();
                    return;
                }
                if (isReload) {
                    changeCommentDom();
                }
                else {
                    event_Show();
                }
            } else {

            }
        }).error(function (er) {
            console.log(er.status + ": " + er.responseText);
        });
    }

    //格式化时间
    function timestampFormat(timestamp, separator) {
        var result = "";
        if (timestamp) {
            //这里可以改变方式
            var newstring = timestamp.slice(6, -2);
            var d = new Date();
            d.setTime(newstring);
            var year = d.getFullYear();
            var month = d.getMonth() + 1;
            var day = d.getDate();
            if (month < 10) {
                month = "0" + month;
            }
            if (day < 10) {
                day = "0" + day;
            }
            result = year + separator + month + separator + day;
        }
        return result;
    }

    return {
      show: function (rsid, cb) {
        dataId = rsid;
        getDataBirthday(function () {
          showNoticeList(null, cb);
        }) 
            
        }
    };
}();

/****
 * 新改版首页调用加判断
 * 
 * */
if (locationUrl == 'https://staff.risfond.com/Home/IndexBoss' ||
  locationUrl == 'http://staff.risfond.com/Home/IndexBoss' ||
  locationUrl == 'http://teststaff.risfond.com/Home/IndexBoss' ||

  locationUrl == 'https://staff.risfond.com/Home/IndexBase' ||
  locationUrl == 'http://staff.risfond.com/Home/IndexBase' ||
  locationUrl == 'http://teststaff.risfond.com/Home/IndexBase' ||

  locationUrl == 'http://graystaff.risfond.com/Home/IndexBoss' ||
  locationUrl == 'http://graystaff.risfond.com/Home/IndexBase' ||
  locationUrl.indexOf("/Home/IndexBoss")>0

) {
  r_anniversary_notice.show(null, function () {
    r_Notice.show();
  });
} else {

}
