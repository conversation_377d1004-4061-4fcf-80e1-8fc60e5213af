/*加载开业管理相关接口*/ 
(function () {
  //定义方法
  let objModle = {
    //调用BMS审批相关接口
    InitBranch: function (branchUrlTicket, branchApproveUrl, branchURL, ticket) {
      $.ajax({
        url: branchUrlTicket,
        dataType: 'json',
        type: 'POST',
        data: {},
        success: (res) => {
          if (!res.Success)
            return;
          var token = res.Data;
          //添加token cookie
          this.setCookie("hrsop-token", token, 1);
          //
          $.ajax({
            url: branchApproveUrl,
            dataType: 'json',
            type: 'POST',
            data: {},
            success: (resdata) => {
              if (resdata.Success) {
                var branchData = resdata.Data || [];
                if (branchData.IsOpenBranch == 1) {
                  $("#BranchUrl").show();;
                  if (branchData.ApproveCount > 0) {
                    $(".CurrentBranchMyApprovalCount").show();
                    $(".CurrentBranchMyApprovalCount .circle").text(branchData.ApproveCount);;
                  }
                  else {
                    $(".CurrentBranchMyApprovalCount").hide();
                  }
                }

                //到期提醒弹框
                if (branchData.BusinessChangeRemindCycle > 0) {
                  layer.confirm("您的公司房租租赁还有" + branchData.BusinessChangeRemindCycle + "个月到期，请及时提交工商变更申请</br>提交申请路径: RNSS-系统管理-公司管理-操作-工商变更申请", {
                    area: ['500px', '200px'], icon: 3, title: "租赁到期提醒",
                    btn: ['稍后再说', '提交申请'] //可以无限个按钮
                    , btn2: function (index, layero) {
                      window.location.href = branchURL + "/changelist?ticket=" + ticket;
                    }
                  }, function (index) {
                    layer.close(index);
                  });
                }

              }
            },
            beforeSend: (request) => {
              request.setRequestHeader("Authorization", token);
              request.setRequestHeader("Content-Type", 'application/json');
              request.setRequestHeader('X-XSS-Protection', '0');
            },
            error: function (xhr, et) {
              if (et && et == "timeout") {
                //this.$message.error('服务器连接超时');
              } else if (xhr.status == "500") {
                //this.$message.error('服务器错误，稍后重试！');
              } else {
                //alert(xhr.responseText);
              }
            }
          });
        },
        error: function (xhr, et) {

        }
      })
    },
    //设置cookie
    setCookie: function (c_name, value, expiredays) {
      var exdate = new Date();
      exdate.setDate(exdate.getDate() + expiredays);
      document.cookie = c_name + "=" + escape(value) +
        ((expiredays == null) ? "" : ";expires=" + exdate.toGMTString());
    },
    //获取cookie
    getCookie: function (c_name) {
      if (document.cookie.length > 0) {
        var c_start = document.cookie.indexOf(c_name + "=");
        if (c_start != -1) {
          c_start = c_start + c_name.length + 1;
          var c_end = document.cookie.indexOf(";", c_start);
          if (c_end == -1) c_end = document.cookie.length;
          return unescape(document.cookie.substring(c_start, c_end));
        }
      }
      return "";
    }
  };
  //调用方法
  var bmsModelData = $("#BMSModelData");
  var branchUrlTicket = bmsModelData.data("branchurlticket"),
    branchApproveUrl = bmsModelData.data("branchapproveurl"),
    branchURL = bmsModelData.data("branchurl"),
    ticket = bmsModelData.data("ticket");
  objModle.InitBranch(branchUrlTicket, branchApproveUrl, branchURL, ticket);
})()