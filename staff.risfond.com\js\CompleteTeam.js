/// <reference path="../Scripts/jquery-1.8.2.js" />
/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
/**
 * 功能：自封装实现select效果
 * 依赖：autoCompleteTeam.js
 * 文档：http://*************:8888/index.php?s=/9&page_id=1728
 */
(function ($) {
  var els = "";
  $.fn.completeTeam = (function (params) {
    if (!this[0]) return;
    var templatebox = "template_" + parseInt(10000000 * Math.random());
    var listBox = templatebox + '-listBox';

    $(this).addClass(listBox);
    var listBoxClass = '.' + listBox;
          var opts =
          {
            url: null,
            type: 0,
            userKey: 'leader',
            maxCount: 1,
            box: listBoxClass,
            fixPos: '.' + templatebox + '-teamName-list',
            posCallback: undefined,
            input: '' + listBoxClass + ' .selector_input',
            boxId: '.' + templatebox + '-selector-list',
            teamListEl: '.' + templatebox + '-category-list',
            teamListOuterBox: '' + listBoxClass + ' .selector_outerbox',
            teamListSelectBox: '.' + templatebox + '-team-hinbox',
            teamListAddTeam: params.switchBtn || ('' + listBoxClass + ' .addTeam'),//提交按钮h
            teamListSelTeam: listBoxClass,
            width: "",
            outboxitemlist: '' + listBoxClass + ' .outbox-item-list',
            beforeselect: function () { return true; },
            selectcallback: function () { },
            cancelcallback: function () { },
            zindex: 10,
            datato: true,//是否数据缓存在this元素上
            append: false,//true:默认。
            markStaffId: 0,//Hr薪资可见性需要的当前编辑用户Id
            // 新增个性化配置
            handleOpts: params.handleOpts || {}
          };
          els = opts.teamListEl;
          var self = this.params = $.extend({}, opts, params || {}), cache, _$this = this;
    if (self.url == null) { cache = "companyteamcache" + self.type; $(this).data("type", self.type); } else { cache = "companyteamcache"; }
    $(this).data("key", self.userKey);
    var CompleteTeam = {
      html: function () {
        return $('<div class="teamName-list ' + templatebox + '-teamName-list"><ul class="selector-list ' + templatebox + '-selector-list"></ul><div class="category-list ' + templatebox + '-category-list"></div><div class="selector_hintbox team-hinbox ' + templatebox + '-team-hinbox"></div></div>');
      },
      init: function () {
        var $this = this;
        if (typeof autoComplete !== 'function') {
          console.error('referenceError: $.completeTeam runtime need consturctor autoComplete at path .../Scripts/autoCompleteTeam.js');
          return;
        }
        //初始化结构,员工选择弹出层的初始化位置,部分页面需要判断main定位
        var parent = $("div.main").size() > 0 ? $("div.main") : $(".page-content");
        if (parent.size() > 0) { if (!self.append) { parent.append($this.html()); } else { $(self.append).after($this.html()); } } else { $("body").append($this.html()); }
        this.getUser();//页面初始化绑定
        this.show();
        this.mouse();
        return setting;
      },
      getUser: function () {
        var tem = $(self.outboxitemlist).attr("data-user");
        var data = common.getArray(tem);
        //$(self.outboxitemlist).removeAttr("data-user");
        this.bindData(data);
      },
      bindData: function (array) {
      /*默认显示的员工，以逗号分隔，ul上面绑定data-user,如：data-user="1,370,250"*/
        if (array.length > 0) {
          $.ajax({
            type: "post",
            url: "/Staff/GetSelectStaffs",
            data: {
              PageSize: 6600,
              PageIndex: 1,
              CurrentStaffId: $(self.outboxitemlist).attr("data-user"),
              JobId: $("#jobid").val()
            },
            success: function (result) {
              CompleteTeam.dataTo().data(cache, result);
              
              $.each(array, function (index, value) {
                for (var i = 0; i < result.Data.length; i++) {
                  if (parseInt(value) === parseInt(result.Data[i].StaffId)) {
                    var UserID = result.Data[i].StaffId;
                    var UserName = result.Data[i].StaffName;
                    if (result.Data[i].Enabled) {
                      $(self.box).find(".outbox-tip").hide();
                      $("#addTeam").attr("disabled", "disabled");
                      $(self.outboxitemlist).append('<li data-id="' + UserID + '"><span title="' + UserName + '" class="people">' + UserName + '</span><input type="hidden" name="' + self.userKey + '" value="' + UserID + '"></li>');
                    }
                    else {
                      $(self.box).find(".outbox-tip").hide();
                      $("#addTeam").removeAttr("disabled");
                      $(self.outboxitemlist).append('<li data-id="' + UserID + '"><span title="' + UserName + '" class="people">' + UserName + '</span><label class="cancel"></label><input type="hidden" name="' + self.userKey + '" value="' + UserID + '"></li>');
                    }
                    

                  }
                }
              })
            }, error: function () {
            }
          });

          return true;
        }
        else {
          return false;
        }
      },
      show: function () {
        if ($(self.outboxitemlist).find("li").size() == 0) {
          $(self.box).find(".outbox-tip").show();
          $(self.input).show();
        } else {
          $(self.box).find(".outbox-tip").hide();
        }
        $(self.box).click(function (e) {
          if ($(e.target).closest(".outbox-item-list").size() == 0) {
            $(self.input).css({ "display": "block" }).focus();
          }
        });
      },
      mouse: function () {
        $(self.box).live("mouseenter", function () {
          var mouseEl = this;
          if ($(self.teamListEl).find("*").size() == 0) {
            var datacache = CompleteTeam.dataTo().data(cache);
            if (datacache == null || datacache == "" || datacache == "undefined") {
              let queryData = {
                PageSize: 150,
                PageIndex: 1
              }
              let url = CompleteTeam.getUrl();
              if (url == "/Staff/GetSelectCompany")
                queryData.PageSize = 9999;//旧的写法不合理，不可编辑的文本框就永远选择不了第二页的数据了
              $.ajax({
                type: "post",
                url: url,
                data: queryData,
                success: function (result) {
                  CompleteTeam.dataTo().data(cache, result);
                  new autoComplete(self.input, {
                    //localData: result.Data.Staffs,
                    handleOpts: self.handleOpts,
                    teamList: result.Data,
                    boxId: self.boxId,
                    selectedNum: self.maxCount,
                    fieldName: self.userKey,
                    teamListEl: self.teamListEl,
                    teamListOuterBox: self.teamListOuterBox,
                    teamListSelectBox: self.teamListSelectBox,
                    teamListAddTeam: self.teamListAddTeam,
                    teamListSelTeam: self.teamListSelTeam,
                    beforeselect: self.beforeselect,
                    selectcallback: self.selectcallback,
                    cancelcallback: self.cancelcallback,
                    purgecallback: self.purgecallback,
                    posCallback: self.posCallback,
                  });
                }
              })
              
            }
            else {
              let queryData = {
                PageSize: 150,
                PageIndex: 1
              }
              let url = CompleteTeam.getUrl();
              if (url == "/Staff/GetSelectCompany")
                queryData.PageSize = 9999;//旧的写法不合理，不可编辑的文本框就永远选择不了第二页的数据了
              $.ajax({
                type: "post",
                url: url,
                data: queryData,
                success: function (result) {
                  CompleteTeam.dataTo().data(cache, result)
                  new autoComplete(self.input, {
                    handleOpts: self.handleOpts,
                    teamList: result.Data,
                    boxId: self.boxId,
                    selectedNum: self.maxCount,
                    fieldName: self.userKey,
                    teamListEl: self.teamListEl,
                    teamListOuterBox: self.teamListOuterBox,
                    teamListSelectBox: self.teamListSelectBox,
                    teamListAddTeam: self.teamListAddTeam,
                    teamListSelTeam: self.teamListSelTeam,
                    beforeselect: self.beforeselect,
                    selectcallback: self.selectcallback,
                    cancelcallback: self.cancelcallback,
                    purgecallback: self.purgecallback
                  });

                }
              })
            }
          }
          $(self.box).die("mouseenter");
          Position();
          function Position() {
            var pos = $(self.box).position();
            $(self.fixPos).css({ 'top': pos.top - 3, 'left': pos.left, 'width': self.width, 'position': 'absolute', "z-index": self.zindex });
            self.posCallback && self.posCallback($(self.fixPos));
          }
          $(self.teamListAddTeam, self.teamListSelTeam).click(function (e) {
            Position();
            $(self.input).css({ "display": "block" }).focus();
          });
          $(self.input).focus(function () {
            Position();
          })
        })
      },
      dataTo: function () {
        if (self.datato) {
          return $(_$this);
        }
        else {
          return $(document);
        }
      },
      getUrl: function (index) {

        var selectUrl = [
          "/services/staffactionhandler.ashx?action=getallstaffs&status=0",//所有员工（不包括离职）
          "/services/staffactionhandler.ashx?action=getallstaffs",//所有员工（全部）
          "/services/staffactionhandler.ashx?action=getallstaffs&status=0&company=1",//分公司（不包括离职）
          "/services/staffactionhandler.ashx?action=getallstaffs&company=1",//分公司(全部)
          "/services/staffactionhandler.ashx?action=getallstaffs&status=1",//所有员工(离职)
          "/services/staffactionhandler.ashx?action=getallstaffs&status=1&company=1",//分公司员工(离职)
          "/services/staffactionhandler.ashx?action=gethrhideinfostaffs&markstaffid=" + self.markStaffId//Hr薪资可见性
        ];//按公司查询所有员工

        //新加接口
        var newSelectUrl = [
          '/Staff/GetSelectCompany',  //获取公司信息
          '/Staff/GetSelectCompany',    // 获取公司信息
          '/Staff/GetSelectCompany'    // 获取公司信息
        ];
        
        var url = "";
        if (self.url == null) {
          //url = selectUrl[self.type];
          url = newSelectUrl[self.type];
        }
        else {
          //url = self.url;
          url = newSelectUrl[self.type];
        }
        return url;

      },
      createPanel: function () {
        var ns = this[0].childNodes, len = ns.length == 0;
        if (!len) return;
        var panel = '<div class="outbox selector_outerbox" style="height: 28px; padding-left: 0; width: 232px"><ul class="outbox-item-list" style="float:none;"></ul><input type="text" autocomplete="off" class="selector_input" style="width:100%" placeholder=' + (self.holder || "输入关键字搜索顾问") + '> </div>'
        this.append(panel);
      }
    }
    var common = {
      getArray: function (array) {
        var params = [];
        if (typeof array == "object") {
          if (array.length > 0) {
            params = array;
          }
          else {
            params = [];
          }
        }
        else if (typeof array == "string") {
          $.each(array.split(','), function () { if (!isNaN(this) && !isNaN(parseInt(this))) { params.push(parseInt(this)); } });
        } else if (typeof array == "number") {
          params.push(array);
        }
        var limitArray = [];
        $.each(params, function (index, value) { if (index < self.maxCount) { limitArray.push(this); } });
        return limitArray;
      }
    };
    var setting = {
      setValue: function (array) {
        var params = common.getArray(array);
        //CompleteTeam.bindData(params)
      },
      getIdentifying: function () {
        return self.outboxitemlist;
      },
      getValue: function () {
        var data = [];
        $.each($(self.outboxitemlist).find("li"), function () {
          var input = $(this).find("input"), span = $(this).find("span");
          data.push({ "StaffId": input.val(), "StaffName": span.text(), "StaffKey": input.attr("name") });
        });
        return data;
      }
    };
    CompleteTeam.createPanel.call($(this));
    return CompleteTeam.init();
  });

  $.fn.completeTeamSetValue = (function (array, key) {
    var _t = this;
    var params = [];
    if (typeof array == "object") {
      if (array.length > 0) {
        params = array;
      }
      else {
        params = [];
      }
    }
    else if (typeof array == "string") {
      $.each(array.split(','), function () { if (!isNaN(this)) { params.push(parseInt(this)); } });
    } else if (typeof array == "number") {
      params.push(array);
    }
    if (params.length > 0) {
      var type = $(this).data("type");
      var data = null;
      if (type == null) {
        data = $(this).data("companyteamcache") || null;
      }
      else {
        data = $(this).data("companyteamcache" + type) || null;
      }
      var userKey = key || $(this).data("key") || "staffid";
      if (data == null) {
        $.getJSON("/services/staffactionhandler.ashx?action=getallstaffs").done(function (result) {
          $.each(params, function (index, value) {
            for (var i = 0; i < result.Data.Staffs.length; i++) {
              if (parseInt(value) == parseInt(result.Data.Staffs[i].Id)) {
                var UserID = result.Data.Staffs[i].Id;
                var UserName = result.Data.Staffs[i].Name;
                $(_t).find(".outbox-tip").hide();
                $(_t).find(".outbox-item-list").append('<li data-id="' + UserID + '"><span title="' + UserName + '" class="people">' + UserName + '</span><label class="cancel"></label><input type="hidden" name="' + userKey + '" value="' + UserID + '"></li>').show();
                return true;
              }
            }
          })
        });
      }
      else {
        $.getJSON("/services/staffactionhandler.ashx?action=getallstaffs").done(function (result) {
          $.each(params, function (index, value) {
            for (var i = 0; i < result.Data.Staffs.length; i++) {
              if (parseInt(value) == parseInt(result.Data.Staffs[i].Id)) {
                var UserID = result.Data.Staffs[i].Id;
                var UserName = result.Data.Staffs[i].Name;
                $(_t).find(".outbox-tip").hide();
                $(_t).find(".outbox-item-list").append('<li data-id="' + UserID + '"><span title="' + UserName + '" class="people">' + UserName + '</span><label class="cancel"></label><input type="hidden" name="' + userKey + '" value="' + UserID + '"></li>').show();
                return true;
              }
            }
          })
        });
        //var result = data;
        //$.each(params, function (index, value) {
        //  for (var i = 0; i < result.Data.Staffs.length; i++) {
        //    if (parseInt(value) == parseInt(result.Data.Staffs[i].Id)) {
        //      var UserID = result.Data.Staffs[i].Id;
        //      var UserName = result.Data.Staffs[i].Name;
        //      $(_t).find(".outbox-tip").hide();
        //      $(_t).find(".outbox-item-list").append('<li data-id="' + UserID + '"><span title="' + UserName + '" class="people">' + UserName + '</span><label class="cancel"></label><input type="hidden" name="' + userKey + '" value="' + UserID + '"></li>').show();
        //      return true;
        //    }
        //  }
        //})
      }

    }
    else {
      return false;
    }
  });

  $.fn.completeTeamSetValue2 = (function (array, key) {
    var _t = this;
    var params = [];
    if (typeof array == "object") {
      if (array.length > 0) {
        params = array;
      }
      else {
        params = [];
      }
    }
    else if (typeof array == "string") {
      $.each(array.split(','), function () { if (!isNaN(this)) { params.push(parseInt(this)); } });
    } else if (typeof array == "number") {
      params.push(array);
    }
    if (params.length > 0) {
      var type = $(this).data("type");
      var data = null;
      if (type == null) {
        data = $(this).data("companyteamcache") || null;
      }
      else {
        data = $(this).data("companyteamcache" + type) || null;
      }
      var userKey = key || $(this).data("key") || "staffid";
      if (data == null) {
        $.getJSON("/services/staffactionhandler.ashx?action=getallstaffs").done(function (result) {
          $.each(params, function (index, value) {
            for (var i = 0; i < result.Data.Staffs.length; i++) {
              if (parseInt(value) == parseInt(result.Data.Staffs[i].Id)) {
                var UserID = result.Data.Staffs[i].Id;
                var UserName = result.Data.Staffs[i].Name;
                $(_t).find(".outbox-tip").hide();
                $(_t).find(".outbox-item-list").append('<li data-id="' + UserID + '"><span title="' + UserName + '" class="people">' + UserName + '</span><input type="hidden" name="' + userKey + '" value="' + UserID + '"></li>').show();
                return true;
              }
            }
          })
        });
      }
      else {
        $.getJSON("/services/staffactionhandler.ashx?action=getallstaffs").done(function (result) {
          $.each(params, function (index, value) {
            for (var i = 0; i < result.Data.Staffs.length; i++) {
              if (parseInt(value) == parseInt(result.Data.Staffs[i].Id)) {
                var UserID = result.Data.Staffs[i].Id;
                var UserName = result.Data.Staffs[i].Name;
                $(_t).find(".outbox-tip").hide();
                $(_t).find(".outbox-item-list").append('<li data-id="' + UserID + '"><span title="' + UserName + '" class="people">' + UserName + '</span><label class="cancel"></label><input type="hidden" name="' + userKey + '" value="' + UserID + '"></li>').show();
                return true;
              }
            }
          })
        });
      }

    }
    else {
      return false;
    }
  });

  $.fn.completeTeamDataSetValue = (function (data, key) {
    var _t = this;
    $(this).find(".outbox-tip").hide();
    var userKey = key || $(this).data("key") || "staffid";
    $.each(data, function () {
      if (this.StaffId != null && this.StaffName != null) {
        $(_t).find(".outbox-item-list").append('<li data-id="' + parseInt(this.StaffId) + '"><span title="' + this.StaffName + '" class="people">' + this.StaffName + '</span><label class="cancel"></label><input type="hidden" name="' + userKey + '" value="' + parseInt(this.StaffId) + '"></li>').show();
      }
    });
    $(_t).find(".outbox-tip").hide();
  });

  $.fn.setCheckedNum = (function () {

  })

  $.fn.completeTeamEmpty = (function () {
    var data = [];
    $(els).hide();
    $.each($(this).find(".outbox-item-list").find("li"), function () {
      var input = $(this).find("input"), span = $(this).find("span");
      data.push({ "StaffId": input.val(), "StaffName": span.text(), "StaffKey": input.attr("name") });
      $(this).remove();
    });
    $(this).find(".outbox-tip").show();
    return data;
  });
})(jQuery);
function initTeamSelect(userKey, urlType, Count, selectcallback, cancelcallback, beforeselect, appendEl) {
  return $(".selTeam").not(".selTeam_R_com_on").completeTeam({ type: urlType, userKey: userKey, maxCount: Count || 1, width: 172, selectcallback: selectcallback, cancelcallback: cancelcallback, beforeselect: beforeselect, append: appendEl });
}
