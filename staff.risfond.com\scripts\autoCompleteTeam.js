/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
var timers = null;
var flagCount = 0;
var divHeight = document.createElement("div");
var loading = document.createElement("img");
var autoComplete = (function ($) {
  var opts = {
    localData: null,
    teamList: null,
    listNum: 10,
    boxId: "autocomplete",
    selectedNum: 5,
    fieldName: "assistant",
    teamListEl: '#teamList',
    teamListOuterBox: "#selector_outerbox_0",
    teamListSelectBox: "#selector_hintbox_0",
    teamListAddTeam: "#addTeam",
    teamListSelTeam: "#selTeam",
    beforeselect: function () { return true; },
    selectcallback: function () { },
    cancelcallback: function () { },
    purgecallback: function () { }
  },
    reEscape = new RegExp('(\\' + ['/', '.', '*', '+', '?', '|', '(', ')', '[', ']', '{', '}', '\\'].join('|\\') + ')', 'g');
  function formatResult(value, currentValue) {
    var pattern = currentValue.replace(reEscape, '\\$1');

    return value.replace(new RegExp(pattern, 'gi'), function ($0) {
      return '<span class="highlight">' + $0 + '</span>';
    });
  }
  function Autocomplete(el, params) {
    this.obj = $(el);
    this.params = $.extend({}, opts, params || {});
    this.autocomplete = $(this.params.boxId);
    this.selectedIdx = -1;
    this.result = [];
    this.nomatch = $(this.params.teamListSelectBox);
    this.addBtn = $(this.params.teamListAddTeam);
    this.tips = $j(this.params.teamListSelTeam).find(".outbox-tip");
    this.locked = true;
    this.teamListEl = $(this.params.teamListEl);
    this.teamListOuterBox = $(this.params.teamListOuterBox);
    this.beforeselect = this.params.beforeselect;
    this.selectcallback = this.params.selectcallback;
    this.cancelcallback = this.params.cancelcallback;
    this.purgecallback = this.params.purgecallback;
    this.posCallback = this.params.posCallback;
    this.init();
    this.SelectStaffs('', 1);
  }
  Autocomplete.prototype = {
    init: function () {
      var self = this, auto = this.autocomplete;
      var teamListEl = this.teamListEl;
      var cacheOuterBox = this.teamListOuterBox;
      this.obj.keydown(this.onKeyPress.bind(this));
      this.obj.keyup(this.onKeyUp.bind(this));
      this.obj.blur(this.kill.bind(this));
      this.obj.focus(this.showTip.bind(this, "none", true));
      auto.on("mouseenter", ".result-item", function () {
        var $this = $(this);
        auto.find(".selected").removeClass("selected");
        $this.addClass("selected");
        self.selectedIdx = parseInt($this.attr("id").replace(/[a-z_]/g, ""));
        self.changeStatus();
      });
      auto.on("click", ".result-item", this.select.bind(this));
      if (document.addEventListener) {
        auto[0].addEventListener('DOMMouseScroll', this.scroll.bind(this), false);
      }
      //auto[0].onmousewheel = this.scroll.bind(this);
      $("#selector_outerbox_0").click(function () {
        $(".category-list").hide();

      });
      $("#selector_input_0").focus(function () {
        $(".category-list").hide();
        $(".outbox-tip").hide();
      })
      this.addBtn.click(function (e) {
        //$(".caregory-height:not(:first)").remove();
        e.stopPropagation();
        if (flagCount == 0) {
          self.showAddTeam();
          flagCount++
        }

      });
      //this.scrollTopRoll();


      $(document).click(function () {
        flagCount = 0;
      })

      // 展示公司下的员工
      this.teamListEl.one("click", function () {
        $(this).addClass("ts-unfold");
      }).unbind("click").on("click", "dt", function (e) {
        var $this = $(this), p = $this.parent();
        var companyId = $this.attr("data-companyId");
        self.showCompanyStaff(companyId, companyId);

        if (!p.hasClass("ts-unfold")) {
          p.parent().find("dl").removeClass("ts-unfold");
          p.addClass("ts-unfold");
        } else {
          p.removeClass("ts-unfold");
        }
      });
      this.teamListEl.on("click", "li", function () {
        var $this = $(this);
        self.showTip();
        $this.toggleClass("ct-selected");
        self.toggleItem($this.attr("data-id"), $this.text(), $this, $this.attr("data-company"), $this.attr("data-companyid"));

      });
      this.teamListEl.click(function (e) {
        e.stopPropagation();
      });

      cacheOuterBox.on("click", ".cancel", function (e) {
        var p = $(this).parent(), id = p.attr("data-id"), par = p.parent();
        e.stopPropagation();
        p.remove();
        teamListEl.find("li[data-id=" + id + "]").removeClass("ct-selected");
        !cacheOuterBox.find("li").length && self.showTip("block");
        self.cancelcallback.call(par, id);
        var heights = $("#selector_outerbox_0 .outbox-item-list li").length;
      });
    },
    SelectStaffs: function (KeyWord, pageIndex) {
      var This = this;
      $.ajax({
        type: "post",
        url: "/Staff/GetSelectStaffs",
        data: {
          KeyWord: KeyWord,
          PageSize: 90,
          PageIndex: pageIndex,
          CurrentStaffId: $(self.outboxitemlist).attr("data-user"),
          JobId: $("#jobid").val()
        },
        success: function (result) {
          if (typeof result === 'string' && result.indexOf("用户登录") > -1) {
            beautAlert.done(setRnssLanguage("您目前登录状态已失效，请重新登录"), "hits");
            setTimeout(function () {
              location.reload();
            }, 1500);
            return;
          }
          This.params.localData = result.Data; 
        }
      })
    },
    onKeyPress: function (e) {
      switch (e.keyCode) {
        case 9: //keyTab
          this.disappear(); break;
        case 13: //keyEnter
          this.select();
          e.preventDefault();
          e.stopPropagation();
          this.disappear();
          break;
        case 27: this.disappear(); break;
        case 38: //keyUp
          this.moveUp(); break;
        case 40: //keyDown
          this.moveDown(); break;
      }
    },
    onKeyUp: function (e) {
      
      var v = $.trim(this.obj.val());
      var killCode = this.params.handleOpts.killkeydownNumber;
      if (killCode == e.keyCode) return;
      //keyup keydown
      if (e.keyCode == 38 || e.keyCode == 40 || e.keyCode == 13 || e.keyCode == 27 || e.keyCode == 9) {
        e.preventDefault();
        return;
      }
      if (e.keyCode == 8 && !v) {
        this.disappear();
        if (this.locked) {
          this.locked = false;
          return;
        }
        if (!this.locked) {
          this.teamListOuterBox.find("li:last").remove();
          this.purgecallback.call();
        }
        return;
      }
      v && this.processResult();
    },
    moveUp: function () {
      this.selectedIdx = this.selectedIdx-- ? this.selectedIdx : 0;
      this.activate("up");
    },
    moveDown: function () {
      this.selectedIdx = this.selectedIdx++ >= this.result.length - 1 ? this.result.length - 1 : this.selectedIdx;
      this.activate("down");
    },
    scroll: function (e) {
      var v = "";
      e = e || window.event;
      if (e.preventDefault) {
        e.preventDefault();
      } else {
        e.returnValue = false;
      }
      if (e.wheelDelta) {//IE/Opera/Chrome
        v = e.wheelDelta;
      } else if (e.detail) {//Firefox
        v = e.detail;
      }
      switch (v) {
        case 3: this.moveDown(); break;
        case -3: this.moveUp(); break;
        case 120: this.moveUp(); break;
        case -120: this.moveDown(); break;
      }

    },

    processResult: function () {
      if (!this.params || !this.params.localData) return;
      this.locked = true;
      var i = 0,
        l = this.params.localData.length,
        d = this.params.localData,
        q = $.trim(this.obj.val());
      var r = this.result;
      r.length = 0;
      for (; i < l; i++) {
        r.push(d[i]);
      }
      !r.length && this.disappear();
      this.obj.css("width", q.length * 12);
      this.selectedIdx = 0;
      this.showList();

    },
    showList: function (index) {
      var This = this;
      var q = $.trim(This.obj.val());
      var This = this;
      var results = [];
      clearTimeout(timers);
      timers = setTimeout(function () {
        $.ajax({
          type: "post",
          url: "/Staff/GetSelectStaffs",
          data: {
            KeyWord: q,
            PageSize: 90,
            PageIndex: 1,
            CurrentStaffId: $(self.outboxitemlist).attr("data-user"),
            JobId: $("#jobid").val(),
            CompanyId: $("#StaffSelect_CompanyId").val()??0
          },
          success: function (result) {
            if (typeof result === 'string' && result.indexOf("用户登录") > -1) {
              beautAlert.done(setRnssLanguage("您目前登录状态已失效，请重新登录"), "hits");
              setTimeout(function () {
                location.reload();
              }, 1500);
              return;
            }
            This.params.localData = result.Data;
            results = result.Data;
            
            This.result = result.Data;
            var r = results, l = r.length;

            var html = "<li class='hint' title='" + setRnssLanguage('可以使用鼠标滚轮进行操作') + "'>" + setRnssLanguage("找到{0}个,当前第{1}个").format(r.length, (This.selectedIdx + 1)) + "</li>";
            for (var i = 0; i < l; i++) {
              if (i == 0) {
                html += "<li class='result-item selected' id='selector_listitem" + i + "' data-companyid='" + results[i].CompanyId + "'><div>" + results[i].StaffName + "</div><span class='name-follow' title='" + results[i].CompanyName + "'>" + (results[i].CompanyName || '') + "</span></li>";
              } else {
                html += "<li class='result-item' id='selector_listitem" + i + "' data-companyid='" + results[i].CompanyId + "'><div>" + results[i].StaffName + "</div><span class='name-follow' title='" + results[i].CompanyName + "'>" + (results[i].CompanyName || '') + "</span></li>";
              }
            }
            var staffHeightss = $(".selector-list").height();
            if (r.length) {
              This.autocomplete.html(html).css("display", "block");
              This.nomatch.css("display", "none");
            } else {
              This.nomatch.css("display", "block").html(setRnssLanguage("没有找到匹配的同事"));
            }
          }
        });

      }, 300)
    },
    activate: function (dir) {
      var auto = this.autocomplete, has = $("#selector_listitem" + this.selectedIdx), q = $.trim(this.obj.val());  // formatResult(this.result[this.selectedIdx].StaffName, q)
      var li;
      if (!has.length) li = $("<li class='result-item' id='selector_listitem" + this.selectedIdx + "'><div>" + this.result[this.selectedIdx].StaffName + "</div><span class='name-follow'>" + this.result[this.selectedIdx].CompanyName || '' + "</span></li>").addClass("selected");
      auto.find(".selected").removeClass("selected");
      if (has.length) {
        has.addClass("selected");
      } else if (dir == "down") {
        auto.find(".result-item:first").remove().end().append(li);
      } else if (dir == "up") {
        li.insertAfter(auto.find(".result-item:last").remove().end().find(".hint"));
      }
      this.changeStatus();
    },
    changeStatus: function () {
      this.autocomplete.find(".hint").html(setRnssLanguage("找到{0}个,当前第{1}个").format(this.result.length, (this.selectedIdx + 1)));
    },
    select: function () {
      if (!this.result[this.selectedIdx]) {
        return;
      }
      var selectedValue = this.result[this.selectedIdx].StaffName, id = this.result[this.selectedIdx].StaffId, company = this.result[this.selectedIdx].CompanyName, companyId = this.result[this.selectedIdx].CompanyId;
      if (typeof this.beforeselect == "function") {
        if (!this.beforeselect.call(selectedValue, id, name, company)) {
          return;
        }
      }
      if (this.teamListOuterBox.find("li").size() < this.params.selectedNum) {
        if (!this.isSelected(id)) {
          var li = '<li data-id=' + id + '><span class="people" title="' + selectedValue + '">' + selectedValue + '</span><label class="cancel"></label><input type="hidden" value="' + id + '" name="' + this.params.fieldName + '"></li>';
          this.teamListOuterBox.find("ul").append(li);
          if (this.teamListOuterBox.find("ul>li").length > 0) {
            if (!this.teamListOuterBox.find("ul").parent().find(".outbox-tip").is(":hidden")) {
              this.teamListOuterBox.find("ul").parent().find(".outbox-tip").hide();
            }
          }
        }
        this.selectcallback.call(this, id, selectedValue, company, companyId, this.teamListOuterBox, "input");

      } else {
        beautAlert.done(setRnssLanguage("您最多可以选择{0}位同事").format(this.params.selectedNum), "hits");
      }
      this.obj.val("").css("width", 12);
    },
    disappear: function () {
      this.selectedIdx = -1;
      this.autocomplete.css("display", "none");
      this.nomatch.css("display", "none");
    },
    kill: function () {
      var self = this;
      !this.teamListOuterBox.find("li").length && self.showTip("block");
      this.obj.val("").css("width", 12);
      $(document).one("click", function (e) {
        self.disappear();

      });
    },
    isSelected: function (id) {
      return !!this.teamListOuterBox.find("li").filter("[data-id=" + id + "]").length;
    },
    showAddTeam: function () {
      var list = [], data = this.params.teamList;
      var cacheTeamListEL = this.teamListEl;
      cacheTeamListEL.css("display", "block");
      for (var i = 0, l = data.length; i < l; i++) {
        var item = "<dl class='cf category-each'><dt data-companyId=" + data[i].CompanyId + ">" + data[i].CompanyName + "</dt><dd id=" + data[i].CompanyId + "><ul class='cf'>";
        item += "</ul></dd></dl>";
        list.push(item);
      }
      list = list.join("");
      divHeight.className = "caregory-height";
      divHeight.innerHTML = list;
      cacheTeamListEL.append(divHeight);
      this.hiddenAddTeam();
      this.posCallback && this.posCallback(this.teamListEl.closest(".teamName-list"));

    },
    // 点击展开公司内部的人员hiddenAddTeam
    showCompanyStaff: function (companyId, id) {
      var PageIndex = 1;
      var This = this;
      $.ajax({
        type: "post",
        url: "/Staff/GetSelectStaffs",
        data: {
          PageSize: 2000,
          PageIndex: PageIndex,
          CompanyId: companyId,
          CurrentStaffId: $(self.outboxitemlist).attr("data-user"),
          JobId: $("#jobid").val()
        },
        success: function (result) {
          if (typeof result === 'string' && result.indexOf("用户登录") > -1) {
            beautAlert.done(setRnssLanguage("您目前登录状态已失效，请重新登录"), "hits");
            setTimeout(function () {
              location.reload();
            }, 1500);
            return;
          }
          var list = [];
          for (var j = 0, s = result.Data.length; j < s; j++) {
            var item = '';
            if (This.isSelected(result.Data[j].StaffId)) {

              item += "<li data-id='" + result.Data[j].StaffId + "' class='category-item ct-selected' data-company='" + result.Data[j].CompanyName + "' data-companyid='" + result.Data[j].CompanyId + "' ";
            }
            else {
              item += "<li data-id='" + result.Data[j].StaffId + "' class='category-item' data-company='" + result.Data[j].CompanyName + "' data-companyid='" + result.Data[j].CompanyId + "' ";
            }
            item += "><span>" + result.Data[j].StaffName + "</span></li>";
            list.push(item);
          }
          list = list.join("");

          var $ulList = $(".caregory-height .category-each dd#" + id + " ul");
          if (!(PageIndex == 1 && $ulList.length > 0)) {
            $(".caregory-height .category-each dd#" + id + " ul").append(list);
          }

          This.staffScroll(companyId, id);

          This.hiddenAddTeam();
        }
      });

    },
    // 新增人员滚动
    staffScroll: function (companyId, id) {
      var This = this;
      var ulHeights = $("dd#" + id + " ul").height();
      var categoryListHight = $(".category-list").height();
      var staffScrollTop = $("dd#" + id + " ul").offset().top;
      var PageIndexs = 1;

      function isSelected(id) {
        return !!This.teamListOuterBox.find("li").filter("[data-id=" + id + "]").length;
      };
      isbool = false;
      //var loading = document.createElement("img");
      loading.src = "../images/loading.gif";
      loading.style.marginLeft = 62 + 'px';
      if (!isbool) {
        $(".caregory-height .category-each dd#" + id + " ul").append(loading);
      }

      if ($(".caregory-height .category-each dd#" + id + " ul li").length == 0) {
        $.ajax({
          type: "post",
          url: "/Staff/GetSelectStaffs",
          data: {
            PageSize: 2000,
            PageIndex: 1,
            CompanyId: companyId,
            CurrentStaffId: $(self.outboxitemlist).attr("data-user"),
            JobId: $("#jobid").val()
          },
          success: function (result) {
            
            $(loading).remove();
            if (typeof result === 'string' && result.indexOf("用户登录") > -1) {
              beautAlert.done(setRnssLanguage("您目前登录状态已失效，请重新登录"), "hits");
              setTimeout(function () {
                location.reload();
              }, 1500);
              return;
            }
            isbool = true;
            var list = [];
            for (var j = 0, s = result.Data.length; j < s; j++) {
              var item = '';

              if (isSelected(result.Data[j].StaffId)) {

                item += "<li data-id='" + result.Data[j].StaffId + "' class='category-item ct-selected' data-company='" + result.Data[j].CompanyName + "' data-companyid='" + result.Data[j].CompanyId + "' ";
              }
              else {
                item += "<li data-id='" + result.Data[j].StaffId + "' class='category-item' data-company='" + result.Data[j].CompanyName + "' data-companyid='" + result.Data[j].CompanyId + "' ";
              }
              item += "><span>" + result.Data[j].StaffName + "</span></li>";
              list.push(item);
            }
            list = list.join("");
            $(".caregory-height .category-each dd#" + id + " ul").append(list);

            This.hiddenAddTeam();
          }
        });
      } else {
        $(loading).remove();
        isbool = true;
        This.hiddenAddTeam();
      }
    },
    hiddenAddTeam: function () {
      var cacheTeamListEL = this.teamListEl;
      $j("body").bind("click.team", function (e) {
        cacheTeamListEL.css("display", "none");
        $j("body").unbind("click.team");
      });

    },
    toggleItem: function (id, value, li, company, companyid) {
      if (this.isSelected(id)) {
        this.teamListOuterBox.find("li").filter("[data-id=" + id + "]").remove();
        this.cancelcallback.call(this.teamListOuterBox, id);
      } else {
        if (this.teamListOuterBox.find("li").size() < this.params.selectedNum) {
          if (typeof this.beforeselect == "function") {
            if (!this.beforeselect.call(this, id, value, company, companyid, this.teamListOuterBox)) {
              return;
            }
          }
          this.teamListOuterBox.find("ul").append('<li data-id=' + id + '><span class="people" title="' + value + '">' + value + '</span><label class="cancel"></label><input type="hidden" value="' + id + '" name="' + this.params.fieldName + '"></li>');
          this.selectcallback.call(this, id, value, company, companyid, this.teamListOuterBox, "select");


        } else {
          beautAlert.done(setRnssLanguage("您最多可以选择{0}位同事").format(this.params.selectedNum), "hits");
          li.removeClass("ct-selected");
        }
      }
    },
    showTip: function (action, flag) {
      this.tips.css("display", action || "none");
      flagCount = 0;
      if (flag) {
        $(".category-list").hide();
      }
    },
    clear: function () {
      $('.category-item .ct-selected').removeClass('ct-selected');
      $('.category-list').hide();
      $('.selector-list').hide();
      $('.outbox-tip').show();
      $('.outbox-item-list').html('');
    }
  };
  return Autocomplete;
})(jQuery);

