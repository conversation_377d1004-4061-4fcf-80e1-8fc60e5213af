# RISFOND RNSS V3.0 - 瑞丰德永才华招聘管理系统

基于原始 staff.risfond.com 网站重新设计和开发的现代化招聘管理系统，完全还原了原始网站的功能布局和设计风格。

## 🌟 项目特色

### 完全还原原始设计
- **精确复制**：基于 staff.risfond.com 的真实网站结构
- **原汁原味**：保持了原始网站的色彩搭配和布局风格
- **功能完整**：实现了所有主要功能模块和交互效果

### 核心功能模块

#### 1. 用户信息面板
- 个人头像和基本信息展示
- 实时业绩数据统计（业绩目标、完成进度、未完成、年度回款等）
- 年度推荐、面试、Offer、入职数据展示

#### 2. 事项提醒系统
- 本月/下月事项切换
- 实时提醒列表展示
- 可点击跳转到相关联系人、职位、客户

#### 3. 快捷入口模块
- 可展开/收起的功能入口网格
- 8个常用功能快捷访问
- 包括考勤、年假、报销、节日福利、业绩统计等

#### 4. 实时排行榜
- **实时回款榜**：显示最新回款信息、金额、支付单位、服务顾问
- **实时推荐榜**：显示推荐状态、人选信息、职位、年薪、推荐人

#### 5. 四季换肤功能
- 春夏秋冬四季主题切换
- 自动根据当前月份设置季节主题
- 背景渐变和装饰元素随季节变化

#### 6. 完整导航系统
- 顶部导航栏：Logo、换肤、搜索、消息、日程、用户菜单
- 左侧菜单：客户管理、职位管理、人选管理、信息管理等
- 多级菜单展开/收起功能

## 🚀 技术架构

### 前端技术栈
- **HTML5** + **CSS3** + **JavaScript (ES6+)**
- **Vue.js 2.6.14** - 响应式数据绑定和组件化
- **Element UI** - UI组件库（下拉菜单、单选框等）
- **jQuery 3.6.0** - DOM操作和事件处理
- **Bootstrap 5.1.3** - 响应式布局框架
- **Font Awesome 6.0** - 图标库

### 设计特色
- **原始色彩方案**：主色调 #2A7FCC（瑞丰蓝）
- **渐变背景**：四季主题渐变色彩
- **卡片式布局**：现代化的卡片设计
- **响应式设计**：完美适配桌面端和移动端

## 📁 项目结构

```
招聘网站/
├── index.html                 # 主页面文件
├── assets/                    # 静态资源目录
│   ├── css/
│   │   ├── main.css          # 主要样式文件
│   │   └── indexbase.css     # 首页专用样式
│   ├── js/
│   │   └── main.js           # 主要JavaScript文件
│   └── images/               # 图片资源目录
├── staff.risfond.com/        # 原始网站参考文件
└── README.md                 # 项目说明文档
```

## 🎯 功能实现详情

### 1. 用户信息展示
```javascript
// 业绩数据展示
personYj: 100,      // 业绩目标（万）
personJd: 68,       // 完成进度（%）
personWwc: 32,      // 未完成（万）
personHk: 85,       // 年度回款（万）
personTj: 156,      // 年度推荐（个）
personMs: 89,       // 年度面试（个）
personOffer: 23,    // 年度Offer（个）
personRz: 12        // 年度入职（个）
```

### 2. 四季换肤系统
```javascript
// 自动季节检测
const currentMonth = today.getMonth() + 1;
switch (currentMonth) {
  case 3,4,5: 春季主题
  case 6,7,8: 夏季主题  
  case 9,10,11: 秋季主题
  case 12,1,2: 冬季主题
}
```

### 3. 实时数据更新
- Vue.js 响应式数据绑定
- 模拟真实API数据结构
- 支持实时数据刷新和更新

## 🔧 安装和使用

### 直接使用
1. 下载项目文件到本地
2. 双击 `index.html` 文件
3. 在浏览器中查看效果

### 本地服务器（推荐）
```bash
# 使用Python启动本地服务器
python -m http.server 8000

# 或使用Node.js
npx http-server

# 然后访问 http://localhost:8000
```

## 🎨 界面展示

### 主要特色
- **顶部导航**：瑞丰蓝渐变背景，包含Logo、换肤、搜索、通知等功能
- **用户信息面板**：左侧显示用户头像、姓名、公司，右侧展示详细业绩数据
- **事项提醒**：右侧面板显示本月/下月待办事项
- **快捷入口**：可展开的功能网格，提供常用功能快速访问
- **实时排行榜**：并列显示回款榜和推荐榜，实时更新数据

### 四季主题
- **春季**：清新绿色渐变 (#a8e6cf → #dcedc1)
- **夏季**：温暖橙色渐变 (#ffd3a5 → #fd9853)
- **秋季**：金黄色渐变 (#ffeaa7 → #fab1a0)
- **冬季**：清冷蓝色渐变 (#74b9ff → #0984e3)

## 📱 响应式支持

- **桌面端** (>768px): 完整功能展示
- **移动端** (≤768px): 
  - 侧边栏折叠为抽屉式菜单
  - 搜索框隐藏，节省空间
  - 布局自动调整为单列显示
  - 触摸友好的交互设计

## 🔮 技术亮点

### Vue.js 组件化
- 顶部导航独立Vue实例
- 主内容区域独立Vue实例
- 数据响应式更新
- 组件间通信

### CSS 高级特性
- CSS Grid 和 Flexbox 布局
- CSS 变量和自定义属性
- 渐变背景和动画效果
- 响应式媒体查询

### JavaScript ES6+
- 模块化代码组织
- Promise 和 async/await
- 箭头函数和解构赋值
- 现代DOM操作

## 🛠 自定义配置

### 修改主题色彩
在 `assets/css/main.css` 中修改：
```css
/* 主色调 */
#2A7FCC → 你的颜色

/* 四季主题色彩 */
.content-wrap-chun { background: 你的春季渐变; }
.content-wrap-xia { background: 你的夏季渐变; }
.content-wrap-qiu { background: 你的秋季渐变; }
.content-wrap-dong { background: 你的冬季渐变; }
```

### 添加新功能
1. 在 `assets/js/main.js` 中添加Vue方法
2. 在 `assets/css/indexbase.css` 中添加样式
3. 在Vue模板中添加HTML结构

## 📞 技术支持

本项目完全基于原始 RISFOND 网站重新开发，保持了原有的设计风格和功能特色。

## 📄 版权说明

本项目仅供学习和参考使用，基于原始 staff.risfond.com 网站进行重新实现。

---

**开发时间**: 2025年6月  
**版本**: v2.0.0 (基于原始网站重构版)  
**状态**: 功能完整，持续优化中  
**技术栈**: Vue.js + Element UI + Bootstrap + jQuery
