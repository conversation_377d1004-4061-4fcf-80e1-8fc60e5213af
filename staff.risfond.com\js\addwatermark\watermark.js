/**
  * 是否禁止F12
  */
var ConsoleManager = {
    onOpen: function () {
        alert("Console is opened")
    },
    onClose: function () {
        alert("Console is closed")
    },
    init: function () {
        var self = this;
        var x = document.createElement('div');
        var isOpening = false, isOpened = false;
        Object.defineProperty(x, 'id', {
            get: function () {
                if (!isOpening) {
                    self.onOpen();
                    isOpening = true;
                }
                isOpened = true;
            }
        });
        setInterval(function () {
            isOpened = false;
            console.info(x);
            console.clear();
            if (!isOpened && isOpening) {
                self.onClose();
                isOpening = false;
            }
        }, 200)
    }
}

ConsoleManager.onOpen = function () {
    
    try {
        window.open('http://staff.risfond.com/', target = '_self');
    } catch (err) {
        var a = document.createElement("button");
        a.onclick = function () {
            window.open('http://staff.risfond.com/', target = '_self');
        }
        a.click();
    }
}
ConsoleManager.onClose = function () {
    alert("Console is closed!!!!!")
}
var _waterItem = 'body';
/**
  * 文字加图片水印效果
  * 对应参数 { 员工编号 员工姓名 归属公司 时间 , 是否禁用F12 } 
  */

function watermark() {
  var tipsArr = Array.from(arguments);
    // 是否禁用F12
    var prohibitF12 = tipsArr.indexOf(true);
    if (prohibitF12 !== -1) {
        tipsArr.splice(prohibitF12, 1);
        ConsoleManager.init();
    }
    var maxWidth = document.documentElement.offsetWidth;
    var maxHeight = document.documentElement.offsetHeight;
    var intervalWidth = 350;                                 // 间隔宽度
    var intervalheight = 200;                                 // 间隔高度
    var rowNumber = (maxWidth - 110) / intervalWidth;        // 横向个数
    var coumnNumber = maxHeight / intervalheight;             // 纵向个数
    coumnNumber = Math.ceil(coumnNumber);
    //默认设置
    var defaultSettings = {
        watermark_color: '#aaa',       // 水印字体颜色
        watermark_alpha: 0.13,        // 水印透明度
        watermark_fontsize: '12px',  // 水印字体大小
        watermark_font: '微软雅黑',  // 水印字体
        watermark_width: 200,         // 水印宽度
        watermark_height: 'auto',     // 水印长度
        watermark_angle: 15            // 水印倾斜度数
  };

    // var _temp = document.createDocumentFragment();

    // 屏幕中心的小范围的
    var _temp = document.createElement("div");
    $(_temp).css("width", '1200px');
    $(_temp).css("height", maxHeight + 'px');
    $(_temp).css("overflow", 'hidden');
    $(_temp).css("margin", '0 auto');
    $(_temp).css("position", 'absolute');
    $(_temp).css("left", '50%');
    $(_temp).css("top", '77px');
    $(_temp).css("marginLeft", '-600px');
    $(_temp).css("pointerEvents", 'none');   // pointer-events:none  让水印不阻止交互事件

    for (var i = 0; i < rowNumber; i++) {
        for (var j = 0; j <= coumnNumber; j++) {
            var x = intervalWidth * i + 20;
            var y = intervalheight * j + 30;
            var mark_div = document.createElement('div');
            var temporarySpan;
            var temporaryText
            // mark_div.id = 'mark_div' + i + j;
            // mark_div.className = 'mark_div';
            for (var tips = 0; tips < tipsArr.length; tips++) {
                temporarySpan = document.createElement("div");
                temporaryText = tipsArr[tips];
                temporarySpan.appendChild(document.createTextNode(temporaryText));
                mark_div.appendChild(temporarySpan);
            }
            //设置水印div倾斜显示
            mark_div.style.webkitTransform = "rotate(" + defaultSettings.watermark_angle + "deg)";
            mark_div.style.MozTransform = "rotate(" + defaultSettings.watermark_angle + "deg)";
            mark_div.style.msTransform = "rotate(" + defaultSettings.watermark_angle + "deg)";
            mark_div.style.OTransform = "rotate(" + defaultSettings.watermark_angle + "deg)";
            mark_div.style.transform = "rotate(" + defaultSettings.watermark_angle + "deg)";
            mark_div.style.visibility = "";
            mark_div.style.position = "absolute";
            mark_div.style.left = x + 'px';
            mark_div.style.top = y + 'px';
            mark_div.style.overflow = "hidden";
            mark_div.style.zIndex = "10000";
            mark_div.style.pointerEvents = 'none';  // pointer-events:none  让水印不阻止交互事件
            mark_div.style.opacity = defaultSettings.watermark_alpha;
            mark_div.style.fontSize = defaultSettings.watermark_fontsize;
            mark_div.style.fontFamily = defaultSettings.watermark_font;
            mark_div.style.color = defaultSettings.watermark_color;
            mark_div.style.textAlign = "center";
            mark_div.style.width = defaultSettings.watermark_width + 'px';
            mark_div.style.height = defaultSettings.watermark_height;
            mark_div.style.display = "block";
            _temp.appendChild(mark_div);
        }
    }
    document.body.appendChild(_temp);
}

/**
  * 文字加图片水印效果 
  * dom
  * 客情关系--对应参数 { 员工编号 员工姓名 归属公司 时间 , 是否禁用F12 } 
  * 简历报告--对应参数 { 员工编号 员工姓名 归属公司 人选名称 时间 邮箱 , 是否禁用F12 }
  */
function watermarkNew() {
  var tipsArr = Array.from(arguments);
  var _width = 1000;
  // 是否禁用F12
  var prohibitF12 = tipsArr.indexOf(true);
  if (prohibitF12 !== -1) {
    tipsArr.splice(prohibitF12, 1);
    ConsoleManager.init();
  }
  var container = document.body;
  if (_waterItem != 'body' && _waterItem != '') {
    container = document.getElementById('' + _waterItem +'');
  }
  
  var maxWidth = document.documentElement.offsetWidth;
  var maxHeight = container.offsetHeight;
  var intervalWidth = 420;                                 // 间隔宽度
  var intervalheight = 300;                                 // 间隔高度
  var rowNumber = (maxWidth - 110) / intervalWidth;        // 横向个数
  if (_waterItem == 'newResumeReportWrap') {
    _width = 1030;
    intervalWidth = 500;
    intervalheight = 400;
    rowNumber = 1;
  } else if (_waterItem == 'clientWrap') {
    _width = 1080;
  }
  var coumnNumber = maxHeight / intervalheight;             // 纵向个数
  coumnNumber = Math.ceil(coumnNumber);

  // 屏幕中心的小范围的
  var _temp = document.createElement("div");
  $(_temp).addClass('temp-mark-wrap');
  for (var i = 0; i < rowNumber; i++) {
    for (var j = 0; j <= coumnNumber; j++) {
      var x = intervalWidth * i + 20;
      var y = intervalheight * j + 30;
      if (_waterItem == 'newResumeReportWrap') {
        x = (_width / 2) - (intervalWidth / 2);
      }
      var mark_div = document.createElement('div');
      $(mark_div).addClass('temp-mark-item');
      var temporarySpan;
      var temporaryText
      for (var tips = 0; tips < tipsArr.length; tips++) {
        temporarySpan = document.createElement("div");
        temporaryText = tipsArr[tips];
        temporarySpan.appendChild(document.createTextNode(temporaryText));
        mark_div.appendChild(temporarySpan);
      }
      mark_div.style.left = x + 'px';
      mark_div.style.top = y + 'px';
      mark_div.style.display = "block";
      _temp.appendChild(mark_div);
    }
  }
  
  container.appendChild(_temp);
}

