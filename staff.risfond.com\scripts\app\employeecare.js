//员工关怀
var birthdayData = [];
var mts;
//获取接口数据
function getDataBirthday(mt) {
  mts = mt;
  $.post("/staff/GetStaffsBirthdayList", { }, function (result) {
    if (result.Data != null) {
      birthdayData = result.Data;
      recordcount = birthdayData.length;
      var html = buildCenterHtml();
      boxHtml.find(".care_hnp_con").append(html);
      $("body").append(boxHtml);
      setMask();
      mask = $("#home_notice_mask_box");
      panel = $("#home_care_panel");
      box = $("#home_care_box");
      mask.fadeIn("slow");
      box.fadeIn("slow", function () {
        setEvent();
        setPager();
      });
    } else { //没有员工关怀数据时
    mts();
    }
  }).error(function (er) {
    console.log(er.status + ": " + er.responseText);
  });
}
  var page = 0, pagesize = 1, recordcount = 0, pagedata = null,
    dataId = 0,
    tId = 0/*记录当前Id*/;

var mask = $("#home_notice_mask_box"),
    panel = $("#home_care_panel"),
    box = $("#home_care_box");
  
  var boxHtml = buildBoxHtml();
  
  
  //向页面中添加遮罩
  function setMask() {
    if ($("#home_notice_mask_box").length > 0) {
      mask.css('display', 'block')
      box.css('display', 'block')
      return;
    };
    /*var html = '<div id="home_notice_mask_box" class="home_notice_mask_box"></div><div class="yah"><img src="/images/employeeCare/ivsv1-1zz8e1920.gif"/></div>';*/
    var html = '<div id="home_notice_mask_box" class="home_notice_mask_box"></div>';
    $("body").append(html);
  }
 
  //生成html
  function buildBoxHtml() {
    var _div = $('<div id="home_care_box" class="home_care_box"></div>');
    _div.append(
      '<div id="home_care_panel" class="home_care_panel">' +
      '<a href="javascript:;" class="care_hn_page_btn hn_prev"><img src="/images/employeeCare/left.png" /></a>' +
      '<a href="javascript:;" class="care_hn_page_btn hn_next"><img src="/images/employeeCare/right.png" /></a>' +
      '<div class= "care_hnp_con">' +
      
      '</div >' +
      
      '<div class="care_hnp_box care_hnp_one"></div>' +
      '<div class="care_hnp_box care_hnp_two"></div>' +
      '<div class="care_hnp_box care_hnp_three"></div>' +
      '<div class="care_hnp_box care_hnp_four"></div>' +
      '<div class="care_hnp_box care_hnp_five"></div>' +
      '<div class="care_hnp_box care_hnp_six"></div>' +
      '</div >' +
     '<div class="hn_pager"><span class="hn_p_yx"></span>&frasl;<span class="hn_p_ym"></span></div>' 
    );
    return _div;
  }
  //生成中间内容部分
  function buildCenterHtml() {
    var html = '',
      d = birthdayData[page],
      data = d;
    html += '<div class="care_hnp_dangao_box">';
    html += '<a href="javascript:;" class="care_hnp_close"><img src="/images/employeeCare/close.png" alt="关闭" /></a>';
    html += '<div class="care_hnp_guang"></div>';
    html += '<div class="care_hnp_dangao">';
    html += '<img src="/images/employeeCare/' + setSX(data.ChineseZodiac) +'.png" alt="蛋糕" />';
    html += '</div>';
    html += '</div>';
    html += '<div class="girl-card-body-wrap">';
    html += '<div class="girl-card-head-wrap">';
    html += '<div class="girl-card-head-mao">';
    html += '<img class="girl-card-mao-img" src="/images/employeeCare/limao.png" alt="生日帽" />';
    html += '</div>';
    html += '<div class="girl-card-head-tou">';
    html += '<img class="girl-card-tou-img" src="' + data.Url +'" alt="用户头像" />';
    html += '</div>';
    html += '</div>';
    html += '<div class="girl-card-title-wrap">';
    html += '<p class="girl-card-title-name care-red-color">Dear~' + data.Name + '：</p>';
    html += '<p class="girl-card-title-name care-red-color">今天是你在锐仕方达过的第' + getValueToTxt(whichData, data.Num) + '个生日</p>';
    html += '</div>';
    html += '<div class="girl-card-content-wrap">';
    if (data.Num <= 1) {
      html += '<p class="girl-which-year-txt">我在此向你表达最深切的祝福，在过去的' + getValueToTxt(whichData, data.Num) + '年中，你展现出了令人瞩目的才华和出色的工作表现，你的工作态度和努力让我们深感钦佩。生日是一个新的起点，一个展望未来的机会。我相信你的才华和潜力，你的未来将充满无限可能，愿新的一岁带给你幸福和成功。</p>';
    } else if (data.Num >= 2 && data.Num < 5) {
      html += '<p class="girl-which-year-txt">在你⽣⽇这个特别的⽇⼦⾥，我代表全体员⼯向你献上最诚挚的祝福。你的存在让我们变的更加强⼤，团队中，出⾊的专业和技能我们有⽬共睹。你的贡献为公司带来了显著的成果，已经成为不可或缺的中坚⼒量。在新的⼀年⾥，希望你能够继续追求卓越，超越⾃我。</p>';
    } else if (data.Num >= 5) {
      html += '<p class="girl-which-year-txt">在过去的' + getValueToTxt(whichData, data.Num) + '年⾥，你展现出了令⼈敬佩的毅⼒和坚持不懈的精神，已经成为我们团队的重要⽀柱。' + getValueToTxt(whichData, d.Num) + '年⾥你克服了⽆数的困难和挑战，不断突破⾃我变得更加强⼤和优秀。坚持和努⼒的你，是我们团队的榜样。你为公司的发展做出了重要贡献，你的成就是我们共同的骄傲。</p>';
    }
    html += '</div>';
    html += '<p class="card-time-box">' + data.Birthdate + '</p>';
    if (data.Gender == 1) {
      html += '<div class="girl-card-kt-wrap"><img class="girl-card-kt-gif" src="/images/employeeCare/girl_right.gif" alt="点赞" /></div>';
    } else {
      html += '<div class="boy-card-kt-wrap"><img class="girl-card-kt-gif" src="/images/employeeCare/boy_right.gif" alt="点赞" /></div>';
    }
    html += ' </div>';
    return html;
  }

  

  //生效返回
  function setSX(remainder) {
    let zodiac = '', imgUrl = '';
    switch (remainder) {
      case 0:
        zodiac = "猴";
        imgUrl = 'hou';
        break;
      case 1:
        zodiac = "鸡";
        imgUrl = 'ji';
        break;
      case 2:
        zodiac = "狗";
        imgUrl = 'gou';
        break;
      case 3:
        zodiac = "猪";
        imgUrl = 'zhu';
        break;
      case 4:
        zodiac = "鼠";
        imgUrl = 'shu';
        break;
      case 5:
        zodiac = "牛";
        imgUrl = 'niu';
        break;
      case 6:
        zodiac = "虎";
        imgUrl = 'hu';
        break;
      case 7:
        zodiac = "兔";
        imgUrl = 'tu';
        break;
      case 8:
        zodiac = "龙";
        imgUrl = 'long';
        break;
      case 9:
        zodiac = "蛇";
        imgUrl = 'she';
        break;
      case 10:
        zodiac = "马";
        imgUrl = 'ma';
        break;
      case 11:
        zodiac = "羊";
        imgUrl = 'yang';
        break;
      default:
        zodiac = "未知";
        imgUrl = '';
        break;
    }
    return imgUrl;
  }
  //职位-根据下拉框的值匹配文本
  var whichData = [ //生日对应集合
    { num: 1, txt: '一' },
    { num: 2, txt: '二' },
    { num: 3, txt: '三' },
    { num: 4, txt: '四' },
    { num: 5, txt: '五' },
    { num: 6, txt: '六' },
    { num: 7, txt: '七' },
    { num: 8, txt: '八' },
    { num: 9, txt: '九' },
    { num: 10, txt: '十' },
    { num: 11, txt: '十一' },
    { num: 12, txt: '十二' },
    { num: 13, txt: '十三' },
    { num: 14, txt: '十四' },
    { num: 15, txt: '十五' },
    { num: 16, txt: '十六' },
    { num: 17, txt: '十七' },
    { num: 18, txt: '十八' },
    { num: 19, txt: '十九' },
    { num: 20, txt: '二十' },
    { num: 21, txt: '二十一' },
    { num: 22, txt: '二十二' },
    { num: 23, txt: '二十三' },
    { num: 24, txt: '二十四' },
    { num: 25, txt: '二十五' },
    { num: 26, txt: '二十六' },
    { num: 27, txt: '二十七' },
    { num: 28, txt: '二十八' },
    { num: 29, txt: '二十九' },
    { num: 30, txt: '三十' },
  ];
  function getValueToTxt(arr, dicKey) {
    var whichDay;
    arr.find(item => {
      if (item.num == dicKey) {
        whichDay = item.txt;
      }
    })
    return whichDay;
}
  //设置翻页
  function setPager() {
    var btnprev = box.find(".hn_prev"),
      btnnext = box.find(".hn_next"),
      p = page + 1;
    box.find(".hn_p_ym").html(recordcount);
    box.find(".hn_p_yx").html(p);
    if (p >= recordcount && !btnnext.hasClass("nogo")) {
      btnnext.addClass("nogo");
    }
    else if (p < recordcount) {
      btnnext.removeClass("nogo");
    }
    if (page <= 0 && !btnprev.hasClass("nogo")) {
      btnprev.addClass("nogo");
    }
    else if (page > 0) {
      btnprev.removeClass("nogo");
    }
  }
  //
  function setPageData() {
    var data = birthdayData[page], p = page + 1;
    var html = buildCenterHtml();
    /*panel.stop(true, false).fadeOut(300, function () {*/
      panel.find(".care_hnp_con").html(html);
    /*});*/
    setPager();
  }
  //绑定切换/关闭事件
  function setEvent() {
    box.find(".care_hn_page_btn").click(function () {
      if ($(this).hasClass("nogo")) return;
      if ($(this).hasClass("hn_prev")) {//前一条
        page -= 1;
      }
      else if ($(this).hasClass("hn_next")) {//后一条
        page += 1;
      }
      setPageData();
    });
    $(document).on("click", ".care_hnp_close", function () {
      mask.fadeOut("slow", function () {
        mask.hide();
      });
      box.fadeOut("slow", function () {
        box.hide();
      });
      page = 0;
      pagesize = 1;
      recordcount = 0;
      birthdayData = null;
      mts();
    });
  }
