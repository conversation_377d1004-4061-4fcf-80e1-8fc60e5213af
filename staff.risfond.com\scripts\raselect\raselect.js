/// <reference path="../jquery-1.8.2.min.js" />
/// <reference path="/static/r-easyui/r.easyui.js" />
/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
/**
 * 用途：是用来选择职位的弹出框
 * 依赖：[r.easyui.js, 职位基础数据: /data.occupation2.js]
 * 文档：http://192.168.30.10:8888/index.php?s=/9&page_id=2099
 */
function r_AdvancedSelect(opts) {
  if (!$.fn.combobox) {
    var errMsg = 'raselect.js: 依赖文件(r.easyui.js)不存在，请重新刷新页面, 或者检查依赖文件/static/r-easyui/r.easyui.js是否存在';
    throw new Error(errMsg);
  }
  this.version = "v1.0.2";
  // 外部数据源配置 Modified by Allen.sun on 2020/05/16
  this.requestOpts = opts.requestOpts || {};
  // 切换数据源配置 Modified by Allen.sun on 2020/05/16
  this.initType = opts.initType || "default";
  this.data = opts.data;
  this.id = Math.randomId();
  this.valueField = opts.valueField || "Id";
  this.textField = opts.textField || "Text";
  this.searchFileld = opts.searchFileld || "label";
  this.parentidFileld = opts.parentidFileld || "ParentId";
  this.Hierarchy = opts.Hierarchy || "Hierarchy";
  this.title = opts.title || "请选择职位类别";
  this.CheckedNum = opts.CheckedNum || 3;
  //this.html = $('<div class="xz_parent2" id=' + this.id + '><div class="xz_top">&nbsp;</div><div class="xz_cent"><div class="biaoti"><div class="bt_left">' + opts.title + '</div><div class="bt_right"><input type="button" value="' + setRnssLanguage("取消") + '" name="button_none" class="button_none"><input type="button" value="' + setRnssLanguage("确定") + '" name="button_sure"  class="button_sure"></div></div><div class="checked-area"></div><div class="txtcompanel"><span class="txtcomtip">' + setRnssLanguage("在选项中搜索：") + '</span><input class="txtcombox" /></div><div class="the_cont_rong cf"><div class="cont_top"><ul class="list-wrap"></ul></div><div class="cont_bot"></div></div></div><div class="xz_bot">&nbsp;</div></div>');
  this.html = $([
    '<div class="k-dialog__wrapper k-dialog--flex" id="' + this.id + '" style="overflow:auto">',//wrapper Start
    '<div class="k-dialog job-share-modal">',//dialog主体 Start
    '<div class="k-dialog__header">',//Header Start
    ' <span class="k-dialog__title">' + this.title + '</span> ',
    //'<a class="k-dialog__header-btn"> <i class="k-dialog__icon fa fa-close"></i> </a> ',
    '<a class="k-dialog__header-btn"> <img src="/scripts/raselect/images/close.png" alt="" /> </a> ',
    //'<div class="txtcompanel"><span class="txtcomtip">' + setRnssLanguage("在选项中搜索：") + '</span><input class="txtcombox" /></div>',//combobox
    '<div class="txtcompanel"><input class="txtcombox" placeholder="在选项中搜索" /></div>',//combobox
    '</div>',//Header End
    '<div class="k-dialog__body">',//Body Start
    '<div class="checked-area p2 cf">',//选中的项目 Start
    '<em class="k-tip">最多选择<span class="k-tip-num">' + this.CheckedNum + '</span>个</em>',
    '</div>',//选中的项目 End
    '<div class="job-type-selector-container">',//内容部分 Start
    '<div class="job-type-selector--item"><ul class="job-type-selector--item--first"></ul></div>',
    '<div class="job-type-selector--item"><ul class="job-type-selector--item--second"></ul></div>',
    '<div class="job-type-selector--item"><ul class="job-type-selector--item--third"></ul></div>',
    '</div>',//内容部分 End

    '</div>',//Body End
    '<div class="k-dialog__footer"> <a href="javascript:;" class="k-button button_none">取消</a> <a href="javascript:;" class="k-button is-primary button_sure">确定</a> </div>',//Footer
    '</div>',//dialog主体 End
    '</div>'//wrapper End
  ].join(""));
  //this.width = opts.width || 750;
  //this.html.css("width", this.width);
  this.checkArea = this.html.find(".checked-area");
  this.companel = this.html.find(".txtcompanel");
  this.txtcombox = this.companel.find(".txtcombox");
  this.comIsHide = opts.comIsHide || false;
  //this.comWidth = opts.comWidth || 280;
  //this.comPanelHeight = opts.comPanelHeight || 158;
  this.comHeight = opts.comHeight || 34;
  this.sureCallBack = opts.sureCallBack || function () { };
  this.hideCallBack = opts.hideCallBack || function () { };
  this.rootDisabled = opts.rootDisabled || false;//父项不可选
  this.selectCallBack = opts.selectCallBack || function (el) { };
  this.getRowIndex = opts.getRowIndex || function (target, value, valueField, searchFileld) {
    var state = $.data(target, 'combobox');
    var opts = state.options;
    var data = state.data;
    var val = value ? $.trim(value) : "";
    for (var i = 0; i < data.length; i++) {
      var d = data[i][valueField] + "";
      if (d && val && (d.toLowerCase() == val.toLowerCase() || data[i][searchFileld].toLowerCase().indexOf(val.toLowerCase()) != -1)) {
        return i;
      }
    }
    return -1;
  };
  this.showmeassage = opts.showmeassage || function (txt) {
    alert(txt);
  };
  this.selList = [];//已经选中的项
  this.typeMap = {
    default: function () {
      this.init();
    },
    interface: function () {
      var _this = this;
      $.ajax({
        url: this.requestOpts.url,
        data: this.requestOpts.data || {},
        method: this.requestOpts.requestType || "post",
        success: function (res) {
          _this.data = _this.tileData(res.Data);
          _this.init();
          _this.requestOpts.success && _this.requestOpts.success(res);
        }
      })
    }
  }
  this._switchInitType()
}

r_AdvancedSelect.prototype = {
  // 切换数据源 Modified by Allen.sun on 2020/05/16
  _switchInitType: function () {
    var typeFn = this.typeMap[this.initType];
    if (!typeFn) {
      console.error("[raSelect]: So far, only two modes have been defined: default and interface. see: http://192.168.30.10:8888/index.php?s=/9&page_id=2099");
      return
    }
    this.typeMap[this.initType].call(this);
  },
  // 引用原有的树形数据平铺 Modified by Allen.sun on 2020/05/16
  tileData: function (datas) {
    if (!datas || !datas.length) {
      return [];
    }
    var list = [];
    datas.forEach(function (item, index) {
      item.Hierarchy = 1;
      if (item.Childens && item.Childens.length>0) {
        item.Childens.forEach(function (item2, index2) {
          item2.Hierarchy = 2;
          if (item2.Childens && item2.Childens.length) {
            item2.Childens.forEach(function (item3, index3) {
              item3.Hierarchy = 3;
            });
          }
        });
      }
    });
    var buildData = function (item) {
      var obj = {};
      for (var i in item) {
        if (i == "Childens") {
          continue;
        }
        if (i == "Id") {
          obj[i] = item[i] + "";
        }
        else if (i == "ParentId" && item[i] == 0) {
          obj[i] = "";
        }
        else if (i == "ParentId") {
          obj[i] = item[i] + "";
        }
        else {
          obj[i] = item[i];
        }
      }
      //obj.Hierarchy = sum;
      return obj;
    };
    var Sum = 0;
    var flat = function (nodes, sum) {
      if (!nodes || nodes.length === 0) return [];
      nodes.forEach(function (node) {
        list.push(buildData(node));
        return flat(node.Childens);
      });
    };
    flat(datas);
    return list;
  },

  builddata: function (dt) {
    var da = [], self = this;
    if (dt) {
      $.each(dt, function (i, n) {
        if (!n[self.parentidFileld]) {
          var d = n;
          $.each(dt, function (j, k) {
            if (k[self.parentidFileld] && k[self.parentidFileld] == d[self.valueField]) {
              if (!d.children) {
                d.children = [];
              }
              d.children.push(k);
            }
          });
          da.push(d);
        }
      });
    }
    return da;
  },
  getdatabyid: function (dt, id) {
    var len = dt.length, _data = dt, self = this;
    for (var i in _data) {
      var d = _data[i];
      if (d[self.valueField] && d[self.valueField] == id) {
        return d;
      }
    }
    return "";
  },
    buildcomboxdata: function (dt) {
        //if (dt == null)
        //    return null;
    var dt2 = dt.concat();
    if (dt2 && dt2.length > 0) {
      dt2.sort(function (a, b) {
        return a.Quanpin > b.Quanpin ? 1 : -1;
      });
    }
    var d = [], _data = $.extend(true, {}, dt2), self = this;
    $.each(_data, function (i, n) {
      var _d = n;
      // 2021年6月7日 添加判断条件
      if(_d.Source == 'System') {
        _d[self.searchFileld] = _d.Quanpin + " " + _d.Shoupin + " " + _d[self.textField];
        var distxt = _d[self.textField] + "（" + _d['Shoupin'] + "）";
        if (_d[self.parentidFileld]) {
          var parent = self.getdatabyid(dt, _d[self.parentidFileld]);
          distxt = _d[self.textField] + "（" + _d['Shoupin'] + "）" + parent[self.textField];
        }
        _d.displayText = distxt;
        _d[self.Hierarchy] == 3 && d.push(_d);
      }
      
    });
    return d;
  },
  init: function () {
    var ul = this.html.find(".job-type-selector--item--first");
    var ret = [], self = this;
    var newdata = $.extend(true, {}, self.data);
    var data = self.builddata(newdata);
    for (var i = 0, l = data.length; i < l; i++) {
      var item = data[i];
      item.Source == 'System' && ret.push('<li data-label="' + item.Text + '" data-value="' + item.Id + '" class=""> <span>' + item.Text + '</span><i class="fa fa-angle-right"></i></li>');
    };
    ret = ret.join("");
    ul.html(ret);
    this.inputs = ul.find("li");
    this.html.find(".button_none").click(this.hidden.bind(this));
    this.html.find(".k-dialog__header-btn").click(this.hidden.bind(this));
    //在这里修改-----触发点击
    this.html.find(".job-type-selector--item--first>li").click(function () {
      var $this = $(this);
      self.html.find(".job-type-selector--item--third").html("");
      self.setSelItemHtml($this, "job-type-selector--item--second", "fa fa-angle-right");
    });
    this.html.on("click", ".job-type-selector--item--second>li", function () {
      var $this = $(this);
      self.setSelItemHtml($this, "job-type-selector--item--third", "fa fa-check");
      self.selCurrentListBySecond();
    });
    this.html.on("click", ".job-type-selector--item--third>li", function () {
      self.appendSelect($(this));
    });
    this.setRaSel();
    
    this.html.find(".button_sure").click(function () {
      self.sureCallBack.call(self, self.selList);
    });
    //this.checkArea.on("click", ".cancel-item", this.clearSelect.bind(this));
    this.checkArea.on("click", ".cancel-item", function (e) {
      self.clearSelect.call(self, $(this).parent());
    });
    if (!self.comIsHide) {
      var newdata2 = $.extend(true, {}, self.data);
      self.companel.show();
      self.txtcombox.combobox({
        valueField: self.valueField,
        textField: 'displayText',//self.textField,
        height: self.comHeight,
        //panelHeight: self.comHeight,
        //prompt: "在选项中搜索",
        //width: self.comWidth,
        //textWidth: self.comWidth,
        ////panelWidth: self.comWidth,
        //panelMinWidth: self.comWidth - 100,
        //panelMaxWidth: self.comWidth + 100,
        //panelHeight: self.comPanelHeight,
        //panelHeight: 30,
        data: self.buildcomboxdata(self.data),
        formatter: function (row) {
          var opts = $(this).combobox('options');
          return row['displayText'];
        },
        selectOnNavigation: false,//键盘上下时是否执行select
        finder: {
          getEl: function (target, val) {
            var index = self.getRowIndex(target, val, self.valueField, self.searchFileld);
            var id = $.data(target, 'combobox').itemIdPrefix + '_' + index;
            return $('#' + id);
          },
          getRow: function (target, p) {
            var state = $.data(target, 'combobox');
            var index = (p instanceof jQuery) ? p.attr('id').substr(state.itemIdPrefix.length + 1) : self.getRowIndex(target, p, self.valueField, self.searchFileld);
            return state.data[parseInt(index)];
          }
        },
        filter: function (q, row) {
          var opts = $(this).combobox('options');
          // 2021年5月31日16:21:58
          if(row.Source != "System") {return false}
          return row[self.textField].toLowerCase().indexOf(q.toLowerCase()) == 0 || row[self.searchFileld].toLowerCase().indexOf(q.toLowerCase()) != -1;
        },
        onSelect: function (a, b, c) {
          self.showmeassage(setRnssLanguage("您最多可选择{0}项").format(self.CheckedNum));
          if (self.rootDisabled && !a[self.parentidFileld]) {
            self.showmeassage(setRnssLanguage("父级不可选"));
            $(this).combobox("setValues", []);
            return;
          }
          if (self.checkArea.find(".ca_item").length < self.CheckedNum) {
            self.select([a[self.valueField]]);
          }
          else {
            self.showmeassage(setRnssLanguage("您最多可选择{0}项").format(self.CheckedNum));
          }
          $(this).combobox("setValues", []);
        }
      });
    }
    else {
      self.companel.hide();
    }
    if (this.html.find(".textbox-text").length) {
      this.html.find(".textbox-text").attr("placeholder", "在选项中搜索");
    }
  },
  setRaSel: function () {
    this.html.find(".job-type-selector--item--first>li").eq(0).triggerHandler("click");
    this.html.find(".job-type-selector--item--second>li").eq(0).click();
    //this.html.find(".job-type-selector--item--third>li").eq(0).click();
  },
  ra_Alert: function (txt) {
    if (window.r_Layout_BeautAlert) {
      r_Layout_BeautAlert.done(txt);
      return;
    }
    else if (window.beautAlert) {
      beautAlert.done(txt, "hits", 1500);
      return;
    }
    alert(txt);
  },
  setSelItemHtml: function (el, selClass, iconClass) {
    var self = this;
    var $this = $(el),
      id = $this.data("value"),
      text = $this.data("label"),
      list = self.data,
      box = self.html.find("." + selClass),
      res = [];
    $this.siblings("li").removeClass("job-type-selector--item__selected");
    if ($this.hasClass("job-type-selector--item__selected")) {
      return;
    }
    $this.addClass("job-type-selector--item__selected");
    var nList = list.filter(function (item) {
      return item[self.parentidFileld] == id;
    });
    if (nList && nList.length) {
      nList.forEach(function (item, index) {
        res.push('<li data-label="' + item.Text + '" data-value="' + item.Id + '" class=""> <span>' + item.Text + '</span> <i class="' + iconClass + '"></i> </li>');
      });
      box.html(res.join(""));
    }
  },
  //根据选中项集合，设置当前项选中状态
  selCurrentList: function () {
    var self = this,
      selList = self.selList,
      first = self.html.find(".job-type-selector--item--first"),
      second = self.html.find(".job-type-selector--item--second"),
      third = self.html.find(".job-type-selector--item--third");
    if (selList && selList.length) {
      var f3 = selList[selList.length - 1];
      f3 = self.data.filter(function (item) {
        return item.Id == f3[self.valueField];
      });
      f3 && f3.length && (f3 = f3[0]);
      var f2 = self.data.filter(function (item) {
        return item.Id == f3[self.parentidFileld];
      });
      var f1;
      if (f2 && f2.length) {
        f2 = f2[0];
        f1 = self.data.filter(function (item) {
          return item.Id == f2[self.parentidFileld];
        });
        f1 && f1.length && (f1 = f1[0]);
      }
      if (f1 && f1.Id && (!first.find("li.job-type-selector--item__selected").length || first.find("li.job-type-selector--item__selected").data("value") != f1.Id)) {
        first.find("li[data-value=" + f1.Id + "]").triggerHandler("click");
      }
      if (f2 && f2.Id && (!second.find("li.job-type-selector--item__selected").length || second.find("li.job-type-selector--item__selected").data("value") != f2.Id)) {
        second.find("li[data-value=" + f2.Id + "]").click();
      }
    }
    var Li = self.html.find(".job-type-selector--item--third>li");
    Li.removeClass("job-type-selector--item__selected");
    selList.forEach(function (item, index) {
      var l = Li.filter("[data-value=" + item.Id + "]");
      if (l.length) {
        if (!l.hasClass("job-type-selector--item__selected")) {
          l.addClass("job-type-selector--item__selected");
        }
      }
    });
  },
  selCurrentListBySecond: function () {
    var self = this,
      selList = self.selList,
      first = self.html.find(".job-type-selector--item--first"),
      second = self.html.find(".job-type-selector--item--second"),
      third = self.html.find(".job-type-selector--item--third");
    var Li = self.html.find(".job-type-selector--item--third>li");
    Li.removeClass("job-type-selector--item__selected");
    selList.forEach(function (item, index) {
      var l = Li.filter("[data-value=" + item.Id + "]");
      if (l.length) {
        if (!l.hasClass("job-type-selector--item__selected")) {
          l.addClass("job-type-selector--item__selected");
        }
      }
    });
  },
  changeCheckArea: function () {
    var self = this,
      selList = self.selList,
      res = [];
    //if (self.CheckedNum == 1) {
    //  return;
    //}
    this.checkArea.find(".ca_item").remove();
    selList.forEach(function (item, index) {
      res.push('<span class="ca_item" data-value="' + item.Id + '" data-text="' + item.Text + '">' + item.Text + '<label class="cancel-item"></label></span>');
    });
    this.checkArea.prepend(res.join(""));
  },
  select: function (values) { //传值，选中某个或多个，可以是加，的字符串，整数，或者数组
    if (!values || !values.length) {
      return;
    }
    var el,
      flag = typeof values[0] == "object" ? true : false,
      self = this;
    var selList = self.selList,
      selLen = selList.length;
    var l = values.length > this.CheckedNum ? this.CheckedNum : values.length;
    if (flag) {

      for (var i = 0; i < l; i++) {
        if (
          !values[i] ||
          !values[i][self.valueField] ||
          !values[i][self.textField] ||
          !$.trim(values[i][self.valueField]) ||
          !$.trim(values[i][self.textField])
        ) {
          return;
        }
        if (self.CheckedNum == 1) {
          self.selList = [values[i]];
        }
        else if (selLen < self.CheckedNum) {
          selList.push(values[i]);
        }
        //this.checkArea.append("<span value='" + values[i][self.valueField] + "' text='" + el.attr("text") + "'>" + values[i][self.textField] + "<label class='cancel-item'></label></span>");
      }
      self.changeCheckArea();
      self.selCurrentList();
    } else {
      var arr = [];
      for (var i = 0; i < l; i++) {
        //el = self.html.find(".job-type-selector--item--third>li[data-value=" + values[i] + "]");
        //el.triggerHandler("click");
        var vs = self.data.filter(function (item) {
          ////console.log(item.Id, item.Text);
          ////console.log(values[i]);
          //console.log(item.Id == values[i] && item);
          ////console.log("___________________________________");
          return item.Id == values[i];
        });
  
        if (vs && vs.length) {
          arr = arr.concat(vs);
        }
      }
      self.select(arr);
    }
    //l == this.CheckedNum && this.html.find(".list-wrap input").not(":checked").prop("checked", false);
    //this.selectCallBack.call(this, values);
  },
  show: function () {
    if (!$("#" + this.id).length) { this.html.appendTo(document.body); }
    centerEl2(this.html);
  },
  hidden: function () {
    this.html.css("display", "none");
    $("#bgWrap").length && $("#bgWrap").fadeOut("300");
    this.clear();
    this.hideCallBack.call(self);
  },
  checkChildren: function (values, flag) {
    var el;
    flag = flag !== null ? flag : true;
    values = typeof values == "string" ? [values] : values;
    for (var i = 0; i < values.length; i++) {
      el = this.inputs.filter("[value=" + values[i] + "]");
      el.siblings("ul").length && el.siblings("ul").find("input").prop("checked", flag);
    }
  },
  clear: function () {
    this.selList = [];
    this.selCurrentList();
    this.changeCheckArea();
  },
  setCheckedNum: function (num) {
    this.CheckedNum = num || 3;
    this.html.find(".k-tip-num").text(this.CheckedNum);
  },
  setSureCallBack: function (fn) {
    this.sureCallBack = fn;
  },
  setHideCallBack: function (fn) {
    this.hideCallBack = fn;
  },
  setSelectCallBack: function (fn) {
    this.selectCallBack = fn;
  },
  appendSelect: function (el) {
    var self = this,
      $this = $(el);
    if ($this.hasClass("job-type-selector--item__selected")) {
      if (self.CheckedNum == 1) {
        return;
      }
      else {
        this.clearSelect(el);
      }
    }
    else {
      if (self.CheckedNum == 1) {
        self.clear();
      }
      else if (self.selList.length == self.CheckedNum) {
        self.ra_Alert("只能选择 " + self.CheckedNum + " 项");
        return;
      }
      var id = $this.data("value");
      var obj = this.data.filter(function (item) {
        return item.Id == id;
      });
      this.select(obj);
    }
    this.selectCallBack.call(this, el);
  },
  clearSelect: function (el) {
    el = $(el);
    var self = this,
      id = el.data("value"),
      selList = self.selList,
      i = -1;
    selList.forEach(function (item, index) {
      if (item.Id == id) {
        i = index;
      }
    });
    if (i != -1) {
      self.selList.splice(i, 1);
    }
    self.changeCheckArea();
    self.selCurrentList();
  }
}