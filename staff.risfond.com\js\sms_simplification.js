/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
function getSmsData(el) {
	var name = $(el).attr("data-name"), number = $(el).attr("data-number"), type = $(el).attr("data-type");
	var username="";
	if (name != ""
		&& name != null
		&& name != "undefined") {
		username = name + setRnssLanguage("，您好");
	}
	else {
		username = setRnssLanguage("您好");
	}
	var opts = {
		DataContent: username,
		DataNumber: [{ info: name, number: number }],
		DataType: type
	};
	return opts;
	//var par= $.extend({}, opts, params || {});
	//$(this).SmsOperate(par).Show();
}