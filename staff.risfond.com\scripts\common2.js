///// <reference path="jquery-2.1.4.min.js" />
/* Array几个Api的兼容处理 Start */
if (!Array.prototype.map) {
  Array.prototype.map = function (callback, thisArg) {
    var T, A, k;
    if (this == null) {
      throw new TypeError(" this is null or not defined");
    }
    // 1. 将O赋值为调用map方法的数组.
    var O = Object(this);
    // 2.将len赋值为数组O的长度.
    var len = O.length >>> 0;
    // 3.如果callback不是函数,则抛出TypeError异常.
    if (Object.prototype.toString.call(callback) != "[object Function]") {
      throw new TypeError(callback + " is not a function");
      // 4. 如果参数thisArg有值,则将T赋值为thisArg;否则T为undefined.
      if (thisArg) {
        T = thisArg;
      }
      // 5. 创建新数组A,长度为原数组O长度len
      A = new Array(len);
      // 6. 将k赋值为0
      k = 0;
      // 7. 当 k < len 时,执行循环.
      while (k < len) {
        var kValue, mappedValue;
        //遍历O,k为原数组索引
        if (k in O) {
          //kValue为索引k对应的值.
          kValue = O[k];
          // 执行callback,this指向T,参数有三个.分别是kValue:值,k:索引,O:原数组.
          mappedValue = callback.call(T, kValue, k, O);
          // 返回值添加到新数组A中.
          A[k] = mappedValue;
        }
        // k自增1
        k++;
      }
      // 8. 返回新数组A
      return A;
    };
  }
}
if (!Array.prototype.forEach) {
  Array.prototype.forEach = function (callback, thisArg) {
    var T, k;
    if (this == null) {
      throw new TypeError(' this is null or not defined');
    }
    var O = Object(this);
    var len = O.length >>> 0;
    if (typeof callback !== "function") {
      throw new TypeError(callback + ' is not a function');
      if (arguments.length > 1) {
        T = thisArg;
      }
      k = 0;
      while (k < len) {
        var kValue;
        if (k in O) {
          kValue = O[k];
          callback.call(T, kValue, k, O);
        }
        k++;
      }
    };
  }
}
if (!Array.prototype.filter) {
  Array.prototype.filter = function (fun /*, thisArg */) {
    "use strict";
    if (this === void 0 || this === null)
      throw new TypeError();
    var t = Object(this);
    var len = t.length >>> 0;
    if (typeof fun !== "function")
      throw new TypeError();
    var res = [];
    var thisArg = arguments.length >= 2 ? arguments[1] : void 0;
    for (var i = 0; i < len; i++) {
      if (i in t) {
        var val = t[i];
        if (fun.call(thisArg, val, i, t))
          res.push(val);
      }
    }
    return res;
  };
}
if (!Array.prototype.find) {
  Object.defineProperty(Array.prototype, 'find', {
    value: function (predicate) {
      if (this == null) {
        throw new TypeError('"this" is null or not defined');
      }
      var o = Object(this);
      var len = o.length >>> 0;
      if (typeof predicate !== 'function') {
        throw new TypeError('predicate must be a function');
      }
      var thisArg = arguments[1];
      var k = 0;
      while (k < len) {
        var kValue = o[k];
        if (predicate.call(thisArg, kValue, k, o)) {
          return kValue;
        }
        k++;
      }
      return undefined;
    }
  });
}
/* Array几个Api的兼容处理 End */
/* 公用cookie操作类 Start */
var risfondCommonInfo = {};
risfondCommonInfo.setCookie = function (c_name, value, expiredays) {
  var exdate = new Date();
  exdate.setDate(exdate.getDate() + expiredays);
  document.cookie = c_name + "=" + escape(value) +
    ((expiredays == null) ? "" : ";expires=" + exdate.toGMTString());
}
risfondCommonInfo.getCookie = function (c_name) {
  if (document.cookie.length > 0) {
    var c_start = document.cookie.indexOf(c_name + "=");
    if (c_start != -1) {
      c_start = c_start + c_name.length + 1;
      var c_end = document.cookie.indexOf(";", c_start);
      if (c_end == -1) c_end = document.cookie.length;
      return unescape(document.cookie.substring(c_start, c_end));
    }
  }
  return "";
}
/* 公用cookie操作类 End */
function ajaxPost(url, data, callback, errorcallback, beforeSend) {
  var result = $.ajax({
    url: url,
    dataType: 'json',   //返回的数据类型
    type: 'POST',
    data: data,//参数
    async: false,//false为同步，true为异步，同步的数据会使页面锁住并且之后的JS也不会执行，一直等待到数据加载完成才解锁,本次操作必须要同步得到数据才能去执行是否隐藏
    success: callback || function () { },
    error: errorcallback || function (xhr, et) {
      if (et && et == "timeout") {
        //beautAlert.done("服务器连接超时", "hits", 1000);
        alert(setRnssLanguage("服务器连接超时"));
      }
      else if (xhr.status == "500") {
        alert(setRnssLanguage("服务器错误，稍后重试！"));
      }
      else {
        alert(xhr.responseText);
      }
      if (waitingLayer) {
        waitingLayer.hide();
      }
    },
    beforeSend: beforeSend
  });
  return result;
}
function ajaxPost2(url, data, dataType, callback, errorcallback, beforeSend) {
  var result = $.ajax({
    url: url,
    dataType: dataType || 'json',   //返回的数据类型
    type: 'POST',
    data: data,//参数
    async: false,//false为同步，true为异步，同步的数据会使页面锁住并且之后的JS也不会执行，一直等待到数据加载完成才解锁,本次操作必须要同步得到数据才能去执行是否隐藏
    success: callback || function () { },
    error: errorcallback || function (xhr, et) {
      if (et && et == "timeout") {
        //beautAlert.done("服务器连接超时", "hits", 1000);
        alert(setRnssLanguage("服务器连接超时"));
      }
      else if (xhr.status == "500") {
        alert(setRnssLanguage("服务器错误，稍后重试！"));
      }
      else {
        alert(xhr.responseText);
      }
      if (waitingLayer) {
        waitingLayer.hide();
      }
    },
    beforeSend: beforeSend
  });
  return result;
}
function ajaxPost3(url, data, dataType, callback, errorcallback, beforeSend) {
  var result = $.ajax({
    url: url,
    dataType: dataType || 'json',   //返回的数据类型
    type: 'POST',
    data: data,//参数
    async: false,//false为同步，true为异步，同步的数据会使页面锁住并且之后的JS也不会执行，一直等待到数据加载完成才解锁,本次操作必须要同步得到数据才能去执行是否隐藏
    success: callback || function () { },
    error: errorcallback || function (xhr, et) {
      if (et && et == "timeout") {
        //beautAlert.done("服务器连接超时", "hits", 1000);
        r_Layout_BeautAlert.done(setRnssLanguage("服务器连接超时"));
      }
      else if (xhr.status == "500") {
        r_Layout_BeautAlert.done(setRnssLanguage("服务器错误，稍后重试！"));
      }
      else {
        r_Layout_BeautAlert.done(xhr.responseText);
      }
      if (waitingLayer) {
        waitingLayer.hide();
      }
    },
    beforeSend: beforeSend
  });
  return result;
}
function rnss_event_wscroll() {
  var wh = window.screen.height > 0 ? window.screen.height : $("body").height();
  var gotop = $("#gotop");
  var scrollbox = jQuery("body").scrollTop() > 0 ? jQuery("body") : jQuery("html");
  scrollbox.on("scrollstop", function (event) {
    scrollbox = jQuery("body").scrollTop() > 0 ? jQuery("body") : jQuery("html");
    var bs = scrollbox.scrollTop();
    var bh = scrollbox.height();
    if (bs > bh / 3) {
      gotop.show();
    }
    else {
      gotop.hide();
    }
  });
  gotop.click(function () {
    var scrollbox = jQuery("body").scrollTop() > 0 ? jQuery("body") : jQuery("html");
    scrollbox.animate({ scrollTop: 0 }, 300);
    $(this).hide();
  });
}
function rnss_submitselectform(name, val) {
  var form = $("#searchform");
  if (typeof (val) == "object") {
    form.find("input[name=" + name + "]").remove();
    for (var i in val) {
      var d = val[i];
      var input = form.find("input[name=" + name + "]");
      form.append("<input type='hidden' name='" + name + "' value='" + d.Id + "' />");
    }
  }
  else {
    var input = form.find("input[name=" + name + "]");
    if (input.length > 0) {
      input.val(val);
    }
    else {
      form.append("<input type='hidden' name='" + name + "' value='" + val + "' />");
    }
  }
  $.each(form.find("input[type=hidden]"), function () {
    if ($(this).val() == "") {
      $(this).remove();
    }
  });
  form.submit();
}
function checkALL2(params) {
  var opts =
    {
      AllButton: "#checkAll",
      ChildButton: ".input-radio",
      ParentBox: ".list-item-3"
    }
  this.params = $.extend({}, opts, params || {});
  var self = this.params;
  $(self.AllButton).on("click", function () {
    $(self.ParentBox).find(self.ChildButton).not(":disabled").prop("checked", $(this).prop('checked'));
    //$(self.ParentBox).find(self.ChildButton).not(":disabled").trigger("click");//引用的模板中会对input做样式上的优化，直接修改checked不可行
    var checked = $(this).prop("checked"), items = $(self.ParentBox).find(self.ChildButton).not(":disabled");
    items.prop("checked", checked);
    items.each(function (i, n) {
      if (checked === true && 　!$(this).parent().hasClass("checked")) {
        $(this).parent().addClass("checked");
      }
      else if (checked === false && $(this).parent().hasClass("checked")) {
        $(this).parent().removeClass("checked");
      }
    });
  });
  $(self.ParentBox).on("click", self.ChildButton, function () {
    if ($(self.ParentBox).find(self.ChildButton).filter(":checked").not(":disabled").length == $(self.ParentBox).find(self.ChildButton).not(":disabled").length) {
      $(self.AllButton).prop("checked", true).parent().addClass("checked");
    } else {
      $(self.AllButton).prop("checked", false).parent().removeClass("checked");
    }
  });
}

var r_Layout_BeautAlert = (function () {
  var panel = $('#Layout_BeautAlert');
  var d = {
    done: function (msg, type, time, callback) {
      //if (type == 'hits' || type == "") {
      //  if (msg.indexOf('成功') > -1 && msg.indexOf('不成功') == -1) {
      //    time = time;
      //  } else {
      //    time = null;
      //  }
      //}
      var t = setRnssLanguage("提示");
      panel.find(".modal-title").text(t);
      panel.find(".modal-body").html(msg);
      panel.modal('show');
      if (time && time > 0) {
        this.hide(time, callback)
      }
      else if (callback && typeof callback == "function") {
        callback();
      }
    },
    //对于done来说，为了保持更好的可扩展性，特地增加下面这个，以后由于需求增加而不得不扩展字段，可以写到options中
    tips: function (msg, options, callback) {
      var params = {
        title: '提示', 
        time: 0,
      };
      $.extend(true, params, options);
      var t = params.title;
      panel.find(".modal-title").text(t);
      panel.find(".modal-body").html(msg);
      panel.modal('show');
      if (params.time && params.time > 0) {
        this.hide(params.time, callback)
      }
      else if (callback && typeof callback == "function") {
        callback();
      }
    },
    hide: function (time, callback) {
      setTimeout(function () {
        panel.modal("hide");
        if (callback && typeof callback == "function") {
          callback();
        }
      }, time);
    }
  };
  return d;
})(jQuery);
var r_Layout_BlockUI = function () {
  var bpanel = "#page-content";
  return {
    show: function (panel, message) {
      var _p = panel || bpanel,
        _message = message || "";
      App && App.blockUI({
        target: _p,
        boxed: true,
        message: _message
      });
    },
    hide: function (panel) {
      var _p = panel || bpanel;
      App && App.unblockUI(_p);
    },
    //第二种方式，全页面锁定
    show2: function (panel, message) {
      var _message = message || "";
      App.blockUI({
        target: "",
        boxed: true,
        message: _message
      });
    },
    hide2: function () {
      App.unblockUI();
    }
  };
}();
var r_Layout_SetModalPosition = function (panel) {
  function centerModals() {
    panel.each(function (i) {
      var $clone = $(this).clone().css('display', 'block').appendTo('body'),
        top = Math.round(($clone.height() - $clone.find('.modal-content').height()) / 2);
      top = top > 0 ? top : 0;
      $clone.remove();
      $(this).find('.modal-content').css("margin-top", top);
    });
  }
  panel.on('show.bs.modal', centerModals);
  $(window).on('resize', centerModals);
};

/* 工具Model
 * 1，r_Tools.r_Confirm 确认框
 * 2，r_Tools.isPhoneAvailable 全局统一手机号码验证
 * 3，r_Tools.isIdCardNo 全局统一身份证号码验证
 * */
var r_Tools = function () {
  function r_Confirm(params) {
    var opts = {
      titleTxt: "确认信息",
      Txt: "确认吗？",
      txtIsHtml: false,
      submitTxt: '确定',
      isShowCancelBtn: true,//是否显示取消按钮
      successCallbcak: function () { console.log("确认了"); },
      cancelCallback: function () { console.log("取消了"); }
    };
    var isSure = false;
    params = $.extend({}, opts, params || {});
    var panel = $("#Layout_Confirm");
    panel.find(".modal-title").text(params.titleTxt);
    panel.find(".btn_Save").text(params.submitTxt);
    if (!params.txtIsHtml) {
      panel.find(".modal-body").text(params.Txt);
    } else {
      panel.find(".modal-body").html(params.Txt);
    }
    if (!params.isShowCancelBtn) {
      panel.find(".btn_Cancel").hide();
    }
    else {
      panel.find(".btn_Cancel").show();
    }
    panel.find(".btn_Save").off("click").on("click", function () {
      isSure = true;
      panel.modal("hide");
      params.successCallbcak();
    });
    panel.off("hidden.bs.modal").on("hidden.bs.modal", function () {
      if (!isSure) {
        params.cancelCallback();
      }
    });
    panel.modal("show");
  }
  function isPhoneAvailable(phone) {
    /* 判断是否为手机号 */
    //var myreg = /^[1][3,4,5,7,8, 9][0-9]{9}$/;
    var myreg = /^[1][0-9]{10}$/;
    if (!myreg.test(phone)) {
      return false;
    }
    return true;
  }
  //验证身份证的函数 Start
  function isDate6(sDate) {
    if (!/^[0-9]{6}$/.test(sDate)) {
      return false;
    }
    var year, month, day;
    year = sDate.substring(0, 4);
    month = sDate.substring(4, 6);
    if (year < 1700 || year > 2500) return false;
    if (month < 1 || month > 12) return false;
    return true;
  }
  function isDate8(sDate) {
    if (!/^[0-9]{8}$/.test(sDate)) {
      return false;
    }
    var year, month, day;
    year = sDate.substring(0, 4);
    month = sDate.substring(4, 6);
    day = sDate.substring(6, 8);
    var iaMonthDays = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    if (year < 1700 || year > 2500) return false;
    if (((year % 4 == 0) && (year % 100 != 0)) || (year % 400 == 0)) iaMonthDays[1] = 29;
    if (month < 1 || month > 12) return false;
    if (day < 1 || day > iaMonthDays[month - 1]) return false;
    return true;
  }
  function isIdCardNo(num) {
    var factorArr = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1);
    var parityBit = new Array("1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2");
    var varArray = new Array();
    var intValue;
    var lngProduct = 0;
    var intCheckDigit;
    var intStrLen = num.length;
    var idNumber = num;
    // initialize
    if ((intStrLen != 15) && (intStrLen != 18)) {
      return false;
    }
    // check and set value
    for (i = 0; i < intStrLen; i++) {
      varArray[i] = idNumber.charAt(i);
      if ((varArray[i] < '0' || varArray[i] > '9') && (i != 17)) {
        return false;
      } else if (i < 17) {
        varArray[i] = varArray[i] * factorArr[i];
      }
    }

    if (intStrLen == 18) {
      //check date
      var date8 = idNumber.substring(6, 14);
      if (isDate8(date8) == false) {
        return false;
      }
      // calculate the sum of the products
      for (i = 0; i < 17; i++) {
        lngProduct = lngProduct + varArray[i];
      }
      // calculate the check digit
      intCheckDigit = parityBit[lngProduct % 11];
      // check last digit
      if (varArray[17] != intCheckDigit) {
        return false;
      }
    }
    else {        //length is 15
      //check date
      var date6 = idNumber.substring(6, 12);
      if (isDate6(date6) == false) {
        return false;
      }
    }
    return true;
  }
  //验证身份证的函数 End
  return {
    init: function () {

    },
    r_Confirm: r_Confirm,
    isPhoneAvailable: isPhoneAvailable,
    isIdCardNo: isIdCardNo
  };
}();

var rightMenuFunc = function () {
  var $container = $(".right-menu-container");
  $container.removeClass("loading");
  if (window.innerWidth < 1366) {
    $container.addClass("is-fold");
    $(".right-expand-btn").click(function () {
      $container.addClass("show-panel");
      return false;
    });

    $(document).on("click", "*", function () {
      if ($(this).closest(".not-collapse-right-menu").length) {
        return false;
      }
      $container.removeClass("show-panel");
    });
  }
}

/* 登录后内部客户创建提醒 Start */
var r_UserLogin_M = function () {
  function setMask() {//向页面中添加遮罩
    if ($("#r_userlogin_m").length > 0) return;
    var html = '<div id="r_userlogin_m" class="home_notice_mask"></div>';
    $("body").append(html);
  }
  function buildBoxHtml() {
    var html = [
      '<div id="r_userlogin_box" class="home_notice_box r_userlogin_box">',//1 start
      '<div id="" class="home_notice_panel">',//1.1 start
      '<a href="javascript:;" class="hnp_close">×</a>',
      '<div class="hnp_con">',//1.1.2 start
      '<h2 class="rul_title">温馨提示</h2>',
      '<div class="rul_con">',
      '为进行内部招聘，您需要创建一个内部招聘专用客户，客户名称尽量使用营业执照上的名称，客户合作成员添加招聘相关负责人，注意开独立运作权限后才可发布职位。',
      //'具体请阅读<a href="">《内部招聘规章制度》</a>。',
      '</div>',
      '<br /><br />',
      '<div class="rul_bbox"><a class="rul_btn" href="/client/editclient?innerclient=1">创建专用客户</a></div>',
      '<p class="rul_tip">点此按钮创建有效，其他途径创建无效。</p>',
      '</div>',//1.1.2 end
      '</div>',//1.1 end
      '</div>'//1 end
    ].join("");
    return html;
  }
  var w_Events = ["getpageearning"];//弹窗关闭后需要执行的事件，如首页的系统公告、大鲨鱼等
  function runWEvents() {
    w_Events.forEach(function (item, index) {
      if (window[item] && typeof window[item] == "function") {
        window[item]();
      }
    });
    setLocal("3");
  }
  function showModal() {
    $("#r_userlogin_box").remove();
    var boxHtml = buildBoxHtml();
    $("body").append(boxHtml);
    setMask();
    mask = $("#r_userlogin_m");
    box = $("#r_userlogin_box");
    mask.fadeIn("slow");
    box.fadeIn("slow", function () {
      box.find(".hnp_close").click(function () {
        mask.fadeOut("slow", function () {
          mask.remove();
        });
        box.fadeOut("slow", function () {
          runWEvents();
          box.remove();
        });
      });
      setLocal("2");
    });
  }
  function setLocal(val) {
    localStorage.setItem("r_UserLogin", val);
  }
  return {
    show: function (pM_CanCreateInnerClient) {
      var m = localStorage.getItem("r_UserLogin");
      if (m && m == "1" && pM_CanCreateInnerClient === true) {
        showModal();
      }
    },
    reset: function () {
      setLocal(1)
    }
  };
}();
/* 登录后内部客户创建提醒 End */

var leftMenuFunc = function () {
  var $eduQueryLink = $(".page-sidebar-menu .nav-link[href='/apps/managediploma.aspx']");
  if ($eduQueryLink.length > 0) {
    $eduQueryLink.attr("href", "javascript:void(0)").click(function () {
      r_Layout_BeautAlert.done("服务已暂停，学历查询请联系求真网负责人鲁伊婷");
    });
  }

  var $cardQueryLink = $(".page-sidebar-menu .nav-link[href='/apps/manageidentitycard.aspx']");
  if ($cardQueryLink.length > 0) {
    $cardQueryLink.attr("href", "javascript:void(0)").click(function () {
      r_Layout_BeautAlert.done("服务已暂停，身份核查请联系求真网负责人鲁伊婷");
    });
  }
  
}

$(document).ready(function () {
  leftMenuFunc();
  rightMenuFunc();
  $("#manageexpense-tab").find(".Exp_showmore").click(function () {
    $(this).closest(".r-tb-item").next(".r-tb-detail").toggle();
  });

  $(".Exp_showmore").click(function () {
    $(this).toggleClass("showmore_act");
    if ($(this).html() == "+") {
      $(this).html("-")
      $(this).css("line-height", "1").css("font-size", "14px").css("font-weight", "bolder");
    } else {
      $(this).html("+");
    }
  });

  $("div.modal.hm_center").each(function (index, item) {
    var id = $(item).attr("id");
    r_Layout_SetModalPosition($("#" + id));
  });
  
  r_UserLogin_M.show(window.pM_CanCreateInnerClient);

  var mailBoxId = "#mailBox",
    mailBox = $(mailBoxId),
    mailUrl = "",
    mailErrorTxt = "";
  mailBox.click(function (e) {
    if ($(".mmp_popover").length > 0) {
      return;
    }
    //r_Layout_BlockUI.show2("", "加载中..");
    //r_Layout_BlockUI.show(mailBoxId, "");
    mailBox.addClass("block");
    $.post("/mail/getloginurl", {}, function (result) {
      //r_Layout_BlockUI.hide(mailBoxId);
      mailBox.removeClass("block");
      if (result.Success) {
        //mailLink.attr("href", result.Data.MailUrl);
        //mailNum.text(result.Data.MassageCount);
        //mailModal.modal("show");
        mailUrl = result.Data.MailUrl;
        mailBox.popover({
          template: [
            '<div class="popover mmp_popover" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'
          ].join(""),
          content: [
            '<div class="mail_popover_box">',//box start
            '<div class="mp_b clearfix">',//mp_b start
            '<img src="/images/workpanel/<EMAIL>" alt="" class="mp_icon" />',
            '<div class="mp_r">',
            '<p class="mp_t">您当前邮箱账号为：</p>',
            '<p class="mp_mail" title="' + result.Data.MailAccount + '">' + result.Data.MailAccount + '</p>',
            '</div>',
            '</div>',//mp_b end
            '<div class="mp_bp clearfix">',//mp_bp start
            '<a href="' + result.Data.MailUrl + '" target="_blank" class="mp_link">点击查看&gt;</a>',
            '<span class="mp_num_b">当前有未读邮件 <span class="mp_num">' + result.Data.MassageCount + '</span> 封</span>',
            '</div>',//mp_bp end
            '</div>'//box end
          ].join(""),
          html: true,
          placement: 'left',
          //trigger: 'hover',
          //trigger: 'manual',
          delay: { "show": 500, "hide": 300 },
          //animation: false
        })
        //  .on("mouseenter", function () {
        //  var _this = this;   // 这里的this触发的dom,需要存起来 否则在下面 .popover的逻辑中this会变为弹出的dom
        //  $(this).popover("show");
        //  $(".popover").on("mouseleave", function () {
        //    $(_this).popover('hide');
        //  });
        //}).on("mouseleave", function () {
        //  var _this = this;
        //  setTimeout(function () {
        //    if (!$(".popover:hover").length) {
        //      $(_this).popover("hide");
        //    }
        //  }, 300);
        //});
        mailBox.popover("show");
      }
      else {
        mailErrorTxt = result.Message || "请求失败，请稍后重试..";
        r_Layout_BeautAlert.done(mailErrorTxt);
      }
    }).error(function (er) {
      //r_Layout_BlockUI.hide2();
      //r_Layout_BlockUI.hide(mailBoxId);
      mailBox.removeClass("block");
      mailErrorTxt = "出现错误，请稍后重试..";
      r_Layout_BeautAlert.done(mailErrorTxt);
      console.log(er.status + ": " + er.responseText);
    });
  });

  $("body").on("click", ".mmp_popover", function (e) {
    e = e || event;
    if (e.stopPropagation) {
      e.stopPropagation();
    }
    //return false;
  }).on("click", ".mp_link", function () {
    mailBox.popover("destroy");
    $(".right-menu-container").removeClass("show-panel");
  });
  $(document).on("click", function () {
    mailBox.popover("destroy");
    $(".right-menu-container").removeClass("show-panel");
  });

});


/* 用来接收消息，并弹出框显示 */
(function () {
  if (typeof toastr == 'object') {
//    receiveSopMessage()
//    setInterval(function () {
//      receiveSopMessage();
//    }, 600000);
  }
  
  function receiveSopMessage() {
    $.ajax({
      type: "post",
      data: {
        StaffId: 0,
      },
      url: "/JobCandidate/GetSopMessage",
      success: function (result) {
        if (!result.Success) {
          return;
        }
        if (result.Data) {
          if (result.data instanceof Array) {
            result.Data.forEach(function (elem) {
              runToastr(elem);
            });
          } else {
            runToastr(result.Data);
          }
        }
        
      }
    });
  }

  function runToastr(data) {
    if (data.Type == 1) {
      toastr['warning']("<a href='/client/viewclient?id=" + data.DataId + "' target='_blank' title='" + data.Content + "'>" + data.Content + "</a>", "客户提示", { toastClass: "received-notice-panel", positionClass: "toast-bottom-right", timeOut: 0, closeButton: true, tapToDismiss: false, hoverCloseDisabled: true });
    } else if (data.Type == 2) {
      toastr['warning']("<a href='/job/viewcandidate?id=" + data.DataId + "' target='_blank' title='" + data.Content + "'>" + data.Content + "</a>", "offer谈判提醒", { toastClass: "received-notice-panel", positionClass: "toast-bottom-right", timeOut: 0, closeButton: true, tapToDismiss: false, hoverCloseDisabled: true });
    }
  }
})();

