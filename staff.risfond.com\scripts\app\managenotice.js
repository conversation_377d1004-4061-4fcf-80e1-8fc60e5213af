/// <reference path="../jquery-1.12.0.js" />
/// <reference path="../jquery-1.12.0.intellisense.js" />
/// <reference path="r_notice.js" />

var rnss_ManageNotice = function () {

    var event_CheckAll = function () {
        checkALL2({ AllButton: '#checkAll', ChildButton: '.input-radio', ParentBox: '.r-tb' });
    }

    var setFilter = function () {
        var filterselect = new commonfilterhelper();//公用筛选条件帮助类
        filterselect.addevent_sctitle();//3类筛选条件事件注册（点击后显示一个下级div）
        filterselect.event_SelectFilter(".tarmcustomsearch .select-box");//2类筛选条件（下拉列表）
        filterselect.event_daterangeselect(".sx-panel .createtime", { datefrom: "datefrom", dateto: "dateto" });//4类筛选条件（时间范围选择）
        $("#selTeam").completeTeam({ type: 0, userKey: "consultantid", maxCount: 1, width: 172, append: $("#selTeam") });//服务顾问
        $("#selTeamStaffid").completeTeam({ type: 0, userKey: "staffid", maxCount: 1, width: 172, append: $("#selTeamStaffid") });//录入人
    }
    function addevent_keyword() {
        $("#keywords").keydown(function (e) {
            var e = e || event;
            e.stopPropagation();
            var evt = e.keyCode || e.which || e.charCode;
            if (evt == "13") {
                $("#btnkeywords").triggerHandler("click");
                return false;
            }
        }).focus();
        $("#btnkeywords").click(function () {
            var txt = $("#keywords").val();
            if (!txt || txt.length == 0) {
                r_Layout_BeautAlert.done(setRnssLanguage("关键词不能为空"));
                return;
            }
            if (txt.length > 50) {
                r_Layout_BeautAlert.done(setRnssLanguage("关键词长度不能超过50"));
                return;
            }
            $(this).closest("form").submit();
        });
    }
    var rnss_DelCheck = function (type) {
        var items = new CheckBoxManager($('input[name=id]'));
        $('#cmdDelete-textitem').click(function () {
            var checkedItems = items.values();
            if (checkedItems.length == 0) {
                r_Layout_BeautAlert.done(setRnssLanguage('请至少选择一条记录'), 'hits');
                return;
            }
            if (!confirm('选中' + checkedItems.length + "条数据，是否删除？")) {
                return;
            }
            var url = '/notice/delnotice';
            r_Layout_BlockUI.show();
            $.post(url, { ids: checkedItems.join(",") }, function (result) {
                if (result.Status == "1") {
                    r_Layout_BeautAlert.done(setRnssLanguage('操作成功！'), 'hits');
                    setTimeout(function () { location = location }, 1000);
                }
                else {
                    r_Layout_BeautAlert.done(setRnssLanguage('操作不成功！'), 'hits');
                }
            }).error(function (er) {
                r_Layout_BlockUI.hide();
                console.log(er.status + ": " + er.responseText);
            });
        });
    }
    var r_SetStatus = function () {
        var items = new CheckBoxManager($('input[name=id]'));
        $("#btnSetStatus_F,#btnSetStatus_C").click(function () {
            var checkedItems = items.values();
            if (checkedItems.length == 0) {
                r_Layout_BeautAlert.done(setRnssLanguage('请至少选择一条记录'), 'hits');
                return;
            }
            if (!confirm(setRnssLanguage('选中') + checkedItems.length + setRnssLanguage("条数据，是否设置发布状态？"))) {
                return;
            }
            var status = $(this).data("status");
            r_Layout_BlockUI.show();
            $.post('/notice/setnoticestatus', { status: status, ids: checkedItems.join(",") }, function (result) {
                if (result.Status == "1") {
                    r_Layout_BeautAlert.done(setRnssLanguage('操作成功！'), 'hits');
                    setTimeout(function () { location = location }, 1000);
                }
                else {
                    r_Layout_BeautAlert.done(setRnssLanguage('操作不成功！'), 'hits');
                }
            }).error(function (er) {
                r_Layout_BlockUI.hide();
                console.log(er.status + ": " + er.responseText);
            });
        });
    };

    var r_Event_sss = function () {
        var panel = $("#home_notice_panel");
        if (!panel.length) {
            setTimeout(function () {
                r_Event_sss();
            }, 500);
            return;
        }
        //$('.hnp_c_b img').zoomify();
        panel.find(".hnp_c_b img").off("click").on("click", function () {
            window.open(this.src)
        })
    };
    var r_Event_YuLan = function () {
        $("#managenotice_tab").on("click", ".r_btn_yulan", function () {
            var id = $(this).data("id");
            r_Notice.show(id);
            r_Event_sss();
        });
    };
    var r_Modify = function () {
        $("#managenotice_tab .btn_nomodify").click(function () {
            r_Layout_BeautAlert.done(setRnssLanguage('公告已经发布不能修改!'), 'hits');
        });
    };
    return {
        init: function () {
            event_CheckAll();
            setFilter();
            r_Event_YuLan();
            r_Modify();
            addevent_keyword();
        },
        rnss_DelCheck: rnss_DelCheck,
        r_SetStatus: r_SetStatus
    };
}();
$(function () {
    rnss_ManageNotice.init();
});