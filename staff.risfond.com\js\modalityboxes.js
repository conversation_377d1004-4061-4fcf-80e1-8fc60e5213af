/// <reference path="../Scripts/jquery-1.8.2.js" />
/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
(function ($) {
	$.fn.modalityboxes = function (params) {
		var modalityboxes = function () {
			this.dom = '<div class="noime div_txt" id="toAreaCtrl" style="cursor: text;">'+
						'<div class="addr_text addr_text_box_outmax">' +
						'<input type="text" accesskey="t" class="addr_inputkey" tabindex="1" autocomplete="off">' +
						'<div class="addr_htmlkey">W</div>' +
						'</div>'+
						'</div>';
			this.opts = {
				ItemBox: '.item-data',
				ItemDelete: '.addr_del_link',
				ItemInputBox: '.addr_text',
				ItemInput: '.addr_inputkey',
				ItemInputWidth: '.addr_htmlkey',
				ItemContent: '.addr_base',
				DialogBox: "#Rs_MsgBox",
				Scope: ".my-short-message",
				Regex: "",
				Format: function (value) { return value.match(this.Regex)[1]; },
				IsNullContact: function (contact, address) { return address },
				ContactLimit: 30,
				AddressLimit:15,
				IsDeleteRepeat: true
			}
			var box, self;
			this.init = function (el) {
				self = this.opts = $.extend({}, this.opts, params || {});
				el.html(this.dom);
				box = el.find("#toAreaCtrl");
				this.setup();
				return this;
			}
			this.setup = function () {
				this.importboxclick();
				this.textitemhover();
				this.importblur();
				this.importkey();
			}
			this.importboxclick = function () {//点击BOX的无效区域
				box.die("click").live("click", function (e) {
					if ($(e.target).closest(self.ItemBox).size() == 0) {
						if (box.find(self.ItemBox).size() > 0) {
							var last = box.find(self.ItemBox).last();
							var currentY = e.clientY + $(document).scrollTop();
							if (last.offset().top + last.height() > currentY) {
								var listEl = box.find(self.ItemBox);
								for (var i = listEl.size() - 1; i >= 0; i--) {
									$t = box.find(self.ItemBox).eq(i);
									if (currentY <= $t.offset().top + $t.height() && currentY >= $t.offset().top) {
										$t.after(box.find(self.ItemInputBox)); box.find(self.ItemInput).trigger("focus");
										$(self.DialogBox).find(".current").removeClass("current");
										return false;
									}
								}
							}
						}
						if (box.find(self.ItemBox).size() > 0) {
							box.find(self.ItemBox).last().after($(self.ItemInputBox));
							box.find(self.ItemInput).trigger("focus");
						}
						if ($(e.target).closest(self.ItemContent).size() == 0) {
							box.find(self.ItemInput).trigger("focus");
						}
						$(self.DialogBox).find(".current").removeClass("current");
					}
				});
				//调整输入框位置
				box.find(self.ItemBox).live("click", function (e) {
					if ($(e.target).closest(self.ItemBox).size() > 0) {
						var x = e.clientX, left = $(this).offset().left, width = $(this).outerWidth(), paddingLeft = 5, paddingRight = 5;
						if (x > left && x < left + paddingLeft) {
							//当前项前面添加输入框
							$(this).before($(self.ItemInputBox));
							$(self.DialogBox).find(".current").removeClass("current");
						}
						else if (((left + width) > x && (left + width - paddingRight) < x)) {
							//当前项后面添加输入框
							$(this).after($(self.ItemInputBox));
							$(self.DialogBox).find(".current").removeClass("current");
						}
						else {
							//收件人文本点击
							$(this).addClass("current").siblings().removeClass("current");
							box.find(self.ItemInputBox).width(0);
						}
						box.find(self.ItemInput).trigger("focus");
						box.find(self.ItemInputBox).width(1);
					}
				});
			},
			this.textitemhover = function () {//鼠标经过效果时，删除按钮显示
				box.find(self.ItemContent).live("mouseenter", function () {
					$(this).addClass("hover");
				}).live("mouseleave", function () {
					$(this).removeClass("hover");
				});
				box.find(self.ItemContent).live("mouseenter", function () {
					$(this).next(".addr_del").find(self.ItemDelete).addClass("icon-show");
				});
				box.find(self.ItemBox).live("mouseleave", function () {
					$(this).find(self.ItemDelete).removeClass("icon-show");
				});
				//删除项操作
				box.find(self.ItemDelete).live("click", function () {
					$(this).parent().parent(self.ItemBox).remove();
				});
				//退格键删除
				$(document).live("keyup", function (e) {
					if ($(self.Scope).size() > 0 && $(e.target).closest(self.ItemInput).size() == 0 && $(self.DialogBox).find(".current").size() > 0) {
						var key = e.which;
						if (key == "8" || key == "46") {
							$(self.DialogBox).find(".current").remove();
							e.preventDefault();
						}
					}
				});
			},
			this.importblur = function () {//鼠标离开输入框事件			
				$(self.DialogBox).live("click", function (e) {
					if ($(e.target).closest(self.Scope).size() == 0 || $(self.Scope).size() == 0) { return; } else {
						if ($(e.target).closest(self.ItemContent).size() == 0) {
							box.find(self.ItemContent).removeClass("current");
						}
					}
				});
			},
			this.importkey = function () {//各种键盘事件
				var $this = this;
				$(document).live("keyup", function (e) {
					var t = $(self.DialogBox).find(".current");
					if (t.size() == 1) {
						var keyboard = e.which;
						if (keyboard == "37") {
							//前
							if ($(t).closest(self.ItemBox).prev(self.ItemBox).size() == 0) return;
							var current = $(t).removeClass("current").closest(self.ItemBox).prev(self.ItemBox).find(self.ItemContent).addClass("current");
							var height = box.height();
							var top = current.position().top || null;
							if (top <= 0) {
								box.scrollTop(box.scrollTop() - height / 2);
							}
						}
						if (keyboard == "39") {
							//后
							if ($(t).closest(self.ItemBox).next(self.ItemBox).size() == 0) return;
							var current = $(t).removeClass("current").closest(self.ItemBox).next(self.ItemBox).find(self.ItemContent).addClass("current");
							var height = box.height();
							var top = current.position().top || null;
							if (top >= height) {
								box.scrollTop(box.scrollTop() + height / 2);
							}
						}
					}
				});
				box.find(self.ItemInput).die("keydown").live("keydown", function (e) {
					var keyboard = e.which;
					if ($(this).val() == "") {
						//删除
						if (keyboard == "8" || keyboard == "46") {
							if ($(self.DialogBox).find(".current").size() == 0) {
								$(this).parent().prev(self.ItemBox).last().remove();
							}
						}
						if (keyboard == "37") {
							//前
							$(this).parent(self.ItemInputBox).prev(self.ItemBox).last().insertAfter($(this).parent(self.ItemInputBox));
							box.find(self.ItemInputBox).width(1);
							var height = box.height();
							var top = $(this).position().top || null;
							if (top <= 0) {
								box.scrollTop(box.scrollTop() - height / 2);
							}
							return false;
						}
						if (keyboard == "39") {
							//后
							$(this).parent(self.ItemInputBox).next(self.ItemBox).first().insertBefore($(this).parent(self.ItemInputBox));
							box.find(self.ItemInputBox).width(1);
							var height = box.height();
							var top = $(this).position().top || null;
							if (top >= height) {
								box.scrollTop(box.scrollTop() + height / 2);
							}
							return false;
						}
					}
					if (keyboard == "59") {
						$this.collection();
					}
					if (keyboard == "13") {
						$this.collection();
					}
				}).live("keyup", function () {
					if ($.trim($(this).val()).length == 0) {
						$(this).val('');
						box.find(self.ItemInputBox).width(1);
					}
				}).live("keydown", function () {
					box.find(self.ItemInputWidth).html($.trim($(this).val()) + "W");
					box.find(self.ItemInputBox).css({ "width": box.find(self.ItemInputWidth).outerWidth() });
				})
				//模拟了blur事件
				$(self.DialogBox).live("click", function (el) {
					if ($.trim(box.find(self.ItemInput).val()).length > 0 && $(self.Scope).size() > 0 && $(this).closest(self.ItemBox).size()==0) {
						$this.collection();
					}
				});
			},
			//收集
			this.collection = function () {
				var address = $(self.ItemInput).val().replace(";", "").replace("；", "");
				if ($.trim(address) == "") { $(self.ItemInput).val(''); return false; }
				if ($.trim(address).length < 1) { return }
				this.dataaddlist(null, address);
			}

			this.itemsmsbox = function (errorClass, contact, address) {
				//有的短信太长，超过输入框，导致样式溢出，所以要截取太长的短信和联系人
				function substring(str, num) {
					if (str == null) return "";
					var value = "";
					if (str.length > num) {
						value = str.substr(0, num) + "…";
					}
					else {
						value = str;
					}
					return value;
				}
				var data = '',title='';
				if (contact == null || $.trim(contact).length == 0) {
					data = '<b unselectable="on">'
				   + substring(address, self.AddressLimit) + '</b><span unselectable="on" class="addr_del_block">&nbsp;</span>';
					title = substring(address, self.AddressLimit);
				}
				else {
					data = '<b unselectable="on">'
					+ substring(contact, self.ContactLimit) + '</b><span unselectable="on"> &lt;'
					+ substring(address, self.AddressLimit)
					+ '&gt; </span>';
					title = substring(contact, self.ContactLimit) + '<' + substring(address, self.AddressLimit) + '>';
				}
				contact = contact || self.IsNullContact(contact,address);
				return '<div class="item-data"><div class="addr_base addr_normal '
					+ errorClass + '" title="' + title + '" data-contact="' + contact
					+ '" data-address="' + address
					+ '" unselectable="on">' 
					+ data +
					'<span class="semicolon">;</span></div><div class="addr_del">'
					+ '<a href="javascript:;" class="addr_del_link" name="del"></a></div></div>';
			}
			//添加短信到BOX
			this.dataaddlist = function (contact, address) {
				//如果输入全部为空，则不提交到列表
				if (contact == null && address == null)
				{
					return;
				}
				if (address) {
					address = $.trim(address);
				}
				//删除当前输入的重复号码
				if (self.IsDeleteRepeat) {
					$.each($(self.ItemContent), function (index, value) {
						if ($.trim($(this).attr("data-address")) == $.trim(address)) {
							$(this).parent(".item-data").remove();
						}
					});
				}
				var errorClass = '';
				if (!self.Regex.test(address)) {
					errorClass = "addr_error";
				}
				else {
					address = self.Format(address);
				}
				var html = this.itemsmsbox(errorClass, contact, address);
				box.find(self.ItemInputBox).before(html);
				$(self.ItemInput).val('');
				$(self.ItemInputWidth).html('');	
			}
			this.empty = function () {
				box.find(self.ItemBox).remove();
				return true;
			}
			this.emptyeq = function (index) {
				box.find(self.ItemBox).eq(index).remove();
				return true;
			}
			this.getalldata = function () {
				var data = new Array();
				$.each(box.find(self.ItemBox), function () {
					data.push({ contact: $(this).find(self.ItemContent).attr("data-contact") || "", address: $(this).find(self.ItemContent).attr("data-address") || "" });
				});
				return data;
			}

		}
		var me = $(this);
		var instance = (new modalityboxes).init(me)
		me.data('modalityboxes', instance);
		return instance;
		
	};
})(window.jQuery, false);