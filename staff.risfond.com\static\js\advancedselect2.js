/// <reference path="../jquery-1.8.2.min.js" />
/// <reference path="/static/r-easyui/r.easyui.js" />
/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
/**
 * 功能：选择框
 * 用途：选择公司
 * 依赖：[jquery.js, r.easyui.js, rs_common.js]
 * 文档：http://192.168.30.10:8888/index.php?s=/9&page_id=1725
 */
function AdvancedSelect2(opts) {
  this.version = opts.version;
  if (!this.version && opts.title == "选择公司") {
    this.version = "1.0";
  }
	this.data = opts.data;
	this.id = Math.randomId();
	this.valueField = opts.valueField || "Id";
	this.textField = opts.textField || "Text";
	this.searchFileld = opts.searchFileld || "label";
	this.parentidFileld = opts.parentidFileld || "ParentId";
  this.customCss = opts.customCss || ""; //二级选择自定义样式
	if (this.version == "1.0" || this.version == "3.0") {
    this.html = $('<div class="xz_parent2 ' + this.customCss + '" id=' + this.id + '><div class="xz_top">&nbsp;</div><div class="xz_cent"><div class="biaoti"><div class="bt_left">' + opts.title + '</div><div class="bt_right"><input type="button" value="' + "取消" + '" name="button_none" class="button_none"><input type="button" value="' + "确定" + '" name="button_sure"  class="button_sure"></div></div><div class="checked-area"></div><div class="txtcompanel"><span class="txtcomtip">' + "在选项中搜索：" + '</span><input class="txtcombox" /><a class="select-all" href="javascript:void(0)">全选</a><a class="reset-all" href="javascript:void(0)">重置</a></div><div class="the_cont_rong cf"><div class="cont_top"><ul class="list-quick-wrap"></ul><ul class="list-wrap"></ul></div><div class="cont_bot"></div></div></div><div class="xz_bot">&nbsp;</div></div>');
  }
  else {
  this.html = $('<div class="xz_parent2 ' + this.customCss + '" id=' + this.id + '><div class="xz_top">&nbsp;</div><div class="xz_cent"><div class="biaoti"><div class="bt_left">' + opts.title + '</div><div class="bt_right"><input type="button" value="' + "取消" + '" name="button_none" class="button_none"><input type="button" value="' + "确定" + '" name="button_sure"  class="button_sure"></div></div><div class="checked-area"></div><div class="txtcompanel"><span class="txtcomtip">' + "在选项中搜索：" + '</span><input class="txtcombox" /><a class="select-all" href="javascript:void(0)">全选</a><a class="reset-all" href="javascript:void(0)">重置</a></div><div class="the_cont_rong cf"><div class="cont_top"><ul class="list-wrap"></ul></div><div class="cont_bot"></div></div></div><div class="xz_bot">&nbsp;</div></div>');
  }
	this.html.css("width", opts.width);
	this.checkArea = this.html.find(".checked-area");
	this.companel = this.html.find(".txtcompanel");
	this.txtcombox = this.companel.find(".txtcombox");
	this.comIsHide = opts.comIsHide || false;
	this.comWidth = opts.comWidth || 280;
	this.comPanelHeight = opts.comPanelHeight || 158;
  this.CheckedNum = opts.count || 3; //可选择的数量
  this.sureCallBack = opts.sureCallBack || function () { };
  this.hideCallBack = opts.hideCallBack || function () { };
	this.rootDisabled = opts.rootDisabled || false;//父项不可选
	this.selectCallBack = opts.selectCallBack || function (el) { }
	this.getRowIndex = opts.getRowIndex || function (target, value, valueField, searchFileld) {
		var state = $.data(target, 'combobox');
		var opts = state.options;
		var data = state.data;
		var val = value ? $.trim(value) : "";
		for (var i = 0; i < data.length; i++) {
			var d = data[i][valueField];
			if (d && val && (d.toLowerCase() == val.toLowerCase() || data[i][searchFileld].toLowerCase().indexOf(val.toLowerCase()) != -1)) {
				return i;
			}
		}
		return -1;
	}
	this.showmeassage = opts.showmeassage || function (txt) {
		alert(txt);
	}
	this.init(opts.activeValue);
}

AdvancedSelect2.prototype = {
	builddata: function (dt) {
		var da = [], self = this;
		if (dt) {
			$.each(dt, function (i, n) {
				if (!n[self.parentidFileld]) {
					var d = n;
					$.each(dt, function (j, k) {
						if (k[self.parentidFileld] && k[self.parentidFileld] == d[self.valueField]) {
							if (!d.children) {
								d.children = [];
							}
							d.children.push(k);
						}
					});
					da.push(d);
				}
			});
		}
		return da;
	},
	getdatabyid: function (dt, id) {
		var len = dt.length, _data = dt, self = this;
		for (var i in _data) {
			var d = _data[i];
			if (d[self.valueField] && d[self.valueField] == id) {
				return d;
			}
		}
		return "";
	},
	buildcomboxdata: function (dt) {
		var dt2 = dt.concat();
		if (dt2 && dt2.length > 0) {
			dt2.sort(function (a, b) {
				return a.Quanpin > b.Quanpin ? 1 : -1;
			});
		}
		var d = [], _data = $.extend(true, {}, dt2), self = this;
		$.each(_data, function (i, n) {
			var _d = n;
			_d[self.searchFileld] = _d.Quanpin + " " + _d.Shoupin + " " + _d[self.textField];
			var distxt = _d[self.textField] + "（" + _d['Shoupin'] + "）";
			if (_d[self.parentidFileld]) {
				var parent = self.getdatabyid(dt, _d[self.parentidFileld]);
				distxt = _d[self.textField] + "（" + _d['Shoupin'] + "）" + parent[self.textField];
			}
			_d.displayText = distxt;
			d.push(_d);
		});
		return d;
	},
	init: function (activeValue) {
	  if (!$.fn.combobox) {
	    console.error('referenceError: AdvancedSelect2 runtime need method combobox at path .../static/r-easyui/r.easyui.js');
      return
	  };
		var ul = this.html.find(".list-wrap"),
		   ret = [], self = this;
		var newdata = $.extend(true, {}, self.data);
		var data = self.builddata(newdata);
		if (self.version == "1.0" || self.version == "3.0") {
      var quickul = this.html.find(".list-quick-wrap"),
        quickret = [];
      var az = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
      for (var i = 0, l = az.length; i < l; i++) {
        var value = az[i];
        var active = "";
		  if (!activeValue && value == "J") {
			  active = " active";
		  }
		  else if (!!activeValue && value == activeValue) {
			  active = " active";
		  }
				var s = '<li data-shoupin="' + value + '" class="list-item0' + active + '">' + value + '</li>';
        quickret.push(s);
      }
      quickret = quickret.join("");
      quickul.html(quickret);
    }
    var itemClassName = "", data_attr = "";
		for (var i = 0, l = data.length; i < l; i++) {
			if (self.version == "1.0" || self.version == "3.0") {
        var shoupin = data[i].Shoupin;
        if (shoupin != "") {
          shoupin = shoupin[0];
        }
        data_attr = 'data-shoupin="' + shoupin + '"';
		  if (shoupin.indexOf('J') == -1 &&(!!activeValue && shoupin.indexOf(activeValue) == -1)) {
          itemClassName = " list-item-hide";
		  }
		  else if (shoupin.indexOf('J') == -1 && !activeValue)
		  {
			  itemClassName = " list-item-hide";
		  }
        else {
          itemClassName = "";
        }
        itemClassName += " list-item-select";
      }
			var rootInput = '';
      if (!this.rootDisabled) { rootInput = '<input type="checkbox" class="adsel-check" id="' + data[i][self.valueField] + this.id + '" value="' + data[i][self.valueField] + '" text="' + data[i][self.textField] + '" />'; }
      //else { rootInput = '<input type="image" value=" " id="' + data[i][self.valueField] + this.id + '" text="' + data[i][self.textField] + '" class="box-disabled" >'; }
      else { rootInput = '<span id="' + data[i][self.valueField] + this.id + '" class="box-disabled"></span>' }
			if (data[i].children) {
        var s = '<li ' + data_attr + ' class="list-item1' + itemClassName + '">' + rootInput + '<label for="' + data[i][self.valueField] + this.id + '"title="' + data[i][self.textField] + '">' + data[i][self.textField] + '</label><ul class="second-select-list">';
				for (var j = 0; j < data[i].children.length; j++) {
          s += '<li><input type="checkbox" class="adsel-check" id="' + data[i].children[j][self.valueField] + this.id + '" value="' + data[i].children[j][self.valueField] + '" text="' + data[i].children[j][self.textField] + '"/><label for="' + data[i].children[j][self.valueField] + this.id + '" title="' + data[i].children[j][self.textField] + '">' + data[i].children[j][self.textField] + '</label></li>';
				}
				s += "</ul></li>";
				ret.push(s);
			} else {
        ret.push('<li ' + data_attr + ' class="list-item2' + itemClassName + '">' + rootInput + '<label for="' + data[i][self.valueField] + this.id + '" title="' + data[i][self.textField] + '">' + data[i][self.textField] + '</label></li>');
			}
		}
		ret = ret.join("");
		ul.html(ret);
    this.inputs = ul.find("input");
    var $selectAllBtn = this.html.find(".select-all");
    $selectAllBtn.click(function (e) {
      var currentNum = 0;
      self.html.find('.list-item2 input').each(function (index, elem) {
        if (self.CheckedNum <= currentNum) {
          return false;
        }
        var $elem = $(elem);
        $elem.prop('checked', true).parent().addClass("selected");
        self.appendSelect($elem);
        currentNum++;
      });
    });
    if (self.data.length > self.CheckedNum) {
      $selectAllBtn.hide();      
    }
		if (self.version == "1.0" || self.version == "3.0") {
			this.html.find(".list-quick-wrap li").click(function (e) {
        var $this = $(this);
				$this.parent().find(".active").removeClass("active");
        $this.addClass("active");
        var activeValue = $this.text();
        $(".list-item-select").addClass("list-item-hide");
        $(".list-item-select").each(function () {
          if ($(this).attr("data-shoupin").indexOf(activeValue) != -1) {
            $(this).removeClass("list-item-hide");
          }
        });
      });
    }
    this.html.find(".reset-all").click(function (e) {
      self.html.find('.list-item2 input').each(function (index, elem) {
        var $elem = $(elem);
        $elem.prop('checked', false).parent().removeClass("selected");
        self.appendSelect($elem);
      });
      self.html.find('.list-item1 input').each(function (index, elem) {
        var $elem = $(elem);
        $elem.prop('checked', false).parent().removeClass("selected");
        self.appendSelect($elem);
      });
    });
		this.html.find(".button_none").click(this.hidden.bind(this));

		this.html.find(".list-item1  input").change(function () {
			var $this = $(this), flag = $this.prop("checked"),
				p = flag ? $this.parents("li.list-item1").addClass("selected") : $this.parents("li.list-item1").removeClass("selected");
			var ul = $this.siblings("ul");
			ul.length && ul.find("input").prop("checked", flag).prop("disabled", flag);
			self.appendSelect($this);
		});
		this.html.find(".list-item2 input").change(function () {
		  var $this = $(this);
		  $this.prop("checked") ? $this.parent().addClass("selected") : $this.parent().removeClass("selected");
		  self.appendSelect($this);
		});


		if ($.support) {
			this.html.find(".list-item1").hover(function () {
				$(this).css("zIndex", 3).find("ul").css("display", "block");
			}, function () {
				$(this).css("zIndex", 2).find("ul").css("display", "none");
			});
		}
		this.html.find(".button_sure").click(function () {
			var arr = [], s = self.checkArea.find("span");
			s.each(function () {
				var $this = $(this);
				arr.push({ Id: $this.attr("value"), Text: $this.attr("text") });
			});
			self.sureCallBack.call(self, arr);
		});
	  //this.checkArea.on("click", ".cancel-item", this.clearSelect.bind(this));
		this.checkArea.on("click", ".cancel-item", function (e) {
		  self.clearSelect.call(self, e);
		});
		if (!self.comIsHide) {
			var newdata2 = $.extend(true, {}, self.data);
			self.companel.show();
			self.txtcombox.combobox({
				valueField: self.valueField,
				textField: 'displayText',//self.textField,
				width: self.comWidth,
				textWidth: self.comWidth,
				//panelWidth: self.comWidth,
				panelMinWidth: self.comWidth - 100,
				panelMaxWidth: self.comWidth + 100,
				panelHeight: self.comPanelHeight,
				data: self.buildcomboxdata(self.data),
				formatter: function (row) {
					var opts = $(this).combobox('options');
					return row['displayText'];
				},
				selectOnNavigation: false,//键盘上下时是否执行select
				finder: {
					getEl: function (target, val) {
						var index = self.getRowIndex(target, val, self.valueField, self.searchFileld);
						var id = $.data(target, 'combobox').itemIdPrefix + '_' + index;
						return $('#' + id);
					},
					getRow: function (target, p) {
						var state = $.data(target, 'combobox');
						var index = (p instanceof jQuery) ? p.attr('id').substr(state.itemIdPrefix.length + 1) : self.getRowIndex(target, p, self.valueField, self.searchFileld);
						return state.data[parseInt(index)];
					}
				},
				filter: function (q, row) {
					var opts = $(this).combobox('options');
					return row[self.textField].toLowerCase().indexOf(q.toLowerCase()) == 0 || row[self.searchFileld].toLowerCase().indexOf(q.toLowerCase()) != -1;
				},
				onSelect: function (a, b, c) {
					if (self.rootDisabled && !a[self.parentidFileld]) {
						self.showmeassage("父级不可选");
						$(this).combobox("setValues", []);
						return;
					}
					if (self.checkArea.find("span").length < self.CheckedNum) {
						self.select([a[self.valueField]]);
					}
					else {
					  self.showmeassage("您最多可选择{0}项".format(self.CheckedNum));
					}
					$(this).combobox("setValues", []);
				}
			});
		}
		else {
			self.companel.hide();
		}
	},
  /*
  {Id:1,Text:"销售"}
  1>3? l=1;
  flag=true
  */
	select: function (values) { //传值，选中某个或多个，可以是加，的字符串，整数，或者数组
		var el, flag = typeof values[0] == "object" ? true : false, self = this;
		var l = values.length > this.CheckedNum ? this.CheckedNum : values.length;
    var choiceItem;
		if (flag) {
			for (var i = 0; i < l; i++) {
				if (values[i] == null || values[i][self.valueField] == null || values[i][self.textField] == null || $.trim(values[i][self.valueField]) == "" || $.trim(values[i][self.textField]) == "") { return; }
				el = this.findItemByValue(values[i][self.valueField]).prop("checked", true);
        
				if (el.length) {
				  el.change();
				} else {
				  /* 
           * Destoried by Allen.sun on 2021/04/16
           * Note: 需求来自 分支 2.0.1 目的是兼容后端解析出的地点数据 在本地数据中不存在 而导致的后端处理复杂问题
           */

					//this.checkArea.append("<span value='" + values[i][self.valueField] + "' text='" + el.attr("text") + "'>" + values[i][self.textField] + "<label class='cancel-item'></label></span>");
				}
			}
      choiceItem = values[0][self.valueField] ;
		} else {
			for (var i = 0; i < l; i++) {
			  el = this.findItemByValue(values[i]).prop("checked", true);
			  el.change();
			}
      choiceItem = values[0];
		}
		l == this.CheckedNum && this.html.find(".list-wrap input").not(":checked").prop("checked", false);
		//this.selectCallBack.call(this, values);

		if (this.version == "1.0") {
			
			var activeValue = $(".list-item-select input[value=" + choiceItem + "]").parent().attr("data-shoupin");
      $(".list-quick-wrap").find(".active").removeClass("active");
      $(".list-quick-wrap li[data-shoupin=" + activeValue + "]").addClass("active");
      $(".list-item-select").addClass("list-item-hide");
      $(".list-wrap li[data-shoupin=" + activeValue + "]").removeClass("list-item-hide");
    }
	},
	show: function () {
		if (!$("#" + this.id).length) { this.html.appendTo(document.body); }
		centerEl2(this.html);
	},
	hidden: function () {
		this.html.css("display", "none");
		$("#bgWrap").length && $("#bgWrap").fadeOut("300");
    this.clear();
    this.hideCallBack.call(self);
	},
	checkChildren: function (values, flag) {
		var el;
		flag = flag !== null ? flag : true;
		values = typeof values == "string" ? [values] : values;
		for (var i = 0; i < values.length; i++) {
			el = this.inputs.filter("[value=" + values[i] + "]");
			el.siblings("ul").length && el.siblings("ul").find("input").prop("checked", flag);
		}
	},
	clear: function () {
    this.inputs.prop("checked", false).removeAttr("disabled");
		this.html.find(".selected").removeClass("selected");
		this.checkArea.html("");
	},
	setCheckedNum: function (num) {
    this.CheckedNum = num || 3;
    var $selectAllBtn = this.html.find(".select-all");
    if (this.data.length > this.CheckedNum) {
      $selectAllBtn.hide();
    } else {
      $selectAllBtn.show();
    }
	},
	setSureCallBack: function (fn) {
		this.sureCallBack = fn;
  },
  setHideCallBack: function (fn) {
    this.hideCallBack = fn;
  },
	setSelectCallBack: function (fn) {
	  this.selectCallBack = fn;
	},
	findItemByValue: function (value) {
		return this.inputs.filter("[value=" + value + "]");
	},
	InitSelect: function (value, text) {
		if (!value)
			return;
		this.findItemByValue(value).prop("checked", true);
			//this.checkArea.find("span[value^='" + el.attr("value") + "']").remove();
			this.checkArea.find("span[value='" + value + "']").remove();
			this.checkArea.append("<span value='" + value + "' text='" + text + "'>" + text + "<label class='cancel-item'></label></span>");
			 this.inputs.not(":checked").attr("disabled", "");
	},
  appendSelect: function (el) {
    //console.log($(el.parents(".list-item1").find('input')[0]).attr('text'))
    //var elParentStr = $(el.parents(".list-item1").find('input')[0]).attr('text');
    //if (elParentStr) {
    //  if (el.attr("text") === elParentStr) {
    //    elParentStr = '';
    //  } else {
    //    elParentStr = elParentStr + '-';
    //  }
    //} else {
    //  elParentStr = '';
    //}
		if (!el.prop("checked")) {
			this.checkArea.find("span[value=" + el.attr("value") + "]").remove();
			this.inputs.not(":checked").removeAttr("disabled");
    } else {
			if (this.checkArea.find("span").length < this.CheckedNum) {
				//this.checkArea.find("span[value^='" + el.attr("value") + "']").remove();
        this.checkArea.find("span[value='" + el.attr("value") + "']").remove();
        this.checkArea.append("<span value='" + el.attr("value") + "' text='"  + el.attr("text") + "'>" + el.attr("text") + "<label class='cancel-item'></label></span>");
				this.checkArea.find("span").length == this.CheckedNum && this.inputs.not(":checked").attr("disabled", "");
			}
		}
		this.selectCallBack.call(this, el);
	},
  clearSelect: function (e) {
    var p = $(e.target).parent();
		p.remove(); 
		this.inputs.not(":checked").removeAttr("disabled");
    //this.inputs.removeAttr("disabled").prop("checked", false);
	  //this.findItemByValue(p.attr("value")).prop("checked",false).triggerHandler("click");
		this.findItemByValue(p.attr("value")).prop("checked", false);//.click();
	},
	clearSelectForNot: function (el) {
	  this.checkArea.find("span[value!=" + el.attr("value") + "]").remove();
	  this.inputs.filter(":checked").not(el.get(0)).prop("checked", false);
	},
	disabledForNot: function (el) {
	  el.prop("disabled", false);
	  this.inputs.not(el.get(0)).prop("disabled", true);
	},
	undisabled: function () {
	  if (this.inputs.filter(":checked").length < this.CheckedNum) {
	    this.inputs.prop("disabled", false);
	  }
  },
}

AdvancedSelect2.setValues = function (result, el, name) {
	var html = "";
	$.each(result, function () {
		html += '<li><span>' + this.text + '</span><input type="hidden" name="' + name + '" value="' + this.value + '" /></li>'
	});
	$(el).html(html).parent().data("data", result);
	return true;
}