/*
  汉化daterangepicker封装,
  author:pzj,
  date:2017-01-13;
*/
function daterangepicker_zh(Selector, options, cb) {
  var ele = $(Selector);
  set_options(options);

  function set_options(options) {
    var data = {};
    data.locale = {
      "format": 'YYYY-MM-DD',
      "separator": "至",
      "applyLabel": "确定",
      "cancelLabel": "取消",
      "fromLabel": "起始时间",
      "toLabel": "结束时间'",
      "customRangeLabel": "手动选择",
      "weekLabel": "W",
      "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
      "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
      "firstDay": 1
    };

    data.ranges = {
      //'最近1小时': [moment().subtract('hours',1), moment()],
      '今日': [moment().startOf('day'), moment()],
      '昨日': [moment().subtract('days', 1).startOf('day'), moment().subtract('days', 1).endOf('day')],
      '最近7日': [moment().subtract('days', 6), moment()],
      '最近30日': [moment().subtract('days', 29), moment()],
      '本月': [moment().startOf("month"), moment().endOf("month")],
      '上个月': [moment().subtract(1, "month").startOf("month"), moment().subtract(1, "month").endOf("month")]
    };
    var new_options,
      new_ranges;
    if (typeof options !== 'object' || options === null) {
      options = {};
    }
    //new_ranges = $.extend({}, options.ranges, data.ranges);
    if (options.ranges && options.ranges.constructor === Object) {
      new_ranges = options.ranges;
    }
    new_options = $.extend(options, data);
    new_options.ranges = new_ranges;

    if (cb) {
      ele.daterangepicker(new_options, cb);
    } else {
      ele.daterangepicker(new_options);
    }

  }
}