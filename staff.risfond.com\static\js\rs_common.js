/// <reference path="../jquery-1.8.2.js" />
/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
Date.prototype.format = function (format) {
  var o = {
    "M+": this.getMonth() + 1,  //month
    "d+": this.getDate(),     //day
    "h+": this.getHours(),    //hour
    "m+": this.getMinutes(),  //minute
    "s+": this.getSeconds(), //second
    "q+": Math.floor((this.getMonth() + 3) / 3),  //quarter
    "S": this.getMilliseconds() //millisecond
  }
  if (typeof this == "string") { return this; }
  if (format == null || format == "undefined") { format = "yyyy-MM-dd"; }

  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
  }

  for (var k in o) {
    if (new RegExp("(" + k + ")").test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
    }
  }
  return format;
};
String.prototype.format = function (args) {
  var result = this;
  if (arguments.length > 0) {
    if (arguments.length == 1 && typeof (args) == "object") {
      for (var key in args) {
        if (args[key] != undefined) {
          var reg = new RegExp("({" + key + "})", "g");
          result = result.replace(reg, args[key]);
        }
      }
    }
    else {
      for (var i = 0; i < arguments.length; i++) {
        if (arguments[i] != undefined) {
          var reg = new RegExp("({)" + i + "(})", "g");
          result = result.replace(reg, arguments[i]);
        }
      }
    }
  }
  return result;
};
function ReloadRnssLanguageResource(lng) {
  //i18n.t("my.key", { lng: "de-DE" });
  var path = "/locales/" + lng + "/translation.json";
  var config = {
    resGetPath: path,
    useCookie: false,
    detectLngFromHeaders: true,
    lowerCaseLng: true,
    debug: false,
    resStore:true
  };
  var config = {
    resGetPath: path,
    useCookie: false,
    lng: lng,
    debug: true,
    resources: {
      en: {
        translation: {
          "简历": "resume"
        }
      }
    }
  };
  $.i18n.init(config, function(err, t, d, c) {
    // initialized and ready to go!
    var str = t('简历');
    console.log(str);
  });
};
function setRnssLanguage(txt) {
  var lng = window.r_Lngs ? r_Lngs : 'zh';
  if (!($.i18n && $.i18n.isInitialized()))
    //return setRnssLanguage(txt);
    //修改 risfond.com/app 页面下的报错
    return txt;
  var d = $.i18n.t("rnss." + txt, { lngs: [lng] });
  if (d && d != "rnss." + txt) {
    return d;
  }
  return txt;
};
if (!window.setRnsslanguage) {
  window.setRnsslanguage = window.setRnssLanguage;
};
$.extend({
  ajaxPost: function (url, data, callback, before, error) {
     var result= $.ajax({
      url: url,
      dataType: 'json',   //返回的数据类型
      type: "post",
      data: data,//参数
      async: false,//false为同步，true为异步，同步的数据会使页面锁住并且之后的JS也不会执行，一直等待到数据加载完成才解锁,本次操作必须要同步得到数据才能去执行是否隐藏
      success: callback,
      error: before,
      beforeSend: error
     });
     if (parseInt(result.status) == 500) {
      alert(setRnssLanguage("服务器异常，稍后重试！"));
     }
     return result;
  },
  ajaxPost2: function (url, type, data, callback, error) {
    var result = $.ajax({
      url: url,
      dataType: 'json',   //返回的数据类型
      type: type,
      data: data,//参数
      async: true,//false为同步，true为异步，同步的数据会使页面锁住并且之后的JS也不会执行，一直等待到数据加载完成才解锁,本次操作必须要同步得到数据才能去执行是否隐藏
      success: callback,
      error: function (xhr, et) {
        if (et && et == "timeout") {
          beautAlert.done(setRnssLanguage("服务器连接超时"), "hits", 1000);
        }
        else if (xhr.status == "500") {
          beautAlert.done(setRnssLanguage("服务器错误，稍后重试！"), "hits", 1000);
        }
        else {
          alert(xhr.responseText);
        }
        if (waitingLayer) {
          waitingLayer.hide();
        }
      },
      beforeSend: error
    });
    return result;
  },
  //计算文本长度：js计算长度多出的问题
  limit: function (text) {
    return text.replace("\n", "").length;
  },
  //将serializeArray序列化成json格式
  serializeArrayToJson: function (data) {
    var o = {};
    $.each(data, function () {
      if (o[this.name]) {
        if (!o[this.name].push) {
          o[this.name] = [o[this.name]];
        }
        o[this.name].push(this.value || '');
      } else {
        o[this.name] = this.value || '';
      }
    });
    return o;
  }
});

