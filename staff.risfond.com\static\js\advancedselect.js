/// <reference path="../jquery-1.8.2.js" />
/*高级选择构造器*/
function AdvancedSelect(opts) {
	this.data = opts.data;
	this.id = Math.randomId();
	this.html = $('<div class="xz_parent1" id=' + this.id + '><div class="xz_top">&nbsp;</div><div class="xz_cent"><div class="biaoti"><div class="bt_left">' + opts.title + '</div><div class="bt_right"><input type="button" value="取消" name="button_none" class="button_none"><input type="button" value="确定" name="button_sure"  class="button_sure"></div></div><div class="checked-area"></div><div class="the_cont_rong cf"><div class="cont_top"><ul class="list-wrap"></ul></div><div class="cont_bot"></div></div></div><div class="xz_bot">&nbsp;</div></div>');
	this.html.css("width", opts.width);
	this.checkArea = this.html.find(".checked-area");
	this.CheckedNum = opts.count || 3;
	this.sureCallBack = opts.sureCallBack || function () { };
	this.rootDisabled = opts.rootDisabled || false;//父项不可选
	this.init();
}

AdvancedSelect.prototype = {
	init: function () {
		var ul = this.html.find(".list-wrap"),
		   ret = [], self = this;
		for (var i = 0, l = this.data.length; i < l; i++) {
			var rootInput = '';
			if (!this.rootDisabled) { rootInput = '<input type="checkbox" id="' + this.data[i].value + this.id + '" value="' + this.data[i].value + '" text="' + this.data[i].text + '" />'; } else { rootInput = '<input type="image" value=" " id="' + this.data[i].value + this.id + '" text="' + this.data[i].text + '" class="box-disabled" >'; }
			if (this.data[i].children) {
				var s = '<li class="list-item1">' + rootInput + '<label for="' + this.data[i].value + this.id + '"title="' + this.data[i].text + '">' + this.data[i].text + '</label><ul class="second-select-list">';
				for (var j = 0; j < this.data[i].children.length; j++) {
					s += '<li><input type="checkbox" id="' + this.data[i].children[j].value + this.id + '" value="' + this.data[i].children[j].value + '" text="' + this.data[i].children[j].text + '"/><label for="' + this.data[i].children[j].value + this.id + '">' + this.data[i].children[j].text + '</label></li>';
				}
				s += "</ul></li>";
				ret.push(s);
			} else {
				ret.push('<li class="list-item2">' + rootInput + '<label for="' + this.data[i].value + this.id + '" title="' + this.data[i].text + '">' + this.data[i].text + '</label></li>');
			}
		}
		ret = ret.join("");
		ul.html(ret);
		this.inputs = ul.find("input");
		this.html.find(".button_none").click(this.hidden.bind(this));
		this.html.find(".list-item1  input").click(function () {
			var $this = $(this), flag = $this.prop("checked"),
				p = flag ? $this.parents("li.list-item1").addClass("selected") : $this.parents("li.list-item1").removeClass("selected");
			var ul = $this.siblings("ul");
			ul.length && ul.find("input").prop("checked", flag).prop("disabled", flag);
			self.appendSelect($this);
		});
		this.html.find(".list-item2 input").click(function () {
			var $this = $(this);
			$this.prop("checked") ? $this.parent().addClass("selected") : $this.parent().removeClass("selected");
			self.appendSelect($this);
		});

		if ($.browser.msie && $.browser.version < 8) {
			this.html.find(".list-item1").hover(function () {
				$(this).css("zIndex", 3).find("ul").css("display", "block");
			}, function () {
				$(this).css("zIndex", 2).find("ul").css("display", "none");
			});
		}
		this.html.find(".button_sure").click(function () {
			var arr = [], s = self.checkArea.find("span");
			s.each(function () {
				var $this = $(this);
				arr.push({ value: $this.attr("value"), text: $this.attr("text") });
			});
			self.sureCallBack.call(self, arr);
		});
		this.checkArea.on("click", ".cancel-item", this.clearSelect.bind(this));
	},
	select: function (values) { //传值，选中某个或多个，可以是加，的字符串，整数，或者数组
		var el, flag = typeof values[0] == "object" ? true : false;
		var l = values.length > this.CheckedNum ? this.CheckedNum : values.length;
		if (flag) {
			for (var i = 0; i < l; i++) {
				if (values[i] == null || values[i].value == null || values[i].text == null || $.trim(values[i].value)=="" || $.trim(values[i].text)=="") { return; }
				el = this.findItemByValue(values[i].value).attr("checked", "");
				if (el.length) {
					el.triggerHandler("click");
				} else {
					this.checkArea.append("<span value='" + values[i].value + "' text='" + el.attr("text") + "'>" + values[i].text + "<label class='cancel-item'>x</label></span>");
				}
			}
		} else {
			for (var i = 0; i < l; i++) {
				el = this.findItemByValue(values[i]).attr("checked", "");
				el.triggerHandler("click");
			}
		}
		l == this.CheckedNum && this.html.find(".list-wrap input").not(":checked").attr("disabled", "");

	},
	show: function () {
		if (!$("#" + this.id).length) { this.html.appendTo(document.body); }
		centerEl(this.html);
	},
	hidden: function () {
		this.html.css("display", "none");
		$("#bgWrap").length && $("#bgWrap").fadeOut("300");
		this.clear();
	},
	checkChildren: function (values, flag) {
		var el;
		flag = flag !== null ? flag : true;
		values = typeof values == "string" ? [values] : values;
		for (var i = 0; i < values.length; i++) {
			el = this.inputs.filter("[value=" + values[i] + "]");
			el.siblings("ul").length && el.siblings("ul").find("input").prop("checked", flag);
		}
	},
	clear: function () {
		this.inputs.prop("checked",false).removeAttr("disabled");
		this.html.find(".selected").removeClass("selected");
		this.checkArea.html("");
	},
	setCheckedNum: function (num) {
		this.CheckedNum = num || 3;
	},
	setSureCallBack: function (fn) {
		this.sureCallBack = fn;
	},
	findItemByValue: function (value) {
		return this.inputs.filter("[value=" + value + "]");
	},
	appendSelect: function (el) {
		if (!el.prop("checked")) {
			this.checkArea.find("span[value=" + el.attr("value") + "]").remove();
			this.inputs.not(":checked").removeAttr("disabled");
		} else {
			if (this.checkArea.find("span").length < this.CheckedNum) {
				//this.checkArea.find("span[value^='" + el.attr("value") + "']").remove();
        this.checkArea.find("span[value='" + el.attr("value") + "']").remove();
				this.checkArea.append("<span value='" + el.attr("value") + "' text='" + el.attr("text") + "'>" + el.attr("text") + "<label class='cancel-item'>x</label></span>");
				this.checkArea.find("span").length == this.CheckedNum && this.inputs.not(":checked").attr("disabled", "");
			}
		}
	},
	clearSelect: function (e) {
		var p = $(e.target).parent();
		p.remove();
		this.inputs.not(":checked").removeAttr("disabled");
		this.findItemByValue(p.attr("value")).prop("checked",false).triggerHandler("click");
	}
}

AdvancedSelect.setValues = function (result, el, name) {
	var html = "";
		$.each(result, function () {
			html += '<li><span>' + this.text + '</span><input type="hidden" name="'+name+'" value="' + this.value + '" /></li>'
		});
		$(el).html(html).parent().data("data", result);
		return true;
	}