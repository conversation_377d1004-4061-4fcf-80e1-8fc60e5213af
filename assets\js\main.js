// 主要JavaScript文件 - 基于原始RISFOND网站

$(document).ready(function() {
  // 初始化应用
  initializeApp();
});

// 初始化应用
function initializeApp() {
  console.log('RISFOND RNSS V3.0 初始化...');
  
  // 初始化Vue应用
  initializeVueApp();
  
  // 初始化侧边栏
  initializeSidebar();
  
  // 初始化搜索功能
  initializeSearch();
  
  // 检查移动端
  checkMobileView();
}

// 初始化Vue应用
function initializeVueApp() {
  // 顶部导航Vue实例
  window.huanfu = new Vue({
    el: "#headHuanfu",
    data: {
      selFuVal: sessionStorage.getItem("SystemBGCode") || '0',
      WorkflowCountNum: 0,
      userName: '顾开发ZJ1(Alvin)',
      userScore: '29856.00'
    },
    mounted() {
      let _this = this;
      setTimeout(function () {
        _this.setSJBG(_this.selFuVal, 'base', 0);
      }, 500);
    },
    methods: {
      ApprovalDialogOpenClick() {
        console.log('打开审批中心');
        // 这里可以添加审批中心的逻辑
      },
      changeSelVal() {
        this.setSJBG(this.selFuVal, 'base', 1);
      },
      setSJBG(selHuanfu, tag, isSave) {
        console.log('设置四季背景:', selHuanfu);

        // 移除所有背景类
        $("#huanfuBG").attr("class", "");
        $(".huanfu-base-head-img").attr("class", "huanfu-base-head-img");

        // 根据选择设置背景
        if (selHuanfu == '-1') {
          // 根据当前时间算出是哪个季节
          const today = new Date();
          const currentMonth = today.getMonth() + 1;
          switch (currentMonth) {
            case 12:
            case 1:
            case 2:
              $("#huanfuBG").addClass('content-wrap-dong');
              $(".huanfu-base-head-img").addClass("huanfu-base-head-dong");
              this.selFuVal = "4";
              break;
            case 3:
            case 4:
            case 5:
              $("#huanfuBG").addClass('content-wrap-chun');
              $(".huanfu-base-head-img").addClass("huanfu-base-head-chun");
              this.selFuVal = "1";
              break;
            case 6:
            case 7:
            case 8:
              $("#huanfuBG").addClass('content-wrap-xia');
              $(".huanfu-base-head-img").addClass("huanfu-base-head-xia");
              this.selFuVal = "2";
              break;
            case 9:
            case 10:
            case 11:
              $("#huanfuBG").addClass('content-wrap-qiu');
              $(".huanfu-base-head-img").addClass("huanfu-base-head-qiu");
              this.selFuVal = "3";
              break;
          }
        } else if (selHuanfu == '0') {
          // 默认无背景
        } else if (selHuanfu == '1') {
          $("#huanfuBG").addClass('content-wrap-chun');
          $(".huanfu-base-head-img").addClass("huanfu-base-head-chun");
        } else if (selHuanfu == '2') {
          $("#huanfuBG").addClass('content-wrap-xia');
          $(".huanfu-base-head-img").addClass("huanfu-base-head-xia");
        } else if (selHuanfu == '3') {
          $("#huanfuBG").addClass('content-wrap-qiu');
          $(".huanfu-base-head-img").addClass("huanfu-base-head-qiu");
        } else if (selHuanfu == '4') {
          $("#huanfuBG").addClass('content-wrap-dong');
          $(".huanfu-base-head-img").addClass("huanfu-base-head-dong");
        }

        // 保存设置
        if (isSave) {
          sessionStorage.setItem("SystemBGCode", selHuanfu);
          console.log('背景设置已保存');
        }
      }
    }
  });

  // 主内容Vue实例
  window.concenter = new Vue({
    el: "#concenter",
    data: {
      // 用户信息
      prsonziliao: {
        staffPictureUrl: '',
        name: '顾开发ZJ1',
        companyName: '瑞丰德永才华'
      },
      // 业绩数据
      personYj: 100,
      personJd: 68,
      personWwc: 32,
      personHk: 85,
      personTj: 156,
      personMs: 89,
      personOffer: 23,
      personRz: 12,
      // 事项提醒
      monthChangeData: 1,
      monthChange: [
        { value: 1, label: '本月' },
        { value: 2, label: '下月' }
      ],
      thingRemind: [],
      // 快捷入口
      isKuaijie: true,
      isKuaijieFlag: false,
      yesAddDTO: [],
      // 排行榜数据
      collectionList: [],
      realTimeList: [],
      shishihuikuangbangIsflag: false,
      shishituijaingbangIsflag: false,
      hkDataLenFlag: false
    },
    mounted() {
      this.loadIndexData();
    },
    methods: {
      // 加载首页数据
      loadIndexData() {
        this.loadPersonData();
        this.loadThingRemind();
        this.loadQuickEntrance();
        this.loadRankingData();
      },
      
      // 加载个人数据
      loadPersonData() {
        // 模拟数据
        this.prsonziliao = {
          staffPictureUrl: '',
          name: '顾开发ZJ1',
          companyName: '瑞丰德永才华'
        };
      },
      
      // 加载事项提醒
      loadThingRemind() {
        // 模拟数据
        this.thingRemind = [
          {
            dateTo: '2025-06-28',
            contactName: '张三',
            jobTitle: 'Java开发工程师',
            jobCandidateId: 1,
            jobId: 1,
            clientId: 1,
            partyAName: '某科技公司'
          },
          {
            dateTo: '2025-06-29',
            contactName: '李四',
            jobTitle: '前端开发工程师',
            jobCandidateId: 2,
            jobId: 2,
            clientId: 2,
            partyAName: '某互联网公司'
          }
        ];
      },
      
      // 加载快捷入口
      loadQuickEntrance() {
        // 模拟数据
        this.yesAddDTO = [
          { shortcutName: '考勤管理', shortcutIconUrl: '', url: '/attendance' },
          { shortcutName: '年假申请', shortcutIconUrl: '', url: '/leave' },
          { shortcutName: '报销管理', shortcutIconUrl: '', url: '/expense' },
          { shortcutName: '节日福利', shortcutIconUrl: '', url: '/welfare' },
          { shortcutName: '业绩统计', shortcutIconUrl: '', url: '/performance' },
          { shortcutName: '快速录入', shortcutIconUrl: '', url: '/quick-entry' },
          { shortcutName: '简历搜索', shortcutIconUrl: '', url: '/resume-search' },
          { shortcutName: '个人设置', shortcutIconUrl: '', url: '/settings' }
        ];
      },
      
      // 加载排行榜数据
      loadRankingData() {
        // 模拟回款榜数据
        this.collectionList = [
          {
            amount: 50000,
            clientExhibitionName: '某科技有限公司',
            consultantStaffId: 1,
            consultantStaffName: '王顾问',
            consultantStaffGender: 1,
            companyName: '瑞丰德永'
          },
          {
            amount: 35000,
            clientExhibitionName: '某互联网公司',
            consultantStaffId: 2,
            consultantStaffName: '李顾问',
            consultantStaffGender: 2,
            companyName: '瑞丰德永'
          }
        ];
        
        // 模拟推荐榜数据
        this.realTimeList = [
          {
            stepStatus: 9,
            contactName: '张工程师',
            jobTitle: 'Java高级开发',
            jobSalary: 25,
            tuijianStaffId: 1,
            tuijianStaffName: '王顾问',
            tuijianStaffGender: 1,
            tuijianStaffCompanyName: '瑞丰德永'
          },
          {
            stepStatus: 8,
            contactName: '李设计师',
            jobTitle: 'UI设计师',
            jobSalary: 18,
            tuijianStaffId: 2,
            tuijianStaffName: '李顾问',
            tuijianStaffGender: 2,
            tuijianStaffCompanyName: '瑞丰德永'
          }
        ];
      },
      
      // 切换快捷入口显示
      toggerKuaiJie() {
        this.isKuaijie = !this.isKuaijie;
      },
      
      // 快捷入口点击
      toDetail(item) {
        console.log('点击快捷入口:', item.shortcutName);
        // 这里可以添加跳转逻辑
      },
      
      // 事项提醒月份切换
      topChangeMonth1(month) {
        this.monthChangeData = month;
        $('#topChangeMonth1, #topChangeMonth2').removeClass('sxtj-active');
        $('#topChangeMonth' + month).addClass('sxtj-active');
        this.getClientRemindMessage();
      },
      
      // 获取客户提醒消息
      getClientRemindMessage() {
        console.log('获取提醒消息，月份:', this.monthChangeData);
        // 这里可以添加API调用逻辑
      },
      
      // 跳转到联系人
      jumpContactName(id) {
        console.log('跳转到联系人:', id);
      },
      
      // 跳转到职位
      jumpjobTitle(id) {
        console.log('跳转到职位:', id);
      },
      
      // 跳转到客户
      jumpPartyAName(id) {
        console.log('跳转到客户:', id);
      }
    },
    template: `
      <div>
        <!-- 顶部信息区域 -->
        <div class="row">
          <div class="index-top index-top-new com-flex border-ra head-11-wrap">
            <!-- 用户信息 -->
            <div class="index-top-left">
              <div class="top-left-left">
                <div class="demo-img">
                  <i class="fas fa-user"></i>
                </div>
                <p class="top-left-left-p">HI，{{prsonziliao.name}}</p>
                <p class="top-left-left-p2">{{prsonziliao.companyName}}</p>
                <div class="hengxian"></div>
                <p class="top-left-left-p3">未来的你,一定会感谢今天努力的自己!</p>
              </div>
              <div class="top-left-right">
                <div class="top-left-right-div">
                  <p class="left-right-div-p">{{personYj}}万</p>
                  <p class="left-right-div-p2">业绩目标</p>
                </div>
                <div class="div-hengxian"></div>
                <div class="top-left-right-div">
                  <p class="left-right-div-p">{{personJd}}%</p>
                  <p class="left-right-div-p2">完成进度</p>
                </div>
                <div class="div-hengxian"></div>
                <div class="top-left-right-div">
                  <p class="left-right-div-p" style="color: #D91E18;">{{personWwc}}万</p>
                  <p class="left-right-div-p2">未完成</p>
                </div>
                <div class="div-hengxian"></div>
                <div class="top-left-right-div">
                  <p class="left-right-div-p">{{personHk}}万</p>
                  <p class="left-right-div-p2">年度回款</p>
                </div>
                <div class="top-left-right-div">
                  <p class="left-right-div-p">{{personTj}}个</p>
                  <p class="left-right-div-p2">年度推荐</p>
                </div>
                <div class="div-hengxian"></div>
                <div class="top-left-right-div">
                  <p class="left-right-div-p">{{personMs}}个</p>
                  <p class="left-right-div-p2">年度面试</p>
                </div>
                <div class="div-hengxian"></div>
                <div class="top-left-right-div">
                  <p class="left-right-div-p">{{personOffer}}个</p>
                  <p class="left-right-div-p2">年度offer</p>
                </div>
                <div class="div-hengxian"></div>
                <div class="top-left-right-div">
                  <p class="left-right-div-p">{{personRz}}个</p>
                  <p class="left-right-div-p2">年度入职</p>
                </div>
              </div>
            </div>
            
            <!-- 事项提醒 -->
            <div class="index-top-right">
              <div class="index-top-right-top">
                <div style="display: flex; align-items: center;">
                  <i class="fas fa-bell h-tixing"></i>
                  <p class="index-top-right-top-p">事项提醒</p>
                </div>
                <div class="index-top-right-top-div">
                  <div id="topChangeMonth1" @click="topChangeMonth1(1)" class="sxtj-left sxtj-active">
                    <p class="sxtj-left-p">本月</p>
                  </div>
                  <div id="topChangeMonth2" @click="topChangeMonth1(2)" class="sxtj-left">
                    <p class="sxtj-left-p">下月</p>
                  </div>
                </div>
              </div>
              <div class="index-top-right-bottom">
                <div v-if="thingRemind.length != 0" v-for="(item,index) in thingRemind" :key="item.index" class="index-top-right-bottom-content">
                  <div class="sxtx-qiu"></div>
                  <p class="index-top-right-bottom-content-p" v-show="monthChangeData == 1" :title="item.dateTo">{{item.dateTo}}</p>
                  <p class="index-top-right-bottom-content-p2" @click="jumpContactName(item.jobCandidateId)" :title="item.contactName" v-show="monthChangeData == 1">{{item.contactName}}</p>
                  <p class="index-top-right-bottom-content-p3" @click="jumpjobTitle(item.jobId)" :title="item.jobTitle" v-show="monthChangeData == 1">{{item.jobTitle}}</p>
                  <p class="index-top-right-bottom-content-p2" @click="jumpPartyAName(item.clientId)" :title="item.partyAName" v-show="monthChangeData == 2">{{item.partyAName}}</p>
                  <p class="index-top-right-bottom-content-p3" :title="item.dateTo" v-show="monthChangeData == 2">{{item.dateTo}}</p>
                </div>
                <div v-if="thingRemind.length == 0" class="noData">
                  <div class="noData-content">
                    <i class="fas fa-inbox h-nodata" style="font-size: 60px; color: #ccc;"></i>
                    <p class="noData-content-p">暂无数据~</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 快捷入口 -->
        <div class="kuaijie-wrap-new1">
          <div v-if="isKuaijie" class="row page-head page-head-new border-ra mt15">
            <div class="page-head-icon" @click="toggerKuaiJie">
              收起 <i class="fas fa-chevron-up page-dead-icon"></i>
            </div>
            <div class="page-title page-mr-left">
              <div class="caption">
                <i class="fas fa-th-large caption-icon"></i>
                <span class="caption-subject font-blue-sharp bold uppercase caption-sharp">快捷入口</span>
              </div>
            </div>
            <div class="kuaijierukou">
              <div class="kuaijierukou-content">
                <div class="kuaijierukou-content-item" v-for="(item,index) in yesAddDTO" :key="index">
                  <div @click="toDetail(item)" style="display: flex; flex-direction: column; align-items: center;">
                    <i class="fas fa-cube" style="font-size: 48px; color: #2A7FCC; margin-bottom: 10px;"></i>
                    <p class="kuaijierukou-content-item-title">{{item.shortcutName}}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="page-no-kuaijie" v-else>
            <div class="page-kuaijie-icon" @click="toggerKuaiJie">
              <i class="fas fa-th-large page-icon2"></i> 快捷入口展开 <i class="fas fa-chevron-down page-icon1"></i>
            </div>
          </div>
        </div>
        
        <!-- 实时排行榜 -->
        <div class="page-head paihangbang index-paihangbang">
          <!-- 实时回款榜 -->
          <div class="portlet border-ra light bordered index-paihangbang-left table-box-new">
            <div class="portlet-title">
              <div class="caption">
                <i class="fas fa-money-bill-wave portlet-title-svg"></i>
                <span class="caption-subject font-blue-sharp bold uppercase base-title">实时回款榜</span>
              </div>
              <div class="kpbig-top-more">
                <a class="kpbig-link" href="/branchadmin/realtimedata.aspx" target="_blank">
                  <p class="kpbig-top-more-p">更多</p> <i class="fas fa-chevron-right moreSvg"></i>
                </a>
              </div>
            </div>
            <div class="portlet-body">
              <div class="tbl-header">
                <table class="table table-striped table-bordered table-hover r-tb-tuijian">
                  <thead>
                    <tr>
                      <th width="25%">金额</th>
                      <th width="35%">支付单位</th>
                      <th width="20%">服务顾问</th>
                      <th width="20%">公司</th>
                    </tr>
                  </thead>
                </table>
              </div>
              <div class="tbl-body">
                <table class="table table-striped table-bordered table-hover r-tb-tuijian">
                  <tbody>
                    <tr v-for="item in collectionList" :key="item.consultantStaffId">
                      <td class="text-color">{{item.amount}}元</td>
                      <td>{{item.clientExhibitionName}}</td>
                      <td>
                        <a href="javascript:void(0);" class="r-tuijian-new-a" :class="item.consultantStaffGender == 1 ? 'male':'female'">{{item.consultantStaffName}}</a>
                      </td>
                      <td>{{item.companyName}}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          
          <!-- 实时推荐榜 -->
          <div class="portlet border-ra light bordered index-paihangbang-left table-box-new">
            <div class="portlet-title">
              <div class="caption">
                <i class="fas fa-user-plus portlet-title-svg"></i>
                <span class="caption-subject font-blue-sharp bold uppercase base-title">实时推荐榜</span>
              </div>
              <div class="kpbig-top-more">
                <a class="kpbig-link" href="/branchadmin/realtimedata.aspx" target="_blank">
                  <p class="kpbig-top-more-p">更多</p> <i class="fas fa-chevron-right moreSvg"></i>
                </a>
              </div>
            </div>
            <div class="portlet-body">
              <div class="tbl-header">
                <table class="table table-striped table-bordered table-hover r-tb-tuijian">
                  <thead>
                    <tr>
                      <th width="15%">状态</th>
                      <th width="20%">人选</th>
                      <th width="25%">推荐职位</th>
                      <th width="15%">年薪</th>
                      <th width="15%">推荐人</th>
                      <th width="10%">公司</th>
                    </tr>
                  </thead>
                </table>
              </div>
              <div class="tbl-body">
                <table class="table table-striped table-bordered table-hover r-tb-tuijian">
                  <tbody>
                    <tr v-for="item in realTimeList" :key="item.tuijianStaffId">
                      <td class="text-color" style="color:red" v-if="item.stepStatus == 9">入职</td>
                      <td class="text-color" style="color:RGB(252,223,18)" v-if="item.stepStatus == 8">Offer</td>
                      <td style="color:#2A7FCC;">{{item.contactName}}</td>
                      <td>{{item.jobTitle}}</td>
                      <td class="text-color" style="color:red;">{{item.jobSalary}}万</td>
                      <td>
                        <a href="javascript:void(0);" class="r-tuijian-new-a" :class="item.tuijianStaffGender == 1 ? 'male':'female'">{{item.tuijianStaffName}}</a>
                      </td>
                      <td>{{item.tuijianStaffCompanyName}}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    `
  });
}

// 初始化侧边栏
function initializeSidebar() {
  // 侧边栏菜单点击事件
  $('.page-sidebar-menu .nav-link').on('click', function(e) {
    const $this = $(this);
    const $parent = $this.parent();
    
    if ($this.hasClass('nav-toggle')) {
      e.preventDefault();
      
      // 切换子菜单
      if ($parent.hasClass('open')) {
        $parent.removeClass('open');
        $parent.find('.sub-menu').slideUp(200);
      } else {
        // 关闭其他菜单
        $('.page-sidebar-menu .nav-item.open').removeClass('open');
        $('.page-sidebar-menu .sub-menu').slideUp(200);
        
        // 打开当前菜单
        $parent.addClass('open');
        $parent.find('.sub-menu').slideDown(200);
      }
    }
    
    // 设置活动状态
    $('.page-sidebar-menu .nav-item').removeClass('active');
    $parent.addClass('active');
  });
}

// 初始化搜索功能
function initializeSearch() {
  // 搜索选项切换
  $('.js-options a').on('click', function(e) {
    e.preventDefault();
    const text = $(this).text();
    const value = $(this).data('value');
    $('.js-currentOption').text(text);
  });
  
  // 搜索提交
  $('.js-submit').on('click', function(e) {
    e.preventDefault();
    const keywords = $('input[name="keywords"]').val();
    const type = $('.js-currentOption').text();
    console.log('搜索:', type, keywords);
    // 这里可以添加搜索逻辑
  });
}

// 检查移动端视图
function checkMobileView() {
  const isMobile = window.innerWidth <= 768;
  
  if (isMobile) {
    $('body').addClass('mobile-view');
    addMobileMenuToggle();
  } else {
    $('body').removeClass('mobile-view');
    $('.page-sidebar-wrapper').removeClass('open');
  }
}

// 添加移动端菜单切换
function addMobileMenuToggle() {
  $('.menu-toggler').on('click', function() {
    $('.page-sidebar-wrapper').toggleClass('open');
  });
}

// 退出登录
function LoginOut() {
  if (confirm('确定要退出登录吗？')) {
    console.log('退出登录');
    // 这里可以添加退出登录的逻辑
    window.location.href = "/login";
  }
}

// 窗口大小改变事件
$(window).on('resize', function() {
  checkMobileView();
});
