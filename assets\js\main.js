// 主要JavaScript功能
document.addEventListener('DOMContentLoaded', function() {
    // 初始化应用
    initializeApp();
    
    // 绑定事件监听器
    bindEventListeners();
    
    // 初始化图表
    initializeCharts();
    
    // 加载数据
    loadDashboardData();
});

// 初始化应用
function initializeApp() {
    console.log('招聘管理系统初始化...');
    
    // 设置默认页面
    showPage('dashboard');
    
    // 初始化侧边栏
    initializeSidebar();
    
    // 检查移动端
    checkMobileView();
}

// 绑定事件监听器
function bindEventListeners() {
    // 侧边栏导航点击事件
    const navItems = document.querySelectorAll('.nav-item[data-page]');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const page = this.getAttribute('data-page');
            showPage(page);
            setActiveNavItem(this);
        });
    });
    
    // 快速入口点击事件
    const accessItems = document.querySelectorAll('.access-item');
    accessItems.forEach(item => {
        item.addEventListener('click', function() {
            const text = this.querySelector('span').textContent;
            handleQuickAccess(text);
        });
    });
    
    // 窗口大小改变事件
    window.addEventListener('resize', function() {
        checkMobileView();
        resizeCharts();
    });
    
    // 移动端菜单切换
    const menuToggle = document.querySelector('.menu-toggle');
    if (menuToggle) {
        menuToggle.addEventListener('click', toggleSidebar);
    }
}

// 显示指定页面
function showPage(pageId) {
    // 隐藏所有页面
    const pages = document.querySelectorAll('.page-content');
    pages.forEach(page => {
        page.classList.remove('active');
    });
    
    // 显示指定页面
    const targetPage = document.getElementById(pageId + '-page');
    if (targetPage) {
        targetPage.classList.add('active');
    }
    
    // 根据页面加载相应内容
    switch(pageId) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'customer':
            loadCustomerData();
            break;
        case 'position':
            loadPositionData();
            break;
        case 'candidate':
            loadCandidateData();
            break;
        case 'resume-search':
            loadResumeSearchPage();
            break;
        default:
            console.log('未知页面:', pageId);
    }
}

// 设置活动导航项
function setActiveNavItem(activeItem) {
    // 移除所有活动状态
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.classList.remove('active');
    });
    
    // 设置当前项为活动状态
    activeItem.classList.add('active');
}

// 初始化侧边栏
function initializeSidebar() {
    const navGroups = document.querySelectorAll('.nav-group');
    
    navGroups.forEach(group => {
        const navItem = group.querySelector('.nav-item');
        const submenu = group.querySelector('.nav-submenu');
        
        if (navItem && submenu) {
            navItem.addEventListener('click', function(e) {
                e.stopPropagation();
                toggleSubmenu(submenu);
            });
        }
    });
}

// 切换子菜单
function toggleSubmenu(submenu) {
    const isVisible = submenu.style.display === 'block';
    
    // 隐藏所有子菜单
    const allSubmenus = document.querySelectorAll('.nav-submenu');
    allSubmenus.forEach(menu => {
        menu.style.display = 'none';
    });
    
    // 切换当前子菜单
    if (!isVisible) {
        submenu.style.display = 'block';
    }
}

// 处理快速入口点击
function handleQuickAccess(accessType) {
    console.log('快速入口点击:', accessType);
    
    switch(accessType) {
        case '考勤':
            showNotification('考勤功能开发中...', 'info');
            break;
        case '年假':
            showNotification('年假管理功能开发中...', 'info');
            break;
        case '报销':
            showNotification('报销功能开发中...', 'info');
            break;
        case '节日福利':
            showNotification('节日福利功能开发中...', 'info');
            break;
        case '业绩统计':
            showPage('performance');
            break;
        case '快速录入':
            showQuickEntryModal();
            break;
        case '简历搜索':
            showPage('resume-search');
            break;
        case '设置个人中心':
            showNotification('个人中心设置功能开发中...', 'info');
            break;
        default:
            showNotification('功能开发中...', 'info');
    }
}

// 初始化图表
function initializeCharts() {
    const chartCanvas = document.getElementById('realtimeChart');
    if (chartCanvas) {
        const ctx = chartCanvas.getContext('2d');
        
        // 创建实时数据图表
        window.realtimeChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '月度业绩',
                    data: [12, 19, 3, 5, 2, 3],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// 加载仪表板数据
function loadDashboardData() {
    console.log('加载仪表板数据...');
    
    // 模拟数据加载
    setTimeout(() => {
        updateStatCards();
        updateRankingList();
        updateRealtimeChart();
    }, 500);
}

// 更新统计卡片
function updateStatCards() {
    const statCards = document.querySelectorAll('.stat-card');
    const mockData = [
        { number: '52,800元', label: '未来年薪' },
        { number: '68%', label: '完成率' },
        { number: '12,500元', label: '本月' },
        { number: '8,900元', label: '生成数据' },
        { number: '156个', label: '平均月薪' },
        { number: '23个', label: '平均年薪' },
        { number: '5个', label: '平均奖金' },
        { number: '12个', label: '平均人数' }
    ];
    
    statCards.forEach((card, index) => {
        if (mockData[index]) {
            const numberEl = card.querySelector('.stat-number');
            const labelEl = card.querySelector('.stat-label');
            
            if (numberEl && labelEl) {
                animateNumber(numberEl, mockData[index].number);
                labelEl.textContent = mockData[index].label;
            }
        }
    });
}

// 数字动画效果
function animateNumber(element, targetText) {
    const isNumber = /^\d+/.test(targetText);
    
    if (isNumber) {
        const targetNumber = parseInt(targetText.replace(/[^\d]/g, ''));
        const suffix = targetText.replace(/[\d,]/g, '');
        let current = 0;
        const increment = targetNumber / 50;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= targetNumber) {
                current = targetNumber;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current).toLocaleString() + suffix;
        }, 30);
    } else {
        element.textContent = targetText;
    }
}

// 更新排行榜
function updateRankingList() {
    const rankingList = document.querySelector('.ranking-list');
    if (rankingList) {
        const mockRankings = [
            { name: '张三', score: '98分', department: '销售部' },
            { name: '李四', score: '95分', department: '市场部' },
            { name: '王五', score: '92分', department: '技术部' },
            { name: '赵六', score: '89分', department: '人事部' },
            { name: '钱七', score: '87分', department: '财务部' }
        ];
        
        let html = '';
        mockRankings.forEach((item, index) => {
            html += `
                <div class="ranking-item" style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #eee;">
                    <div>
                        <span style="font-weight: bold; color: ${index < 3 ? '#ff6b6b' : '#666'};">${index + 1}. ${item.name}</span>
                        <small style="color: #999; margin-left: 10px;">${item.department}</small>
                    </div>
                    <span style="color: #667eea; font-weight: bold;">${item.score}</span>
                </div>
            `;
        });
        
        rankingList.innerHTML = html;
    }
}

// 更新实时图表
function updateRealtimeChart() {
    if (window.realtimeChart) {
        const newData = Array.from({length: 6}, () => Math.floor(Math.random() * 50) + 10);
        window.realtimeChart.data.datasets[0].data = newData;
        window.realtimeChart.update();
    }
}

// 检查移动端视图
function checkMobileView() {
    const isMobile = window.innerWidth <= 768;
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    
    if (isMobile) {
        document.body.classList.add('mobile-view');
        if (!document.querySelector('.menu-toggle')) {
            addMobileMenuToggle();
        }
    } else {
        document.body.classList.remove('mobile-view');
        if (sidebar) {
            sidebar.classList.remove('open');
        }
    }
}

// 添加移动端菜单切换按钮
function addMobileMenuToggle() {
    const header = document.querySelector('.header-container');
    if (header && !document.querySelector('.menu-toggle')) {
        const menuToggle = document.createElement('button');
        menuToggle.className = 'menu-toggle';
        menuToggle.innerHTML = '<i class="fas fa-bars"></i>';
        menuToggle.style.cssText = `
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 10px;
            margin-right: 15px;
        `;
        
        header.insertBefore(menuToggle, header.firstChild);
        menuToggle.addEventListener('click', toggleSidebar);
    }
}

// 切换侧边栏
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const overlay = getOrCreateOverlay();

    if (sidebar) {
        const isOpen = sidebar.classList.contains('open');

        if (isOpen) {
            sidebar.classList.remove('open');
            overlay.classList.remove('show');
        } else {
            sidebar.classList.add('open');
            overlay.classList.add('show');
        }
    }
}

// 获取或创建遮罩层
function getOrCreateOverlay() {
    let overlay = document.querySelector('.sidebar-overlay');

    if (!overlay) {
        overlay = document.createElement('div');
        overlay.className = 'sidebar-overlay';
        document.body.appendChild(overlay);

        // 点击遮罩关闭侧边栏
        overlay.addEventListener('click', function() {
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                sidebar.classList.remove('open');
                overlay.classList.remove('show');
            }
        });
    }

    return overlay;
}

// 调整图表大小
function resizeCharts() {
    if (window.realtimeChart) {
        window.realtimeChart.resize();
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 80px;
        right: 20px;
        z-index: 3000;
        min-width: 300px;
        animation: slideInRight 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .mobile-view .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .mobile-view .sidebar.open {
        transform: translateX(0);
    }
`;
document.head.appendChild(style);
