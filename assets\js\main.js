// 主要JavaScript文件 - 基于原始RISFOND网站

$(document).ready(function() {
  // 初始化应用
  initializeApp();
});

// 初始化应用
function initializeApp() {
  console.log('RISFOND RNSS V3.0 初始化...');
  
  // 初始化Vue应用
  initializeVueApp();
  
  // 初始化侧边栏
  initializeSidebar();
  
  // 初始化搜索功能
  initializeSearch();
  
  // 检查移动端
  checkMobileView();
}

// 初始化Vue应用
function initializeVueApp() {
  // 顶部导航Vue实例
  window.huanfu = new Vue({
    el: "#headHuanfu",
    data: {
      selFuVal: sessionStorage.getItem("SystemBGCode") || '0',
      WorkflowCountNum: 0,
      userName: '顾开发ZJ1(Alvin)',
      userScore: '29856.00'
    },
    mounted() {
      let _this = this;
      setTimeout(function () {
        _this.setSJBG(_this.selFuVal, 'base', 0);
      }, 500);
    },
    methods: {
      ApprovalDialogOpenClick() {
        console.log('打开审批中心');
        // 这里可以添加审批中心的逻辑
      },
      changeSelVal() {
        this.setSJBG(this.selFuVal, 'base', 1);
      },
      setSJBG(selHuanfu, tag, isSave) {
        console.log('设置四季背景:', selHuanfu);

        // 移除所有背景类
        $("#huanfuBG").attr("class", "");
        $(".huanfu-base-head-img").attr("class", "huanfu-base-head-img");

        // 根据选择设置背景
        if (selHuanfu == '-1') {
          // 根据当前时间算出是哪个季节
          const today = new Date();
          const currentMonth = today.getMonth() + 1;
          switch (currentMonth) {
            case 12:
            case 1:
            case 2:
              $("#huanfuBG").addClass('content-wrap-dong');
              $(".huanfu-base-head-img").addClass("huanfu-base-head-dong");
              this.selFuVal = "4";
              break;
            case 3:
            case 4:
            case 5:
              $("#huanfuBG").addClass('content-wrap-chun');
              $(".huanfu-base-head-img").addClass("huanfu-base-head-chun");
              this.selFuVal = "1";
              break;
            case 6:
            case 7:
            case 8:
              $("#huanfuBG").addClass('content-wrap-xia');
              $(".huanfu-base-head-img").addClass("huanfu-base-head-xia");
              this.selFuVal = "2";
              break;
            case 9:
            case 10:
            case 11:
              $("#huanfuBG").addClass('content-wrap-qiu');
              $(".huanfu-base-head-img").addClass("huanfu-base-head-qiu");
              this.selFuVal = "3";
              break;
          }
        } else if (selHuanfu == '0') {
          // 默认无背景
        } else if (selHuanfu == '1') {
          $("#huanfuBG").addClass('content-wrap-chun');
          $(".huanfu-base-head-img").addClass("huanfu-base-head-chun");
        } else if (selHuanfu == '2') {
          $("#huanfuBG").addClass('content-wrap-xia');
          $(".huanfu-base-head-img").addClass("huanfu-base-head-xia");
        } else if (selHuanfu == '3') {
          $("#huanfuBG").addClass('content-wrap-qiu');
          $(".huanfu-base-head-img").addClass("huanfu-base-head-qiu");
        } else if (selHuanfu == '4') {
          $("#huanfuBG").addClass('content-wrap-dong');
          $(".huanfu-base-head-img").addClass("huanfu-base-head-dong");
        }

        // 保存设置
        if (isSave) {
          sessionStorage.setItem("SystemBGCode", selHuanfu);
          console.log('背景设置已保存');
        }
      }
    }
  });

  // 主内容Vue实例
  window.concenter = new Vue({
    el: "#concenter",
    data: {
      // 用户信息
      prsonziliao: {
        staffPictureUrl: '',
        name: '顾开发ZJ1',
        companyName: '瑞丰德永才华'
      },
      // 业绩数据
      personYj: 100,
      personJd: 68,
      personWwc: 32,
      personHk: 85,
      personTj: 156,
      personMs: 89,
      personOffer: 23,
      personRz: 12,
      // 事项提醒
      monthChangeData: 1,
      monthChange: [
        { value: 1, label: '本月' },
        { value: 2, label: '下月' }
      ],
      thingRemind: [],
      // 快捷入口
      isKuaijie: true,
      isKuaijieFlag: false,
      yesAddDTO: [],
      // 排行榜数据
      collectionList: [],
      realTimeList: [],
      shishihuikuangbangIsflag: false,
      shishituijaingbangIsflag: false,
      hkDataLenFlag: false,
      // 时间数据
      currentDate: '',
      currentTime: ''
    },
    mounted() {
      this.loadIndexData();
      this.startTimeUpdate();
    },
    methods: {
      // 加载首页数据
      loadIndexData() {
        this.loadPersonData();
        this.loadThingRemind();
        this.loadQuickEntrance();
        this.loadRankingData();
      },
      
      // 加载个人数据
      loadPersonData() {
        // 模拟数据
        this.prsonziliao = {
          staffPictureUrl: '',
          name: '顾开发ZJ1',
          companyName: '瑞丰德永才华'
        };
      },
      
      // 加载事项提醒
      loadThingRemind() {
        // 模拟数据
        this.thingRemind = [
          {
            dateTo: '2025-06-28',
            contactName: '张三',
            jobTitle: 'Java开发工程师',
            jobCandidateId: 1,
            jobId: 1,
            clientId: 1,
            partyAName: '某科技公司'
          },
          {
            dateTo: '2025-06-29',
            contactName: '李四',
            jobTitle: '前端开发工程师',
            jobCandidateId: 2,
            jobId: 2,
            clientId: 2,
            partyAName: '某互联网公司'
          },
          {
            dateTo: '2025-06-30',
            contactName: '王五',
            jobTitle: 'Python开发工程师',
            jobCandidateId: 3,
            jobId: 3,
            clientId: 3,
            partyAName: '某AI公司'
          },
          {
            dateTo: '2025-07-01',
            contactName: '赵六',
            jobTitle: '产品经理',
            jobCandidateId: 4,
            jobId: 4,
            clientId: 4,
            partyAName: '某电商公司'
          },
          {
            dateTo: '2025-07-02',
            contactName: '钱七',
            jobTitle: 'UI设计师',
            jobCandidateId: 5,
            jobId: 5,
            clientId: 5,
            partyAName: '某设计公司'
          }
        ];
      },
      
      // 加载快捷入口
      loadQuickEntrance() {
        // 模拟数据
        this.yesAddDTO = [
          { shortcutName: '考勤管理', shortcutIconUrl: '', url: '/attendance' },
          { shortcutName: '年假申请', shortcutIconUrl: '', url: '/leave' },
          { shortcutName: '报销管理', shortcutIconUrl: '', url: '/expense' },
          { shortcutName: '节日福利', shortcutIconUrl: '', url: '/welfare' },
          { shortcutName: '业绩统计', shortcutIconUrl: '', url: '/performance' },
          { shortcutName: '快速录入', shortcutIconUrl: '', url: '/quick-entry' },
          { shortcutName: '简历搜索', shortcutIconUrl: '', url: '/resume-search' },
          { shortcutName: '个人设置', shortcutIconUrl: '', url: '/settings' }
        ];
      },
      
      // 加载排行榜数据
      loadRankingData() {
        // 模拟回款榜数据
        this.collectionList = [
          {
            amount: 50000,
            clientExhibitionName: '某科技有限公司',
            consultantStaffId: 1,
            consultantStaffName: '王顾问',
            consultantStaffGender: 1,
            companyName: '瑞丰德永'
          },
          {
            amount: 35000,
            clientExhibitionName: '某互联网公司',
            consultantStaffId: 2,
            consultantStaffName: '李顾问',
            consultantStaffGender: 2,
            companyName: '瑞丰德永'
          },
          {
            amount: 28000,
            clientExhibitionName: '某金融公司',
            consultantStaffId: 3,
            consultantStaffName: '张顾问',
            consultantStaffGender: 1,
            companyName: '瑞丰德永'
          },
          {
            amount: 22000,
            clientExhibitionName: '某制造企业',
            consultantStaffId: 4,
            consultantStaffName: '赵顾问',
            consultantStaffGender: 2,
            companyName: '瑞丰德永'
          }
        ];

        // 模拟推荐榜数据
        this.realTimeList = [
          {
            stepStatus: 9,
            contactName: '张工程师',
            jobTitle: 'Java高级开发',
            jobSalary: 25,
            tuijianStaffId: 1,
            tuijianStaffName: '王顾问',
            tuijianStaffGender: 1,
            tuijianStaffCompanyName: '瑞丰德永'
          },
          {
            stepStatus: 8,
            contactName: '李设计师',
            jobTitle: 'UI设计师',
            jobSalary: 18,
            tuijianStaffId: 2,
            tuijianStaffName: '李顾问',
            tuijianStaffGender: 2,
            tuijianStaffCompanyName: '瑞丰德永'
          },
          {
            stepStatus: 9,
            contactName: '陈架构师',
            jobTitle: '系统架构师',
            jobSalary: 35,
            tuijianStaffId: 3,
            tuijianStaffName: '张顾问',
            tuijianStaffGender: 1,
            tuijianStaffCompanyName: '瑞丰德永'
          },
          {
            stepStatus: 8,
            contactName: '刘产品',
            jobTitle: '高级产品经理',
            jobSalary: 22,
            tuijianStaffId: 4,
            tuijianStaffName: '赵顾问',
            tuijianStaffGender: 2,
            tuijianStaffCompanyName: '瑞丰德永'
          },
          {
            stepStatus: 9,
            contactName: '周运营',
            jobTitle: '运营总监',
            jobSalary: 28,
            tuijianStaffId: 5,
            tuijianStaffName: '钱顾问',
            tuijianStaffGender: 1,
            tuijianStaffCompanyName: '瑞丰德永'
          }
        ];
      },
      
      // 切换快捷入口显示
      toggerKuaiJie() {
        this.isKuaijie = !this.isKuaijie;
      },
      
      // 快捷入口点击
      toDetail(item) {
        console.log('点击快捷入口:', item.shortcutName);
        // 这里可以添加跳转逻辑
      },
      
      // 事项提醒月份切换
      topChangeMonth1(month) {
        this.monthChangeData = month;
        $('#topChangeMonth1, #topChangeMonth2').removeClass('sxtj-active');
        $('#topChangeMonth' + month).addClass('sxtj-active');
        this.getClientRemindMessage();
      },
      
      // 获取客户提醒消息
      getClientRemindMessage() {
        console.log('获取提醒消息，月份:', this.monthChangeData);
        // 这里可以添加API调用逻辑
      },
      
      // 跳转到联系人
      jumpContactName(id) {
        console.log('跳转到联系人:', id);
      },
      
      // 跳转到职位
      jumpjobTitle(id) {
        console.log('跳转到职位:', id);
      },
      
      // 跳转到客户
      jumpPartyAName(id) {
        console.log('跳转到客户:', id);
      },

      // 开始时间更新
      startTimeUpdate() {
        this.updateCurrentTime();
        setInterval(() => {
          this.updateCurrentTime();
        }, 1000);
      },

      // 更新当前时间
      updateCurrentTime() {
        const now = new Date();
        this.currentDate = now.toLocaleDateString('zh-CN');
        this.currentTime = now.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        });
      },

      // 模拟数据刷新
      refreshData() {
        console.log('刷新数据...');
        // 这里可以添加实际的数据刷新逻辑
        this.loadRankingData();
      },

      // 添加快捷方式
      addShortcut() {
        console.log('添加快捷方式');
        // 这里可以添加自定义快捷方式的逻辑
      },

      // 查看更多排行榜
      viewMoreRankings() {
        console.log('查看更多排行榜');
        // 这里可以跳转到详细排行榜页面
      }
    },
    template: `
      <div>
        <!-- 顶部用户信息和天气区域 -->
        <div class="main-header-section">
          <div class="user-info-card">
            <div class="user-avatar">
              <img src="assets/images/avatar-placeholder.jpg" alt="用户头像" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
              <div class="avatar-placeholder" style="display: none;">
                <i class="fas fa-user"></i>
              </div>
            </div>
            <div class="user-details">
              <h3 class="user-name">{{prsonziliao.name}}</h3>
              <p class="user-company">{{prsonziliao.companyName}}</p>
              <div class="user-stats">
                <div class="stat-item">
                  <span class="stat-value">{{personYj}}万</span>
                  <span class="stat-label">业绩目标</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{personJd}}%</span>
                  <span class="stat-label">完成进度</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{personOffer}}个</span>
                  <span class="stat-label">年度offer</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{personRz}}个</span>
                  <span class="stat-label">年度入职</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 天气信息 -->
          <div class="weather-info">
            <div class="weather-today">
              <div class="weather-icon">
                <i class="fas fa-sun"></i>
              </div>
              <div class="weather-temp">25°</div>
              <div class="weather-desc">晴天</div>
            </div>
            <div class="weather-forecast">
              <div class="forecast-item">
                <span class="forecast-day">明天</span>
                <i class="fas fa-cloud-sun"></i>
                <span class="forecast-temp">23°</span>
              </div>
              <div class="forecast-item">
                <span class="forecast-day">后天</span>
                <i class="fas fa-cloud-rain"></i>
                <span class="forecast-temp">20°</span>
              </div>
            </div>
          </div>

          <!-- 右侧信息栏 -->
          <div class="info-sidebar">
            <div class="date-info">
              <div class="current-date">{{currentDate}}</div>
              <div class="current-time">{{currentTime}}</div>
            </div>
            <div class="quick-stats">
              <div class="quick-stat-item">
                <span class="quick-stat-label">今日访问</span>
                <span class="quick-stat-value">128</span>
              </div>
              <div class="quick-stat-item">
                <span class="quick-stat-label">本周推荐</span>
                <span class="quick-stat-value">15</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content-area">
          <!-- 左侧快捷入口 -->
          <div class="shortcuts-section">
            <div class="section-header">
              <h4><i class="fas fa-th-large"></i> 快捷入口</h4>
              <button class="btn-add-shortcut" @click="addShortcut" title="添加快捷方式">
                <i class="fas fa-plus"></i>
              </button>
            </div>
            <div class="shortcuts-grid">
              <div class="shortcut-item" v-for="(item,index) in yesAddDTO" :key="index" @click="toDetail(item)">
                <div class="shortcut-icon">
                  <i class="fas fa-user-friends" v-if="item.shortcutName.includes('联系人')"></i>
                  <i class="fas fa-search" v-else-if="item.shortcutName.includes('搜索')"></i>
                  <i class="fas fa-briefcase" v-else-if="item.shortcutName.includes('职位')"></i>
                  <i class="fas fa-chart-bar" v-else-if="item.shortcutName.includes('统计')"></i>
                  <i class="fas fa-cog" v-else-if="item.shortcutName.includes('设置')"></i>
                  <i class="fas fa-plus-circle" v-else-if="item.shortcutName.includes('录入')"></i>
                  <i class="fas fa-cube" v-else></i>
                </div>
                <span class="shortcut-label">{{item.shortcutName}}</span>
              </div>
            </div>
          </div>

          <!-- 右侧实时推荐榜 -->
          <div class="rankings-section">
            <div class="section-header">
              <h4><i class="fas fa-trophy"></i> 实时推荐榜</h4>
              <div class="header-actions">
                <button class="btn-refresh" @click="refreshData" title="刷新数据">
                  <i class="fas fa-sync-alt"></i>
                </button>
                <a href="#" class="more-link" @click="viewMoreRankings">更多 <i class="fas fa-chevron-right"></i></a>
              </div>
            </div>
            <div class="rankings-content">
              <div class="ranking-tabs">
                <div class="tab-item active">学习推荐榜</div>
                <div class="tab-item">实时回款榜</div>
              </div>
              <div class="ranking-list">
                <div class="ranking-item" v-for="(item, index) in realTimeList" :key="index">
                  <div class="rank-number">{{index + 1}}</div>
                  <div class="rank-info">
                    <div class="rank-name">{{item.tuijianStaffName}}</div>
                    <div class="rank-detail">{{item.jobTitle}} - {{item.jobSalary}}万</div>
                  </div>
                  <div class="rank-status" :class="item.stepStatus == 9 ? 'status-success' : 'status-pending'">
                    {{item.stepStatus == 9 ? '入职' : 'Offer'}}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部内容区域 -->
        <div class="bottom-content-area">
          <!-- 联系人动态 -->
          <div class="contacts-section">
            <div class="section-header">
              <h4><i class="fas fa-users"></i> 联系人动态</h4>
              <a href="#" class="more-link">更多 <i class="fas fa-chevron-right"></i></a>
            </div>
            <div class="contacts-list">
              <div class="contact-item" v-for="(item, index) in thingRemind" :key="index">
                <div class="contact-avatar">
                  <i class="fas fa-user"></i>
                </div>
                <div class="contact-info">
                  <div class="contact-name">{{item.contactName}}</div>
                  <div class="contact-company">{{item.partyAName}}</div>
                  <div class="contact-position">{{item.jobTitle}}</div>
                </div>
                <div class="contact-actions">
                  <button class="btn-contact"><i class="fas fa-phone"></i></button>
                  <button class="btn-contact"><i class="fas fa-envelope"></i></button>
                  <button class="btn-contact"><i class="fas fa-wechat"></i></button>
                </div>
              </div>
            </div>
          </div>

          <!-- 达人推荐 -->
          <div class="experts-section">
            <div class="section-header">
              <h4><i class="fas fa-star"></i> 达人推荐</h4>
              <a href="#" class="more-link">更多 <i class="fas fa-chevron-right"></i></a>
            </div>
            <div class="experts-grid">
              <div class="expert-card" v-for="(item, index) in collectionList" :key="index">
                <div class="expert-avatar">
                  <img src="assets/images/expert-placeholder.jpg" alt="专家头像" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                  <div class="avatar-placeholder" style="display: none;">
                    <i class="fas fa-user-tie"></i>
                  </div>
                </div>
                <div class="expert-info">
                  <div class="expert-name">{{item.consultantStaffName}}</div>
                  <div class="expert-company">{{item.companyName}}</div>
                  <div class="expert-achievement">回款: {{item.amount}}元</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 公司动态 -->
          <div class="company-news-section">
            <div class="section-header">
              <h4><i class="fas fa-newspaper"></i> 公司动态</h4>
              <a href="#" class="more-link">更多 <i class="fas fa-chevron-right"></i></a>
            </div>
            <div class="news-list">
              <div class="news-item">
                <div class="news-date">06-25</div>
                <div class="news-content">
                  <div class="news-title">瑞丰德永才华2025年度表彰大会圆满举行</div>
                  <div class="news-summary">公司对优秀员工进行表彰，激励全体员工再创佳绩...</div>
                </div>
              </div>
              <div class="news-item">
                <div class="news-date">06-24</div>
                <div class="news-content">
                  <div class="news-title">新版招聘管理系统正式上线</div>
                  <div class="news-summary">系统功能全面升级，提升工作效率...</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `
  });
}

// 初始化侧边栏
function initializeSidebar() {
  // 侧边栏菜单点击事件
  $('.page-sidebar-menu .nav-link').on('click', function(e) {
    const $this = $(this);
    const $parent = $this.parent();
    
    if ($this.hasClass('nav-toggle')) {
      e.preventDefault();
      
      // 切换子菜单
      if ($parent.hasClass('open')) {
        $parent.removeClass('open');
        $parent.find('.sub-menu').slideUp(200);
      } else {
        // 关闭其他菜单
        $('.page-sidebar-menu .nav-item.open').removeClass('open');
        $('.page-sidebar-menu .sub-menu').slideUp(200);
        
        // 打开当前菜单
        $parent.addClass('open');
        $parent.find('.sub-menu').slideDown(200);
      }
    }
    
    // 设置活动状态
    $('.page-sidebar-menu .nav-item').removeClass('active');
    $parent.addClass('active');
  });
}

// 初始化搜索功能
function initializeSearch() {
  // 搜索选项切换
  $('.js-options a').on('click', function(e) {
    e.preventDefault();
    const text = $(this).text();
    const value = $(this).data('value');
    $('.js-currentOption').text(text);
  });
  
  // 搜索提交
  $('.js-submit').on('click', function(e) {
    e.preventDefault();
    const keywords = $('input[name="keywords"]').val();
    const type = $('.js-currentOption').text();
    console.log('搜索:', type, keywords);
    // 这里可以添加搜索逻辑
  });
}

// 检查移动端视图
function checkMobileView() {
  const isMobile = window.innerWidth <= 768;
  
  if (isMobile) {
    $('body').addClass('mobile-view');
    addMobileMenuToggle();
  } else {
    $('body').removeClass('mobile-view');
    $('.page-sidebar-wrapper').removeClass('open');
  }
}

// 添加移动端菜单切换
function addMobileMenuToggle() {
  $('.menu-toggler').on('click', function() {
    $('.page-sidebar-wrapper').toggleClass('open');
  });
}

// 退出登录
function LoginOut() {
  if (confirm('确定要退出登录吗？')) {
    console.log('退出登录');
    // 这里可以添加退出登录的逻辑
    window.location.href = "/login";
  }
}

// 窗口大小改变事件
$(window).on('resize', function() {
  checkMobileView();
});
