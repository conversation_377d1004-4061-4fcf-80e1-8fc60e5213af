/*! 
 * 日期时间选择插件
 * daterangeselect.min.js v0.0.1 
 * author liuht 
 * github: https://github.com/liu75675231/daterangeselect
 * author personal website: http://www.lht.ren 
*/
(function () {
  'use strict'; function a(a) { var b = ["<button class=\"btn btn-success btn-sm drs-btn-submit drs-opt-btn\" type=\"button\">\u786E\u5B9A</button>", "<button class=\"btn btn-default btn-sm drs-btn-cancel drs-opt-btn\" type=\"button\">\u53D6\u6D88</button>"]; return a.output && a.output.allowEmtpy && b.push("<button class=\"btn btn-default btn-sm drs-btn-reset drs-opt-btn\" type=\"button\">\u91CD\u7F6E</button>"), b.join("") } function b(a) { for (var b = [], c = +a - 10, d = 0; d < 18; d++)b.push("<div class=\"drs-header-popup-item drs-year-" + c + "\" data-num=\"" + c + "\">" + c + "</div>"), c++; return b.join("") } function c() { return 18 } function d() { return j } function e(a) { var b = []; return a.range.show && a.range.data.forEach(function (a) { var c; c = "object" === ("undefined" == typeof a ? "undefined" : o(a)) ? a : n.ranges[a], c && b.push("<div class=\"drs-shortcut-item\" data-type=\"" + c.type + "\" data-start=\"" + c.offsetStart + "\" data-end=\"" + c.offsetEnd + "\">" + c.name + "</div>") }), b.join("") } function f(a) { var b = a.$DateRangeSelect; b.find(".drs-shortcut-item").click(function () { var b, c = $(this); b = c.index(), a.$select[0].selectedIndex = b, a.setRange(c.data("type"), c.data("start"), c.data("end")) }) } function g(a) { var b = a.$DateRangeSelect, c = a.options; c.range.show && (b.addClass("with-range"), !c.range.data.length && (c.range.data = Object.keys(n.ranges)), b.find(".drs-shortcut-list").html(e(c)), f(a)) } var h, i, j = new Date().getFullYear(), k = ["\u4E00", "\u4E8C", "\u4E09", "\u56DB", "\u4E94", "\u516D", "\u65E5"], l = ["1\u6708", "2\u6708", "3\u6708", "4\u6708", "5\u6708", "6\u6708", "7\u6708", "8\u6708", "9\u6708", "10\u6708", "11\u6708", "12\u6708"], m = function () { function a(a, c, e, f) { var g; this.DateRangeSelect = a, this.$calendar = c, this.date = e, this.isStartCalendar = f, g = this, this.date.setHours(0, 0, 0, 0), this._visibleMonth = this.month(), this._visibleYear = this.year(), this.$title = this.$calendar.find(".drs-header-date-title"), this.$dayHeaders = this.$calendar.find(".drs-header-week"), this.$input = this.$calendar.find(".drs-input-date"), this.$days = this.$calendar.find("tbody"), this.$dateDisplay = this.$calendar.find(".drp-calendar-date"), this.currentHeaderPopupMaxYear = d(), this.$title.find(".drs-header-popup-container").html(b(this.currentHeaderPopupMaxYear)), c.find(".drs-arrow").click(function () { return "next" === $(this).data("type") ? "month" === $(this).data("span") ? g.showNextMonth() : g.showNextYear() : "month" === $(this).data("span") ? g.showPreviousMonth() : g.showPreviousYear(), !1 }), this.$title.find(".drs-header-btn").off("click").click(function (a) { var b = $(a.target), c = g.$title.find(".drs-header-popup-year"), e = g.$title.find(".drs-header-popup-month"); "year" === b.data("type") ? ("block" === c.css("display") ? c.hide() : (g.setYearSelectPanel(d()), g.$title.find(".drs-header-popup-item").removeClass("drs-selected"), g.$title.find(".drs-year-" + g._visibleYear).addClass("drs-selected"), c.show()), e.hide()) : ("block" === e.css("display") ? e.hide() : (g.$title.find(".drs-header-popup-item").removeClass("drs-selected"), g.$title.find(".drs-month-" + g._visibleMonth).addClass("drs-selected"), e.show()), c.hide()), "start" === g.$calendar.data("type") ? g.DateRangeSelect.endCalendar.$title.find(".drs-header-popup").hide() : g.DateRangeSelect.startCalendar.$title.find(".drs-header-popup").hide() }), this.$title.on("click", ".drs-header-popup-item", function (a) { var b = $(a.target); if (b.hasClass("drs-header-popup-priv")) return void g.setYearSelectPanelToPriv(); if (b.hasClass("drs-header-popup-next")) return void g.setYearSelectPanelToNext(); var c = b.parent(".drs-header-popup").data("type"); if ("month" === c) g.setCurrentMonth(b.data("num")); else { var d; d = b.hasClass("drs-header-popup-this-year") ? new Date().getFullYear() : b.data("num"), g.setCurrentYear(d) } }) } return a.prototype.setYearSelectPanelToPriv = function () { this.currentHeaderPopupMaxYear -= c(), this.$title.find(".drs-header-popup-container").html(b(this.currentHeaderPopupMaxYear)) }, a.prototype.setYearSelectPanelToNext = function () { this.currentHeaderPopupMaxYear += c(), this.$title.find(".drs-header-popup-container").html(b(this.currentHeaderPopupMaxYear)) }, a.prototype.setYearSelectPanel = function (a) { this.currentHeaderPopupMaxYear = a, this.$title.find(".drs-header-popup-container").html(b(this.currentHeaderPopupMaxYear)) }, a.prototype.showPreviousMonth = function () { return 1 === this._visibleMonth ? (this._visibleMonth = 12, this._visibleYear -= 1) : this._visibleMonth -= 1, this.draw() }, a.prototype.showPreviousYear = function () { return this._visibleYear -= 1, this.draw() }, a.prototype.setCurrentYear = function (a) { this._visibleYear = a, this.draw() }, a.prototype.showNextMonth = function () { return 12 === this._visibleMonth ? (this._visibleMonth = 1, this._visibleYear += 1) : this._visibleMonth += 1, this.draw() }, a.prototype.setCurrentMonth = function (a) { this._visibleMonth = a, this.draw() }, a.prototype.showNextYear = function () { return this._visibleYear += 1, this.draw() }, a.prototype.setDay = function (a) { return this.setDate(this.visibleYear(), this.visibleMonth(), a), this.DateRangeSelect.showCustomDate() }, a.prototype.setDate = function (a, b, c) { return this.date = new Date(a, b - 1, c), this.DateRangeSelect.showCustomDate(), this.DateRangeSelect.draw() }, a.prototype.draw = function () { var a, b, c; for (this.$dayHeaders.empty(), this.$title.find(".drs-header-year").text(this.visibleYear() + "\u5E74"), this.$title.find(".drs-header-month").text(this.nameOfMonth(this.visibleMonth())), (b = 0, c = k.length); b < c; b++)a = k[b], this.$dayHeaders.append($("<th>" + a + "</th>")); return this.drawDateDisplay(), this.drawDays() }, a.prototype.dateIsSelected = function (a) { return a.getTime() === this.date.getTime() }, a.prototype.dateIsInRange = function (a) { return a >= this.DateRangeSelect.startDate() && a <= this.DateRangeSelect.endDate() }, a.prototype.dayClass = function (a, b) { var c, d; return d = "priv" === b ? new Date(this.visibleYear(), this.visibleMonth() - 2, a) : "next" === b ? new Date(this.visibleYear(), this.visibleMonth(), a) : new Date(this.visibleYear(), this.visibleMonth() - 1, a), c = "", this.dateIsSelected(d) ? (c = "drs-active", this.$calendar.hasClass("drs-start-date") && (c += " drs-start-day"), this.$calendar.hasClass("drs-end-date") && (c += " drs-end-day")) : this.dateIsInRange(d) ? (c = "drs-in-range", d.getTime() === this.DateRangeSelect.endDate().getTime() && (c += " drp-day-last-in-range")) : this.isStartCalendar ? d > this.DateRangeSelect.endDate() && (c += " drs-day-disabled") : d < this.DateRangeSelect.startDate() && (c += " drs-day-disabled"), c }, a.prototype.isLastDayOfWeek = function (a, b, c) { return 0 == (a + b - 2) % 7 || a === c }, a.prototype.drawDays = function () { var a, b, c, d, e; d = this, this.$days.empty(), a = this.firstDayOfMonth(this.visibleMonth(), this.visibleYear()); var f = this.daysOfLastWeekOfLastMonth(this.visibleMonth(), this.visibleYear()); c = this.daysInMonth(this.visibleMonth(), this.visibleYear()); for (var g = [], h = 0; h < f.length; h++)g.push("<td class='drs-not-current-month " + this.dayClass(f[h], "priv") + "' data-type='priv'>" + f[h] + "</td>"); for (b = e = 1; e <= c; b = e += 1)if (g.push("<td class='" + this.dayClass(b, "cur") + "' data-type='cur'>" + b + "</td>"), this.isLastDayOfWeek(b, a, c)) { if (7 > g.length) for (var j = 6 - g.length, k = 0; k <= j; k++)g.push("<td class='drs-not-current-month " + this.dayClass(k + 1, "next") + "' data-type='next'>" + (k + 1) + "</td>"); this.$days.append($("<tr>" + g.join("") + "</tr>")), g = [] } return this.$calendar.find("td").click(function () { var a; return !$(this).hasClass("drs-day-disabled") && (a = parseInt($(this).text(), 10), !isNaN(a) && void ("priv" === $(this).data("type") ? (d.setDate(d._visibleYear, d._visibleMonth - 1, a), d.showPreviousMonth()) : "next" === $(this).data("type") ? (d.setDate(d._visibleYear, d._visibleMonth + 1, a), d.showNextMonth()) : d.setDay(a))) }) }, a.prototype.drawDateDisplay = function () { return this.$dateDisplay.text([this.month(), this.day(), this.year()].join("/")) }, a.prototype.month = function () { return this.date.getMonth() + 1 }, a.prototype.day = function () { return this.date.getDate() }, a.prototype.dayOfWeek = function () { return this.date.getDay() + 1 }, a.prototype.year = function () { return this.date.getFullYear() }, a.prototype.visibleMonth = function () { return this._visibleMonth }, a.prototype.visibleYear = function () { return this._visibleYear }, a.prototype.nameOfMonth = function (a) { return l[a - 1] }, a.prototype.firstDayOfMonth = function (a, b) { return new Date(b, a - 1, 1).getDay() + 1 }, a.prototype.daysOfLastWeekOfLastMonth = function (a, b) { for (var c = new Date(b, a - 1, 0), d = c.getDate(), e = c.getDay(), f = [], g = e; g--;)f.push(d - g); return f }, a.prototype.daysInMonth = function (a, b) { return a || (a = this.visibleMonth()), b || (b = this.visibleYear()), new Date(b, a, 0).getDate() }, a }(), n = { rangeTypes: { day: !0, week: !0, month: !0, season: !0, year: !0 }, ranges: { today: { type: "day", name: "\u4ECA\u5929", offsetStart: 0, offsetEnd: 0 }, yesterday: { type: "day", name: "\u6628\u5929", offsetStart: -1, offsetEnd: -1 }, last7Days: { type: "day", name: "\u6700\u8FD17\u5929", offsetStart: -6, offsetEnd: 0 }, thisWeek: { type: "week", name: "\u672C\u5468", offsetStart: 0, offsetEnd: 0 }, lastWeek: { type: "week", name: "\u4E0A\u5468", offsetStart: -1, offsetEnd: -1 }, thisMonth: { type: "month", name: "\u672C\u6708", offsetStart: 0, offsetEnd: 0 }, lastMonth: { type: "month", name: "\u4E0A\u6708", offsetStart: -1, offsetEnd: -1 }, thisSeason: { type: "season", name: "\u672C\u5B63", offsetStart: 0, offsetEnd: 0 }, lastSeason: { type: "season", name: "\u4E0A\u5B63", offsetStart: -1, offsetEnd: -1 }, thisYear: { type: "year", name: "\u4ECA\u5E74", offsetStart: 0, offsetEnd: 0 }, lastYear: { type: "year", name: "\u53BB\u5E74", offsetStart: -1, offsetEnd: -1 } } }, o = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (a) { return typeof a } : function (a) { return a && "function" == typeof Symbol && a.constructor === Symbol && a !== Symbol.prototype ? "symbol" : typeof a }; h = jQuery; var p = { output: { show: void 0, allowEmtpy: !1, plain: !1 }, range: { show: !0, data: [] }, submitCallback: function () { } }; i = function () {
    function b(a, b) { this.$select = a, this.$DateRangeSelect = h("\n    <div class=\"drs-datepicker-container\">\n        <div class=\"drs-shortcut-wrapper\">\n            <div class=\"drs-shortcut-list\">\n                <div class=\"drs-shortcut-item\" data-type=\"day\" data-start=\"0\" data-end=\"0\">\u4ECA\u5929</div>\n                <div class=\"drs-shortcut-item\" data-type=\"day\" data-start=\"-1\" data-end=\"-1\">\u6628\u5929</div>\n                <div class=\"drs-shortcut-item\" data-type=\"day\" data-start=\"-6\" data-end=\"0\">\u6700\u8FD17\u5929</div>\n                <div class=\"drs-shortcut-item\" data-type=\"week\" data-start=\"0\" data-end=\"0\">\u672C\u5468</div>\n                <div class=\"drs-shortcut-item\" data-type=\"week\" data-start=\"-1\" data-end=\"-1\">\u4E0A\u5468</div>\n                <div class=\"drs-shortcut-item\" data-type=\"month\" data-start=\"0\" data-end=\"0\">\u672C\u6708</div>\n                <div class=\"drs-shortcut-item\" data-type=\"month\" data-start=\"-1\" data-end=\"-1\">\u4E0A\u6708</div>\n                <div class=\"drs-shortcut-item\" data-type=\"season\" data-start=\"0\" data-end=\"0\">\u672C\u5B63</div>\n                <div class=\"drs-shortcut-item\" data-type=\"season\" data-start=\"-1\" data-end=\"-1\">\u4E0A\u5B63</div>\n                <div class=\"drs-shortcut-item\" data-type=\"year\" data-start=\"0\" data-end=\"0\">\u4ECA\u5E74</div>\n                <div class=\"drs-shortcut-item\" data-type=\"year\" data-start=\"-1\" data-end=\"-1\">\u53BB\u5E74</div>\n            </div>\n        </div>\n        \n        <div class=\"drs-main-content\">\n            <div class=\"drs-calendar-content\">\n                <div class=\"drs-start-date drs-date-panel\" data-type=\"start\">\n                    <div class=\"input-group input-group-sm\">\n                        <span class=\"input-group-btn\">\n                            <span class=\"btn btn-default\">\n                                <b class=\"glyphicon glyphicon-calendar fa fa-calendar\"></b>\n                            </span>\n                        </span>\n                        <input type=\"text\" class=\"form-control drs-input-date\" placeholder=\"\u5F00\u59CB\u65F6\u95F4\" data-type=\"start\">\n                    </div>\n                    <table>\n                        <thead>\n                            <tr>\n                                <th class=\"drs-arrow\" data-type=\"priv\" title=\"\u4E0A\u4E00\u5E74\" data-span=\"year\">\n                                    <b class=\"glyphicon glyphicon-backward\"></b>\n                                </th>\n                                <th class=\"drs-arrow\" data-type=\"priv\" title=\"\u4E0A\u4E00\u4E2A\u6708\" data-span=\"month\">\n                                    <b class=\"glyphicon glyphicon-chevron-left\"></b>\n                                </th>\n                                <th class=\"drs-header-date-title\" colspan=\"3\">\n                                    <span class=\"drs-header-year drs-header-btn\" data-type=\"year\"></span>&nbsp;\n                                    <span class=\"drs-header-month drs-header-btn\" data-type=\"month\"></span>\n                                    <div class=\"drs-header-popup drs-header-popup-month\" data-type=\"month\" style=\"display: none;\">\n                                        <div class=\"drs-header-popup-item drs-month-1\" data-num=\"1\">1\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-2\" data-num=\"2\">2\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-3\" data-num=\"3\">3\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-4\" data-num=\"4\">4\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-5\" data-num=\"5\">5\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-6\" data-num=\"6\">6\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-7\" data-num=\"7\">7\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-8\" data-num=\"8\">8\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-9\" data-num=\"9\">9\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-10\" data-num=\"10\">10\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-11\" data-num=\"11\">11\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-12\" data-num=\"12\">12\u6708</div>\n                                    </div>\n                                    <div class=\"drs-header-popup drs-header-popup-year\" data-type=\"year\" style=\"display: none;\">\n                                        <div class=\"drs-header-popup-container\"></div>\n                                        <div class=\"drs-header-popup-item drs-header-popup-priv\"><i class=\"glyphicon glyphicon-arrow-left drs-header-popup-priv\"></i></div>\n                                        <div class=\"drs-header-popup-item drs-header-popup-this-year\">\u4ECA\u5E74</div>\n                                        <div class=\"drs-header-popup-item drs-header-popup-next\"><i class=\"glyphicon glyphicon-arrow-right drs-header-popup-next\"></i></div>\n                                    </div>\n                                </th>\n                                <th class=\"drs-arrow\" data-type=\"next\" title=\"\u4E0B\u4E00\u4E2A\u6708\" data-span=\"month\">\n                                    <b class=\"glyphicon glyphicon-chevron-right\"></b>\n                                </th>\n                                 <th class=\"drs-arrow\" data-type=\"next\" title=\"\u4E0B\u4E00\u5E74\" data-span=\"year\">\n                                    <b class=\"glyphicon glyphicon-forward\"></b>\n                                </th>\n                            </tr>\n                            <tr class=\"drs-header-week\"></tr>\n                        </thead>\n                        <tbody></tbody>\n                    </table>\n                </div>\n                <div class=\"drs-end-date drs-date-panel\"  data-type=\"end\">\n                    <div class=\"input-group input-group-sm\">\n                        <span class=\"input-group-btn\">\n                            <span class=\"btn btn-default\">\n                                <b class=\"glyphicon glyphicon-calendar fa fa-calendar\"></b>\n                            </span>\n                        </span>\n                        <input type=\"text\" class=\"form-control  drs-input-date\" placeholder=\"\u7ED3\u675F\u65F6\u95F4\" data-type=\"end\">\n                    </div>\n                    <table>\n                        <thead>\n                            <tr>\n                                <th class=\"drs-arrow\" data-type=\"priv\" title=\"\u4E0A\u4E00\u5E74\" data-span=\"year\">\n                                    <b class=\"glyphicon glyphicon-backward\"></b>\n                                </th>\n                                <th  class=\"drs-arrow\" data-type=\"priv\"  title=\"\u4E0A\u4E00\u4E2A\u6708\" data-span=\"month\">\n                                    <b class=\"glyphicon glyphicon-chevron-left\"></b>\n                                </th>\n                                <th class=\"drs-header-date-title\" colspan=\"3\">\n                                    <span class=\"drs-header-year drs-header-btn\" data-type=\"year\"></span>&nbsp;\n                                    <span class=\"drs-header-month drs-header-btn\" data-type=\"month\"></span>\n                                    <div class=\"drs-header-popup drs-header-popup-month\" data-type=\"month\" style=\"display: none;\">\n                                        <div class=\"drs-header-popup-item drs-month-1\" data-num=\"1\">1\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-2\" data-num=\"2\">2\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-3\" data-num=\"3\">3\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-4\" data-num=\"4\">4\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-5\" data-num=\"5\">5\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-6\" data-num=\"6\">6\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-7\" data-num=\"7\">7\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-8\" data-num=\"8\">8\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-9\" data-num=\"9\">9\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-10\" data-num=\"10\">10\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-11\" data-num=\"11\">11\u6708</div>\n                                        <div class=\"drs-header-popup-item drs-month-12\" data-num=\"12\">12\u6708</div>\n                                    </div>\n                                    <div class=\"drs-header-popup drs-header-popup-year\" data-type=\"year\" style=\"display: none;\">\n                                        <div class=\"drs-header-popup-container\"></div>\n                                        <div class=\"drs-header-popup-item  drs-header-popup-priv\"><i class=\"glyphicon glyphicon-arrow-left drs-header-popup-priv\"></i></div>\n                                        <div class=\"drs-header-popup-item drs-header-popup-this-year\">\u4ECA\u5E74</div>\n                                        <div class=\"drs-header-popup-item  drs-header-popup-next\"><i class=\"glyphicon glyphicon-arrow-right drs-header-popup-next\"></i></div>\n                                    </div>\n                                </th>\n                                <th  class=\"drs-arrow\" data-type=\"next\"  title=\"\u4E0B\u4E00\u4E2A\u6708\" data-span=\"month\">\n                                    <b class=\"glyphicon glyphicon-chevron-right\"></b>\n                                </th>                                 \n                                <th class=\"drs-arrow\" data-type=\"next\" title=\"\u4E0B\u4E00\u5E74\" data-span=\"year\">\n                                    <b class=\"glyphicon glyphicon-forward\"></b>\n                                </th>\n                            </tr>\n                            <tr class=\"drs-header-week\"></tr>\n                        </thead>\n                        <tbody></tbody>\n                    </table>\n                </div>\n            </div>\n            <div class=\"drs-btn-panel\">  \n            </div>\n        </div>\n    </div>"), this.$select.attr("tabindex", "-1").before(this.$DateRangeSelect), this.isHidden = !0, this.options = h.extend(!0, {}, p, b), this.initOptions(), this.initBindings(), b.startDate && b.endDate ? this.setRange("direct", b.startDate, b.endDate) : this.setRange("day", 0, 7) } return b.prototype.initOptions = function () { g(this), this.$select.parent().css({ position: "relative" }), this.$DateRangeSelect.find(".drs-btn-panel").html(a(this.options)) }, b.prototype.initBindings = function () { var a; a = this, this.$select.on("focus mousedown", function () { var a; return a = this, setTimeout(function () { return a.blur() }, 0), !1 }), this.$DateRangeSelect.bind("click", function (b) { var c = h(b.target); return c.hasClass("drs-header-popup-priv") || c.hasClass("drs-header-popup-next") ? b.stopPropagation() : (c.hasClass("drs-header-btn") || a.$DateRangeSelect.find(".drs-header-popup").hide(), b.stopPropagation()) }), h(document).click(function (b) { return (b.target === a.$select[0] || h(b.target).parent(a.$select[0])[0] === a.$select[0]) && a.isHidden ? (a.$DateRangeSelect.find(".drs-header-popup").hide(), a.show()) : a.isHidden ? void 0 : a.hide() }), this.$DateRangeSelect.find(".drs-btn-cancel").click(function () { return a.hide() }), a.options.output && a.options.output.allowEmtpy && this.$DateRangeSelect.find(".drs-btn-reset").click(function () { var b = a.options.output; return b.show && ("input" === b.show.prop("tagName").toLowerCase() ? b.show.val("") : b.show.text("")), b.start && ("input" === b.start.prop("tagName").toLowerCase() ? b.start.val("") : b.start.text("")), b.end && ("input" === b.end.prop("tagName").toLowerCase() ? b.end.val("") : b.end.text("")), a.options.submitCallback && a.options.submitCallback(void 0, void 0), a.hide() }), this.$DateRangeSelect.find(".drs-btn-submit").click(function () { if (a.startCalendar.date > a.endCalendar.date) return void console.error("\u5F00\u59CB\u65F6\u95F4\u4E0D\u80FD\u5927\u4E8E\u7ED3\u675F\u65F6\u95F4\uFF0C\u8BF7\u91CD\u65B0\u9009\u62E9"); var b = a.formatDate(a.startDate()), c = a.formatDate(a.endDate()), d = a.startDate(), e = a.endDate(); if (a.options.output) { var f = a.options.output, g = f.show, h = f.start, i = f.end; if (g) { var j = b + "\u81F3" + c; "input" === g.prop("tagName").toLowerCase() ? g.val(j) : g.text(j) } h && ("input" === h.prop("tagName").toLowerCase() ? h.val(b) : h.html(b)), i && ("input" === i.prop("tagName").toLowerCase() ? i.val(c) : i.text(c)), a.options.output.plain && (d = a.formatDate(d), e = a.formatDate(e)) } return a.options.submitCallback && a.options.submitCallback(d, e), a.hide() }), this.$DateRangeSelect.on("keypress blur", ".drs-input-date", function (b) { if ("keypress" === b.type && 13 !== b.which) return !0; var c = !0, d = {}; return a.$DateRangeSelect.find(".drs-input-date").each(function (b, e) { if ("" === e.value || "Invalid Date" == new Date(e.value)) return c = !1, console.error("\u65E5\u671F\u4E3A\u7A7A\u6216\u8005\u4E0D\u5408\u6CD5"), a.showCustomDate(), !1; var f = h(e); d[f.data("type")] = e.value }), c && (d.start > d.end ? a.setRange("direct", d.start, d.start) : a.setRange("direct", d.start, d.end)), !0 }) }, b.prototype.hide = function () { return this.isHidden = !0, this.$DateRangeSelect.hide() }, b.prototype.show = function () { return this.isHidden = !1, this.adjustPosition(), this.$DateRangeSelect.show() }, b.prototype.adjustPosition = function () { var a = this.$select.get(0).getBoundingClientRect(), b = { top: a.height + "px" }; a.left + 650 > window.innerWidth ? b.right = "0px" : b.left = "0px", this.$DateRangeSelect.css(b) }, b.prototype.showCustomDate = function () { this.startCalendar.$input.val(this.formatDate(this.startDate())), this.endCalendar.$input.val(this.formatDate(this.endDate())) }, b.prototype.formatDate = function (a) { return a.getFullYear() + "-" + this.formatNumber(a.getMonth() + 1) + "-" + this.formatNumber(a.getDate()) }, b.prototype.formatNumber = function (a) { var b = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : 2; return (Array(b).join(0) + a).slice(-b) }, b.prototype.setRange = function () {
      var a, b, c = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : "day", d = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : 0, e = 2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : 30, f = { day: function (a, b) { a.setDate(a.getDate() + b) }, week: function (a, b, c) { var d = void 0, d = a.getDate() - a.getDay() + 1 + 7 * b; c && (d += 6), a.setDate(d) }, month: function (a, b, c) { a.setDate(1), c ? (a.setMonth(a.getMonth() + b + 1), a.setDate(a.getDate() - 1)) : a.setMonth(a.getMonth() + b) }, season: function (a, b, c) { var d = Math.ceil; a.setDate(1); var e = d((a.getMonth() + 1) / 3) + b; c ? (a.setMonth(3 * e), a.setDate(a.getDate() - 1)) : a.setMonth(3 * (e - 1)) }, year: function (a, b, c) { a.setDate(1), a.setMonth(0), c ? (a.setFullYear(a.getFullYear() + b + 1), a.setDate(a.getDate() - 1)) : a.setFullYear(a.getFullYear() + b) }, direct: function (a, b) { var c = new Date(b); return "Invalid Date" == c ? (console.error("\u65E5\u671F\u683C\u5F0F\u4E0D\u6B63\u786E"), !1) : void (a.setDate(c.getDate()), a.setMonth(c.getMonth()), a.setFullYear(c.getFullYear())) } }; return f[c] ? (a = new Date, b = new Date, f[c](b, d, !1), f[c](a, e, !0), this.startCalendar = new m(this, this.$DateRangeSelect.find(".drs-start-date"), b, !0), this.endCalendar = new m(this, this.$DateRangeSelect.find(".drs-end-date"), a, !1), this.showCustomDate(), this.draw()) : void console.error("\u7C7B\u578B\u4E0D\u5BF9\uFF1A" + c + ", we need " + Object.keys(f).join(","));// daysAgo -= 1;
    }, b.prototype.endDate = function () { return this.endCalendar.date }, b.prototype.startDate = function () { return this.startCalendar.date }, b.prototype.draw = function () { return this.startCalendar.draw(), this.endCalendar.draw() }, b
  }(), h.fn.DateRangeSelect = function (a) { return new i(this, a) }
})();
