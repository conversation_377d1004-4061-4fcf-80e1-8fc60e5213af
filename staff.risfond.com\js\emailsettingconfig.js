/// <reference path="../Scripts/jquery-1.8.2.js" />
/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
(function ($) {
	var emailsettingconfig = {
		init: function (el, params) {
			var opts = {
				callback: function () { },
				getconfigurl: "/services/staffactionhandler.ashx?action=getemailconfig",
				updateconfigurl: "/services/staffactionhandler.ashx?action=updateemailconfig",
				vaildurl: "/services/staffactionhandler.ashx?action=validateemaillaccount"
			}
			this.opts = $.extend({}, opts, params || {});
			this.getconfig();
			this.submit();
			this.vaildEvent();
			return this.setting;
		},
		collectionparams: function () {
			return {
				emailaccount: $(".email-box .emailaccount").val(),
				emailaccounttype: $(".email-box .emailaccounttype").val(),
				emailpwd: $(".email-box .emailpwd").val(),
				emailsign: $(".email-box .emailsign").val()
			};
		},
		verification: function (data) {
			if ($.trim(data.emailaccount).length == 0 && data.emailaccount.indexOf("@") < 0) {
				beautAlert.done(setRnssLanguage("邮件账号设置错误"), 'hits'); return false;
			}
			if ($.trim(data.emailaccounttype).length == 0) {
				beautAlert.done(setRnssLanguage("邮件账号服务器类别设置错误"), 'hits'); return false;
			}
			if ($.trim(data.emailsign).length == 0) {
				beautAlert.done(setRnssLanguage("邮件签名设置错误"), 'hits'); return false;
			}
			return true;
		},
		submit: function () {
			var $this = this;
			$(".my-email-message .btn-submit").click(function () {
				var data = $this.collectionparams();
				if (!$this.verification(data)) {
					return;
				}
				var account = $this.dom.find(".emailaccount").val();
				var pwd = $this.dom.find(".emailpwd").val();
				var type = $this.dom.find(".emailaccounttype").val();
				if (!$this.vaild(type, account, pwd)) {
					var result = confirm(setRnssLanguage("邮箱用户名密码错误，确认修改?"));
					if (!result) {
						return;
					}
				}
				$.ajaxPost($this.opts.updateconfigurl, data, function (result) {
					if (result.Status == 1) {
						beautAlert.done(setRnssLanguage("邮件配置成功!"), 'succ');
						DialogAlert.msgClose();
						$this.opts.callback.call(this, $this.setting);
					}
				});
			});
			$(".my-email-message .btn-cancel").click(DialogAlert.msgClose);
		},
		getconfig: function () {
			var $this = this;
			$.ajaxPost(this.opts.getconfigurl, {}, function (result) {
				if (result.Status == 1) {
					$this.dom.find(".emailaccount").val(result.Data.EmailAccount);
					$this.dom.find(".emailsign").val(result.Data.EmailSign)
					$this.dom.find(".emailaccounttype").val(result.Data.EmailAccount)
					$this.dom.find(".emailpwd").val(result.Data.EmailPwd)
					$this.show($this.dom);
				}
				else {
					beautAlert.done(result.Data, 'hits');
				}
			})
		},
		dom: $('<div class="email-box cf"><div class="line"><label class="emailkey">' + setRnssLanguage('邮件账号：') + '</label><div class="emailvalue"><input type="text" class="emailaccount input-v1" /><span class="emailconnect">@</span><select class="emailaccounttype"><option value="2">risfond.com</option></select><a href="javascript:;" class="btn-setsendmail">' + setRnssLanguage('发送邮件') + '</a></div></div><div class="line"><label class="emailkey">' + setRnssLanguage('邮件密码：') + '</label><div class="emailvalue"><input type="text" class="emailpwd input-v1" /><div class="email-number-tip"><span class="tip-status"></span></div></div></div><div class="line"><label class="emailkey">' + setRnssLanguage('邮件签名：') + '</label><div class="emailvalue"><textarea class="emailsign emailsign-config textarea-v1"></textarea></div></div></div><div class="dialog-footer"><div class="my-dialog-form my-email-message"><input type="button" class="btn-submit" value="' + setRnssLanguage('确定') + '" /><input type="button" class="btn-cancel" value="' + setRnssLanguage('取消') + '" /></div></div>'),
		show: function (html) {
			var $this = this;
			DialogAlert.msgOpen({msgTitle: setRnssLanguage("邮件配置"), msgBody: html, msgWidth: 500, msgPanelWidth: 490});
		},
		setting: function () {
			return {
				setcategory: function (value) {
					$(".email-box .emailaccounttype").val(value);
				},
				callback: function () { }
			};
		},
		vaildEvent: function () {
			var $this = this;
			$(".email-box .emailpwd,.email-box .emailaccount").die("blur").live("blur", function () {
				var account = $this.dom.find(".emailaccount").val();
				var pwd = $this.dom.find(".emailpwd").val();
				var type = $this.dom.find(".emailaccounttype").val();
				var status = $this.dom.find(".email-number-tip .tip-status");
				if ($.trim(account).length == 0 || $.trim(pwd).length == 0) { return; }
				var result = $this.vaild(type, account, pwd);
				if (result) {
					status.addClass("succ").removeClass("hits").attr("title", setRnssLanguage("用户名密码正确"));
				}
				else {
					status.addClass("hits").removeClass("succ").attr("title", setRnssLanguage("用户名密码错误"));
				}
			});
		},
		vaild: function (type, account, pwd) {
			var r = false;
			$.ajaxPost(this.opts.vaildurl, { type: type, account: account, pwd: pwd }, function(result) {
				if (result.Status == 1) {
					r = true;
				} else {
					r = false;
				}
			}, "json");
			return r;
		}

	}
	$.fn.EmailSettingConfig = function (params) {
		return emailsettingconfig.init(this, params);
	}
})(jQuery);