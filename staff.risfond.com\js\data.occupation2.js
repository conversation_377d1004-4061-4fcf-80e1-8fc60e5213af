var data_occupation2 = undefined,
  data_occupation3;
var buildOccUpationDatas = function (datas) {
  if (!datas || !datas.length) {
    return [];
  }
  var list = [];
  datas.forEach(function (item, index) {
    item.Hierarchy = 1;
    if (item.Childens && item.Childens.length) {
      item.Childens.forEach(function (item2, index2) {
        item2.Hierarchy = 2;
        if (item2.Childens && item2.Childens.length) {
          item2.Childens.forEach(function (item3, index3) {
            item3.Hierarchy = 3;
          });
        }
      });
    }
  });
  var buildData = function (item) {
    var obj = {};
    for (var i in item) {
      if (i == "Childens") {
        continue;
      }
      if (i == "Id") {
        obj[i] = item[i] + "";
      }
      else if (i == "ParentId" && item[i] == 0) {
        obj[i] = "";
      }
      else if (i == "ParentId") {
        obj[i] = item[i] + "";
      }
      else {
        obj[i] = item[i];
      }
    }
    //obj.Hierarchy = sum;
    return obj;
  };
  var Sum = 0;
  var flat = function (nodes, sum) {
    if (!nodes || nodes.length === 0) return [];
    nodes.forEach(function (node) {
      list.push(buildData(node));
      return flat(node.Childens);
    });
  };
    flat(datas);
    return list;
};

$.ajax({
  type: "POST",
    url: "/Occupation/GetOccupation?ContainsEx=" + getQueryString("ContainsEx") + "&occupationsSplit=" + getQueryString("occupationsSplit"),
  data: {},
    dataType: "json",
    async: false,
  success: function (req) {
    data_occupation2 = req;
    data_occupation3 = buildOccUpationDatas(data_occupation2);
  }
});
function getQueryString(name) {
    var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    var r = window.location.search.substr(1).match(reg);
    if (r != null) {
        return unescape(r[2]);
    }
    return null;
}
/**
 * @description: 将多余的行业数据移除
 * @param {*} list
 * @time 2021-5-31
 * @return {*}
 */
//  function setOccupationData(list) {
//   var menuOne = []
//   // 遍历一级目录
//   list.forEach((listItem, ListIndex)=>{
//     // 如果满足要求则保留
//     if(listItem.Source == 'System') {
//       // 如果为空则停止
//       if(listItem.Childens.length==0) {return}
//       // 将符合要求的数据进行保存
//       listItem.Childens = setOccupationData(listItem.Childens)
//       menuOne.push(listItem)
//     }
//   })
//   return menuOne
// }
