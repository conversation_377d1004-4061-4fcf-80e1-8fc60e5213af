// 之所以要写成template的形式，是因为有两个layout模板需要引用同一个html模板，为了避免同一个代码写两遍导致的未来可维护性问题，所以写到这里
function insertChatTemplate() {
  var template = `
     <div class="modal-dialog" role="document" v-on:click.stop="bubbleToRoot">
      <div class="modal-content">
        <div class="chat-container">
          <img class="chat-hide-icon" src="/images/chat/model-close.png" v-on:click="hideChatModal"/> 
          <div class="chat-menu">
            <div class="chat-menu-header">
              <!-- 头部当前登录人信息-->
              <div class="chat-menu-header-profile-panel">
                <div class="chat-menu-header-profile">
                  <img class="chat-menu-profile-logo" :src="curUser.PictureUrl" />
                  <span class="chat-menu-profile-name">
                    {{ curUser.Name_Cn }}({{ curUser.Name_En }})
                  </span>
                </div>
                <div class="chat-menu-header-opt" v-if="false" v-on:click="openOrCloseOptPanel">
                  <i class="fa fa-bars chat-menu-header-opt-icon"></i>
                </div>
                <ul class="chat-menu-opt-popup" v-if="false" v-show="isShowOptPanel">
                  <li class="chat-menu-opt-popup-item" v-on:click="openSearchGroup()">申请入群</li>
                  <li class="chat-menu-opt-popup-item" v-on:click="openPopup('createGroup')">创建群组</li>
                  <li class="chat-menu-opt-popup-item">清空缓存</li>
                </ul>
              </div>
              <div class="chat-menu-search-panel">
                <img class="chat-menu-search-icon" src="/images/chat/search.png" />
                <input class="chat-menu-search-input" v-model="search.keywords" placeholder="请按回车进行搜索" v-on:keyup.enter="searchStaffAndGroup"/>
                <img v-show="search.keywords !== ''" 
                  class="chat-menu-search-icon search-remove" 
                  src="/images/chat/search-remove.png" 
                  v-on:click="removeSearchKeywords"/>
                <div class="chat-menu-search-popup" v-if="search.isShow">
                  <div class="chat-menu-search-scroll-wrapper">
                    <div class="chat-menu-search-list">
<!-- 搜索回车后查询出的顾问列表-->
                      <div class="chat-menu-search-title">通讯录</div>
                      <div class="chat-menu-search-item" v-for="staff in search.staffList" v-on:click="openChatPanel1('single', staff);removeSearchKeywords();">
                        <div class="chat-menu-search-staff-info">
                          <img class="chat-menu-search-staff-logo" :src="staff.PictureUrl" />
                          <div class="chat-menu-search-staff-profile">
                            <div class="chat-menu-search-staff-name">{{ staff.Name_Cn }}（{{ staff.Name_En }}）</div>
                            <div class="chat-menu-search-staff-company">{{ staff.CompanyName_Cn }}</div>
                          </div>
                        </div>
                      </div>
                      <div class="chat-menu-search-loading-more" 
                        v-if="!search.loading && search.page.page * search.page.pageSize < search.total" 
                        v-on:click.stop="searchStaffByPage(search.page.page + 1)">点击加载更多</div>
                      <div class="chat-menu-search-loading-more" v-if="search.loading">加载中</div>
                      <div class="chat-menu-search-title">群组</div>
                      <div class="chat-menu-search-item" v-for="group in search.groupList" v-on:click="openChatPanel('group', group.groupid);removeSearchKeywords();">
                        <div class="chat-menu-search-staff-info">
                          <img class="chat-menu-search-staff-logo" src="/images/chat/group-empty.png" />
                          <span class="chat-menu-search-staff-name">{{ group.groupname }}11</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="chat-menu-tab">
              <div class="chat-menu-tab-item chat-menu-tab-messages"
                   title="聊天消息"
                   v-bind:class="{active: menuShowType === 'messages'}" 
                   v-on:click="changeMenuShowType('messages')">
                <div class="tab-icon-wrapper">
                  <img class="tab-icon tab-icon-unselected" src="/images/chat/menu-message.png" />
                  <img class="tab-icon tab-icon-selected" src="/images/chat/menu-message-selected.png" />
                  <div class="tab-icon-unread" v-if="hasUnreadMessage"></div>
                </div>
              </div>
              <div class="chat-menu-tab-item chat-menu-tab-friends"
                   title="好友列表"
                   v-on:click="changeMenuShowType('friends')"
                   v-bind:class="{active: menuShowType === 'friends'}">
                  <img class="tab-icon tab-icon-unselected" src="/images/chat/menu-friends.png" />
                  <img class="tab-icon tab-icon-selected" src="/images/chat/menu-friends-selected.png" />
              </div>
<!--群组列表隐藏<div class="chat-menu-tab-item chat-menu-tab-groups"
                   title="群组列表"
                   v-on:click="changeMenuShowType('groups')"
                   v-bind:class="{active: menuShowType === 'groups'}">
                  <img class="tab-icon tab-icon-unselected" src="/images/chat/menu-group.png" />
                  <img class="tab-icon tab-icon-selected" src="/images/chat/menu-group-selected.png" />
              </div>-->
            </div>
            <div class="chat-menu-content" v-bind:class="{'menu-mask': search.isShow }">
              <div class="chat-menu-content-item chat-menu-messages-panel" v-if="menuShowType === 'messages'">
                <div class="chat-list">
<!--24.01.31注释群通知<div class="chat-item" v-on:click="showGroupNoticePanel" v-bind:class="{active: chatPanelType === 'notice'}">
                    <div class="chat-message-item-wrapper">
                      <div class="chat-item-time" v-if="notice.info.createDate">{{ formatedDate(notice.info.createDate, true) }}</div>
                      <div class="chat-item-avatar">
                        <img class="chat-item-avatar-icon" src="/images/chat/notice.png" />
                      </div>
                      <div class="chat-item-info">
                        <div class="chat-item-nickname">群通知</div>
                        <div class="chat-item-msg">{{ notice.info.msg }}</div>
                      </div>
                      <div v-if="notice.info.isUnread" class="chat-list-unread-icon">{{ notice.info.isUnread }}</div>
                    </div>
                  </div>-->
<!-- 左侧数据集合-->
                  <div class="chat-item" v-for="item in messageList"
                       v-bind:class="{active: isMessageSelected(item.id, item.type)}"
                       v-on:click="openChatPanel(item.type === 'chat' ? 'single': 'group', item.id, item.unreadNum)"
                       v-on:contextmenu.prevent="chatMessageRightClick(item.id, 'messageList', $event)">
                    <div class="chat-message-item-wrapper">
                      <div class="chat-item-time">
                        {{ formatedDate(item.date, true) }}
                      </div>
                      <img v-if="item.type === 'chat'" class="chat-item-avatar" :src="item.user.img != null ? item.user.img : '/images/chat/staff-girl.png'" />  
<!--<img v-if="item.type === 'chat'" class="chat-item-avatar" :src="getStorageData('staff', item.id).PictureUrl" />  -->
                      <img v-else class="chat-item-avatar" src="/images/chat/group-empty.png" />                      
                      <div class="chat-item-info">
                        <div class="chat-item-nickname">{{ item.user.name }}</div>
<!--<div class="chat-item-nickname">{{ getMessageName(item.id, item.type) }}</div>-->
                        <div class="chat-item-msg">
                          <template v-if="item.notice.type">
                            <span class="chat-item-msg-notice">[{{ getMessageNoticeTypeConf(item.notice.type) }}]</span>
                            <span v-html="item.notice.msg"></span>
                          </template>
                          <template v-else>
                            {{ item.msg }}
                          </template>

                        </div>
                      </div>
<!-- item.isUnread || -->
                      <div class="chat-list-unread-icon" v-if="item.unreadNum > 0">{{ item.unreadNum }}</div>
                    </div>      
                  </div>
                </div>
              </div>
<!-- menuShowType === 'groups'-->
              <div class="chat-menu-content-item chat-menu-groups-panel" v-if="menuShowType === 'groups'">
                <div class="chat-list">
                  <div class="chat-menu-groups-empty" v-if="Object.keys(groupList).length === 0">
                    <img class="chat-menu-groups-empty-img" src="/images/chat/empty.png">
                    <div class="chat-menu-groups-empty-name">暂无群组</div>
                  </div>
                  <div class="chat-item" 
                       v-for="group in groupList"
                       v-bind:class="{ active: curChatbox.type === 'group' && curChatbox.toId === group.groupid }"
                       v-on:click="openChatPanel('group', group.groupid)">
                      <img src="/images/chat/group-empty.png" class="chat-item-avatar" />
                      <div class="chat-item-info">
                        {{ group.groupname }}    
                      </div>
                  </div>
                </div>
              </div>
<!-- 好友列表-menuShowType === 'friends'-->
              <div class="chat-menu-content-item chat-menu-friends-panel" v-if="menuShowType === 'friends'">
                <div class="chat-item-wrapper" v-for="(friend, index) in friendList">
                  <div class="chat-item company-item" v-on:click="openOrCloseStaffPanel(index, friend.Id, $event)">
                    {{ friend.Text }}
                    <b class="chat-item-icon chat-item-icon-open glyphicon glyphicon-plus"></b>
                    <b class="chat-item-icon chat-item-icon-close glyphicon glyphicon-minus"></b>
                  </div>
                  <div class="chat-item-role">
                    <div class="chat-item-role-item chat-item chat-item-loading">加载中...</div>
                    <div class="chat-item-role-item chat-item" 
                         v-for="staff in friend.staffList" 
                         v-bind:class="{ active: isMenuStaffSelected(staff.StaffId) }"
                         v-on:click="openStaffIntroPanel(staff.StaffId)"
                         v-on:dblclick="openChatPanel1('single', staff)">
                      <img :src="staff.PictureUrl" class="chat-item-avatar" />
                      <div class="chat-item-info">
                        <div class="chat-item-nickname">{{ staff.Name_Cn }}（{{ staff.Name_En }}）</div>
                        <div class="chat-item-msg">{{ staff.Department_Cn }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
            </div>
          </div>
          <div class="chat-panel">
            <div class="chat-panel-header">
              <div class="chat-panel-title">
                {{ chatboxTitle }}&nbsp;
                <div class="chat-panel-header-opt" 
                     v-if="false"
                     v-on:click="openGroupSettingPanel">
                  <b class="chat-panel-header-down glyphicon glyphicon-chevron-down" v-if="chatPanelType === 'groupSetting'"></b>
                  <b class="chat-panel-header-up glyphicon glyphicon-chevron-up" v-if="chatPanelType === 'chatbox'"></b>
                </div>
                <span class="chat-modal-close" title="关闭聊天窗口" v-on:click="hideChatModal">&times</span>
              </div>
            </div>
            <div class="chat-panel-panel">
              <div class="chat-group-setting-wrapper" v-if="chatPanelType === 'groupSetting'">
                <div class="chat-group-setting-item">
                  <div class="chat-group-setting-item-title">
                    <div class="chat-group-setting-item-title-name">
                      群名称
                    </div>
                    <div class="chat-group-setting-item-title-opt" v-if="!groupSetting.info.membersonly && !groupSetting.isNameEdit" v-on:click="showGroupSetting('name')">
                      <b class="glyphicon glyphicon-pencil"></b>
                    </div>
                  </div>
                  <div class="chat-group-setting-item-content">
                    <div v-if="groupSetting.isNameEdit">
                      <div class="input-group input-group-sm">
                        <input type="text" class="form-control" v-model.lazy="groupSetting.name" placeholder="请输入名称">
                        <span class="input-group-btn">
                          <button class="btn btn-default" type="button" v-on:click="updateGroupSetting('name')">保存</button>
                          <button class="btn btn-default" type="button" v-on:click="cancelGroupSetting('name')">撤销</button>
                        </span>
                      </div>
                    </div>
                    <span v-else>
                      {{ groupSetting.info.name }}
                    </span>
                  </div>
                </div>
                <div class="chat-group-setting-item">
                  <div class="chat-group-setting-item-title">
                    
                    <div class="chat-group-setting-item-title-name">
                      群组成员
                    </div>
                    <div class="chat-group-setting-item-title-opt">
                      共{{ groupSetting.info.affiliations_count }}人
                    </div>
                  </div>
                  <div class="chat-group-setting-item-content">
                    <div class="chat-group-member-item"
                         v-for="(item, staffId) in groupSetting.members"
                         v-bind:class="{ 'is-owner': item.type === 'owner', 'is-admin': item.type === 'admin' }"
                         v-on:click.stop="showStaffDetailsPopup($event, staffId, 'editGroup')">
                      <img class="chat-group-member-logo" :src="getStorageData('staff', staffId).PictureUrl" />
                      <div class="chat-group-member-name">{{ getStorageData('staff', staffId).Name_Cn }}</div>
                    </div>
                    <div class="chat-group-member-item add-member"
                         v-if="groupSetting.info.allowinvites || (!groupSetting.info.allowinvites && groupSetting.members[curUserId].type !== 'member')"
                         v-on:click="showUserSelectPopup(editGroupAddMemberToGroup)">
                      <div class="chat-group-member-logo">+</div>
                      <div class="chat-group-member-name">邀请</div>
                    </div>
                  </div>
                </div>
                <div class="chat-group-setting-item">
                  <div class="chat-group-setting-item-title">
                    <div class="chat-group-setting-item-title-name">
                      群公告
                    </div>
                    <div class="chat-group-setting-item-title-opt" 
                         v-if="!groupSetting.info.membersonly && !groupSetting.isDescEdit" 
                         v-on:click="showGroupSetting('description')">
                      <b class="glyphicon glyphicon-pencil"></b>
                    </div>
                  </div>
                  <div class="chat-group-setting-item-content">
                    <div v-if="groupSetting.isDescEdit">
                      <div class="input-group input-group-sm">
                        <input type="text" class="form-control" v-model.lazy="groupSetting.description" placeholder="请输入群公告">
                        <span class="input-group-btn">
                          <button class="btn btn-default" type="button" v-on:click="updateGroupSetting('description')">保存</button>
                          <button class="btn btn-default" type="button" v-on:click="cancelGroupSetting('description')">撤销</button>
                        </span>
                      </div>
                    </div>
                    <span v-else>
                      {{ groupSetting.info.description || '暂无公告' }}
                    </span>
                  </div>
                </div>
                <div class="chat-group-setting-item">
                  <div class="chat-group-setting-list-item">
                    <div class="chat-group-setting-list-title">群聊ID</div>
                    <div class="chat-group-setting-list-content">{{ groupSetting.info.id }}</div>
                  </div>
                  <div class="chat-group-setting-list-item last-item">
                    <div class="chat-group-setting-list-title">是否公开</div>
                    <div class="chat-group-setting-list-content">{{ groupSetting.info.public ? '公开': '未公开'}}</div>
                  </div>
                </div>
                <div class="chat-group-leave-btn" v-if="groupSetting.info.owner == curUserId" v-on:click="dissolveGroup(groupSetting.info.id)">
                  解散群聊
                </div>
                <div class="chat-group-leave-btn" v-else v-on:click="quitGroup(groupSetting.info.id)">
                  退出群聊
                </div>
              </div>
<!-- :paste粘贴-->
              <div class="chat-wrapper" v-if="chatPanelType === 'chatbox' && curChatbox.loaded" v-on:paste="handlePasteImage">
                  <div class="chat-content" id="chat-content-box">
                    <!-- 聊天页数据集合-->
                    <div class="chat-msg-list"><!-- chatListPage.per * chatListPage.page < chatListPage.total -->
                      <div class="chat-msg-load-more" v-show="chatListPage.vernier < chatListPage.total" v-on:click="loadChatMsgByPage">加载更多消息</div>
                      <div class="chat-msg-item" v-for="(item, index) in chatList" v-bind:class="{me: item.from == curUserId }">
                        <div class="chat-msg-time" v-if="isShowChatTime(item.createDate, chatList[index - 1] ? chatList[index - 1].createDate : undefined)">
                          {{ formatedDate(item.createDate, false) }}
                        </div>
<!--item.ext.nickUrl  item.ext.nickName-->
                        <div class="chat-msg-item-data">
                          <img class="chat-msg-item-logo" :src="getStorageData('staff', item.from).PictureUrl != null && getStorageData('staff', item.from).PictureUrl ? getStorageData('staff', item.from).PictureUrl : '/static/images/resumereportDefault.png'" v-on:click.stop="showStaffDetailsPopup($event, item.from, 'chatbox')"/>
                          <div class="chat-msg-item-info" v-bind:class="item.from == curUserId ? 'me-item-info' : ''">
<!-- 已读、未读-->
<div class="chat-read-status">
  <!-- 已读状态-->
  <div class="chat_box_status read-stats" v-if="item.isRead == 1">已读</div>
  <!-- 未读状态-->
  <div class="chat_box_status1 read-stats" v-else-if="item.isRead == 0">未读</div>
</div>
<div>
                            <div class="chat-msg-info-name">{{ getStorageData('staff', item.from).Name_Cn }}</div>
                            <div class="chat-msg-info-bubble">
<!-- custom自定义卡片-->
                              <template v-if="item.messageType === 'custom'">
 <!-- 自定义卡片-在线简历-->
                                <div v-if="item.ext.type === 'industry_resume'" class="chat-msg-info-plain chat-msg-resume" v-on:contextmenu="chatMessageRightClick" v-on:click="openResumePage(item.ext.messageResume.Id)">
                                   
                                  <div class="reusmeCard_mian_box">
                                    <image class="reusmeCardimg" src="/images/chat/chatInlineResume.png" />
                                    <div class="resumeSignView">
                                      <span class="resumeName">{{ item.ext.messageResume.Name }}</span>
                                      <span class="signText">
                                        {{ showMessageResume(item.ext).signTitleName }}
                                      </span>
                                    </div>
                                  </div >

<!--<div class="chat-resume-profile">
                                    <div class="chat-resume-name">{{ item.ext.messageResume.Name }}</div>
                                    <div class="chat-resume-salary">{{ item.ext.messageResume.YearSalary }}</div>
                                  </div>
                                  <div class="chat-resume-company">{{ item.ext.messageResume.JobTitle }}</div>
                                  <div class="chat-resume-info-list">
                                    <div class="chat-resume-info" v-if="item.ext.messageResume.LiveLocationTxt" 
                                      v-bind:title="item.ext.messageResume.LiveLocationTxt">
                                      {{ item.ext.messageResume.LiveLocationTxt }}
                                    </div>
                                    <div class="chat-resume-info" v-if="item.ext.messageResume.Age" 
                                      v-bind:title="item.ext.messageResume.Age + '岁'">
                                      {{ item.ext.messageResume.Age }}岁
                                    </div>
                                    <div class="chat-resume-info" v-if="item.ext.messageResume.WorkYear" 
                                      v-bind:title="item.ext.messageResume.WorkYear + '经验'">
                                      {{ item.ext.messageResume.WorkYear }}工作经验
                                    </div>
                                    <div class="chat-resume-info" v-if="item.ext.messageResume.EducationLevelTxt" 
                                      v-bind:title="item.ext.messageResume.EducationLevelTxt">
                                      {{ item.ext.messageResume.EducationLevelTxt }}
                                    </div>
                                  </div>-->
                                </div>
<!-- 自定义卡片，职位卡片消息-->
                                <div v-else-if="item.ext.type === 'industry_job'" class="chat-msg-info-plain chat-msg-job" v-on:contextmenu="chatMessageRightClick" v-on:click="openJobPage(item.ext.messageJob.ID)">
                                  <div class="chat-resume-profile">
                                    <div class="chat-resume-name">{{ item.ext.messageJob.Title }}</div>
                                    <div class="chat-resume-salary">{{ item.ext.messageJob.Salary }}</div>
                                  </div>
                                  <div class="chat-resume-company">{{ item.ext.messageJob.ClientName }}</div>
                                  <div class="chat-resume-info-list">
                                    <div class="chat-resume-info" v-if="item.ext.messageJob.Locations">{{ item.ext.messageJob.Locations }}</div>
                                    <div class="chat-resume-info" v-if="item.ext.messageJob.RunDay" v-if="item.ext.messageJob.RunDay !== undefined">
                                      {{ item.ext.messageJob.RunDay }}天
                                    </div>
                                    <div class="chat-resume-info" v-if="item.ext.messageJob.Code">{{ item.ext.messageJob.Code }}</div>
                                    <div class="chat-resume-info" v-if="item.ext.messageJob.LastCommunicationTime">
                                      {{ item.ext.messageJob.LastCommunicationTime }}
                                    </div>
                                  </div>
                                </div>
                              </template>
<!-- 其它：文本或者表情包-->
                                <div class="chat-msg-info-plain" v-if="item.messageType === 'txt'"
                                  v-on:contextmenu="chatMessageRightClick" 
                                  v-html="formatChatHtml(item.messagePlain)"></div>
<!-- 文件消息-->
                              <div class="chat-msg-info-file" v-if="item.messageType === 'file'">
                                <div class="chat-msg-info-file-logo-panel">
                                  <img class="chat-msg-info-file-logo" src="/images/chat/chat-file.png" />
                                </div>
                                <div class="chat-msg-info-file-info">
                                  <div class="chat-msg-info-file-title" :title="item.filename">{{ item.filename }}</div>
                                  <div class="chat-msg-info-file-desc">
                                    <span>{{ formatFileLen(item.file_length) }}</span>
                                    <span>|</span>
                                    <span class="file-download" v-on:click="downloadFile(item)">点击查看</span>
                                  </div>
                                </div>
                              </div>
<!-- 图片消息-->
                              <div class="chat-msg-info-image" v-if="item.messageType === 'img'">
                                <img class="chat-msg-info-image-img chat-image-loadding" 
                                  :src="item.url" 
                                  v-on:load="chatImgLoaded" 
                                  v-on:click="showPreviewImg(item.url)"/>
                              </div>
<!-- 语音消息-->
                              <div class="chat-msg-info-audio" v-if="item.messageType === 'audio'">
                                <!-- 抱歉，当前不支持语音消息，请到app端查看-->
                                <audio 
                                  :src="item.fileUrl"
                                  class="content-audio"
                                  :controls="true"
                                ></audio>
                              </div>
<!-- 视频消息-->
                              <div class="chat-msg-info-audio" v-if="item.messageType === 'video'">
                                <!-- 抱歉，当前不支持视频消息，请到app端查看-->
                                <video :src="item.fileUrl" class="content-video"
                                  v-on:click="showVideo(item.fileUrl)"
                                  controls style="width: 150px" />
                              </div>
                            </div>
</div>
                          </div>
                        </div>
                      </div>

<!-- 图片或者文件发送时的loading展占位图-->
                    <div class="chat-msg-item me" v-if="isImgLoading">
                        <div class="chat-msg-item-data">
                          <img class="chat-msg-item-logo" :src="curUser.PictureUrl != null && curUser.PictureUrl ? curUser.PictureUrl : '/static/images/resumereportDefault.png'"/>
                          <div class="chat-msg-item-info">
                            <div class="chat-msg-info-bubble"> 
<!-- 图片占位-->
                              <div class="chat-msg-info-image">
                                <img class="chat-msg-info-image-img chat-image-loadding" style="width:200px;"
                                  src="/images/chat/schedule.gif"/>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>


                    </div>
                  </div>
                  <div class="chat-input-panel">
                    <div class="chat-input-panel-toolbar" v-on:click.stop="return false;" v-on:mouseup.stop="return false;">
                      <div class="chat-toolbar-item">
                        <i class="fa fa-smile-o chat-toolbar-icon" v-on:mousedown.stop="openExpressionPanel"></i>
                      </div>
                      <div class="chat-toolbar-item" v-if="false">
                        <i class="fa fa-file-image-o chat-toolbar-icon"></i>
                        <input type="file" style="display: none;" v-on:change="sendImage"/>
                      </div>
                      <div class="chat-toolbar-item">
                        <i class="fa fa-file-o chat-toolbar-icon file" v-on:click="selectSendFile"></i>
                        <input id="chat-file-send" style="display: none;" type="file" v-on:change="sendFile"/>
                      </div>
                      <div class="chat-expression-panel" v-if="curChatbox.emojiPopup.isShow" v-on:click.stop="void(0);">
                        <div class="chat-expression-wrapper">
                          <ul class="chat-expression-header">
                            <li class="chat-expression-item" v-bind:class="{active: curChatbox.emojiPopup.activeMenu === 'default'}">
                              <span class="chat-expression-item-words" v-on:click="changeActiveMenu('default')">表情</span>
                            </li>
                            <!--<li class="chat-expression-item" v-bind:class="{active: curChatbox.emojiPopup.activeMenu === 'tuzki'}">
                              <span class="chat-expression-item-words" v-on:click="changeActiveMenu('tuzki')">兔斯基</span>
                            </li>-->
                          </ul>
                          <div class="chat-expression-content-panel">
                            <div class="chat-expression-emoji-list" v-if="curChatbox.emojiPopup.activeMenu === 'default'">
                              <div class="chat-expression-emoji-item" 
                                   v-for="(item, index) in getEmojiList('default')" 
                                   v-on:click="selectExpression(index, 'default')">
                                <img class="chat-expression-emoji-icon" :src="emojiPath + item.img" />
                              </div>

                            </div>
                            <div class="chat-expression-emoji-list big-img hover-to-gif" v-if="curChatbox.emojiPopup.activeMenu === 'tuzki'">
                              <div class="chat-expression-emoji-item" 
                                   v-for="(item, index) in getEmojiList('tuzki')"
                                   v-on:click="selectExpression(index, 'tuzki')">
                                <img class="chat-expression-emoji-icon" :src="emojiPath + item.img" />
                                <img class="chat-expression-emoji-icon emoji-hover" :src="emojiPath + item.gif" />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="chat-input-panel-content">
                      <pre id="chat-input" class="chat-input-panel-input"
                           v-on:keyup.shift.50="openAtPanel"
                           v-on:keydown.enter="handleInputBoxEnter"
                           v-on:paste="handlePaste"
                           v-on:blur="insertTextAfterCursor('<span class=placeholder></span>', $event)"
                           v-model="curChatbox.chatMsg" contenteditable></pre>
                      <div class="chat-input-at-popup" v-if="curChatbox.atPopup.isShow" v-bind:style="{top: curChatbox.atPopup.top + 'px', left: curChatbox.atPopup.left + 'px'}">
                        <div class="chat-input-at-item" v-for="staffId in curChatbox.atPopup.staffList" v-on:mouseup="selectedAtStaff(staffId)">
                          <div class="chat-input-at-info">
                            <img class="chat-input-at-img" :src="getStorageData('staff', staffId).PictureUrl" />
                            <span class="chat-input-at-name">{{ getStorageData('staff', staffId).Name_Cn }}</span>
                          </div>
                          <div class="chat-input-at-company">{{ getStorageData('staff', staffId).CompanyName_Cn }}</div>
                        </div>
                      </div>
                    </div>
                    <div class="chat-input-panel-opt">
                      <span class="chat-input-opt-notice">按下Ctrl+Enter换行</span>
                      <span class="chat-input-opt-send" v-on:click.stop="sendChatMsg">
                        发送
                        <div class="chat-empty-msg-notice" v-if="curChatbox.isShowBlankMsgNotice">
                          不能发送空白消息
                        </div>
                      </span>
                    </div>
                  </div>
              </div>
              <div class="chat-staff-intro-wrapper" v-if="chatPanelType === 'intro'">
                  <div class="chat-staff-intro-panel">
                    <div class="chat-staff-intro-profile">
                      <img class="chat-staff-intro-img" :src="curStaffIntro.PictureUrl" />
                      <div class="chat-staff-intro-name-panel">
                        <div class="chat-staff-intro-name">{{ curStaffIntro.Name_Cn }}（{{ curStaffIntro.Name_En }}）</div>
                        <div class="chat-staff-intro-title">{{ curStaffIntro.Position_Cn }}</div>
                      </div>
                    </div>
                    <div class="chat-staff-intro-info">
                      <div class="chat-staff-intro-item">
                        <div class="chat-staff-intro-item-title">公司</div>
                        <div class="chat-staff-intro-item-content">{{ curStaffIntro.CompanyName_Cn }}</div>
                      </div>
                      <div class="chat-staff-intro-item">
                        <div class="chat-staff-intro-item-title">部门</div>
                        <div class="chat-staff-intro-item-content">{{ curStaffIntro.Department_Cn }}</div>
                      </div>
                      <div class="chat-staff-intro-item">
                        <div class="chat-staff-intro-item-title">团队</div>
                        <div class="chat-staff-intro-item-content">{{ curStaffIntro.LeaderName }}</div>
                      </div>
                      <div class="chat-staff-intro-item">
                        <div class="chat-staff-intro-item-title">手机号</div>
                        <div class="chat-staff-intro-item-content" v-if="StaffContactViewLogType==1">{{ StaffContactViewLogMobilePhone }}</div>
                        <div class="chat-staff-intro-item-content all-titphone" v-else><a v-on:click="ShowStaffContactViewLogTips(curStaffIntro.StaffId)" class="sc-txt-view" style="color:#2A7FCC;text-decoration: underline;cursor: pointer;">点击查看</a></div>
                      </div>
                      <div class="chat-staff-intro-item">
                        <div class="chat-staff-intro-item-title">邮箱</div>
                        <div class="chat-staff-intro-item-content">{{ curStaffIntro.Email }}</div>
                      </div>
                    </div>
                    <div class="chat-staff-chat">
                      <span class="chat-staff-chat-btn" v-on:click="openChatPanel1('single', curStaffIntro)">发消息</span>
                    </div>
                  </div>
              </div>
              <div class="chat-notice-wrapper" v-if="chatPanelType === 'notice'">
                <div class="chat-notice-list-wrapper">
                  <div class="chat-notice-loading" v-if="notice.list.length === 0 && notice.isLoading">数据加载中，请稍后...</div>
                  <div class="chat-notice-list">
                    <div class="chat-notice-item" v-for="item in notice.list">
                      <div class="chat-notice-info">
                        <template v-if="!item.Imgs">
                          <img class="chat-notice-icon" src="/images/chat/group-empty.png" />
                        </template>
                        <template v-else-if="item.Imgs.length === 1">
                          <img class="chat-notice-icon" :src="item.Imgs[0]" />
                        </template>

                        <template v-else>
                          <div class="chat-notice-icon-group">
                            <img class="chat-notice-icon-group-item" v-for="(img, index) in item.Imgs" v-if="index < 4" :src="img" />
                          </div>

                        </template>

                        <div class="chat-notice-desc">
                          <div class="chat-notice-group-name">{{ item.Title }}</div>
                          <div class="chat-notice-msg">{{ item.Content }}</div>
                        </div>
                      </div>
                      <div class="chat-notice-time">
                        {{ formatedDate(item.DateTime, true) }}
                      </div>
                    </div>
                  </div>
                </div>
                
              </div>
              <div class="chat-empty-wrapper" v-if="chatPanelType === undefined">
                  <div class="chat-empty-content">
                    <div>
                      <b class="chat-empty-icon fa fa-weixin"></b>
                    </div>
                    <div class="chat-empty-notice">未选择聊天</div>
                  </div>
              </div>
            </div>
         
          </div>
        </div>
        
        <ul class="menu-popup" v-if="menuPopup.isShow" v-bind:style="{ top: menuPopup.y + 'px', left: menuPopup.x + 'px'}">
          <template v-if="menuPopup.type === 'messageList'">
            <li class="menu-popup-item" v-on:click="removeMessageByUserId(menuPopup.id)">删除</li>
          </template>
          <template v-if="menuPopup.type === 'chatbox'">
            <li class="menu-popup-item" v-on:click="removeMessageByMessageId(menuPopup.id)">删除</li>

          </template>
        </ul>
        <div class="menu-popup user-detail-popup" 
             v-if="staffDetailsPopup.isShow" 
             v-bind:style="{ top: staffDetailsPopup.top + 'px', left: staffDetailsPopup.left + 'px'}">
          <img class="user-detail-logo" :src="showHDOriginImage(staffDetailsPopup.info.PictureUrl)" />
          <div class="user-detail-info">
            <div class="user-detail-brief">
              <div class="user-detail-name">
                {{ staffDetailsPopup.info.Name_Cn }}（{{ staffDetailsPopup.info.Name_En }}）
              </div>
              <div class="user-detail-title">{{ staffDetailsPopup.info.Position_Cn }}</div>
            </div>
            <div class="user-detail-details-list">
              <div class="user-detail-details-item">
                <div class="user-detail-item-name">公司</div>
                <div class="user-detail-item-val">{{ staffDetailsPopup.info.CompanyName_Cn }}</div>
              </div>
              <div class="user-detail-details-item">
                <div class="user-detail-item-name">部门</div>
                <div class="user-detail-item-val">{{ staffDetailsPopup.info.Department_Cn }}</div>
              </div>
              <div class="user-detail-details-item">
                <div class="user-detail-item-name">团队</div>
                <div class="user-detail-item-val">{{ staffDetailsPopup.info.LeaderName }}</div>
              </div>
              <div class="user-detail-details-item">
                <div class="user-detail-item-name">手机号</div>
                <div class="user-detail-item-val">{{ staffDetailsPopup.info.MobilePhone }}</div>
              </div>
              <div class="user-detail-details-item">
                <div class="user-detail-item-name">邮箱</div>
                <div class="user-detail-item-val">{{ staffDetailsPopup.info.Email }}</div>
              </div>
            </div>
            <div v-if="staffDetailsPopup.type !== ''">
              <div class="user-detail-opt-list"  v-if="staffDetailsPopup.type === 'editGroup' && staffDetailsPopup.info.StaffId != curUserId">
                <div class="user-detail-opt-item" 
                     v-if="isShowRemoveGroup(staffDetailsPopup.info.StaffId)" 
                     v-on:click="removeSingleGroupMember(groupSetting.info.id, staffDetailsPopup.info.StaffId)">移出群聊</div>
                <div class="user-detail-opt-item" v-if="groupSetting.info.owner == curUserId" v-on:click="setAdmin(groupSetting.info.id, staffDetailsPopup.info.StaffId)">{{ groupSetting.members[staffDetailsPopup.info.StaffId] && groupSetting.members[staffDetailsPopup.info.StaffId].type === 'admin' ? '取消管理员' : '设为管理员' }}</div>
                <div class="user-detail-opt-item" v-if="groupSetting.info.owner == curUserId" v-on:click="transferGroupOwner(groupSetting.info.id, groupSetting.info.owner, staffDetailsPopup.info.StaffId)">转让群主</div>
                <div class="user-detail-opt-item" v-on:click="openChatPanel('single', staffDetailsPopup.info.StaffId)">发送消息</div>
              </div>
            </div>
          </div>  
        </div>
        <div class="chat-popup" v-if="findGroupData.isShow" v-on:click="closeFindGroup">
          <div class="chat-popup-panel search-group" v-on:click.stop="return false;">
            <div class="chat-popup-panel-title">
              查找群组
            </div>
            <div class="chat-popup-panel-content">
              <div class="search-group-search">
                <div class="input-group input-group-sm">
                  <input type="text" class="form-control" placeholder="搜索群ID/群名称">
                  <span class="input-group-btn">
                    <button class="btn btn-default" type="button">搜索</button>
                  </span>
                </div>
              </div>
              <div class="search-group-list">
                <div class="search-group-item">
                  <div class="search-group-item-id">id</div>
                  <div class="search-group-item-name">群组名称</div>
                  <div class="search-group-item-opt">操作</div>
                </div>
                <div class="search-group-list-content">
                  <div class="search-group-item" v-for="item in findGroupData.list">
                    <div class="search-group-item-id">{{ item.groupid }}</div>
                    <div class="search-group-item-name">{{ item.groupname }}</div>
                    <div class="search-group-item-opt" v-on:click="joinGroup(item.groupid)">申请</div>
                  </div>
                </div>
                
              </div>
            </div>
          </div>
        </div>

        <div class="chat-popup" v-if="popup.isShow" v-on:click="closePopup">
          <div class="chat-popup-panel create-group" v-on:click.stop="return false;">
            <div class="chat-popup-panel-title">
              创建群组
            </div>
            <div class="chat-popup-panel-content">
              <div class="chat-popup-panel-item">
                <span class="create-group-name">群组名称</span>
                <input class="create-group-input" v-model="createGroupData.groupname" />
              </div>
              <div class="chat-popup-panel-item">
                <span class="create-group-name"></span>
                <div class="create-group-input">
                  <div class="checkbox create-group-checkbox">
                    <label>
                      <input type="checkbox" v-model="createGroupData.public"> 公开群组
                    </label>
                  </div>
                </div>
              </div>
              <div class="chat-popup-panel-item">
                <div class="create-group-name">添加成员</div>
                <div class="create-group-input user-list">
                  <div class="chat-group-member-item" v-for="(userId, index) in createGroupData.users">
                    <div class="chat-group-member-wrapper">
                      <img class="chat-group-member-logo" :src="getStaffInfo(userId).PictureUrl" />
                      <div class="chat-group-member-name">{{ getStaffInfo(userId).Name_Cn }}</div>
                      <span class="chat-group-member-remove glyphicon glyphicon-remove" v-on:click="removeStaffFromCreateGroup(index)"></span>
                    </div>
                    
                  </div>
                  <div class="chat-group-member-item add-member" v-on:click="showUserSelectPopup(createGroupAddMemberToGroup)">
                    <div class="chat-group-member-logo">
                      +
                    </div>
                    <div class="chat-group-member-name">邀请</div>
                  </div>
                </div>
              </div>

              <div class="create-group-opt">
                <div class="create-group-opt-btn" v-on:click="createGroupNew">确定</div>
              </div>
            </div>
          </div>
        </div>
        <div class="chat-popup" v-if="userSelectPopup.isShow"  v-on:click="closeUserSelectPopup">
          <div class="chat-popup-panel select-user-panel" v-on:click.stop="return false;">
            <div class="chat-popup-panel-title">
              <div class="chat-popup-panel-name">
                选择用户
              </div>
              <div class="chat-popup-panel-opt">
                <span class="btn btn-default btn-sm" type="submit" v-on:click="saveUserSelect">保存</span>
              </div>
            </div>

            <div class="chat-popup-panel-content">
              <div class="input-group input-group-sm">
                <input type="text" class="form-control" placeholder="搜索">
                <span class="input-group-btn">
                  <button class="btn btn-default" type="button">搜索</button>
                </span>
              </div>
              <div class="select-user-users-title">通讯录</div>
              <div class="select-user-users">
                <div class="select-user-users-list">
                  <div class="select-user-user-item" v-for="(friend, index) in friendList">
                    <div class="select-user-company" v-on:click="openOrCloseStaffList($event, index, friend.Id)">
                      <div>
                        {{ friend.Text }}
                      </div>
                      <div>
                        <span class="select-user-company-icon-close">+</span>
                        <span class="select-user-company-icon-open">-</span>
                      </div>
                    </div>
                    <div class="select-user-staff-list">
                      <div class="select-user-staff chat-item-loading" v-if="friend.staffList.length === 0">加载中...</div>
                      <div class="select-user-staff" v-for="staff in friend.staffList">
                        <div>
                          <input class="select-user-staff-checkbox" type="checkbox" v-on:change="selectOrCancelUserSelect($event, staff)" />
                          {{ staff.Name_Cn }}（{{ staff.Name_En }}）
                        </div>
                        <div>
                          {{ staff.Department_Cn }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="chat-popup" v-if="sendImageNoticePopup.isShow">
          <div class="chat-popup-panel chat-clipboard-image-popup">
            <div class="chat-popup-panel-title">
              发送图片
            </div>
            <div class="chat-popup-panel-content">
              <div class="chat-notice-popup-wrapper">
                <div class="chat-notice-popup-msg">
                  <img class="chat-clipboard-img" v-bind:src="sendImageNoticePopup.url" />
                </div>
              </div>
              <div class="chat-notice-popup-opt">
                <span class="chat-notice-popup-btn" v-on:click="saveSendImageNoticePopup">发送</span>
                <span class="chat-notice-popup-btn chat-notice-cancel-btn" v-on:click="cancelSendImageNoticePopup">取消</span>
              </div> 
            </div>
          </div>
        </div>
        <div class="chat-popup" v-if="noticePopup.isShow" v-on:click="isNoticePopupAllowBubble">
          <div class="chat-popup-panel chat-notice-popup" v-on:click.stop="return false;">
            <div class="chat-popup-panel-title">
              提示
            </div>
            <div class="chat-popup-panel-content">
              <div class="chat-notice-popup-wrapper">
                <div class="chat-notice-popup-msg" v-html="noticePopup.msg">
                </div>
              </div>
              <div class="chat-notice-popup-opt" v-if="noticePopup.optList.length > 0">
                <span v-for="opt in noticePopup.optList" class="chat-notice-popup-btn" v-on:click="opt.click">{{ opt.name }}</span>
              </div>
            </div>
          </div>
        </div>
    </div>
  </div>
  <div class="preview-popup" v-if="imgPreviewPopup.isShow">
    <div class="preview-popup-mask" v-on:click="hidePreviewImg"></div>
    <div class="preview-popup-wrapper">
      <template v-if="imgPreviewPopup.type == 'pdf' || imgPreviewPopup.type == 'docx' || imgPreviewPopup.type == 'doc'">
        <div class="img-box">
          <iframe :src="imgPreviewPopup.url" width="100%" height="100%"></iframe>
        </div>
      </template>
      <template v-else>
        <img class="preview-popup-img" v-bind:src="imgPreviewPopup.url">
      </template>
    </div>
    <img class="preview-popup-remove-icon" src="/images/chat/model-close.png" v-on:click="hidePreviewImg"/>
  </div>

  <div class="preview-popup" v-if="videoPreviewPopup.isShow">
    <div class="preview-popup-mask" v-on:click="hidePreviewVideo"></div>
    <div class="preview-popup-wrapper">
      <video id="myVideo" :src="videoPreviewPopup.url" class="content-video" controls style="width: 650px" />
    </div >
    <img class="preview-popup-remove-icon" src="/images/chat/model-close.png" v-on:click="hidePreviewVideo" />
  </div >`;

  $("#chat-modal").html(template);
}
insertChatTemplate();


var chatVue = new Vue({
  el: '#chat-modal',
  data: function () {
    return {
      StaffContactViewLogType: 0,
      StaffContactViewLogMobilePhone: "",
      isInit: false,
      connectStatus: 'online',     //  枚举值，为online, offline
      //      mobileHost: 'http://************:801',    // 测试
      mobileHost: 'http://rnssapi.risfond.com',    // 线上
      ready: false,
      openModalCallback: [],   // modal打开且环信链接成功后的回调，注意，回调执行后，回调函数会清空
      isShowOptPanel: false,
      imgPreviewPopup: {
        isShow: false,
        img: '',
        type: '',
        url: ''
      },
      videoPreviewPopup: {
        isShow: false,
        url: ''
      },
      noticePopup: {
        popupToRoot: true,
        isShow: false,
        msg: '',
        showOpt: true,
        optList: [],
      },
      sendImageNoticePopup: {
        isShow: false,
        blob: undefined,
        url: '',
      },
      staffDetailsPopup: {
        isShow: false,
        top: 0,
        left: 0,
        info: {},
        type: '',        // 枚举值：editGroup
      },
      userSelectPopup: {
        isShow: false,
        callback: undefined,
        selected: {},
      },
      notice: {
        info: {
          msg: '',
          isUnread: false,
          createDate: 0,
        },
        isLoading: false,
        list: [],
      },
      sysNotice: {
        info: {
          msg: '',
          isUnread: false,
          createDate: 0,
        },
        list: [],
      },
      popup: {
        isShow: false,
        type: 'createGroup',      // 值为createGroup, findGroup
      },
      menuPopup: {
        isShow: false,
        x: 0,
        y: 0,
        id: undefined,
        type: undefined,    // 枚举值：messageList, chatbox
      },
      menuShowType: 'messages',
      findGroupData: {
        isShow: false,
        list: [],
      },
      createGroupData: {
        groupname: '',
        desc: '',
        members: [],
        public: true,
        approval: false,
        allowinvites: true,
        users: [],
      },
      noticeList: [],
      originCreateGroupData: {},
      createGroupMsg: '',
      friendList: [],
      groupList: {},
      chatList: [],
      chatListPage: {//消息分页
        per: 15,
        page: 1,
        total: 0,
        vernier: 0,//从0开始
      },
      messageList: [],//消息集合
      indexedDB: window.indexedDB || window.webkitIndexedDB || window.mozIndexedDB || window.msIndexedDB,
      curUserId: undefined,//当前用户id
      curUser: {},//当前用户
      curUserDate: {},//初始化用户数据
      /**
      staffIndexedDBConf: {
        database: "staff",
        table: "data",
        id: '',
      },
      **/
      chatPanelType: undefined,       // 其值为undefined, "chatbox"， "intro", "groupSetting", "notice", "sysNotice"的枚举值，分别代表右侧框未选择，为聊天框样式，员工详情介绍页, 群提示，系统提示 
      curStaffIntro: {},//通讯录点击用户信息
      curChatbox: {
        type: "",
        database: "",
        table: "",
        toId: '',
        chatMsg: '',
        users: {},
        loaded: false,
        emojiPopup: {
          isShow: false,
          activeMenu: 'default',     //枚举值，为：default,tuzki  
        },
        atPopup: {
          isShow: false,
          staffList: [],
          top: 0,
          left: 0,
          beforeMsg: '',
        },
        isShowBlankMsgNotice: false,
      },
      groupSetting: {
        isNameEdit: false,
        isDescEdit: false,
        name: '',
        description: '',
        info: {},
        users: {},
        members: {},
      },
      storage: {
        staff: {
          admin: {
            Name_Cn: '系统小助手',
            PictureUrl: '/images/<EMAIL>',
          },
        },
      },
      storageCache: {
        idList: {},
        isRunningAjax: false,
        isClearData: false,
      },
      search: {
        isShow: false,
        type: '',
        page: {
          page: 1,
          pageSize: 5,
        },
        total: 0,
        keywords: '',
        staffList: [],
        groupList: [],
        loading: false,
        timer: undefined,
      },
      respData: {
        userId: 0,
        accountId: "",
        userName: null,
        userIcon: null,
        chinaName: null,
        passWord: "",
        userType: 1
      },
      //聊天类型
      MESSAGE_TYPE : {
        IMAGE: 'img',
        TEXT: 'txt',
        LOCATION: 'location',
        VIDEO: 'video',
        AUDIO: 'audio',
        EMOJI: 'emoji',
        FILE: 'file',
        CUSTOM: 'custom',
      },
      extension: ['pdf', 'png', 'jpg', 'jpeg', 'doc', 'docx'],//上传类型
      //chatApi: 'https://test.haidou.com/ris-elite-api',//聊天域名地址
      chatApi: PCChatUrl + 'ris-elite-api',//聊天域名地址https://elite-api.risfond.com:8015/
      toAccountId: '',//IM对方accoundId
      isImgLoading: false,//图片及文件上传loading占位
      isScorllButtom: false,//发送消息时滚动底部
      isScorllReceive: false,//接收消息时，不滚动
      isSendFileOrImgLoading: false,//发送文件或者图片时，loading后滚动底部
      UserIdNew:null,
    };
  },
  created: function () {
    this.originCreateGroupData = $.extend({}, this.createGroupData, true);

    this.ajaxGetCompany();
    this.init(0);
  },
  mounted: function () {
    var vm = this;
    var params = this.getUrlVars();

    if (params['isShowChat']) {
      $(".chat-box").css({
        display: 'initial',
      });
    }
    this.initEvent();
    $('#chat-modal').on('shown.bs.modal', function () {//打开对话框之前的事情
      if (vm.isInit) {//是否已经初始化
        vm.openModalCallbackHandler();
        vm.initMessageList(vm.curUserDate);
      } else {
        vm.init(1);
      }
      vm.resizePanel();
    })
    $("#chat-modal").keydown(function (e) {
      if (e.keyCode != 27) {
        return;
      }
      if (vm.imgPreviewPopup.isShow) {
        vm.hidePreviewImg();
      } else {
        vm.hideChatModal();
      }
    });
    $(window).resize(function () {
      vm.resizePanel();
    });
  },
  methods: {
    openModalCallbackHandler: function () {
      var $this = this;
      this.openModalCallback.forEach(function (elem) {
        elem.call($this);
      });
      this.openModalCallback = [];
    },
    openPanelAndChat: function (id) {
      $("#chat-modal").modal("show");
      var $this = this;
      this.openModalCallback.push(function () {
        $this.openChatPanel('single', id);
      });
    },
    login: function () {
      WebIMAPI.login(undefined, this.curUserId);
      this.connectStatus = 'online';

    },
    getUrlVars: function () {
      var vars = {}, hash;
      var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
      for (var i = 0; i < hashes.length; i++) {
        hash = hashes[i].split('=');
        vars[hash[0]] = hash[1];
      }
      return vars;
    },
    //初始化
    init: function (flg) {
      if (this.isInit) {
        return;
      }
      this.isInit = true;
      var vm = this;
      this.ajaxGetWebMI(function (data) {
        $.ajax({
          url: `${vm.chatApi}/eim/im-info?userId=${data.EaseMobAccount}&userType=1`,
          method: 'get',
          success: function (resp) {
            if (!resp || !resp.success) return;
            vm.respData = resp.data;
            if (resp.data.accountId && resp.data.passWord) {
              vm.initWebIMAPI(resp.data.accountId, resp.data.passWord, function () {
                vm.getGroupList();
              });
            } else {
              console.error("Invalid data received from API");
            }
            // 后续的初始化操作
            var noticeObj = vm.getNoticeObjFromStorage();
            vm.notice.info = noticeObj;
            vm.initDB();
            vm.getCurUserStaffInfo();//获取当前登录人的信息

            // 确保initMessageList调用时传入正确类型的数据
            vm.curUserDate = resp.data;
            if (flg == 1) {
              vm.initMessageList(resp.data);
            }
            vm.unReadCount();
          }
        }).fail(function (e) {
          console.error(e);
        });
        //vm.initWebIMAPI(data.EaseMobAccount, data.EaseMobPwd, function () {
        //  vm.getGroupList();
        //});
        //var noticeObj = vm.getNoticeObjFromStorage();
        //vm.notice.info = noticeObj;
        //vm.initDB();
        //vm.getCurUserStaffInfo();//获取当前用户staff信息
        //vm.initMessageList();//加载左侧聊天集合
      });
    },
    //获取未读数
    unReadCount: function () {
      let vm = this;
      $.ajax({
        url: `${this.chatApi}/imMsg/session-un_read_num`,
        method: 'get',
        data: {
          currentId: this.curUserId,
          isFiltration: 1
        },
        success: function (resp) {
          if (!resp || !resp.success) return;
          if (resp.data > 0) {
            setUnreadIconToRightMenu(true, resp.data)
          } else {
            setUnreadIconToRightMenu(false, 0)
          }
        }
      })
    },
    resizePanel: function () {
      this.resizeChatMenu();
      this.resizeChatPanel();
    },
    resizeChatPanel: function () {
      var modalHeight = $("#chat-modal .modal-content").height() - $("#chat-modal .chat-panel-header").height();
      $("#chat-modal .chat-panel-panel").css({
        height: modalHeight,
      });
    },
    resizeChatMenu: function () {
      var height = $("#chat-modal .modal-content").height() - $("#chat-modal .chat-menu-header").height() - $("#chat-modal .chat-menu-tab").height();
      $("#chat-modal .chat-menu-content").css({
        height: height + 'px',
      });
    },
    forbiddenPreviewPopupBubble: function (e) {
      e.preventDefault();
      e.stopPropagation();
      return false;
    },
    //预览图片
    showPreviewImg: function (img) {
      this.imgPreviewPopup = {
        isShow: true,
        img: img,
        url: img,
        type: 'png'
      };
    },
    //关闭预览图片
    hidePreviewImg: function () {
      this.imgPreviewPopup.isShow = false;
    },
    //放大看视频
    showVideo: function (src) {
      this.videoPreviewPopup = {
        isShow: true,
        url: src
      };
      setTimeout(function () {
        var video = document.getElementById("myVideo");
        video.play();
      }, 10)
    },
    //关闭视频播放
    hidePreviewVideo: function () {
      this.videoPreviewPopup.isShow = false;
    },
    removeSearchKeywords: function () {
      this.search.keywords = '';
    },
    isNoticePopupAllowBubble: function (e) {
      if (!this.noticePopup.popupToRoot) {
        e.stopPropagation();
        return false;
      }
    },
    cancelSendImageNoticePopup: function () {
      this.sendImageNoticePopup.isShow = false;
    },
    //发送内容框粘贴事件
    handlePaste: function (event) {
      var clipboardData = event.clipboardData || window.clipboardData;
      var paste = clipboardData.getData('text');

      var selection = window.getSelection();
      if (!selection.rangeCount) return false;
      selection.deleteFromDocument();
      selection.getRangeAt(0).insertNode(document.createTextNode(paste));

      event.preventDefault();
      //      this.insertTextAfterCursor(data, event)
    },
    closeNoticePopup: function () {
      this.noticePopup.isShow = false;
    },
    showChatBlankMsgNotice: function () {
      var vm = this;
      if (!this.curChatbox.isShowBlankMsgNotice) {
        setTimeout(function () {
          vm.curChatbox.isShowBlankMsgNotice = false;
        }, 1000);
      }

      this.curChatbox.isShowBlankMsgNotice = true;
    },
    
    //关闭聊天弹窗
    hideChatModal: function () {
      $(this.$el).modal('hide');
    },
    //接收消息后--重置消息
    receiveChatMsg: async function (data, messageType, messagePlain, option) {
      var vm = this;
      // 如果是自己发给自己的消息，为了防止重复，则接收侧直接抛弃消息，但是以后需要考虑多端通信的情况
      if (data.from == data.to) {
        return;
      }
      if (option === undefined) {
        option = {};
      }
      data.createDate = Date.now();
      data.messageType = messageType;
      data.messagePlain = messagePlain;

      var type = data.type === 'chat' ? 'single' : 'group';
      var id = type === 'single' ? data.from : data.to;
      //判断是否有存储----后期添加
      if (!this.storage.staff[id]) {//本地this.storage数据staffId对应信息如果为空
        await vm.ajaxGetStaffInfo(id, function (staffData) {
          vm.addToStorage('staff', staffData[0], true);
          vm.setChatMsg(data, type, id, messageType);
        });
      } else {
        vm.setChatMsg(data, type, id, messageType);
      }
    },
    //接收消息后--重置消息-存储及是否已读
    setChatMsg: function (data, type, id, messageType, option) {
      let vm = this;
      this.add(data, {
        database: 'chat-' + type + '-' + this.curUserId + '-' + id,
        table: 'chat_history',
      });
      var message = data.messagePlain;
      if (data.type === 'groupchat') {
        message = data.ext.nickName + ': ' + message;
      }
      var isUnread = true;
      //当恰好聊天面板打开的时候，就会直接显示内容，并且消息列表不显示未读，且聊天框是打开状态
      if (this.chatPanelType === 'chatbox' && this.curChatbox.toId == id) {
        if ($("#chat-modal").css('display') !== 'none') {//恰好聊天面板打开
          isUnread = false;
          //设置消息已读回执
          WebIMAPI.sendReadMessage(data.id, id);
          //单个设置已读调用接口
          setTimeout(function () {//因为监听环信接收消息过快，修改状态接口会比插入聊天消息接口先执行
            vm.setMessageSingleRead(data.id);
          }, 500)
        }
        if (messageType == 'custom') {
          if (data.customEvent == "industry_resume") {
            data.ext.messageResume = JSON.parse(data.ext.messageResume);
          } else if (data.customEvent == "industry_job") {
            data.ext.messageJob = JSON.parse(data.ext.messageJob);
          }
        }
        this.chatList.push(data);
        //游标值加1
        this.chatListPage.vernier++;
        this.chatListPage.total++;
        this.isScorllReceive = true;
        let isScroll = vm.checkScrollBottom();
        if (isScroll) {
          this.$nextTick(function () {
            vm.scrollToBottom();
          });
        }
      } else {
        if (option && option.hasAt) {
          // at提示
          if (data.ext.em_at_list && data.ext.em_at_list.length > 0 && data.ext.em_at_list.indexOf(vm.curUserId) !== -1) {
            option.notice = {
              msg: message,
              type: 'at',
            };
          }
        }
      }
      this.setMessageList(id, message, data.type, isUnread, messageType);
      //this.$nextTick(function () {
      //  vm.scrollToBottom();
      //});
    },
    //在聊天面板，修改消息已读状态
    setMessageSingleRead(msgId) {
      var vm = this;
      $.ajax({
        url: `${vm.chatApi}/imMsg/update/${msgId}`,
        method: 'post',
        success: function (resp) {
          if (!resp) return;
        }
      }).fail(function (e) {
        console.error(e);
      });
    },
    //设置聊天html
    formatChatHtml: function (html) {
      return html.replace(/(?:\\[n]|[\n])/g, "<br>")
    },
    //图片loading
    chatImgLoaded: function (e) {
      $(e.path && e.path[0]).removeClass('chat-image-loadding');
      if (this.chatListPage.vernier == 0 || this.isSendFileOrImgLoading) {//this.chatListPage.page === 0 || this.chatListPage.page === 1 || this.isSendFileOrImgLoading
        this.scrollToBottom();
        this.isSendFileOrImgLoading = false;
      }
    },
    formatFileLen: function (fileSize) {
      if (fileSize < 1024) {
        return fileSize + 'B';
      } else if (fileSize < (1024 * 1024)) {
        var temp = fileSize / 1024;
        temp = temp.toFixed(2);
        return temp + 'KB';
      } else if (fileSize < (1024 * 1024 * 1024)) {
        var temp = fileSize / (1024 * 1024);
        temp = temp.toFixed(2);
        return temp + 'MB';
      } else {
        var temp = fileSize / (1024 * 1024 * 1024);
        temp = temp.toFixed(2);
        return temp + 'GB';
      }
    },
    //点击职位卡片，跳转到职位详情页
    openJobPage: function (id) {
      window.open('/apps/viewjob2.aspx?id=' + id);
    },
    //点击简历卡片，跳转简历详情页
    openResumePage: function (id) {
      window.open('/resume/viewresume?id=' + id);
    },
    //获取存储中昵称
    getMessageName: function (id, type) {
      if (type === 'chat') {
        return this.getStorageData('staff', id).Name_Cn;
      } else {
        if (this.groupList[id]) {
          return this.groupList[id].groupname;
        } else {
          return '';
        }
      }
    },
    //判断是否展示时间
    isShowChatTime: function (date, privDate) {
      if (!privDate) {
        return true;
      }
      if (Math.floor(date / 1000) - Math.floor(privDate / 1000) < 60) {
        return false;
      }
      return true;
    },
    removeMessageByMessageId: function (id) {

    },
    //左侧会话列表单击右键，点击删除该会话
    removeMessageByUserId: function (id) {
      var messageObj = undefined;
      var messageIndex = undefined;
      this.messageList.forEach(function (elem, index) {
        if (elem.id == id) {
          messageObj = elem;
          messageIndex = index;
          return true;
        }
      });
      if (!messageObj) {
        return;
      }
      var type = messageObj.type === 'chat' ? 'single' : 'group';
      this.clearData({
        database: 'chat-' + type + '-' + this.curUserId + '-' + messageObj.id,
        table: 'chat_history',
      }, function () {

      });
      this.$delete(this.messageList, messageIndex);
      //如果删除的时候，正好聊天页面打开的话，则关闭这个页面
      if (this.chatPanelType === 'chatbox' && id == this.curChatbox.toId && this.curChatbox.type == type) {
        this.chatPanelType = undefined;
      }
    },
    setMessageToDraft: function (id, type) {
      var draftHtml = $("#chat-modal .chat-input-panel-input").html();
      if (draftHtml && draftHtml != '<br>') {
        if (type === 'single') {
          type = 'chat';
        } else {
          type = 'groupchat';
        }
        var data = {
          notice: {
            type: 'draft',
            msg: draftHtml,
          },
        };
        var msg = '';
        this.messageList.forEach(function (elem) {
          if (elem.id == id) {
            msg = elem.msg;
            return false;
          }
        });
        this.setMessageList(id, msg, type, false, data);
      }
    },
    getMessageNoticeTypeConf: function (type) {
      var conf = {
        draft: '草稿',
        at: '有人@我',
      };
      return conf[type];
    },
    //设置左侧会话列表谁为当前聊天人
    isMessageSelected: function (id, type) {
      var type = type === 'chat' ? 'single' : 'group';
      
      if (this.chatPanelType === 'chatbox' && this.curChatbox.toId == id && this.curChatbox.type == type) {
        return true;
      }
      if (this.chatPanelType === 'groupSetting' && this.groupSetting.info.id == id) {
        return true;
      }
      return false;
    },
    //聊天页
    setMessageToReaded: function (id) {
      var vm = this;
      this.messageList.forEach(function (elem, index) {
        if (elem.id == id) {
          var data = $.extend({}, vm.messageList[index], true);
          data.isUnread = false;
          vm.$set(vm.messageList, index, data);
          return false;
        }
      });
    },
    receiveNotice: function (data) {
      var info = {
        msg: data.data,
        isUnread: true,
        createDate: data.createDate,
      };
      var vm = this;
      if (this.chatPanelType === 'notice') {
        this.ajaxGetGroupMessages(function (resp) {
          vm.notice.list = resp;
        });
        info.isUnread = false;
      }
      this.notice.info = info;
      this.setNoticeMsgToStorage(info);


    },
    selectedAtStaff: function (staffId) {
      var popup = this.curChatbox.atPopup;
      popup.isShow = false;
      var staffName = this.getStorageData('staff', staffId).Name_Cn;
      $(".chat-input-panel-input .placeholder").get(0).outerHTML = staffName + ' ';

    },
    insertTextAfterCursor: function (text, e) {
      if (!this.curChatbox.emojiPopup.isShow) {
        return;
      }
      if (e.relatedTarget !== $("#chat-modal").get(0)) {
        return;
      }
      var $obj = $(".chat-input-panel-input");
      $obj.focus();
      return;
      /**

      $obj.find(".at-placeholder").remove();
      if (!this.curChatbox.atPopup.isShow) {
        return;
      }
       */

      var obj = $(".chat-input-panel-input").get(0);
      var range, node;
      if (!obj.hasfocus) {
        obj.focus();
      }

      if (document.selection && document.selection.createRange) {
        this.focus();
        document.selection.createRange().pasteHTML(text);
        this.focus();
      } else if (window.getSelection && window.getSelection().getRangeAt) {
        range = window.getSelection().getRangeAt(0);
        range.collapse(false);
        node = range.createContextualFragment(text);
        var c = node.lastChild;
        range.insertNode(node);
        if (c) {
          range.setEndAfter(c);
          range.setStartAfter(c)
        }
        var j = window.getSelection();
        j.removeAllRanges();
        j.addRange(range);
        $obj.focus();
      }
    },
    getTextFromHeadToCaret: function () {

      var element = $(".chat-input-panel-input").get(0);
      var caretOffset = 0;
      if (typeof window.getSelection != "undefined") {
        var range = window.getSelection().getRangeAt(0);
        var preCaretRange = range.cloneRange();
        preCaretRange.selectNodeContents(element);
        preCaretRange.setEnd(range.endContainer, range.endOffset);
        caretOffset = preCaretRange.toString().length;
      } else if (typeof document.selection != "undefined" && document.selection.type != "Control") {
        var textRange = document.selection.createRange();
        var preCaretTextRange = document.body.createTextRange();
        preCaretTextRange.moveToElementText(element);
        preCaretTextRange.setEndPoint("EndToEnd", textRange);
        caretOffset = preCaretTextRange.text.length;
      }
      var divStr = $(".chat-input-panel-input").html();
      return divStr.substring(0, caretOffset);
      //      this.curChatbox.atPopup.beforeMsg = divStr.substring(0, caretOffset);
    },
    openAtPopup: function (pos) {
      var popup = this.curChatbox.atPopup;
      popup.isShow = true;
      popup.top = pos.top + 20 + $(document).scrollTop();
      popup.left = pos.left;
    },
    getCursorPos: function (textDom) {
      win = window;
      var doc = win.document;
      var sel = doc.selection, range, rects, rect;
      var x = 0, y = 0;
      if (sel) {
        if (sel.type != "Control") {
          range = sel.createRange();
          range.collapse(true);
          x = range.boundingLeft;
          y = range.boundingTop;
        }
      } else if (win.getSelection) {
        sel = win.getSelection();
        if (sel.rangeCount) {
          range = sel.getRangeAt(0).cloneRange();
          if (range.getClientRects) {
            range.collapse(true);
            rects = range.getClientRects();
            if (rects.length > 0) {
              rect = rects[0];
            }
            // 光标在行首时，rect为undefined
            if (rect) {
              x = rect.left;
              y = rect.top;
            }
          }
          // Fall back to inserting a temporary element
          if ((x == 0 && y == 0) || rect === undefined) {
            var span = doc.createElement("span");
            if (span.getClientRects) {
              // Ensure span has dimensions and position by
              // adding a zero-width space character
              span.appendChild(doc.createTextNode("\u200b"));
              range.insertNode(span);
              rect = span.getClientRects()[0];
              x = rect.left;
              y = rect.top;
              var spanParent = span.parentNode;
              spanParent.removeChild(span);

              // Glue any broken text nodes back together
              spanParent.normalize();
            }
          }
        }
      }

      var pos = $("#chat-modal .modal-content").offset();
      return {
        top: y - pos.top,
        left: x - pos.left,
      }

    },
    openAtPanel: function () {
      return false;
      if (this.curChatbox.type === 'single') {
        return;
      }

      var staffIds = Object.keys(this.curChatbox.users);
      var vm = this;
      var popupStaffIds = [];
      staffIds.forEach(function (staffId) {
        if (vm.curUserId != staffId) {
          popupStaffIds.push(staffId);
        }
      });

      this.$set(this.curChatbox.atPopup, 'staffList', popupStaffIds.splice(0, 5));
      var pos = this.getCursorPos($("#chat-modal .chat-input-panel-input").get(0));
      this.openAtPopup(pos);
    },
    //选择表情包
    selectExpression: function (id, type) {
      var exp = WebIMAPI.getEmojiConf()[type][id];
      this.curChatbox.chatMsg = $("#chat-modal .chat-input-panel-input").html();
      if (type === 'default') {
        var img = document.createElement('img');
        img.className = "chat-msg-emotion chat-msg-emotion-img class1";
        img.setAttribute('src', this.emojiPath + exp.img);
        img.setAttribute('data-emotion', exp.name);
        img.setAttribute('data-emotion-txt', exp.txt);
        img.setAttribute('data-emotion-txtimg', exp.txtimg);
        img.setAttribute('data-type', type);
        img.setAttribute('data-id', id);
        this.insertPositionNode('chat-input', img);
      } else {
        $("#chat-modal .chat-input-panel-input").html('<img src="' + this.emojiPath + exp.gif + '" class="chat-msg-img-emoji chat-msg-emotion-img class2" data-emotion="' + exp.name + '" data-type="' + type + '" data-id="' + id + '">');
        this.sendChatMsg();
      }
      this.curChatbox.emojiPopup.isShow = false;
    },
    openExpressionPanel: function () {
      //      this.insertTextAfterCursor('<span class=expression-placeholder></span>');
      this.curChatbox.emojiPopup.isShow = true;
      this.curChatbox.emojiPopup.activeMenu = 'default';
      if (document.activeElement !== document.getElementById('chat-input')) {
        $('#chat-input').focus();
      }
    },
    changeActiveMenu: function (type) {
      this.curChatbox.emojiPopup.activeMenu = type;
    },
    //获取表情列表
    getEmojiList: function (type) {
      return WebIMAPI.getEmojiConf()[type];
    },
    //设置管理员/取消管理员
    setAdmin: function (groupId, userId) {
      var vm = this;
      if (vm.groupSetting.members[userId].type === 'admin') {
        WebIMAPI.removeAdmin(groupId, userId, function (resp) {
          if (resp.data.result === 'success') {
            vm.$set(vm.groupSetting.members[resp.data.oldadmin], 'type', 'member');
          }
        });
      } else {
        WebIMAPI.setAdmin(groupId, userId, function (resp) {
          if (resp.data.result === 'success') {
            vm.$set(vm.groupSetting.members[resp.data.newadmin], 'type', 'admin');
          }
        });
      }

    },
    isShowRemoveGroup: function (userId) {
      // 群主除了自己以外，谁都可以删
      if (this.groupSetting.info.owner == this.curUserId && this.groupSetting.info.owner != userId) {
        return true;
      }
      //管理员只能删除普通成员
      if (this.groupSetting.members[userId].type === 'member' && this.groupSetting.members[this.curUserId].type === 'admin') {
        return true;
      }
      return false;
    },
    transferGroupOwner: function (groupId, userId, newUserId) {
      var vm = this;
      WebIMAPI.transferGroupOwner(groupId, newUserId, function (resp) {
        if (resp.data.newowner) {
          var info = vm.groupSetting.info;
          info.affiliations.forEach(function (elem, index) {
            var key = Object.keys(elem)[0];
            if (elem[key] == userId) {
              vm.$set(info.affiliations, index, {
                member: userId,
              });
            }
            if (elem[key] == newUserId) {
              vm.$set(info.affiliations, index, {
                owner: newUserId,
              });
            }
          });
          vm.$set(vm.groupSetting.members[userId], 'type', 'member');
          vm.$set(vm.groupSetting.members[newUserId], 'type', 'owner');
          vm.$set(info, 'owner', newUserId);
          vm.$set(info, 'membersonly', true);
        }
      });
    },
    getStaffInfoFromGroupMember: function (obj) {
      var staffId = undefined;
      var type = undefined;
      if (obj.member) {
        staffId = obj.member;
        type = 'member';
      }
      if (obj.owner) {
        staffId = obj.owner;
        type = 'owner';
      }
      return {
        staffId: staffId,
        type: type,
      }
    },
    handleInputBoxEnter: function (e) {
      e.preventDefault();
      var $dom = $(e.target)
      // ctrl + enter，换行
      if (e.ctrlKey) {
        var dom = $dom.get(0);
        var br = document.createElement("br");
        br.className = 'current';
        this.insertPositionNode('chat-input', br);
        var htmlArr = $("#chat-input").html().split('<br class="current">');
        var prevHtml = htmlArr[0];
        var nextHtml = htmlArr[1];
        if (!prevHtml.endsWith("<br>") && nextHtml === '') {
          var br = document.createElement("br");
          this.insertPositionNode('chat-input', br);
        }
        $("#chat-input br").removeAttr('class');
        var modalTop = $("#chat-modal .modal-content").offset().top;
        var scrollTop = $(document).scrollTop();
        var panelBottom = $("#chat-input").offset().top - scrollTop - 18 + $("#chat-input").height();
        var cursorBottom = this.getCursorPos(dom).top + scrollTop;
        if (panelBottom - cursorBottom < 18) {
          dom.scrollTop = dom.scrollTop + 21;
        }
        return;
      }

      // 发送
      if (!(e.altKey || e.ctrlKey || e.shiftKey)) {
        this.sendChatMsg();
      }

    },
    insertPositionNode: function (nodeId, doc) {
      var range, selection;//记录光标位置对象
      selection = window.getSelection();//获取当前选中区域
      //判断选中区域为某个节点id时才能触发
      if (selection.baseNode && (selection.baseNode.id == nodeId || selection.baseNode.parentElement.id == nodeId)) {
        var node = selection.anchorNode;
        // 这里判断是做是否有光标判断，因为弹出框默认是没有的
        if (node != null) {
          range = selection.getRangeAt(0);// 获取光标起始位置
        } else {
          range = undefined;
        }
        range.insertNode(doc);// 在光标位置插入该对象
        selection.collapseToEnd();//光标移动至末尾
        return range.endOffset;
      };
    },
    showHDOriginImage: function (url) {
      if (!url) {
        return '';
      }
      return url.replace('?x-oss-process=image/resize,m_fixed,h_92,w_92', '');
    },
    //发送图片
    sendImage: function () {
      //console.log(333, '发送图片')
    },
    bubbleToRoot: function (e) {
      this.search.isShow = false;
      this.menuPopup.isShow = false;
      this.staffDetailsPopup.isShow = false;
      this.curChatbox.emojiPopup.isShow = false;
      this.curChatbox.atPopup.isShow = false;
      this.curChatbox.isShowBlankMsgNotice = false;
      this.closeNoticePopup();
    },
    removeSingleGroupMember: function (groupId, userId) {
      var vm = this;
      WebIMAPI.removeSingleGroupMember(groupId, userId, function (resp) {
        var info = vm.groupSetting.info;
        vm.$set(info, 'affiliations_count', info.affiliations_count - 1);
        var userIndex = -1;
        info.affiliations.forEach(function (elem, index) {
          var key = Object.keys(elem)[0];
          if (elem[key] == userId) {
            userIndex = index;
          }
        });
        vm.$delete(info.affiliations, userIndex);
        vm.$delete(vm.groupSetting.members, userId);
      });
    },
    //展示聊天区信息
    showStaffDetailsPopup: function (e, id, confType) {
      return false;
      var obj = this.staffDetailsPopup;

      obj.type = confType;
      this.ajaxGetStaffInfo([id], function (data) {
        if (data.length > 0) {
          obj.info = data[0];
        } else {
          obj.info = {};
        }
      });
      this.getCurClickPos(e, function (x, y) {
        obj.left = x;
        obj.top = y;
        obj.isShow = true;
      });

    },
    setNoticeMsgToStorage: function (data) {
      localStorage.setItem('notice-' + this.curUserId, JSON.stringify(data));
    },
    getNoticeObjFromStorage: function () {
      var str = localStorage.getItem('notice-' + this.curUserId);
      var obj = undefined;
      if (str) {
        obj = JSON.parse(str);
      } else {
        obj = {
          msg: '',
          isUnread: false,
        };
      }
      return obj;
    },
    getGroupList: function (func) {
      var vm = this;
      WebIMAPI.getGroupList(function (status, data) {
        var obj = {};
        data.forEach(function (elem) {
          obj[elem.groupid] = elem;
        });
        vm.groupList = obj;
        func && func();
      });
    },
    searchStaffByPage: function (page) {
      this.search.page.page = page;
      this.searchStaffByName();
    },
    //搜索框回车事件
    searchStaffAndGroup: function () {
      this.search.page.page = 1;
      this.search.isShow = true;
      this.searchGroupByName();
      this.searchStaffByName();
      this.$nextTick(function () {
        var height = $(".chat-menu-content").height();
        $(".chat-menu-search-scroll-wrapper").css({
          'max-height': height + 'px',
        });
      });
    },
    searchStaffByName: function () {
      var arr = [];
      var vm = this;
      this.search.loading = true;
      this.ajaxGetStaffList(undefined, 0, this.search.keywords, {
        pageSize: this.search.page.pageSize,
        page: this.search.page.page,
      }, undefined, function (data, total) {
        if (vm.search.page.page == 1) {
          vm.search.staffList = data;
        } else {
          vm.search.staffList = vm.search.staffList.concat(data);
        }
        vm.search.total = total;
        vm.search.loading = false;
      });

    },
    searchGroupByName: function () {
      var vm = this;
      var handle = function () {
        var arr = [];
        Object.keys(vm.groupList).forEach(function (elem) {
          if (vm.groupList[elem].groupname.indexOf(vm.search.keywords) !== -1) {
            arr.push(vm.groupList[elem]);
          }
        });
        vm.search.groupList = arr;
      }
      if ($.isEmptyObject(this.groupList)) {
        this.getGroupList(function () {
          handle();
        });
      } else {
        handle();
      }
    },
    removeStaffFromCreateGroup: function (index) {
      this.$delete(this.createGroupData.users, index);
    },
    getStaffInfo: function (id) {
      return this.storage.staff[id];
    },
    showCustomMenu: function (event) {
      var event = event || window.event;
      var menu = $(".menu-item-popup").get(0);
      menu.style.display = 'block';
      var l, t;
      var pos = $("#chat-modal .modal-content").offset();
      l = event.clientX - pos.left;
      t = event.clientY - pos.top + $(document).scrollTop();

      menu.style.left = l + 'px';
      menu.style.top = t + 'px';
      return false;
    },
    showGroupNoticePanel: function () {
      this.chatPanelType = 'notice';
      var vm = this;
      this.ajaxGetGroupMessages(function (data) {
        vm.notice.list = data;
      });
      if (this.notice.info.isUnread) {
        this.notice.info.isUnread = false;
        this.setNoticeMsgToStorage(vm.notice.info);
      }

    },
    showSysNoticePanel: function () {
      this.chatPanelType = 'sysNotice';
    },
    ajaxGetGroupMessages: function (func) {
      var vm = this;
      var startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 6);
      var startDateStr = startDate.getFullYear() + '-' + (startDate.getMonth() + 1) + '-' + startDate.getDate();
      var endDate = new Date();
      endDate.setDate(endDate.getDate() + 1);
      var endDateStr = endDate.getFullYear() + '-' + (endDate.getMonth() + 1) + '-' + endDate.getDate();
      this.notice.isLoading = true;
      $.ajax({
        url: this.mobileHost + '/EaseMob/GetGroupMessages',
        method: 'post',
        data: {
          Page: 1,
          Size: 50,
          StaffId: this.curUserId,
          StartTime: startDateStr,
          EndTime: endDateStr,
        },
        success: function (resp) {
          if (!resp) return;
          vm.notice.isLoading = false;
          if (resp.Success) {
            func(resp.Data);
          } else {
            vm.reportInterfaceErr("/EaseMob/GetGroupMessages", {
              Page: 1,
              Size: 50,
              StaffId: vm.curUserId,
              StartTime: startDateStr,
              EndTime: endDateStr,
            }, resp);
          }
        }
      }).fail(function (e) {
        console.error(e);
      });
    },
    reportInterfaceErr: function (url, reqData, respData) {
      return;
      var msg = '';
      if (typeof respData === 'object' && respData !== null) {
        msg = JSON.stringify(respData);
      } else {
        msg = respData;
      }
    },
    //加载左侧列表数据集合
    initMessageList: function (respdata) {
      //通过接口获取左侧聊天数据集合
      var messageList = [];
      var vm = this;
      let param = {
        accId: respdata.accountId,
        queryType: 0,//0全部、1未读、2沟通中
        isFiltration: 1,//是否过滤自动回复：0否、1是
      }
      $.ajax({//根据当前登录环信accId查询会话列表
        url: `${vm.chatApi}/imMsgSession/getSessionList`,
        method: 'get',
        data: param,
        success: function (resp) {
          if (!resp || !resp.success || !resp.data) return;
          resp.data.forEach(item => {
            var messageL = {};
            messageL.id = item.channelId;
            messageL.date = item.time;
            messageL.isUnread = false;
            messageL.unreadNum = item.unreadNum;//未读数
            messageL.msg = "";
            messageL.type = "chat";
            messageL.notice = {
              msg: ""
            };
            messageL.user = {//获取消息to用户的昵称和头像
              name: item.ext.nickName,
              img: item.ext.nickUrl
            };

            // 解析 JSON 字符串
            try {
              var msgBody = JSON.parse(item.lastMessage.msgBody);
              if (msgBody.type === vm.MESSAGE_TYPE.TEXT) {//如果发送的是文本，判断是不是emo表情
                var emoList = WebIMAPI.getEmojiConf().default;//表情集合
                messageL.msg = msgBody.msg;
                emoList.forEach((item) => {//后期替换表情
                  if (msgBody.msg.indexOf(item.name) != -1) {
                    messageL.msg = messageL.msg.replace(item.name, "[" + item.txt + "]");
                  }
                })
              } else if (msgBody.type === vm.MESSAGE_TYPE.IMAGE) {//图片
                messageL.msg = "[图片]"
              } else if (msgBody.type === vm.MESSAGE_TYPE.AUDIO) {//语音
                messageL.msg = "[语音]"
              } else if (msgBody.type === vm.MESSAGE_TYPE.FILE) {//文件
                messageL.msg = "[文件]"
              } else if (msgBody.type === vm.MESSAGE_TYPE.VIDEO) {//视频
                messageL.msg = "[视频]"
              } else if (msgBody.type === vm.MESSAGE_TYPE.CUSTOM) {//自定义信息
                messageL.msg = "[自定义信息]"
              }
            } catch (error) {
              console.error('Invalid JSON string:', item.lastMessage.msgBody);
              return;
            }

            messageList.push(messageL);
          });
          vm.messageList = messageList;
          var _index = vm.messageList.findIndex(item => item.id === vm.curChatbox.toId)//判断已打开会话的索引
          if (_index >= 0) {
            if (vm.chatPanelType === 'chatbox' && vm.curChatbox.toId && vm.messageList[_index].unreadNum > 0) {//判断是否有未读标签，有的话调用会话已读回执
              vm.readConversation(vm.curChatbox.toId)//调用全部已读
              WebIMAPI.sendReadChannelMessage(vm.curChatbox.toId);
              //滑动到最底部
              vm.scrollToBottom();
            }
          }
        }.bind(this) // 使用 bind(this) 绑定回调函数的上下文
      }).fail(function (e) {
        console.error(e);
      });
      //var messageList = JSON.parse(localStorage.getItem('chatrecord-' + this.curUserId));
      //if (messageList) {
      //  this.messageList = messageList;
      //}
    },
    //设置左侧会话信息
    setMessageList: async function (id, message, type, isUnread, option) {
      if (isUnread === undefined) {
        isUnread = true;
      }
      let count = 0;
      id = String(id);
      if (option === this.MESSAGE_TYPE.TEXT) {
        message = message.replace(/<br\s*\/?>/gi, '\r\n');
        var $messageObj = $('<div>' + message + '</div>');
        if ($messageObj.find('.chat-msg-emotion-img').length > 0) {
          $messageObj.find('.chat-msg-emotion-img').each(function (index, elem) {
            var emoList = WebIMAPI.getEmojiConf().default;//表情集合
            var msg = $(elem).data('emotion');
            emoList.forEach((item) => {
              if (msg.indexOf(item.name) != -1) {
                msg = "["+ item.txt +"]"
              }
            })
            message = message.replace(elem.outerHTML, msg);
          });
        } else {
          var emoList = WebIMAPI.getEmojiConf().default;//表情集合
          emoList.forEach((item) => {
            if (message.indexOf(item.name) != -1) {
              message = message.replace(item.name, "["+ item.txt +"]");
            }
          })
        }
      } else if (option === this.MESSAGE_TYPE.FILE) {//文件
        message = "[文件]"
      } else if (option === this.MESSAGE_TYPE.IMAGE) {//图片
        message = "[图片]"
      } 
      var toIndex = -1;
      var chatbox = this.curChatbox;
      var vm = this;
      vm.messageList.forEach(function (elem, index) {
        if (id == elem.id) {
          toIndex = index;
        }
      });

      if (toIndex >= 0) {
        var originMessage = this.messageList[toIndex];
        count = originMessage.unreadNum;
        this.messageList.splice(toIndex, 1);
      }

      var userData = {};
      
      if (type === 'groupchat') {
        if (this.groupList[id]) {
          userData = {
            name: this.groupList[id].groupname,
            img: undefined,
          };

        } else {
          userData = {
            name: '',
            img: undefined,
          };
          this.getGroupList(function () {

            vm.messageList.forEach(function (elem, index) {
              if (id === elem.id) {
                vm.$set(vm.messageList[index].user, 'name', vm.groupList[id].groupname);
                return false;
              }
            });
          });
        }
      }

      var notice = {
        type: undefined,
        msg: '',
      };
      if (originMessage && originMessage.notice) {
        notice = originMessage.notice;
      }
      if (option) {
        if (option.notice) {
          notice.type = option.notice.type;    //  枚举值，为at，draft，如果为undefined，则代表没有消息提示
          notice.msg = option.notice.msg;
        }
      }
      if (type === 'chat') {
        var staff = await vm.getStorageData('staff', id);//未等获取完消息就输出了
        vm.UserIdNew=id
        setTimeout(function () {
          userData = {
            name: staff?.Name_Cn,
            img: staff?.PictureUrl,
          };
          if (isUnread) {
            count++
          }
          var data = {
            id: id,
            msg: message,
            type: type,
            date: Date.now(),
            user: userData,
            isUnread: isUnread,
            unreadNum: count,
            notice: notice,
          };
          vm.messageList.unshift(data);
        },100)
        
      }
      
    },
    editGroupAddMemberToGroup: function (selected) {
      var vm = this;
      var arr = [];
      selected.forEach(function (elem) {
        arr.push(String(elem));
      });
      WebIMAPI.inviteToGroup(this.groupSetting.info.id, arr, function (resp) {
        var info = vm.groupSetting.info;
        vm.$set(info, 'affiliations_count', info.affiliations_count + resp.data.length);
        resp.data.forEach(function (elem) {
          vm.$set(info.affiliations, info.affiliations.length, {
            member: elem.user,
          });
          vm.$set(vm.groupSetting.members, elem.user, {
            staffId: elem.user,
            type: 'member',
          });
        });

      });
    },
    
    saveUserSelect: function () {
      var vm = this;
      var ids = vm.addToStorage('staff', vm.userSelectPopup.selected, true);
      this.userSelectPopup.callback.call(this, ids);
      this.userSelectPopup.isShow = false;
    },
    //获取staff存储--id：传输为accId
    getStorageData: function (type, id) {
      var vm = this;
      if (type === 'staff') {
        if (!this.storage.staff[id] && !this.storageCache.isRunningAjax) {//本地this.storage数据staffId对应信息如果为空
          //这个策略主要是在ajax请求期间和在一个主执行线程内，所有的数据都攒起来，在下一个执行线下一次执行，这种执行方式可以明显提高ajax请求效率
          vm.ajaxGetStaffInfo(id, function (data) {
            vm.addToStorage('staff', id, data[0], false);
            vm.storageCache.isRunningAjax = false;
            vm.storageCache.idList = {};
            vm.storageCache.isRunningAjax = true;
            vm.storageCache.idList[id] = true;
            return vm.storage.staff[id];
          });
        }
        return vm.storage.staff[id];
      }
    },
    //存储accId的基本数据
    addToStorage: function (type, id, data, isGroup) {//id为accId
      if (isGroup === undefined) {
        isGroup = false;
      }
      if (isGroup) {
        var vm = this;
        var arr = [];
        if (data instanceof Array) {
          data.forEach(function (elem) {
            arr.push(vm.addToStorage(type, id, elem, false));
          });
        } else {
          Object.keys(data).forEach(function (elem) {
            arr.push(vm.addToStorage(type, id, data[elem], false));
          });
        }
        return arr;
      } else {
        if (type === 'staff') {
          this.$set(this.storage.staff, id, data);
          return data.StaffId;
        }
      }
    },
    selectOrCancelUserSelect: function (e, staff) {
      if ($(e.target).prop('checked')) {
        this.userSelectPopup.selected[staff.StaffId] = staff;
      } else {
        this.$delete(this.userSelectPopup.selected, staff.StaffId);
      }
    },
    //通讯录点击顾问操作
    openOrCloseStaffList: function (e, index, companyId) {
      var $userItem = $(e.target).closest('.select-user-user-item');
      if ($userItem.hasClass('show-staff')) {
        $userItem.removeClass('show-staff');
      } else {
        $userItem.addClass('show-staff');
        if (this.friendList[index].staffList.length === 0) {
          var $wrapper = $(e.target).closest('.select-user-user-item');
          this.ajaxGetStaffList(index, companyId, undefined, undefined, $wrapper);
        }
      }
    },
    showUserSelectPopup: function (func, selected) {
      if (selected === undefined) {
        selected = [];
      }
      this.userSelectPopup.callback = func;
      this.userSelectPopup.isShow = true;
      this.userSelectPopup.selected = {};
    },
    createGroupAddMemberToGroup: function (selected) {
      var data = [];
      var data = Array.from(new Set(this.createGroupData.users.concat(selected)));

      this.createGroupData.users = data;
    },
    closeUserSelectPopup: function () {
      this.userSelectPopup.isShow = false;
    },
    //初始化IM聊天回调
    initWebIMAPI: function (user, pwd, func) {
      this.curUserId = user;
      var vm = this;
      WebIMAPI.init({
        callbackOnOpened: function () {
          vm.ready = true;
        },
        callbackOnError: function () {
          vm.ready = true;
        },
        callbackOnClosed: function () {
          vm.connectStatus = 'offline';
        },
        //收到文本消息回调
        callbackOnTextMessage: function (data) {
          data = vm.formateEmojiData(data);
          // 群通知
          if (data.from === 'admin' && data.ext.nickName === "群通知") {
            data.createDate = Date.now();
            data.messageType = 'text';
            data.messagePlain = data.data;
            vm.receiveNotice(data);
          } else if (data.from !== 'admin' || (data.from === 'admin' && data.ext.nickName === "系统小助手")) {
            if (data.from === 'admin') {
              data.ext.nickUrl = '/images/<EMAIL>';
            }
            // 收到普通文本消息
            if (data.ext.messageResume) {
              data.ext.messageResumeObj = JSON.parse(data.ext.messageResume);
            }
            if (data.ext.messageJob) {
              data.ext.messageJob = JSON.parse(data.ext.messageJob);
            }

            var messagePlain = '';
            if (data.data instanceof Array) {
              messagePlain = data.messagePlain;
            } else {
              messagePlain = data.data;
            }
            vm.receiveChatMsg(data, 'txt', messagePlain, {
              hasAt: true,
            });
          }
        },
        //收到表情消息回调
        callbackOnEmojiMessage: function (data) {
          data.createDate = Date.now();
          data = vm.formateEmojiData(data);
          vm.receiveChatMsg(data, 'txt', data.messagePlain, {
            hasAt: true,
          });
        },
        //收到文件消息回调
        callbackOnFileMessage: function (data) {     // 文件消息回调
          vm.receiveChatMsg(data, 'file', '[文件]');
        },
        //收到图片消息回调
        callbackOnPictureMessage: function (data) {   // 图片消息回调
          vm.receiveChatMsg(data, 'img', '[图片]');
        },
        //收到语音消息回调
        callbackOnAudioMessage: function (data) {
          vm.receiveChatMsg(data, 'audio', '[语音]');
        },
        //收到视频消息回调
        callbackOnVideoMessage: function (data) {
          vm.receiveChatMsg(data, 'video', '[视频]');
        },
        //监听自定义消息回调
        callbackOnCustomMessage: function (data) {
          vm.receiveChatMsg(data, 'custom', '[自定义消息]');
        },
        //收到消息已读回执
        callbackOnReadMessage: function (data) {
          //重置内容，分页到第一页，重新加载聊天数据，滑动到最底部，会遇到如果正在看聊天记录时，遇到已读回执，会直接滑动到最新
          vm.chatList.forEach(item => {
            if (item.msgId === data.mid) {
              item.isRead = 1;
            }
          })
          //vm.chatListPage.page = 1;
          //vm.chatList = [];//
          //vm.readAll2(vm.curChatbox.toId, function () {//获取消息列表集合
          //  vm.$nextTick(function () {
          //    vm.scrollToBottom();
          //  });
          //});
        },
        //接收会话已读回执-对方点击进入聊天面板时触发
        callbackOnChannelMessage: function (data) {
          //判断当前打开的toId和会话已读回执回调中的fromId是同一个人，重置聊天数据中都为已读
          if (data.from === vm.toAccountId) {
            vm.chatList.forEach(item => {
              item.isRead = 1;
            })
          }
        },
        callbackOnLeaveGroup: function (data) {
          vm.$delete(vm.groupList, data.from);
          if (vm.chatPanelType === 'chatbox' && vm.curChatbox.toId == data.from) {
            vm.chatPanelType = undefined;
          }

          var curIndex = -1;
          vm.messageList.forEach(function (elem, index) {
            if (elem.id == data.from) {
              curIndex = index;
            }
          });
          if (curIndex > -1) {
            vm.$delete(vm.messageList, curIndex);
          }
        },
      });

      WebIMAPI.getTokenFromServer(user, pwd, function (status, token) {
        func();
      });
    },
    //表情消息回执后，处理
    formateEmojiData: function (data) {
      if (data.ext.groupName === "tuzki") {
        if (data.data && data.data.length > 0) {
          data.data[0].data = WebIM.Emoji.path + WebIMAPI.getEmojiConf()[data.ext.groupName][data.ext.codeId].gif;
        } else {
          var tuzkiData = WebIMAPI.getEmojiConf()[data.ext.groupName][data.ext.codeId];
          data.data = [];
          data.data.push({
            code: tuzkiData.name,
            data: WebIM.Emoji.path + tuzkiData.gif,
            type: 'emotion',
          });
          data.ext.em_is_big_expression = true;
        }
      } else if (data.data instanceof Array) {
        var str = '';
        data.data.forEach(function (elem) {
          if (elem.type === 'txt') {
            str += elem.data;
          }
          if (elem.type === 'emotion') {
            if (data.ext.em_is_big_expression) {
              str += '<img class="chat-msg-img-emoji chat-msg-emotion-img class3" src="' + elem.data + '" data-emotion="' + elem.code + '">';
            } else {
              str += '<img class="chat-msg-emotion chat-msg-emotion-img class4" src="' + elem.data + '" data-emotion="' + elem.code + '">';
            }

          }
        });
        data.messagePlain = str;
      } else {
        let msg = data.data;
        WebIMAPI.getEmojiConf()['default'].forEach(function (elem) {
          if (msg.indexOf(elem.name) > -1) {
            msg = msg.replaceAll(elem.name, '<img class="chat-msg-emotion chat-msg-emotion-img class7" src="/images/chat/emoji/new/' + elem.img + '" data-emotion="' + elem.name + '">');
            data.data = msg;
          }
        })
      }
      return data;
    },
    closeFindGroup: function () {
      this.findGroupData.isShow = false;
    },
    joinGroup: function (groupId) {
      WebIMAPI.joinGroup(groupId, function (res) {
      });
    },
    openSearchGroup: function () {
      this.findGroupData.isShow = true;
      if (this.findGroupData.list.length === 0) {
        var vm = this;
        WebIMAPI.listGroups(function (res) {
          vm.findGroupData.list = res.data;
        });
      }
    },
    //左侧会话列表右键触发
    chatMessageRightClick: function (id, type, event) {
      var event = event || window.event;
      event.preventDefault();

      var l, t;
      var pos = $("#chat-modal .modal-content").offset();
      l = event.clientX - pos.left;
      t = event.clientY - pos.top + $(document).scrollTop();

      this.menuPopup.type = type;
      this.menuPopup.id = id;
      this.menuPopup.isShow = true;
      this.menuPopup.x = l;
      this.menuPopup.y = t;
    },
    getCurClickPos: function (event, func) {
      var event = event || window.event;
      event.preventDefault();

      var l, t;
      var pos = $("#chat-modal .modal-content").offset();
      l = event.clientX - pos.left;
      t = event.clientY - pos.top + $(document).scrollTop();

      func(l, t);
    },
    selectSendFile: function () {
      $("#chat-file-send").val('');
      $("#chat-file-send").click();
    },
    //调用发送图片api
    saveImage(chatType, width, height, imgName, imgType, file) {
      let vm = this;
      var formData = new FormData();
      formData.append("file", file);
      $.ajax({
        url: '/UploadFile/UploadChatsFile',
        type: 'post',
        name: 'image',
        data: formData,
        contentType: false,
        processData: false,
        success: function (fileRes) {
          if (fileRes) {
            if (fileRes.Url) {
              WebIMAPI.sendClipboardImg(vm.curChatbox.toId, vm.sendImageNoticePopup.blob, chatType, {
                width: width,
                height: height,
              }, {
                nickName: vm.getStorageData('staff', vm.curUserId).Name_Cn,
                nickUrl: vm.getStorageData('staff', vm.curUserId).PictureUrl,
              }, function (toId, id, msgId) {
                var options = {
                  createDate: Date.now(),
                  error: false,
                  errorCode: "",
                  errorText: "",
                  ext: {
                    imageName: imgName,
                    imageType: imgType,
                    nickName: vm.getStorageData('staff', vm.curUserId).Name_Cn,
                    nickUrl: vm.getStorageData('staff', vm.curUserId).PictureUrl,
                  },
                  file_length: "",
                  filename: undefined,
                  filetype: "",
                  from: vm.curUserId,
                  height: height,
                  msgId: msgId,
                  messagePlain: "[图片]",
                  messageType: "img",
                  thumb: undefined,
                  thumb_secret: undefined,
                  to: toId,
                  type: chatType,
                  isRead: 0,
                  //url: resp.uri + '/' + resp.entities[0].uuid,
                  url: fileRes.Url,
                  width: width,
                };
                vm.add(options);
                vm.isImgLoading = false;
                vm.chatList.push(options);
                vm.isScorllButtom = true;
                vm.isSendFileOrImgLoading = true;
                //保存到服务器
                vm.saveMessage(options);
                //vm.setMessageList(toId, '[图片]', chatType, false);
              });
            }
          }
        }
      }).fail(function (e) {
        vm.isImgLoading = false;
        vm.$message({
          message: '图片发送失败',
          type: 'warning'
        });
      });

    },
    //粘贴弹窗-点击发送图片确认按钮
    saveSendImageNoticePopup: function () {
      var vm = this;
      var img = $("#chat-modal .chat-clipboard-img").get(0);
      var chatType = vm.curChatbox.type === 'single' ? 'chat' : 'groupchat';
      let name = new Date() + '.png';
      vm.saveImage(chatType, img.naturalWidth, img.naturalHeight, name, 'png', vm.sendImageNoticePopup.blob)
      this.sendImageNoticePopup.isShow = false;
      //发送提示
      vm.isImgLoading = true;
      //滚动到最底部
      setTimeout(function () { vm.scrollToBottom(); }, 50)
    },
    //粘贴打开图片发送弹窗
    handlePasteImage: function (e) {
      if (e.clipboardData && e.clipboardData.types) {
        if (e.clipboardData.items.length > 0) {
          for (index in Object.keys(e.clipboardData.items)) {
            var elem = e.clipboardData.items[index];
            if (/^image\/\w+$/.test(elem.type)) {
              var blob = elem.getAsFile();
              var url = window.URL.createObjectURL(blob);//处理图片为blob格式
              this.sendImageNoticePopup = {
                isShow: true,
                blob: blob,
                url: url,
              };
            }
          };
        }
      }
    },
    //发送文件
    sendFile: function (e) {
      var vm = this;
      var input = e.target;
      var fileName = input.files[0].name;
      var size = input.files[0].size;
      var type = fileName.substring(fileName.lastIndexOf('.') + 1, fileName.length)
      if (1024 * 1024 * 10 < size) {
        this.noticePopup = {
          isShow: true,
          popupToRoot: false,
          msg: '发送的文件大小不能超过10M',
          optList: [
            {
              name: '确定',
              click: this.closeNoticePopup,
            },
          ],
        };
        return;
      }
      if (vm.extension.indexOf(type) == -1) {
        this.noticePopup = {
          isShow: true,
          popupToRoot: false,
          msg: '上传的格式为pdf/doc/docx/png/jpg/jpeg',
          optList: [
            {
              name: '确定',
              click: this.closeNoticePopup,
            },
          ],
        };
        return;
      }
      var chatType = this.curChatbox.type === 'single' ? 'chat' : 'groupchat';
      //发送提示
      vm.isImgLoading = true;
      //滚动到最底部
      setTimeout(function () { vm.scrollToBottom(); }, 50)
      //判断是文件还是图片
      let elem = input.files[0];
      if (/^image\/\w+$/.test(elem.type)) {//发送图片
        var url = window.URL.createObjectURL(elem);//处理图片为blob格式
        this.sendImageNoticePopup = {
          isShow: false,
          blob: elem,
          url: url,
        };
        // 创建一个FileReader对象
        const reader = new FileReader();

        reader.onload = function (event) {
          // 当图片加载完成后，创建一个Image对象
          const img = new Image();
          img.onload = function () {
            // 获取图片的宽度和高度
            const width = img.width;
            const height = img.height;
            vm.saveImage(chatType, width, height, fileName, type, elem)
          };
          // 设置Image对象的src为FileReader读取的结果（图片的base64编码）
          img.src = event.target.result;
        };

        // 以DataURL的形式读取文件内容
        reader.readAsDataURL(elem);
      } else {//发送文件
        vm.sendTypeFile(input, chatType, size)
      }
    },
    //调用发送文件api
    sendTypeFile(input, chatType, size) {
      let vm = this;
      var formData = new FormData();
      formData.append("file", input.files[0]);
      $.ajax({
        url: '/UploadFile/UploadChatsFile',
        type: 'post',
        name: 'image',
        data: formData,
        contentType: false,
        processData: false,
        success: function (fileRes) {
          if (fileRes) {
            if (fileRes.Url) {
              WebIMAPI.sendFile(vm.curChatbox.toId, input, chatType, {
                nickName: vm.getStorageData('staff', vm.curUserId).Name_Cn,
                nickUrl: vm.getStorageData('staff', vm.curUserId).PictureUrl,
              }, function (file, toId, id, msgId) {
                var fileName = file.filename;

                var options = {
                  createDate: Date.now(),
                  error: false,
                  errorCode: "",
                  errorText: "",
                  file_length: size,
                  filename: fileName,
                  from: vm.curUserId,
                  msgId: msgId,
                  messageType: "file",
                  to: toId,
                  type: chatType,
                  isRead: 0,
                  //url: resp.uri + '/' + resp.entities[0].uuid,
                  url: fileRes.Url,
                  ext: {
                    nickName: vm.getStorageData('staff', vm.curUserId).Name_Cn,
                    nickUrl: vm.getStorageData('staff', vm.curUserId).PictureUrl,
                  },
                };
                vm.add(options);
                  vm.isImgLoading = false;
                  vm.chatList.push(options);//设置消息页消息内容
                  vm.isScorllButtom = true;
                  vm.isSendFileOrImgLoading = true;
                //vm.setMessageList(toId, '[文件]', chatType, false);//设置消息列表最后信息展示
                //保存到服务器
                vm.saveMessage(options);
              });
            }
          }
        }
      }).fail(function (e) {
        vm.isImgLoading = false;
        //输出失败错误信息
        vm.$message({
          message: '文件发送失败',
          type: 'warning'
        });
      })
    },
    //点击下载文件-在弹出层中展示
    downloadFile: function (item) {
      //判断文件类型
      let fileType = item.filename.substring(item.filename.lastIndexOf(".") + 1);
      this.imgPreviewPopup = {
        isShow: true,
        url: item.url,
        type: fileType
      };
      //if (fileType == 'png' || fileType == 'jpg' || fileType == 'jpge') {
      //  //打开预览图片
      //} else if (fileType == 'pdf' || fileType == 'doc' || fileType == 'docx') {
      //  //打开iframe查看
      //}
      //window.open(url, '_blank');
    },
    createGroupNew: function () {
      var data = this.createGroupData;
      if (data.groupname === "") {
        return;
      }
      var vm = this;

      this.createGroupData.members = this.createGroupData.users;
      WebIMAPI.createGroupNew(this.createGroupData, function (data) {
        vm.popup.isShow = false;
      });
      this.createGroupData = $.extend({}, this.originCreateGroupData);

    },
    openPopup: function (type) {
      this.popup.isShow = true;
      this.popup.type = type;
      this.createGroupData = $.extend({}, this.originCreateGroupData, true);
    },
    closePopup: function () {
      this.popup.isShow = false;
    },
    modifyGroup: function () {
      var groupSetting = this.groupSetting;
      WebIMAPI.modifyGroup(groupSetting.info.id, groupSetting.info.name, groupSetting.info.description, function (desc) {
      });
    },
    showGroupSetting: function (type) {
      var groupSetting = this.groupSetting;
      if (type === 'name') {
        groupSetting.isNameEdit = true;
        groupSetting.name = groupSetting.info.name;
      }
      if (type === 'description') {
        groupSetting.isDescEdit = true;
        groupSetting.description = groupSetting.info.description;
      }
    },
    updateGroupSetting: function (type) {
      var groupSetting = this.groupSetting;
      if (type === 'name') {
        groupSetting.info.name = groupSetting.name;
        groupSetting.isNameEdit = false;
      }
      if (type === 'description') {
        groupSetting.info.description = groupSetting.description;
        groupSetting.isDescEdit = false;
      }
      this.modifyGroup();

    },
    cancelGroupSetting: function (type) {
      var groupSetting = this.groupSetting;
      if (type === 'name') {
        groupSetting.isNameEdit = false;
      }
      if (type === 'description') {
        groupSetting.isDescEdit = false;
      }
    },
    quitGroup: function (gid) {
      WebIMAPI.quitGroup(gid, function (data) {

      });
    },
    dissolveGroup: function (gid) {
      WebIMAPI.dissolveGroup(gid, function (data) {
      });
    },
    openGroupSettingPanel: function () {
      if (this.chatPanelType === 'chatbox') {
        this.chatPanelType = 'groupSetting';
        var vm = this;
        /**
        WebIMAPI.listGroupMember(this.curChatbox.toId, {
          pageNum: 1,
          pageSize:1000,
        }, function (resp) {
          console.log(resp);
          });
        **/

        var groupId = this.curChatbox.toId;
        WebIMAPI.getGroupInfo(this.curChatbox.toId, function (data) {
          vm.groupSetting.info = data.data[0];
          var arr = [];

          var members = {};
          vm.groupSetting.info.affiliations.forEach(function (elem) {
            var key = Object.keys(elem)[0];
            members[elem[key]] = {
              staffId: elem[key],
              type: key,
            };
          });
          var adminList = [];
          WebIMAPI.getGroupAdmin(groupId, function (resp) {
            resp.data.forEach(function (elem) {
              vm.$set(vm.groupSetting.members[elem], 'type', 'admin');
            });
          });
          vm.$set(vm.groupSetting, 'members', members);
        });
        return;
      }
      if (this.chatPanelType === 'groupSetting') {
        this.chatPanelType = 'chatbox';
        return;
      }
    },
    formatedDate: function (timestamp, isAbbr) {
      var now = new Date();
      var date = new Date(timestamp);

      var timeFunc = function () {
        var hours = date.getHours();
        var minutes = "0" + date.getMinutes();
        var seconds = "0" + date.getSeconds();
        var morningOrAfternoon = hours >= 12 ? '下午' : '上午';
        return morningOrAfternoon + hours + ':' + minutes.substr(-2);
      }

      // 检查是否为今天
      if (date.getFullYear() === now.getFullYear() && date.getMonth() === now.getMonth() && date.getDate() === now.getDate()) {
        return timeFunc();
      }

      // 检查是否为昨天
      var yesterday = new Date();
      yesterday.setDate(now.getDate() - 1);
      if (date.getFullYear() === yesterday.getFullYear() && date.getMonth() === yesterday.getMonth() && date.getDate() === yesterday.getDate()) {
        if (isAbbr) {
          return '昨天';
        } else {
          return '昨天 ' + timeFunc();
        }
      }

      // 检查是否为最近7天内
      var before6Day = new Date();
      before6Day.setDate(now.getDate() - 6);
      before6Day.setHours(0);
      before6Day.setMinutes(0);
      before6Day.getSeconds(0);
      if (date >= before6Day) {
        var weekConf = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
        var weekOfDayStr = weekConf[date.getDay()];

        if (isAbbr) {
          return weekOfDayStr;
        } else {
          return weekOfDayStr + ' ' + timeFunc();
        }
      }

      //一周以外的情况
      if (isAbbr) {
        return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
      } else {
        return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' ' + timeFunc();
      }

      return timeFunc();
    },
    //获取当前用户staff信息
    getCurUserStaffInfo: function () {
      var vm = this;
      this.ajaxGetStaffInfo(this.curUserId, function (data) {
        var data = data[0];
        data.id = data.StaffId;

        vm.addToStorage('staff', vm.curUserId, data);//添加当前人存储staffId
        vm.curUser = data;
      });
    },
    //无头像时，默认设置头像
    getDefaultStaffImg: function (gender) {
      if (gender == 2) {
        return '/images/chat/staff-girl.png';
      } else {
        return '/images/chat/staff-boy.png';
      }
    },
    //固定到消息最底部
    scrollToBottom: function () {
      if ($('.chat-content').length > 0) {
        $('.chat-content').scrollTop($('.chat-content')[0].scrollHeight);
      }
    },
    //检查是否滚动到底部的函数
    checkScrollBottom() {
      let el = document.getElementById('chat-content-box');
      return el && el.scrollHeight - el.scrollTop === el.clientHeight;
    },
    isMenuStaffSelected: function (staffId) {
      if (this.chatPanelType === 'intro' && this.curStaffIntro.StaffId === staffId) {
        return true;
      }
      if (this.chatPanelType === 'chatbox' && this.curChatbox.toId === staffId) {
        return true;
      }
      return false;

    },
    //初始化元素-右侧浮动聊天通讯点击-没起作用
    initEvent: function () {
      var vm = this;
      $(document).click(function (e) {
        var $target = $(e.target);
        if (!$target.hasClass("chat-menu-header-opt-icon") && !$target.hasClass(".chat-menu-header-opt")) {
          vm.isShowOptPanel = false;
        }

      });
    },
    openOrCloseOptPanel: function () {
      this.isShowOptPanel = !this.isShowOptPanel;
    },
    initDB: function () {
      //      this.createDB(this.staffIndexedDBConf);
    },
    
    //好友会话-单击展示详情
    openStaffIntroPanel: function (staffId) {
      var vm = this;
      this.chatPanelType = 'intro';
      let idArr = [];
      idArr.push(staffId)
      $.ajax({
        url: '/chat/GetStaffInfo',//获取在职员工信息
        method: 'post',
        data: {
          staffIds: idArr,
        },
        success: function (resp) {
          if (!resp) return;
          vm.curStaffIntro = resp.Data[0];
        }
      }).fail(function (e) {
        console.error(e);
      });
      //this.ajaxGetStaffInfo(staffId, function (data) {
      //  vm.curStaffIntro = data[0];
      //});
    },
    openOrCloseStaffPanel: function (index, id, e) {
      var $target = $(e.target);
      var $wrapper = $target.closest('.chat-item-wrapper');
      if ($wrapper.hasClass("chat-item-open")) {
        $wrapper.removeClass('chat-item-open');
      } else {
        $wrapper.addClass('chat-item-open');
      }
      this.ajaxGetStaffList(index, id, undefined, undefined, $wrapper);
    },
    ajaxGetWebMI: function (func) {
      var vm = this;
      $.ajax({
        url: '/chat/GetWebMI',
        method: 'post',
        success: function (resp) {
          if (!resp) return;
          if (resp.Success) {
            func(resp.Data);
          } else {
            vm.reportInterfaceErr("/chat/GetWebMI", {}, resp);
          }
        }
      }).fail(function (e) {
        console.error(e);
      });
    },
    ShowStaffContactViewLogTips: function (staffId, mobileNumber) {
      var vm = this;
      var data = { ViewStaffId: staffId, ViewPage: "chat" };
      ajaxPost3("/Staff/GetStaffContactViewLog", data, 'json', function (result) {
        if (result.StatusCode == 1) {
          var count = result.Data.Count;
          var allow = false;
          var limitCount = result.Data.LimitCount
          if (!result.Data.IsView) {
            if (count >= limitCount) {
              r_Layout_BeautAlert.done("可查看次数不足");
              return;
            }
            allow = confirm("确定要查看吗？今日还剩" + (limitCount - count) + "次（每日限" + limitCount + "次）系统会记录您的查看记录");
          }
          else {
            allow = true;
          }
          if (allow) {
            ajaxPost3("/Staff/AddStaffContactViewLog", data, 'json', function (result) {
              if (result.StatusCode == 1) {
                vm.StaffContactViewLogMobilePhone = result.Data;
                vm.StaffContactViewLogType = 1;
              }
              else {
                r_Layout_BeautAlert.done(result.Message);
              }
            });
          }
        }
        else {
          r_Layout_BeautAlert.done(result.Message);
        }
      });
    },
    //根据环信accountId获取系统类型（判断是顾问还是人选）
    accidGetStaffid: function (id) {

      let vm = this;
      return new Promise(function (resolve, reject) {
        $.ajax({
          url: `${vm.chatApi}/eim/im-info/${id}`,
          method: 'get',
          success: function (resp) {
            if (!resp) {
              reject(new Error('No response returned from the request.'));
            }
            if (resp.success) {
             // resolve(resp.data.userId);
              resolve(resp.data)
            } else {
              resolve(id); // 如果响应不成功，仍然解析传入的id
            }
          }
        }).fail(function (e) {
          console.error(e);
          reject(e);
        });
      });
    },
    //接口获取staff用户信息
    ajaxGetStaffInfo: function (id, func) {
      var idArr = [];
      var userType = '';
      var userId = ''
      var vm = this;
      var promiseArray = [];//后期放开这些判断
      if (id instanceof Array) {
        idArr = id;
      } else if (id && id.toString().indexOf("h") == 0) {//当前id为环信accId，通过接口环信accId拿到staffId
        // 通过环信新accid获取系统中staffid
        promiseArray.push(this.accidGetStaffid(id));
      } else {//从好友列表点击
        idArr.push(id);
      }
      Promise.all(promiseArray).then(function (results) {
        //将所有返回的staffid添加到idArr中
        results.forEach(function (item) {
          if (item.userId) {
            idArr.push(item.userId);
            userType = item.userType;
            userId = item.userId;
          }
        });
        if (!idArr.length) {
          func([]);
          return;
        }
        if (userType == 1) {//顾问信息
          $.ajax({
            url: '/chat/GetStaffInfo',//获取在职员工信息
            method: 'post',
            data: {
              staffIds: idArr,
            },
            success: function (resp) {
              if (!resp) return;
              if (resp.Success) {
                resp.Data.forEach(function (elem) {
                  if (!elem.PictureUrl) {
                    elem.PictureUrl = vm.getDefaultStaffImg(elem.Gender);
                  }
                });
                vm.StaffContactViewLogType = 0;
                if (!vm.storage.staff[id]) {//如果没有存储本地
                  vm.addToStorage('staff', id, resp.Data[0], false);
                }
                func(resp.Data);
              } else {
                vm.reportInterfaceErr("/chat/GetStaffInfo", {
                  staffIds: idArr,
                }, resp);
              }
            }
          }).fail(function (e) {
            console.error(e);
            func([]);
          });
        } else {
          vm.$nextTick(() => {
          
          $.ajax({
            url: `${vm.chatApi}/eim/im-info?userId=` + vm.UserIdNew +`&userType=1`,//人选用户信息
            method: 'get',
            success: function (resp) {
              if (!resp || !resp.success) return;
              //重组用户信息结构
              let arr = [{
                StaffId: resp.data.userId,
                Name_Cn: resp.data.userName,
                PictureUrl: resp.data.userIcon,
                accountId: resp.data.accountId
              }]
              //判断是否有存储，无的话存储
              if (!vm.storage.staff[resp.data.accountId]) {
                vm.addToStorage('staff', resp.data.accountId, arr[0], false);
              }
              func(arr);
             
              /*vm.respData = resp.data;*/
            }
          }).fail(function (e) {
            console.error(e);
            func([]);
          });
          })
        }
      })
    },
    /***
     * created by 吴冠圣
     * date:2021.7.22
     * description:查询归属公司
     *用户首次登录，调用查询归属公司接口，将接口返回的数据存入缓存中，避免重复调用接口
     * */
    ajaxGetCompany: function () {
      var vm = this;
      var list = [];
      let companyData = localStorage.getItem('companyData');
      if (companyData && companyData.length > 0) {
        companyData = JSON.parse(companyData)
        companyData.forEach(function (elem) {
          list.push({
            Id: elem.Id,
            Text: elem.Text,
            staffList: [],
          });
        });
        vm.friendList = list;
        return;
      }
      $.ajax({
        url: '/chat/GetCompany',
        method: 'post',
        success: function (resp) {

          if (!resp) return;
          if (resp.Success) {
            var list = [];
            resp.Data.forEach(function (elem) {
              list.push({
                Id: elem.Id,
                Text: elem.Text,
                staffList: [],
              });
            });
            vm.friendList = list;
            let jsonString = JSON.stringify(list);
            localStorage.setItem('companyData', jsonString);
          } else {
            vm.reportInterfaceErr("/chat/GetCompany", {}, resp);
          }
        }
      }).fail(function (e) {
        console.error(e);
      });
    },

    ajaxGetStaffList: function (index, companyId, name, page, $dom, func) {
      var vm = this;
      if (companyId !== 0 && this.friendList[index].staffList.length > 0) {
        return;
      }

      if ($dom) {
        $dom.find(".chat-item-loading").css({
          display: 'block',
        });
      }

      if (page === undefined) {
        page = {
          page: 1,
          pageSize: 100000,
        };
      }
      $.ajax({
        url: '/chat/GetStaffList',
        method: 'post',
        data: {
          CompanyId: companyId,
          Name: name,
          PageSize: page.pageSize,
          Page: page.page,
        },
        success: function (resp) {
          if (resp.Status) {
            resp.Data.forEach(function (elem) {
              elem.PictureUrl = elem.PictureUrl ? elem.PictureUrl : vm.getDefaultStaffImg(elem.Gender);
            });
            if (companyId !== 0) {
              var list = [];
              resp.Data.forEach(function (elem) {
                var data = {
                  id: elem.StaffId,
                  StaffId: elem.StaffId,
                  PictureUrl: elem.PictureUrl,
                  Name_Cn: elem.Name_Cn,
                  Name_En: elem.Name_En,
                  Department_Cn: elem.Department_Cn,
                };
                list.push(data);
                //                vm.add(data, vm.staffIndexedDBConf);
              });

              vm.$set(vm.friendList[index], 'staffList', list);
            }

            func && func(resp.Data, resp.Total);
          } else {
            func && func([], 0);
          }
          if ($dom) {
            $dom.find(".chat-item-loading").css({
              display: 'none',
            });
          }
        }
      }).fail(function (e) {
        console.error(e);
      });
    },
    changeMenuShowType: function (type) {
      this.menuShowType = type;
    },
    //全部已读
    readConversation(toId) {
      let param = {
        fromId: toId,
        toId: this.curUserId
      }
      var vm = this;
      $.ajax({
        url: `${vm.chatApi}/imMsg/im-message/has-read`,//https://test.haidou.com/ris-elite-api
        method: 'get',
        data: param,
        success: function (resp) {
          if (!resp) return;
          vm.setLeftMessageListRead(toId)
        }
      }).fail(function (e) {
        console.error(e);
      });
    },
    //设置左侧未读数为已读，且重新调用全部未读计算
    setLeftMessageListRead(toId) {
      //根据index设置messageList数据的未读数为0 
      //let toIndex = 0;
      this.messageList.forEach(function (elem, index) {
        if (toId == elem.id) {
          elem.unreadNum = 0;
          //toIndex = index;
        }
      });
      this.unReadCount()
    },
    //1、点击搜索框加载的用户列表---加载左侧数据后，数据有错乱
    //2、双击好友列表顾问时
    //3、单机好友列表顾问后，右侧详情的“发送消息”按钮
    openChatPanel1: function (type, staff) {
      var vm = this;
      //根据staffId获取accId，userType为1是顾问身份
      $.ajax({
        url: `${vm.chatApi}/eim/im-info?userId=${staff.StaffId}&userType=1`,
        method: 'get',
        success: function (resp) {
          if (!resp || !resp.success) return;
          //vm.respData = resp.data;//存在疑问为何点击通讯里中顾问，需要铺数据
          if (resp.data.accountId && resp.data.passWord) {
            vm.addToStorage('staff', resp.data.accountId, staff)//存储顾问到本地
            //判断是否已存在左侧会话列表，无的话重新增加一条会话数据
            vm.menuShowType = 'messages';//如果是2/3的情况，展示左侧会话列表
            vm.openChatPanel(type, resp.data.accountId)//调用打开聊天内容
          } else {
            console.error("Invalid data received from API");
          }
         
        }
      }).fail(function (e) {
        console.error(e);
      });
      
    },
    //点击左侧会话列表，打开聊天页-type:单聊：id：为环信accId；unReadNum：未读数
    //1、点击左侧会话列表
    //2、点击通讯录注册环信accId后调用
    openChatPanel: function (type, id, unReadNum) {
      this.isImgLoading = false;//关闭上传占位图
      this.toAccountId = id;
      this.setMessageToReaded(id);
      this.curChatbox.loaded = false;
      this.curChatbox.type = type;
      this.curChatbox.database = 'chat-' + type + '-' + this.curUserId + '-' + id;
      this.curChatbox.table = "chat_history";
      this.curChatbox.toId = String(id);
      this.createDB();

      var vm = this;
      this.chatList = [];
      this.chatListPage.vernier = 0;//设置游标的值
      //this.chatListPage.page = 1;
      this.readAll2(id, function () {//获取消息列表集合
        if (unReadNum > 0) {
          vm.readConversation(vm.curChatbox.toId)//调用全部已读
          //判断是否有未读标签，有的话调用会话已读回执
          WebIMAPI.sendReadChannelMessage(id);
        }
        vm.$nextTick(function () {
          vm.scrollToBottom();
        });
      });
      this.readDataCount(function (total) {
        vm.chatListPage.total = total;
      });
      if (type === 'single') {
        this.getData({
          database: 'staff',
          table: 'data',
          toId: id,
        }, function (data) {
          if (!data) {
              
            if (!vm.storage.staff[id]) {//判断是没有本地accId信息
              vm.ajaxGetStaffInfo(id, function (data) {//直接存储对方的信息
                vm.addToStorage('staff', id, data[0], false);
                vm.$nextTick(function () {
                  vm.scrollToBottom();
                  vm.curChatbox.loaded = true;
                });
              });
            }
            else {
              vm.curChatbox.loaded = true;
            } 
            
          } else {
            vm.curChatbox.users[data.StaffId] = data;
            vm.curChatbox.loaded = true;
          }
        });
      } else {
        WebIMAPI.getGroupInfo(id, function (resp) {
          var staffList = [];
          resp.data[0].affiliations.forEach(function (elem) {
            var key = Object.keys(elem)[0];
            staffList.push(elem[key]);
          });
          vm.ajaxGetStaffInfo(staffList, function (data) {
            vm.addToStorage('staff',id , data[0], true);
            var users = vm.curChatbox.users;
            data.forEach(function (elem) {
              vm.$set(vm.curChatbox.users, elem.StaffId, elem);
            });
          });
        });
      }


      this.curChatbox.users[this.curUserId] = this.curUser;
      this.chatPanelType = "chatbox";

      this.$nextTick(function () {
        var draft = '';
        var isCreateMessage = true;   //如果打开的聊天框，在左侧消息列表中不存在，则在左侧消息列表新建一个 
        vm.messageList.forEach(function (elem, index) {
          // 草稿功能
          if (elem.id == id) {
            isCreateMessage = false;
            if (elem.notice.type == 'draft') {
              draft = elem.notice.msg;
            }
            vm.$set(vm.messageList[index].notice, 'type', undefined);
            vm.$set(vm.messageList[index].notice, 'msg', "");
            return false;
          }

        });
        if (draft) {
          setTimeout(function () {
            $("#chat-modal .chat-input-panel-input").html(draft);
          }, 100);
        }
        if (isCreateMessage) {
          var chatType = type === 'single' ? 'chat' : 'groupchat';
          vm.setMessageList(id, '', chatType, false);
        }
      });

    },
    //点击发送按钮-发送文本消息
    sendChatMsg: function () {
      var vm = this;
      var originChatMsg = this.curChatbox.chatMsg;
      var chatbox = this.curChatbox;
      this.curChatbox.chatMsg = $("#chat-modal .chat-input-panel-input").html();
      if (!chatbox.chatMsg.replace().replace(/<br\s*\/?>/gi, '').trim()) {
        this.showChatBlankMsgNotice();
        return;
      }

      var ext = {
        nickName: this.getStorageData('staff', this.curUserId).Name_Cn,
        nickUrl: this.getStorageData('staff', this.curUserId).PictureUrl,
      }
      var arr = [];
      var emotion = [];
      var $chatMsg = $("<div>" + chatbox.chatMsg + "</div>");
      var chatMsgHtml = $chatMsg.html().replace(/<br\s*\/?>/gi, '\r\n');
      var type = undefined;
      var id = undefined;
      type = $chatMsg.find(".chat-msg-img-emoji").data('type');
      if (type === 'tuzki') {
        $chatMsg.find(".chat-msg-img-emoji").each(function (index, elem) {
          var emotionStr = $(elem).data("emotion");
          type = $(elem).data('type');
          id = $(elem).data('id');
          chatMsgHtml = chatMsgHtml.replace(elem.outerHTML, emotionStr);
        });
        ext.codeId = String(id);
        ext.em_is_big_expression = true;
        ext.groupName = type;
        ext.type = 'GIF';
      } else {
        $chatMsg.find(".chat-msg-emotion").each(function (index, elem) {
          var emotionStr = $(elem).data("emotion");
          type = $(elem).data('type');
          id = $(elem).data('id');
          chatMsgHtml = chatMsgHtml.replace(elem.outerHTML, emotionStr);
        });
      }

      var obj = undefined;

      WebIMAPI.sendChatMsg(chatbox.type, chatMsgHtml, chatbox.toId, ext, function (msgId) {

        obj = {
          msgId: msgId,
          data: chatMsgHtml,
          error: false,
          errorCode: "",
          errorText: "",
          messageType: 'txt',
          messagePlain: $chatMsg.html(),
          ext: ext,
          from: vm.curUserId,
          to: chatbox.toId,
          isRead: 0,
          createDate: Date.now(),
        };
        if (chatbox.type === "group") {
          obj.type = "groupchat";
        } else {
          obj.type = "chat";
        }
        vm.add(obj);
        vm.chatList.push(obj);
        vm.isScorllButtom = true;
        //this.setMessageList(chatbox.toId, chatbox.chatMsg, obj.type, false);
        if (type !== undefined && type !== 'default') {
          vm.curChatbox.chatMsg = originChatMsg;
          $("#chat-modal .chat-input-panel-input").html(originChatMsg);
        } else {
          vm.curChatbox.chatMsg = '';
          $("#chat-modal .chat-input-panel-input").html('');
        }
        vm.saveMessage(obj);//测试发送消息是否成功，在放开
      });
    },
    //保存消息到服务器
    saveMessage: function (obj) {
      let vm = this;
      const msgBody = {};
      if (obj.messageType === vm.MESSAGE_TYPE.TEXT) {//文本
        msgBody.type = obj.messageType;
        msgBody.msg = obj.data;
        msgBody.ext = obj.ext;
      } else if (obj.messageType === vm.MESSAGE_TYPE.FILE) {//文件
        msgBody.type = obj.messageType;
        msgBody.chatType = obj.chatType;
        msgBody.fileName = obj.filename || '';
        //msgBody.fileType = obj.body.type || '';
        msgBody.file_length = obj.file_length || 0;
        msgBody.fileUrl = obj.url || '';
        msgBody.ext = obj.ext;
      } else if (obj.messageType === vm.MESSAGE_TYPE.IMAGE) {//图片
        msgBody.type = 'img';
        msgBody.chatType = obj.chatType;
        msgBody.width = obj.width || 0;
        msgBody.height = obj.height || 0;
        msgBody.url = obj.url || '';
        msgBody.ext = obj.ext;
      }
      
      const request = {
        msgId: obj.msgId,
        sessionId: "",
        msgFrom: this.respData.accountId,
        msgTo: obj.to,
        sendType: 0, //0:正常发送 1：自动回复
        msgBody: JSON.stringify(msgBody)
      };

      fetch(`${vm.chatApi}/imMsg/saveMsg`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.statusText);
          }
          return response.json();
        })
        .then(data => {
          // this.initMessageList(this.respData);
          vm.setMessageList(obj.to, obj.data, obj.type, false, obj.messageType);//发送消息成功后，重新设置会话列表
          //游标值加1
          vm.chatListPage.vernier++;
          vm.chatListPage.total++;
        })
        .catch(error => {
          console.error("保存失败：", error.message);
        });
    },
    // indexdb demo website: https://www.cnblogs.com/oukele/p/10727790.html
    createDB: function (data) {
      var chatbox = data ? data : this.curChatbox;

      var request = indexedDB.open(chatbox.database, 2);

      // 请求数据库失败的回调函数
      request.onerror = function (err) {
        console.log('打开数据库失败,错误信息为:' + err.target.message);
      }

      // 请求数据库成功的回调函数
      request.onsuccess = function (success) {

        var db = success.target.result;
      }

      // 数据库版本更新
      request.onupgradeneeded = function (event) {

        var db = event.target.result;

        // 判断对象储存空间名称是否存在
        if (!db.objectStoreNames.contains(chatbox.table)) {

          //创建信息对象存储空间;指定keyPath选项为Id（即主键为Id）
          var objectStore = db.createObjectStore(chatbox.table, {
            keyPath: 'createDate',
          });

          objectStore.createIndex('idx_id', 'createDate', {
            unique: true
          });

        }
      }

    },
    add: function (msgObj, data) {
      var chatbox = data ? data : this.curChatbox;
      // 打开数据库
      var request = indexedDB.open(chatbox.database, 2);
      // 请求数据库成功的回调函数
      request.onsuccess = function (success) {
        // 获取数据库实例对象
        var db = success.target.result;
        // 对某个表 进行事务操作的事务权限控制 
        var transaction = db.transaction(chatbox.table, 'readwrite');
        // 对表进行操作
        var objectStore = transaction.objectStore(chatbox.table);

        // 使用add方法,此方法是异步的
        // 有success,error事件
        //objectStore.add(argument);
        // 使用定义add方法
        var add = objectStore.add(msgObj);

        var msg = JSON.stringify(msgObj);

        // 添加成功的回调函数
        add.onsuccess = function (e) {
        }
        add.error = function (e) {
          console.log('向表新增一条数据为失败！！');
        }
      }

      // 数据库版本更新
      request.onupgradeneeded = function (event) {

        var db = event.target.result;

        // 判断对象储存空间名称是否存在
        if (!db.objectStoreNames.contains(chatbox.table)) {

          //创建信息对象存储空间;指定keyPath选项为Id（即主键为Id）
          var objectStore = db.createObjectStore(chatbox.table, {
            keyPath: 'createDate',
          });

          objectStore.createIndex('idx_id', 'createDate', {
            unique: true
          });

        }
      }
    },
    clearData: function (data, callback) {
      var chatbox = data ? data : this.curChatbox;
      // 请求打开数据库
      var request = indexedDB.open(chatbox.database);
      // 请求成功的回调函数
      request.onsuccess = function (e) {
        // 获取实例
        var db = e.target.result;
        // 表名事务权限控制
        var transaction = db.transaction(chatbox.table, 'readwrite');
        // 进行操作
        var objectStore = transaction.objectStore(chatbox.table);
        // 清除数据
        var clearResult = objectStore.clear();
        // 清除成功的回调函数
        clearResult.onsuccess = function (e) {
          callback && callback();
        }
      }
    },
    getData: function (data, callback) {
      var vm = this;
      var chatbox = data ? data : this.curChatbox;
      // 打开数据库
      var request = indexedDB.open(chatbox.database, 2);
      // 请求打开数据库的回调函数
      request.onsuccess = function (success) {
        var db = success.target.result;
        var transaction = db.transaction([chatbox.table], 'readwrite');
        var objectStore = transaction.objectStore(chatbox.table);
        var getResult = objectStore.get(data.toId);

        getResult.onsuccess = function (e) {
          callback(e.target.result);
        }
      }
      // 数据库版本更新
      request.onupgradeneeded = function (event) {

        var db = event.target.result;

        // 判断对象储存空间名称是否存在
        if (!db.objectStoreNames.contains(chatbox.table)) {

          //创建信息对象存储空间;指定keyPath选项为Id（即主键为Id）
          var objectStore = db.createObjectStore(chatbox.table, {
            keyPath: 'createDate',
          });

          objectStore.createIndex('idx_id', 'createDate', {
            unique: true
          });

        }
      }
    },
    readDataCount: function (callback) {
      var vm = this;
      var chatbox = this.curChatbox;
      var request = indexedDB.open(chatbox.database);
      request.onsuccess = function (e) {
        var objectStore = e.target.result.transaction(chatbox.table, 'readwrite').objectStore(chatbox.table);
        var count = objectStore.count();

        count.onsuccess = function () {
          callback && callback(count.result);
        }
      }
    },
    //获取消息列表-通过接口处理-游标查询
    readAll2: function (toId, callback) {
      let vm = this;
      $.ajax({
        url: `${vm.chatApi}/imMsg/getMsgListByVernier`,
        method: 'get',
        data: {
          toId: toId,
          //pageNum: vm.chatListPage.page,
          //pageSize: vm.chatListPage.per,
          vernier: vm.chatListPage.vernier,//游标
          fromId: this.curUserId,
          isFiltration: 1,//是否过滤自动回复
        },
        success: function (resp) {
          if (!resp || !resp.success) return;
          vm.chatListPage.total = resp.data.total;
          
          if (resp.data.msgList.length > 0) {
            if (vm.chatListPage.vernier == 0) {
              vm.chatList = [];
            }
            //给游标赋值
            vm.chatListPage.vernier += resp.data.msgList.length;//返回的值
            resp.data.msgList.forEach((item) => {
              let arr = [];
              let msgBody = JSON.parse(item.msgBody);
              let obj = {
                msgId: item.msgId,//消息的id
                createDate: item.time,
                from: item.msgFrom,
                to: item.msgTo,
                isRead: item.msgStatus,
                ext: msgBody.ext
              }
              if (msgBody.type == 'txt') {//文本消息
                obj.messageType = 'txt';
                obj.ext = msgBody.ext;
                //循环判断emo表情替换
                let msg = msgBody.msg
                WebIMAPI.getEmojiConf()['default'].forEach(function (elem) {
                  if (msg.indexOf(elem.name) > -1) {
                    msg = msg.replaceAll(elem.name, '<img class="chat-msg-emotion chat-msg-emotion-img class7" src="/images/chat/emoji/new/' + elem.img + '" data-emotion="' + elem.name + '">');
                   }
                })
                obj.messagePlain = msg;
              } else if (msgBody.type == 'custom') {//自定义卡片
                obj.messageType = 'custom';
                let obj2 = {};
                if (msgBody.ext.type == 'industry_resume') {
                  obj2 = JSON.parse(msgBody.ext.messageResume)
                  obj.ext.messageResume = obj2
                } else if (msgBody.ext.type == 'industry_job') {
                  obj2 = JSON.parse(msgBody.ext.messageJob)
                  obj.ext.messageJob = obj2
                }
              } else if (msgBody.type == 'file') {//文件
                  obj.messageType = 'file';
                  obj.filename = msgBody.fileName;
                  obj.file_length = msgBody.file_length;
                  obj.url = msgBody.fileUrl;
              } else if (msgBody.type == 'img') {//图片
                obj.messageType = 'img';
                  obj.url = msgBody.url;
                  obj.width = msgBody.width;
                  obj.height = msgBody.height;
              } else if (msgBody.type == 'audio') {//语音
                obj.messageType = msgBody.type;
                obj.fileName = msgBody.filename || '';
                obj.fileType = msgBody.filetype || '';
                obj.Duration = msgBody.Duration || 0;
                obj.fileUrl = msgBody.fileUrl || '';
              } else if (msgBody.type == 'video') {//视频
                obj.messageType = msgBody.type;
                obj.fileName = msgBody.filename || '';
                obj.fileType = msgBody.filetype || '';
                obj.Duration = msgBody.Duration || 0;
                obj.fileUrl = msgBody.fileUrl || '';
              }
              arr.push(obj)
              vm.chatList.unshift(...arr)
            })
          }
          callback && callback();
        }
      }).fail(function (e) {
        console.error(e);
      });
    },
    //获取消息列表-通过接口处理---备份
    readAll3: function (toId, callback) {
      let vm = this;
      $.ajax({
        url: `${vm.chatApi}/imMsg/getMsgList`,
        method: 'get',
        data: {
          toId: toId,
          pageNum: vm.chatListPage.page,
          pageSize: vm.chatListPage.per,
          fromId: this.curUserId
        },
        success: function (resp) {
          if (!resp || !resp.success) return;
          vm.chatListPage.total = resp.data.total;
          if (resp.data.records.length > 0) {
            if (vm.chatListPage.page == 1) {
              vm.chatList = [];
            }
            resp.data.records.forEach((item) => {
              let arr = [];
              let msgBody = JSON.parse(item.msgBody);
              let obj = {
                msgId: item.msgId,//消息的id
                createDate: item.time,
                from: item.msgFrom,
                to: item.msgTo,
                isRead: item.msgStatus,
                ext: msgBody.ext
              }
              if (msgBody.type == 'txt') {//文本消息
                obj.messageType = 'txt';
                obj.ext = msgBody.ext;
                //循环判断emo表情替换
                let msg = msgBody.msg
                WebIMAPI.getEmojiConf()['default'].forEach(function (elem) {
                  if (msg.indexOf(elem.name) > -1) {
                    msg = msg.replaceAll(elem.name, '<img class="chat-msg-emotion chat-msg-emotion-img class7" src="/images/chat/emoji/new/' + elem.img + '" data-emotion="' + elem.name + '">');
                  }
                })
                obj.messagePlain = msg;
              } else if (msgBody.type == 'custom') {//自定义卡片
                obj.messageType = 'custom';
                let obj2 = {};
                if (msgBody.ext.type == 'industry_resume') {
                  obj2 = JSON.parse(msgBody.ext.messageResume)
                  obj.ext.messageResume = obj2
                } else if (msgBody.ext.type == 'industry_job') {
                  obj2 = JSON.parse(msgBody.ext.messageJob)
                  obj.ext.messageJob = obj2
                }
              } else if (msgBody.type == 'file') {//文件
                obj.messageType = 'file';
                obj.filename = msgBody.fileName;
                obj.file_length = msgBody.file_length;
                obj.url = msgBody.fileUrl;
              } else if (msgBody.type == 'img') {//图片
                obj.messageType = 'img';
                obj.url = msgBody.url;
                obj.width = msgBody.width;
                obj.height = msgBody.height;
              } else if (msgBody.type == 'audio') {//语音
                obj.messageType = msgBody.type;
                obj.fileName = msgBody.filename || '';
                obj.fileType = msgBody.filetype || '';
                obj.Duration = msgBody.Duration || 0;
                obj.fileUrl = msgBody.fileUrl || '';
              } else if (msgBody.type == 'video') {//视频
                obj.messageType = msgBody.type;
                obj.fileName = msgBody.filename || '';
                obj.fileType = msgBody.filetype || '';
                obj.Duration = msgBody.Duration || 0;
                obj.fileUrl = msgBody.fileUrl || '';
              }
              arr.push(obj)
              vm.chatList.unshift(...arr)
            })
          }
          callback && callback();
        }
      }).fail(function (e) {
        console.error(e);
      });
    },
    //获取聊天页面-的信息进行数据平铺
    readAll: function (callback) {
      var vm = this;
      var chatbox = this.curChatbox;
      // 请求打开数据库
      var request = indexedDB.open(chatbox.database);
      // 请求成功的回调函数
      request.onsuccess = function (e) {
        // db = e.target.result // 获取实例
        // transaction = db.transaction(tableName,'readwrite'); // 权限控制
        // objectStore = transaction.objectStore(tableName); // 进行操作对象
        var objectStore = e.target.result.transaction(chatbox.table, 'readwrite').objectStore(chatbox.table);
        // 打开游标
        var cursor = objectStore.openCursor(undefined, 'prev');

        // 储存值
        var arr = [];

        var isAdvanced = false;
        // 成功打开游标的回调函数
        cursor.onsuccess = function (e) {
          var result = e.target.result;
          if (!isAdvanced) {
            isAdvanced = true;
            if (result && vm.chatListPage.page > 0) {
              result.advance(vm.chatListPage.per * vm.chatListPage.page + (vm.chatList.length - vm.chatListPage.per * vm.chatListPage.page));
              return;
            }
          }

          if (result) {
            
            // 将数据一条一条保存到arr中
            arr.unshift(result.value);
            if (arr.length >= vm.chatListPage.per) {
              vm.chatList = arr.concat(vm.chatList);
              vm.chatListPage.page++;
              callback && callback();
              //              vm.chatList.unshift(result.value);
              return;
            } else {
              result.continue();
            }
          } else {
            if (!arr) {
              console.log('没有数据....');
            } else {
              vm.chatList = arr.concat(vm.chatList);
              vm.chatListPage.page++;
              callback && callback();
            }
          }
        }
      }
    },
    //点击加载更多分页-获取聊天数据
    loadChatMsgByPage: function () {
      //this.chatListPage.page++;
      this.readAll2(this.toAccountId);
    },
    //当前页为第一页时，滚动到底部，否则滚动到当前页高度
    chatScrollToBottom: function () {
      var vm = this;
      //if (isScorllButtom) {
      //  vm.chatListPage.page = 1;
      //}
      if (vm.isScorllReceive) {//接收的消息通知
        if (vm.chatListPage.vernier == 0) {//且在第一页时//vm.chatListPage.page == 0 || vm.chatListPage.page == 1
          vm.$nextTick(function () {
            vm.scrollToBottom();
          });
        }
        vm.isScorllReceive = false;
        return;
      }

      if (vm.chatListPage.vernier == 0 || vm.isScorllButtom) {//vm.chatListPage.page == 0 || vm.chatListPage.page == 1 || vm.isScorllButtom
        vm.$nextTick(function () {
          vm.scrollToBottom();
          vm.isScorllButtom = false;
        });
      } else {
        vm.$nextTick(function () {
          var height = 0;
          $('.chat-content .chat-msg-item').each(function (index, elem) {
            if (index > vm.chatListPage.per - 1) {
              height += $(elem).height() + 16;
            }
          });
          if ($('.chat-content').length > 0) {
            $('.chat-content').scrollTop($('.chat-content')[0].scrollHeight - height);
          }
        });
      }
    },
  },
  computed: {
    //简历卡片
    showMessageResume() {
      return function (item) {
        let resumeExt = item.messageResume;
        const genderStr = resumeExt.GenderId == 1 ? '男' : '女'
        const workYear = resumeExt.WorkExperience + '年工作经验'
        const age = resumeExt.Age + '岁'
        const educationLevel = resumeExt.EducationLevelTxt || '--'
        let signArray = [genderStr, workYear, age, educationLevel]
        let signTitleStr = signArray.join(' · ')
        return {
          resumeName: resumeExt.Name || '--',
          signTitleName: signTitleStr
        }
      };
    },
    hasUnreadMessage: function () {
      var vm = this;
      var isUnread = false;
      this.messageList.forEach(function (elem) {
        if (elem.isUnread) {
          isUnread = true;
          return false;
        }
      });
      if (!isUnread && this.notice.info.isUnread) {
        isUnread = true;
      }
      return isUnread;
    },
    //表情路径
    emojiPath: function () {
      return WebIM.Emoji.path;
    },
    //获取聊天标题数据
    chatboxTitle: function () {
      if (this.chatPanelType === 'chatbox' && this.curChatbox.loaded) {
        var chatbox = this.curChatbox;
        if (chatbox.type === 'single') {
          let name = this.getStorageData('staff', chatbox.toId).Name_Cn;
          if (this.getStorageData('staff', chatbox.toId).Name_En) {
            name = name + '('+ this.getStorageData('staff', chatbox.toId).Name_En +')'
          }
          return name;
        } else {
          return this.groupList[this.curChatbox.toId].groupname;
        }
      }
      if (this.chatPanelType === 'groupSetting') {
        return this.groupList[this.curChatbox.toId].groupname;
      }
      if (this.chatPanelType === 'intro') {
        return '详细信息';
      }
      if (this.chatPanelType === 'notice') {
        return '群通知';
      }
      return '';
    },
  },
  watch: {
    ready: function () {
      this.openModalCallbackHandler();
    },
    //监听连线状态
    connectStatus: function (val) {
      if (val === 'offline') {
        this.noticePopup = {
          popupToRoot: false,
          isShow: true,
          msg: '抱歉，现在已经离线，点击按钮或者重新<span class="msg-link" onclick="location.reload()">刷新页面</span>以重新连接',
          showOpt: false,
          optList: [
            {
              name: '重新连接',
              click: this.login,
            },
            {
              name: '退出',
              click: this.hideChatModal,
            }
          ],
        };
      } else {
        this.noticePopup.isShow = false;
      }
    },
    //聊天数据叠加时
    chatList: function (val) {
      this.chatScrollToBottom();
    },
    'curChatbox.loaded': function () {
      this.chatScrollToBottom();
    },
    messageList: function () {
      localStorage.setItem('chatrecord-' + this.curUserId, JSON.stringify(this.messageList));
    },
    'curChatbox.database': function (val, oldVal) {
      if (this.chatPanelType === 'chatbox') {
        var oldArr = oldVal.split('-');
        this.setMessageToDraft(oldArr[3], oldArr[1]);
      }
    },
    chatPanelType: function (val, oldVal) {
      if (oldVal === 'chatbox' && val !== 'chatbox') {
        this.setMessageToDraft(this.curChatbox.toId, this.curChatbox.type);
      }
    },
  }
});

chatVue.$watch('hasUnreadMessage', function (val) {
  if (val) {
    //调用未读总数
    $.ajax({
      url: `${this.chatApi}/imMsg/session-un_read_num`,
      method: 'get',
      data: {
        currentId: this.curUserId,
        isFiltration: 1
      },
      success: function (resp) {
        if (!resp || !resp.success) return;
        setUnreadIconToRightMenu(val, resp.data);
      }
    })
  }
  
});
setUnreadIconToRightMenu(chatVue.hasUnreadMessage);
//设置右侧浮动的未读标识
function setUnreadIconToRightMenu(val, num) {
  let count = 0;
  if (num > 0) {
    if (num > 0 && num < 100) {
      count = num;
    } else {
      count = '99+';
    }
  }
  $(".chat-box-unread").html(count > 0 ? count : '')
  $(".chat-box-unread").css({
    'display': val ? 'initial' : 'none',
  });
}

function relogin() {
  chatVue.login();
}
