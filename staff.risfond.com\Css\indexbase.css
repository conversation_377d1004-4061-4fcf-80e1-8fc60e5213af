.index-top {
  width: 100%;
  height: 245px;
}

.h-left {
  float: left;
  width: 100%;
  text-align: left;
}

.h-right {
  float: left;
  width: 100%;
  text-align: right;
}

.index-top-left {
  float: left;
  width: 639px;
  background-color: #FFFFFF;
  height: 247px;
}

.index-top-right {
  float: right;
  width: 381px;
  background-color: #FFFFFF;
  height: 247px;
}

.top-left-left {
  width: 190px;
  height: 247px;
  background-color: #F9F9F9;
  position: relative;
  float: left;
  border-right: 1px solid #DDDDDD;
}

.demo-img {
  position: absolute;
  width: 70px;
  height: 70px;
  border-radius: 50% !important;
  margin: 20px 60px 13px 60px;
}

.top-left-left-p {
  font-size: 14px;
  font-weight: 500;
  color: #2A7FCC;
  line-height: 20px;
  text-align: center;
  position: absolute;
  top: 103px;
  width: 100%;
}

.top-left-left-p2 {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  position: absolute;
  width: 100%;
  top: 129px;
  line-height: 20px;
  text-align: center;
}

.hengxian {
  width: 100%;
  height: 1px;
  position: absolute;
  top: 169px;
  background-color: #DDDDDD;
}

.top-left-left-p3 {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  position: absolute;
  width: 162px;
  top: 182px;
  line-height: 20px;
  left: 16px;
}

.top-left-right {
  position: relative;
  width: 449px;
  background-color: #FFFFFF;
  height: 247px;
  float: right;
}

.top-left-right-div {
  width: 107px;
  height: 50px;
  float: left;
  text-align: center;
  margin-top: 45px;
}

.left-right-div-p {
  width: 100%;
  font-size: 18px;
  font-weight: 500;
  color: #2A7FCC;
  line-height: 26px;
}

.left-right-div-p2 {
  width: 100%;
  font-size: 12px;
  font-weight: 400;
  color: #333333;
  line-height: 14px;
}

.div-hengxian {
  width: 1px;
  height: 25px;
  border-left: 1px solid #EEEEEE;
  margin-top: 60px;
  float: left;
}

.kpbig {
  width: 1034px;
  height: 387px;
  background-color: #FFFFFF;
  margin-top: 14px;
}

.kpbig-top {
  margin: 0 auto;
  width: 992px;
  height: 70px;
}

.kpbig-bottom {
  width: 1034px;
  height: 317px;
}

.gongsiSvg {
  width: 22px;
  height: 22px;
  float: left;
  margin-top: 22px;
}

.kpbig-top-p {
  float: left;
  font-size: 20px;
  font-weight: 400;
  color: #333333;
  line-height: 28px;
  margin-top: 21px;
  margin-left: 12px;
}

.kpbig-top-more {
  width: 72px;
  height: 30px;
  border-radius: 15px !important;
  border: 1px solid #2A7FCC;
  float: right;
  margin-top: 20px;
  cursor: pointer;
}

.kpbig-top-more-p {
  width: 28px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #2A7FCC;
  line-height: 20px;
  margin-top: 5px;
  margin-left: 15px;
  float: left;
}

.moreSvg {
  width: 16px;
  height: 16px;
  margin-left: 3px;
  margin-top: 7px;
  float: left;
}

.kpbig-bottom-content {
  width: 318px;
  height: 317px;
  float: left;
  text-align: left;
  margin-left: 20px;
}

.kpbig-bottom-content-img {
  width: 318px;
  height: 178px;
  border-radius: 6px !important;
  cursor: pointer;
  position: relative;
}

.kpbig-bottom-content-imggg {
  position: absolute;
  left: 0;
  top: 0;
  width: 318px;
  height: 178px;
  border-radius: 6px !important;
}

.kpbig-bottom-content-img-p {
  position: absolute;
  z-index: 99;
  width: 204px;
  height: auto;
  font-size: 22px;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 30px;
  text-align: center;
  left: 50%;
  top: 46%;
  transform: translate(-50%,-50%);
}

.kpbig-bottom-content-p:hover {
  color: #2A7FCC;
}

.kpbig-bottom-content-p {
  width: 100%;
  height: 25px;
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  line-height: 25px;
  margin-top: 14px;
  cursor: pointer;
}

.kpbig-bottom-content-p2 {
  width: 100%;
  height: 14px;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 14px;
  margin-top: 8px;
}

.kpbig-bottom-content-p3 {
  margin-top: 9px;
  width: 308px;
  height: 44px;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  line-height: 22px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.lhjf-content {
  /*width: 1034px;*/
  width: 100%;
  /*height: 426px;*/
  max-height: 630px;
  overflow: hidden;
  margin-top: 14px;
}

.lhjf-left {
  /*width: 730px;*/
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  height: 630px;
  /*float: left;*/
  background-color: #FFFFFF;
}
.lhjf-content .lhjf-left {
  border-radius: 8px!important;
}
.lhjf-right {
  float: right;
  width: 290px;
  height: 426px;
  background-color: #FFFFFF;
}

.lhjf-left-top {
  /*width: 690px;*/
  margin: 0 auto;
  height: 72px;
}

.h-hezuo {
  width: 26px;
  height: 26px;
  float: left;
  margin-top: 26px;
}

.lhjf-left-top-p {
  float: left;
  margin-top: 26px;
  margin-left: 11px;
  width: 82px;
  height: 28px;
  font-size: 20px;
  font-weight: 400;
  color: #333333;
  line-height: 28px;
}

.lhjf-left-top-shezhi {
  width: 162px;
  height: 30px;
  border-radius: 15px !important;
  border: 1px solid #2A7FCC;
  float: left;
  margin-top: 25px;
  margin-left: 16px;
  cursor: pointer;
}

.h-shezhi {
  width: 14px;
  height: 14px;
  float: left;
  margin-top: 7px;
  margin-left: 15px;
}

.lhjf-left-top-shezhi-p {
  width: 120px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #2A7FCC;
  line-height: 20px;
  margin-left: 4px;
  margin-top: 4px;
  float: left;
}

.lhjf-left-center {
  /*width: 690px;*/
  margin: 0 auto;
  height: 40px;
}

.lhjf-left-center-div3 {
  /*float: left;
  width: 510px;*/
  width: calc(100% - 180px);
  height: 40px;
  border-bottom: 1px solid #DDDDDD;
}

.lhjf-left-center-div1-p {
  float: left;
  width: 90px;
  height: 40px;
  border-bottom: 1px solid #DDDDDD;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
  margin: 0;
  padding: 10px 15px 10px 17px;
  cursor: pointer;
}

.lhjf-left-center-div1-p2 {
  border-top: 1px solid #DDDDDD;
  border-left: 1px solid #DDDDDD;
  border-right: 1px solid #DDDDDD;
  border-bottom: 0;
  color: #2A7FCC;
}

.lhjf-left-bottom {
  /*width: 690px;*/
  margin: 0 auto;
  margin-top: 20px;
  height: 80px;
  border-bottom: 1px solid #DDDDDD;
}

.lhjf-left-bottom-top {
  height: 25px;
  margin-top: 20px;
}

.lhjf-left-bottom p {
  /*float: left;*/
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.lhjf-left-bottom-top-p {
  /*max-width: 181px;*/
  max-width: 260px;
  height: 22px;
  font-size: 16px;
  font-weight: 600;
  color: #2A7FCC;
  line-height: 22px;
}

.lhjf-left-bottom-top-p2 {
  max-width: 45px;
  height: 22px;
  font-size: 16px;
  font-weight: 400;
  color: #2A7FCC;
  line-height: 22px;
  margin-left: 10px;
}
.lhjf-left-new-box {
  max-width: 70px;
}

.lhjf-left-bottom-top-p3 {
  width: 58px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  line-height: 20px;
  margin-left: 11px;
  cursor: auto !important;
  margin-top: 8px;
}

.lhjf-left-bottom-top-p4 {
 /* width: 182px;*/
 width: 240px;
  height: 22px;
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  line-height: 22px;
}

.lhjf-left-bottom-top-p5 {
  cursor: auto !important;
  float: right !important;
  width: 90px;
  height: 25px;
  font-size: 18px;
  font-weight: 600;
  color: #F94444;
  line-height: 25px;
  margin-right: 19px;
  text-align: center;
}

.lhjf-left-bottom-top-left {
  float: left;
  width: 359px;
}
.lhjf-left-box {
  flex: 1;
}
.lhjf-center-box {
  width: 300px;
}
.lhjf-right-box {
  width: 140px;
}
.h-vv {
  /*margin-top: 9px;*/
  width: 26px;
  height: 17px;
  /*float: left;*/
}

.lhjf-left-bottom-bottom {
  height: 22px;
  margin-top: 9px;
}

.lhjf-right-top {
  width: 290px;
  height: 72px;
}

.h-laba {
  width: 26px;
  height: 26px;
  margin-top: 25px;
  margin-left: 17px;
  float: left;
}

.lhjf-right-top-p {
  float: left;
  width: 164px;
  height: 28px;
  font-size: 20px;
  font-weight: 400;
  color: #333333;
  line-height: 28px;
  margin-top: 24px;
  margin-left: 11px;
}

.lhjf-right-cetent {
  width: 250px;
  height: 83px;
  border-top: 1px solid #DDDDDD;
  margin: 0 auto;
}

.lhjf-right-cetent2 {
  width: 250px;
  height: 350px;
  margin: 0 auto;
  overflow: hidden;
}

.lhjf-right-cetent-top {
  width: 250px;
  height: 26px;
  margin-top: 15px;
}

.lhjf-right-cetent-left {
  width: 114px;
  height: 26px;
  float: left;
}

.lhjf-right-cetent-touxiang {
  float: left;
  width: 26px;
  height: 26px;
  border-radius: 50% !important;
}

.lhjf-right-cetent-left-p {
  float: left;
  width: 70px;
  margin-top: 4px;
  margin-left: 11px;
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.h-hezuo2 {
  float: left;
  width: 22px;
  height: 22px;
  margin-top: 6px;
}

.lhjf-right-cetent-right {
  width: 80px;
  height: 26px;
  float: right;
}

.lhjf-right-cetent-bottom {
  width: 250px;
  height: 57px;
  margin-top: 10px;
}

.lhjf-right-cetent-bottom-p {
  float: left;
  width: 49px;
  height: 17px;
  font-size: 12px;
  font-weight: 400;
  color: #333333;
  line-height: 17px;
}

.lhjf-right-cetent-bottom-p2 {
  float: left;
  width: 100px;
  height: 17px;
  font-size: 12px;
  font-weight: 500;
  color: #2A7FCC;
  line-height: 17px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-left: 10px;
}

.lhjf-right-cetent-bottom-p3 {
  width: 25px;
  height: 17px;
  font-size: 12px;
  font-weight: 400;
  color: #333333;
  line-height: 17px;
  margin-right: 5px;
  float: right;
}

.lhjf-right-cetent-bottom-p4 {
  float: right;
  width: 42px;
  height: 17px;
  font-size: 12px;
  font-weight: 500;
  color: #F94444;
  line-height: 17px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.h-shuxian {
  float: left;
  width: 1px;
  height: 12px;
  background-color: #DDDDDD;
  margin-left: 11px;
  margin-top: 11px;
}

.lhjf-left-bottom-p2 {
  float: left;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
  margin-left: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.lhjf-left-bottom-p {
  float: left;
  height: 22px;
  font-size: 16px;
  font-weight: 400;
  color: #F94444;
  line-height: 22px;
}

.lhjf-left-bottom-p3 {
  float: left;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
  margin-left: 12px;
}

.lhjf-left-bottom-p4 {
  margin-left: 12px;
  float: left;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
}

.lhjf-left-bottom-bottom-right {
  width: 330px;
  float: left;
  height: 22px;
}

.jjka-p {
  float: right;
  width: 116px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
}

.index-content4 {
  width: 100%;
 /* width: 1034px;*/
  height: 401px;
  margin-top: 14px;
}

.index-content4-left {
  float: left;
  /*width: 510px;*/
  width: calc(50% - 7px);
  height: 401px;
  padding: 0 20px;
  background-color: #FFFFFF;
}

.index-content5 {
  min-width: 1034px;
  width: 100%;
  padding: 0 20px;
  height: 191px;
  margin-top: 14px;
  background-color: #FFFFFF;
}

.index-content5-top {
  width: 994px;
  height: 50px;
  margin: 0 auto;
}

.h-school {
  float: left;
  width: 26px;
  height: 26px;
  margin-top: 20px;
}

.index-content5-top-p {
  float: left;
  margin-top: 21px;
  margin-left: 12px;
  width: 164px;
  height: 28px;
  font-size: 20px;
  font-weight: 400;
  color: #333333;
  line-height: 28px;
}

.index-content5-bottom {
  /*width: 1034px;*/
  width: 100%;
  height: 101px;
  display: flex;
  margin-top: 20px;
}

.index-content5-bottom-img {
  float: left;
  /*width: 183px;*/
  width: calc(20% - 16px);
  height: 101px;
  background-color: bisque;
  cursor: pointer;
  margin-left: 20px;
  border-radius: 4px !important;
}
.index-content5-bottom-img:first-child {
  margin-left: 0;
}

.index-content6-left-top {
  /*width: 470px;*/
  width: 100%;
  height: 68px;
  margin: 0 auto;
}

.h-dingyue {
  width: 26px;
  height: 26px;
  float: left;
  margin-top: 21px;
}

.index-content6-left-top-p {
  float: left;
  width: 164px;
  height: 28px;
  font-size: 20px;
  font-weight: 400;
  color: #333333;
  line-height: 28px;
  margin-left: 11px;
  margin-top: 20px;
}

.index-content6-content {
  /*width: 470px;*/
  width: 100%;
  height: 40px;
  margin: 0 auto;
  background-color: #F9F9F9;
}

.wangleShipei {
  width: 100%;
  height: 330px;
}

.index-content6-content2 {
  /*width: 470px;*/
  width: 100%;
  height: 40px;
  margin: 0 auto;
  background-color: #FFFFFF;
}

.greenlaba {
  float: left;
  width: 25px;
  height: 25px;
  margin-top: 8px;
  margin-left: 6px;
  background-color: #FFFFFF;
}

.raddd {
  width: 14px;
  height: 14px;
  margin-top: 6px;
  margin-left: 6px;
}

.index-content6-content-p {
  float: left;
  margin-left: 17px;
  margin-top: 10px;
  /*width: 286px;*/
  width: 70%;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

  .index-content6-content-p:hover {
    color: #2A7FCC;
  }

.newRuku {
  float: right;
  width: 108px;
}

.index-content6-content-p2 {
  float: left;
  margin-top: 10px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
}

.index-content6-content-timep {
  float: right;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #999999;
  line-height: 20px;
  margin-top: 10px;
  margin-right: 16px;
}

.index-paihang-top {
  width: 470px;
  height: 69px;
  margin: 0 auto;
}

.h-paihang {
  width: 26px;
  height: 26px;
  margin-top: 20px;
  float: left;
}

.paihang-p {
  margin-left: 10px;
  margin-top: 22px;
  height: 28px;
  font-size: 20px;
  font-weight: 400;
  color: #333333;
  line-height: 28px;
  float: left;
}

.paihang-select {
  float: left;
  width: 90px;
  height: 30px;
  border-radius: 2px !important;
  margin-left: 3px;
  margin-top: 20px;
}

.el-input__inner {
  height: 30px;
  line-height: 30px;
}

.el-input__icon {
  line-height: 30px;
}

.paihang-date {
  float: right;
  width: 162px;
  height: 30px;
  border-radius: 2px !important;
  border: 1px solid #DDDDDD;
  margin-top: 20px;
  background-color: #FFFFFF;
}

.paihang-date-div {
  float: left;
  width: 40px;
  height: 28px;
  border-right: 1px solid #DDDDDD;
  position: relative;
  cursor: pointer;
  color: #333333;
}

.paihang-date-div-p {
  position: absolute;
  width: 40px;
  height: 17px;
  text-align: center;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 17px;
}

.click-divv {
  background-color: #2A7FCC !important;
  color: #FFFFFF !important;
}

.index-paihang-bottom {
  /*width: 470px;*/
  width: 100%;
  height: 280px;
  margin: 0 auto;
}

.index-paihang-tableHead {
  /*width: 470px;*/
  width: 100%;
  height: 40px;
  border: 1px solid #DDDDDD;
}

  .index-paihang-tableHead div {
    float: left;
    height: 40px;
  }

  .index-paihang-tableHead p {
    height: 25px;
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    line-height: 25px;
  }

.index-paihang-mc {
  width: 59px;
  border-right: 1px solid #DDDDDD;
}

.index-paihang-mc-p {
  width: 59px;
  text-align: center;
}

.index-paihang-xm {
  width: 85px;
  border-right: 1px solid #DDDDDD;
}

.index-paihang-xm-p {
  margin-left: 14px;
}

.index-paihang-yj {
  width: 120px;
  border-right: 1px solid #DDDDDD;
}

.index-paihang-gs {
  width: 90px;
  border-right: 1px solid #DDDDDD;
}
.index-paihang-dq {
  text-align: center;
  flex: 1;
}

.index-paihang-gs-p {
  width: 122px;
  text-align: center;
  line-height: 25px;
}
.index-paihang-gs-p-new {
  width: 100%;
}

.index-paihang-tableBody {
  /*width: 470px;*/
  width: 100%;
  height: 240px;
}

  .index-paihang-tableBody div {
    float: left;
    border: 0;
  }

.h-No1 {
  width: 22px;
  height: 28px;
  margin-top: 6px;
  margin-left: 17px;
}

.index-paihang-xm2 {
  max-width: 58px;
  background-color: rgb(38,194,129);
  padding: 2px 8px;
  color: #FFFFFF;
  margin-left: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  cursor: pointer;
  border-radius: 4px !important;
}

.index-paihang-xm2-women {
  cursor: pointer;
  max-width: 58px;
  background-color: #E26A6A;
  padding: 2px 8px;
  color: #FFFFFF;
  margin-left: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  border-radius: 4px !important;
}

.index-paihang-yj-p2 {
  height: 25px;
  font-size: 14px;
  font-weight: 500;
  color: #D91E18;
  line-height: 25px;
  margin-left: 14px;
}

.index-paihang-tableBody-content {
  /*width: 470px;*/
  width: 100%;
  height: 40px;
  background-color: #F9F9F9;
}

.index-paihang-tableBody-content2 {
  width: 470px;
  height: 40px;
  background-color: #FFFFFF;
}

.index-paihang-tableFoot {
  /*width: 470px;*/
  width: 100%;
  height: 40px;
}

.index-paihang-tableFoot-p {
  width: 470px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #2A7FCC;
  line-height: 20px;
  text-align: center;
  margin-top: 10px;
  cursor: pointer;
}

.index-top-right-top {
  width: 346px;
  height: 70px;
  margin-left: 15px;
}

.h-tixing {
  float: left;
  width: 26px;
  height: 26px;
  margin-top: 23px;
}

.index-top-right-top-p {
  float: left;
  width: 82px;
  height: 28px;
  font-size: 20px;
  font-weight: 400;
  color: #333333;
  line-height: 28px;
  margin-left: 12px;
  margin-top: 23px;
}

.index-top-right-top-div {
  width: 102px;
  height: 30px;
  float: left;
  margin-left: 9px;
  margin-top: 21px;
  border: 1px solid #DDDDDD;
}

.sxtj-left {
  cursor: pointer;
  float: left;
  width: 50px;
  height: 28px;
  border-right: 1px solid #DDDDDD;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
}

.sxtj-active {
  color: #FFFFFF !important;
  background-color: #2A7FCC;
}

.sxtj-left-p {
  width: 50px;
  height: 20px;
  text-align: center;
}

.index-top-right-bottom {
  width: 339px;
  height: 155px;
  margin-left: 26px;
}

.index-top-right-bottom-content {
  width: 339px;
  height: 31px;
  border-left: 1px solid #DDDDDD;
}

  .index-top-right-bottom-content p {
    float: left;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
  }

.index-top-right-bottom-content-p {
  width: 120px;
  height: 20px;
  color: #333333;
  margin-left: 15px;
  cursor: auto !important;
}

.index-top-right-bottom-content-p2 {
  width: 56px;
  height: 20px;
  color: #2A7FCC;
  margin-left: 21px;
}

.index-top-right-bottom-content-p3 {
  width: 112px;
  height: 20px;
  color: #2A7FCC;
  margin-left: 10px;
}

.sxtx-qiu {
  float: left;
  width: 8px;
  height: 8px;
  background-color: #C6C6C6;
  border-radius: 50% !important;
  margin-top: 10px;
  margin-left: -4px;
}

.selectzhiwei {
  width: 500px;
  height: 434px;
  position: absolute;
  background-color: #FFFFFF;
  z-index: 999;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
  border-radius: 6px;
}

.selectzhiwei-top {
  width: 500px;
  height: 50px;
  margin: 0 auto;
  border-bottom: 1px solid #EEEEEE;
}

.selectzhiwei-top-p {
  float: left;
  width: 176px;
  height: 20px;
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  line-height: 20px;
  margin-top: 16px;
  margin-left: 20px;
}

.closee {
  position: relative;
  width: 20px;
  height: 20px;
  float: right;
  margin-top: 16px;
  cursor: pointer;
  margin-right: 20px;
}

  .closee::before,
  .closee::after {
    position: absolute;
    content: ' ';
    background-color: #333333;
    left: 10px;
    width: 1px;
    height: 20px;
  }

  .closee::before {
    transform: rotate(45deg);
  }

  .closee::after {
    transform: rotate(-45deg);
  }

.selectzhiwei-center {
  width: 460px;
  height: 300px;
  margin: 0 auto;
}

.selectzhiwei-center-red {
  width: 460px;
  height: 54px;
  background: #FFF5ED;
  margin-top: 18px;
  position: relative;
}

.selectzhiwei-center-red-p {
  width: 436px;
  height: 38px;
  font-size: 14px;
  font-weight: 400;
  color: #FF3333;
  line-height: 19px;
  position: absolute;
  top: 2px;
  left: 14px;
}

.selectzhiwei-center-content {
  width: 460px;
  height: 40px;
  margin-top: 14px;
}

.selectzhiwei-center-content-p {
  float: left;
  width: 62px;
  height: 19px;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 19px;
  margin-top: 8px;
}

.selectzhiwei-center-content-select {
  float: right;
  width: 388px;
  height: 38px;
  background: #FFFFFF;
  border-radius: 2px !important;
  border: 1px solid #DDDDDD;
  font-size: 14px;
  font-weight: 400;
  color: #999999;
  line-height: 20px;
  cursor: pointer;
}

.selectzhiwei-center-p {
  margin-top: 9px;
  margin-left: 12px;
}

.selectzhiwei_p {
  color: #333;
}

.selectzhiwei-center-content2 {
  width: 460px;
  height: 50px;
  margin-top: 14px;
}

.selectzhiwei-center-sx {
  float: left;
  width: 62px;
  height: 50px;
}

.selectzhiwei-center-sxp {
  width: 62px;
  height: 19px;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 19px;
}

.selectzhiwei-center-sx2 {
  float: right;
  width: 388px;
  height: 50px;
  margin-top: 7px;
}

.selectzhiwei-bottom {
  border-top: 1px solid #EEEEEE;
  width: 500px;
  height: 64px;
}

.selectzhiwei-bottom-ok {
  cursor: pointer;
  float: right;
  width: 90px;
  height: 34px;
  background: #2A7FCC;
  border-radius: 2px !important;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  margin-top: 15px;
  margin-right: 20px;
  position: relative;
}

.selectzhiwei-bottom-okp {
  color: #FFFFFF;
  position: absolute;
  top: 1px;
  left: 31px;
}

.el-checkbox {
  margin-right: 15px;
}

.el-checkbox__label {
  padding-left: 7px;
}

.checker {
  display: none !important;
}
#uniform-checkbox_but {
  display: inline-block !important;
}
.subMessage {
  width: 500px;
  height: auto;
  position: absolute;
  background-color: #FFFFFF;
  z-index: 999;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
  border-radius: 6px;
}

.subMessage-center {
  width: 460px;
  height: auto;
  margin: 0 auto;
}

.add-tianjia {
  cursor: pointer;
  text-align: center;
  float: left;
  width: 66px;
  height: 30px;
  background: #2A7FCC;
  border-radius: 0px 2px 2px 0px;
  font-size: 14px;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 30px;
  margin: 0 !important;
}

.selectzhiwei-add {
  width: 460px;
  height: 17px;
  font-size: 12px;
  font-weight: 400;
  color: #2A7FCC;
  line-height: 17px;
  text-align: center;
  margin-bottom: 16px;
  cursor: pointer;
}

.messageinteraction-top {
  width: 470px;
  height: 70px;
  margin: 0 auto;
}

.h-message {
  width: 26px;
  height: 26px;
  float: left;
  margin-top: 24px;
}

.messageinteraction-top-p {
  float: left;
  margin-left: 9px;
  margin-top: 22px;
  width: 123px;
  height: 28px;
  font-size: 20px;
  font-weight: 400;
  color: #333333;
  line-height: 28px;
}

.subscribeMessage {
  float: left;
  margin-top: 19px;
  width: 108px;
  height: 30px;
  border-radius: 15px !important;
  border: 1px solid #2A7FCC;
  cursor: pointer;
}

.h-wifi {
  float: left;
  margin-top: 6px;
  margin-left: 15px;
  width: 16px;
  height: 16px;
}

.subscribeMessage-p {
  float: right;
  margin-top: 5px;
  margin-right: 14px;
  width: 56px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #2A7FCC;
  line-height: 20px;
}

.messageinteraction-center {
  width: 470px;
  height: 260px;
  margin: 0 auto;
  overflow-y: auto;
  overflow-x: hidden;
  display: grid;
}

.messageinteraction-bottom {
  width: 470px;
  height: 50px;
  margin: 10px auto;
}

.messageinteraction-center-content {
  position: relative;
  width: 460px;
  height: auto;
  margin-top: 10px;
}

.messageinteraction-touxing {
  float: left;
  width: 45px;
  height: 45px;
  border-radius: 50% !important;
}

.messageinteraction-touxing-right {
  float: right;
  width: 45px;
  height: 45px;
  border-radius: 50% !important;
  margin-right: 45px;
}

.messageinteraction-content {
  float: left;
  width: 360px;
  text-align: left;
  border-left: 2px solid #1BBC9B;
  background-color: #fafafa;
  padding: 5px;
  position: relative;
  margin-left: 10px;
}

.messageinteraction-content-right {
  float: right;
  width: 360px;
  text-align: left;
  border-right: 2px solid #1BBC9B;
  background-color: #fafafa;
  padding: 5px;
  position: relative;
  margin-right: 10px;
}

.arrow-left {
  display: block;
  position: absolute;
  top: 5px;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  left: -8px;
  width: 0;
  height: 0;
  border-right: 8px solid #1BBC9B;
}

.arrow-right {
  display: block;
  position: absolute;
  top: 5px;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  right: -8px;
  width: 0;
  height: 0;
  border-left: 8px solid #1BBC9B;
}

.message-content-top {
  width: 100%;
  height: 20px;
}

  .message-content-top p {
    float: left;
    font-size: 13px;
    font-weight: 400;
    margin-left: 5px;
  }

.message-content-top-right {
  width: 100%;
  height: 20px;
}

  .message-content-top-right p {
    float: right;
    font-size: 13px;
    font-weight: 400;
    margin-left: 5px;
  }

.message-content-top-p {
  color: #3590c1;
  cursor: pointer;
}

.message-content-top-p2 {
  color: #7B7B7B;
}

.message-content-top-p3 {
  color: #BFBFBF;
}

.message-content-content {
  width: 98%;
  height: auto;
  margin-left: 5px;
}

.h-renren {
  position: absolute;
  right: 0;
  top: 5px;
  bottom: 5px;
  width: 42px;
  text-align: center;
  display: table-cell;
  vertical-align: middle;
}

.selectzhiwei-center-bottom {
  width: 100%;
  display: flex;
  margin-bottom: 20px;
}

.add-rongqi {
  width: 380px;
  margin-left: 62px;
  float: left;
}

.h-delect {
  float: right;
  width: 16px;
  height: 16px;
  cursor: pointer;
  margin-top: 10px;
}

.add-forData {
  float: left;
  height: 24px;
  border-radius: 2px !important;
  border: 1px solid #2A7FCC;
  margin-left: 10px;
  margin-top: 10px;
}

.add-forData-p {
  float: left;
  height: 12px;
  font-size: 12px;
  font-weight: 400;
  color: #2A7FCC;
  line-height: 12px;
  margin-top: 6px;
  margin-left: 9px;
}

.h-chacha {
  cursor: pointer;
  float: right;
  width: 14px;
  height: 14px;
  margin: 5px 7px 5px 4px;
}

.dingyue-tankuang {
  width: 100%;
  height: 100%;
  z-index: 300;
  position: fixed;
  display: none;
  top: 0;
  right: 0;
  margin: 0;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.5);
}

.zhilei-tankuang {
  width: 100%;
  height: 100%;
  z-index: 100;
  position: fixed;
  display: none;
  top: 0;
  right: 0;
  margin: 0;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.5);
}

.noData {
  width: 100%;
  height: 52vh;
  position: relative;
}

.noData-content {
  width: 72px;
  height: 104px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
}

.h-nodata {
  width: 72px;
  height: 72px;
}

.noData-content-p {
  width: 99px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #999999;
  line-height: 20px;
  margin-top: 12px;
}

.kpbig-bottom-content-p3 p {
  margin: 0 !important;
}

[v-cloak] {
  display: none !important;
}
/*
@keyframes rowup {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  100% {
    -webkit-transform: translate3d(0, -307px, 0);
    transform: translate3d(0, -307px, 0);
    display: none;
  }
}*/


.list {
  margin: 20px auto;
  margin-top: 0;
  position: relative;
  height: 546px;
  overflow: hidden;
}

  .list .rowup {
    -webkit-animation: 0s rowup1 linear infinite normal;
    animation: 0s rowup1 linear infinite normal;
    position: relative;
  }
.rowup-ch {
  position: relative;
  top: 0;
}
.item1 {
  overflow: hidden;
}

@keyframes rowup1 {
  0% {
    transform: translateY(0%);
  }

  100% {
    transform: translateY(-50%);
  }
}

@-webkit-keyframes rowup1 {
  0% {
    transform: translateY(0%);
  }

  100% {
    transform: translateY(-50%);
  }
}

.lianheTab {
  background: #fff;
  margin-top: 14px;
}

* {
  font-family: "Microsoft YaHei";
}

.lianhe-listData {
  height: 544px;
}

.lianhe-li {
  border: 1px solid #EEEEEE;
  padding: 20px 10px;
  width: 240.5px;
  height: 262px;
  float: left;
  list-style: none;
  margin-right: 10px;
  margin-bottom: 10px;
}

  .lianhe-li:nth-child(4n) {
    margin-right: 0;
  }

.lianhe-list {
  /*width: 348px;*/
  width: 33.33%;
  height: 80px;
  background: #f9f9f9;
  text-align: center;
  padding-top: 10px;
  cursor: pointer;
}
  .lianhe-list:first-child {
    border-top-left-radius: 8px!important;
  }
  .lianhe-list:last-child {
    border-top-right-radius: 8px !important;
  }

  .lianhe-list-on {
    background: #fff;
    font-weight: 600;
    background: linear-gradient(180deg, #DAECFF 0%, rgba(228,241,255,0.8) 0%, #FFFFFF 100%, #FFFFFF 100%);
  }

.lianhe-list-title {
  font-size: 16px;
  color: #333;
}

.lianhe-list-cont {
  font-size: 14px;
  color: #999;
  padding-top: 12px;
}

.lianhe-list-on .lianhe-list-cont {
  font-weight: normal;
}

.lianhe-moddle {
  padding: 20px;
}

.lianhe-check {
  background: #E9F2FF;
  padding: 5px 6px;
}

.lianhe-moddle-any {
  padding-bottom: 18px;
  border-bottom: 1px solid #E3E3E3;
}
.lianhe-check {
  margin-right: 20px !important; 
}
.lianhe-check.is-checked {
  background: #E9F2FF;
  padding: 5px 6px;
  border-radius: 2px !important;
  margin: 0 20px 14px 0;
}

.el-checkbox__input + .el-checkbox__label {
  color: #666666;
}

.el-checkbox__input.is-checked + .el-checkbox__label {
  color: #1D68AC !important;
  font-weight:600;

}

.lianhe-check .el-checkbox__input .el-checkbox__inner {
  background: none !important;
  border-color: #cbcbcb !important;
}

.lianhe-check.is-checked .el-checkbox__input .el-checkbox__inner {
  border-color: #1C68AE !important;
}

  .lianhe-check.is-checked .el-checkbox__input .el-checkbox__inner::after {
    border-color: #1C68AE;
  }

.lianhe-check.is-checked el-checkbox__label {
  color: #1C68AE !important;
}

.lianhe-renovate {
  font-size: 14px;
  color: #2A7FCC;
  text-align: end;
  margin: 20px 0;
  cursor: pointer;
}

  .lianhe-renovate .renovate-icon {
    width: 14px;
    height: 14px;
    display: inline-block;
  }

.lianhe-img {
  width: 52px;
  height: 52px;
  border-radius: 50% !important;
  position: relative;
  cursor:pointer;
}

.img-grade {
  /*position: absolute;
  width: 18px;*/
  height: 16px;
  border-radius: 3px !important;
  background: #FF8B00;
  color: #fff;
  line-height: 16px;
  text-align: center;
  font-weight: 600;
  /*right: 0;
  bottom: 0;*/
  font-size: 12px;
  font-style: normal;
  padding: 0 4px;
}

.lianhe-right-name {
  font-size: 16px;
  color: #333333;
  margin: 0;
}

.lianhe-right-cont {
  font-size: 14px;
  color: #666;
  margin: 12px 0 0 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 136px;
  height: 20px;
}

.lianhe-list-top {
  display: flex;
}


.lianhe-list-num {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 30px 0 20px;
}

.list-num-left {
  font-size: 12px;
  color: #2A7FCC;
}

.list-num-right {
  font-size: 14px;
  color: #FAAD14;
}

  .list-num-left svg, .list-num-right svg {
    vertical-align: sub;
  }

.listData-cont {
  padding: 10px 8px;
  background: #F9F9F9;
  color: #666;
  font-size: 12px;
  height: 46px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  cursor: pointer;
}

.hoverCont {
  position: absolute;
  width: 218px;
  background: #F9F9F9;
  z-index: 1;
  color: #666;
  font-size: 12px;
  top: 0;
  padding: 10px 8px;
  display: none;
}

.hover-cont:hover .hoverCont {
  cursor: pointer;
  display: block;
}

.list-btn {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.list-btn-left, .list-btn-moddle, .list-btn-right {
  text-align: center;
  line-height: 28px;
  border-radius: 2px !important;
  cursor: pointer;
  padding: 0px 5px;
}

.list-btn-left {
  background: #1C68AE;
  border: 1px solid #1C68AE;
  font-size: 12px;
  color: #fff;
  position: relative;
  line-height: 27px;
  height: 30px;
}

.list-btn-right {
  line-height: 28px;
  height: 30px;
}

.list-btn-moddle, .list-btn-right {
  border: 1px solid #1C68AE;
  color: #1C68AE;
}

  .list-btn-moddle a:hover, .list-btn-moddle a:active, .list-btn-moddle a:focus {
    text-decoration: none
  }

.list-span {
  font-size: 12px;
}

.call-phone {
  position: absolute;
  width: 104px;
  height: 128px;
  display: inline-block;
  background: #fff;
  left: -20px;
  top: -134px;
  border-radius: 4px !important;
  display: none;
  padding: 8px 10px;
  box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.16);
}

.list-btn-left:hover .call-phone {
  display: inline-block;
}

.hall-left-ul {
  display: flex;
}

.hall-left-search {
  padding: 9px 16px;
  border: 1px solid #DDDDDD;
  font-size: 14px;
  color: #666;
  cursor: pointer;
}
.hall-left-search:not(:first-child) {
  margin-left: -1px;
}
.hall-left-search-on {
  border: 1px solid #2A7FCC !important;
  color: #1C68AE;
  background: #F5FAFF;
  z-index: 1;
}

.hall-left {
  flex: 1;
}

.lianhe-hall {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-with-hall .el-input__inner {
  height: 40px;
}

.input-with-hall .el-input-group__prepend {
  background: none;
}

.input-with-hall .el-input-group {
  background: none;
  color: #666;
  border: 1px solid #D9D9D9;
  border-right: none;
}

.input-with-hall .input-with-search {
  width: 88px;
  background: #1C68AE !important;
  color: #fff !important;
  text-align: center;
  height: 40px;
}

.input-with-fabu, .input-with-fabu:hover, .input-with-fabu:active, .input-with-fabu:focus {
  background: #1C68AE !important;
  color: #fff !important;
  text-align: center !important;
  margin-left: 20px !important;
  border: none !important;
  height: 40px !important;
}

.list-left-img {
  width: 58px;
  /*height: 58px;*/
  /*border-radius: 50% !important;*/
  /*position: relative;*/
}
.list-left-img img {
  width: 100%;
  height: 58px;
  border-radius: 50% !important;
}

.lianhe-moddle-list {
  margin: 0 20px;
  padding: 0 0 20px;
}

.lianhe-moddle-list ul {
  /*max-height: 544px;
  overflow: auto;*/
  }

.lianhe-moddle-list ul ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: #f5f5f5;
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(128, 128, 128, 0.7);
  border-radius: 10px;
  background-color: #f5f5f5;
}

/*���� ��Ӱ~Բ��*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(128, 128, 128, 0.7);
  background-color: rgb(149, 147, 147);
}

.moddle-list-li {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #E3E3E3;
  padding: 20px 0;
  width:98%;
}
.audiotShow {
  text-align: end;
  cursor: pointer;
  margin: 0;
  font-size: 14px;
  color: #175199;
  position: absolute;
  top: 40px;
  right: 2px;
  background: #fff;
  display: inline;
  width: 67px;
}
.audiotHide {
  width: 100%;
  text-align: end;
  cursor: pointer;
  margin: 0;
  font-size: 14px;
  color: #76839b;
}

.p {
  margin: 0;
}

.mr12 {
  margin-right: 12px;
}

.moddle-list-right {
  flex: 1;
  margin-left: 14px;
  /*overflow: hidden;*/
}

.list-right-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.top-left-name {
  color: #2A7FCC;
  font-size: 14px;
}

.top-left-company {
  color: #333333;
  font-size: 14px;
}

.top-left-status1 {
  background: #ECFCE1;
  border-radius: 12px !important;
  color: #3B8A05;
  font-size: 12px;
  padding: 2px 8px;
}

.top-left-status {
  background: #FFF1EB;
  border-radius: 12px !important;
  color: #C55322;
  font-size: 12px;
  padding: 2px 8px;
}

.right-top-right {
  font-size: 14px;
  color: #666;
}

.right-tab-list {
  background: #E9F2FF;
  color: #003683;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 12px !important;
  margin-right: 10px;
  margin-bottom: 5px;
  display: inline-block;
}

  .right-tab-list svg {
    vertical-align: text-bottom;
  }

.jubao-font {
  font-size: 14px;
  color: #1C68AE;
}

.list-right-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-right-btn {
  display: flex;
}

.list-right-cont {
  margin-top: 16px;
}

.list-right-tag {
  margin: 15px 0 9px;
}
.clueDialog .el-dialog {
  border-radius: 8px !important;
}
.projectSetting .el-dialog .el-dialog__header, .clueDialog .el-dialog .el-dialog__header {
  display: none;
}

.projectSetting .el-dialog .el-dialog__body, .clueDialog .el-dialog .el-dialog__body {
  padding: 0;
}

.project-top {
  padding: 20px;
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.project-cont {
  background: #FFF7F3;
  padding: 20px;
  margin-bottom: 20px;
}

.xingxing {
  font-style: normal;
  font-size: 14px;
  color: #E11717;
}

.project-select {
  padding: 0 20px;
}

.project-flex {
  display: flex;
  margin-bottom: 20px;
}

.project-input {
}

  .project-input .el-input__inner {
    width: 428px;
    height: 36px;
  }

.project-yearM {
  width: 428px;
  margin: 0 0 0 88px;
}

.small-salary, .big-salary {
  width: 192px !important;
}

  .small-salary .el-input__inner, .big-salary .el-input__inner {
    height: 36px;
  }

  .small-salary .el-input__suffix, .big-salary .el-input__suffix {
    top: 8px;
    color: #333;
    font-size: 14px;
  }

.project-salary {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.jubao-font svg {
  vertical-align: text-top;
}

.salary-num {
  border: 1px solid #C8C8C8;
  background: none;
  color: #666666;
  font-size: 12px;
  padding: 3px 8px;
  margin-right: 12px;
  cursor: pointer;
  border-radius: 2px !important;
}

.salary-num-on {
  background: #2A7FCC;
  border: none;
  color: #fff;
  border: none;
}

.tag-status {
  border: 1px solid #C8C8C8;
  padding: 6px 8px;
  font-size: 12px;
  color: #666;
  margin-right: 12px;
  display: inline-block;
  cursor: pointer;
  margin-bottom: 8px;
}

.project-bot {
  padding: 0 20px 20px;
  text-align: end;
}

.setting-btn, .setting-btn:hover, .setting-btn:active, .setting-btn:focus {
  border: 1px solid #1C68AE !important;
  color: #1C68AE !important;
  font-size: 14px !important;
  padding: 9px 18px !important;
  background: none !important;
  border-radius: 2px !important;
}

.submit-btn, .submit-btn:hover, .submit-btn:active, .submit-btn:focus {
  border: 1px solid #1C68AE !important;
  color: #fff !important;
  font-size: 14px !important;
  padding: 9px 25px !important;
  background: #1C68AE !important;
  border-radius: 2px !important;
}

.project-input {
  width: 430px;
  height: 36px;
  border: 1px solid #E2E2E2;
  padding: 8px 10px;
  cursor: pointer;
}

.distag-on {
  background: #2A7FCC;
  border-color: #2A7FCC;
  color: #fff;
}

.goodFont {
  width: 88px;
  padding-top: 9px;
}

.goodTags {
  width: 84%;
}

/*��������*/
.publishClue {
}

.publisHead {
  padding:14px 22px;
  border-bottom: 1px solid #F4F4F4;
  display: flex;
  justify-content: space-between;
}

.publisHead-fon {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.publisHead-close {
  color: #4E5969;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
}

.publisTabs-list {
  width: 180px;
  height: 78px;
  /*border: 1px solid #DDDDDD;*/
  border-radius: 4px !important;
  display: inline-block;
  margin-right: 20px;
  cursor: pointer;
  background: url(../images/publisTabs.png) no-repeat;
  background-size: 100% 100%;
}

.publisTabs-on {
  /*border: 1px solid #1C68AE;*/
  background: url(../images/publisTabsOn.png) no-repeat;
  background-size: 100% 100%;
}

.border-p {
  padding: 10px 15px;
}

.publisTabs {
  padding: 20px 20px 0;
}

.font-p1 {
  font-size: 14px;
  color: #666;
}

.font-p2 {
  font-size: 12px;
  color: #999;
}

.publisTabs-on .font-p1 {
  color: #1C68AE;
  font-weight: 600;
}

.publisTabs-on .font-p2 {
  color: #1C68AE;
}

.fileIcon {
  width: 14px;
  height: 14px;
  display: inline-block;
  position: relative;
  top: 2px;
}

.file-font {
  font-size: 14px;
  color: #2A7FCC;
  cursor: pointer;
}

.publisCubmit, .publisCubmit:hover, .publisCubmit:active, .publisCubmit:focus {
  padding: 10px 26px !important;
  background: #1C68AE !important;
  border: none !important;
  color: #fff !important;
  border-radius: 2px !important;
}

.imgList-li {
  display: inline-block;
  margin-right: 10px;
  margin-top: 10px;
  vertical-align: middle;
}

.imgList-li-add {
  width: 88px;
  height: 88px;
  border: 1px solid #EBEBEB;
  border-radius: 4px !important;
  display: inline-block;
  margin-right: 10px;
  margin-top: 10px;
  vertical-align: middle;
}

.img-C {
  width: 88px;
  height: 88px;
  border: 1px solid #EBEBEB;
  border-radius: 4px !important;
  display: inline-block;
  position: relative;
}

.deleteImg {
  width: 14px;
  height: 14px;
  display: inline-block;
  position: absolute;
  right: 0;
  cursor: pointer;
  top: 0;
}

.addImg {
  cursor: pointer;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.newCardTags + .newCardTags {
  margin-left: 10px;
}

.newCardTags {
  height: 24px !important;
  line-height: 24px !important;
  border-radius: 12px !important;
  background: #E9F2FF !important;
  font-size: 12px !important;
  color: #003683 !important;
  border: none !important;
}

  .newCardTags .el-icon-close::before {
    color: #003683;
    font-weight: 600;
  }

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-newCard {
  width:110px !important;
  height: 24px !important;
  border: none !important;
  background: #E9F2FF !important;
  border-radius: 12px !important;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
  display: block !important;
}

.list-btn-left:hover {
  background: #16528A;
}

.list-btn-moddle:hover, .list-btn-right:hover {
  background: #1C68AE;
  border: 1px solid #1C68AE;
}

  .list-btn-moddle:hover a, .list-btn-right:hover span {
    color: #fff;
  }

.list-btn-moddle {
  padding: 0;
  height: 30px;
}

.list-btn-moddle a {
  display: inline-block;
  width: 60px;
  line-height: 26px;
  height: 30px;
  font-size: 12px;
  margin-left: 0;
}

.test-p {
  display: block;
  width: 100%;
  line-height: 20px;
  font-size: 16px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  font-size:14px;
}

.el-tooltip__popper.is-dark {
  z-index: 10060 !important;
}

.fileCli {
  width: 100%;
  padding: 8px 20px;
  background: #F9F9F9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fileCli-cont {
  flex: 1;
  margin-left: 16px;
}

.fileCli-img {
  width: 30px;
  height: 30px;
}
.el-date-editor .el-range-separator{
  line-height:22px !important;
  width:8%;
}
.jobTagsClass {
  min-height: 39px;
  max-height: 78px;
  overflow: hidden;
  display: inline-block;
  width: 97.4%;
}
.sm_open, .sm_close {
  display: inline-block;
  float: right;
  top: -24px;
  position: relative;
  cursor: pointer;
  color: #2A7FCC;
  font-size: 14px;
  margin: 0;
}
p.sm_close {
  display: none;
}
p.sm_close i {
  position: absolute;
  width: 6px;
  height: 11px;
  /*background: url(../images/zkmore.gif) 0px -19px no-repeat;*/
  top: 2px;
  margin-left: 5px;
}

/**Ա���ػ�������ʽ*/
.home_care_box {
  width: 710px;
  margin: 0 auto;
  /*display: block;*/
  background-color: transparent;
  position: absolute;
  z-index: 99997;
  left: 50%;
  margin-left: -348px;
  top: 75px;
  font-size: 14px;
  font-family: 'Microsoft YaHei', '����';
  padding-bottom: 36px;
}
.home_notice_mask_box {
  width: 100%;
  height: 100%;
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 99996;
  overflow: hidden;
  background: #000;
  filter: alpha(opacity=50);
  -moz-opacity: 0.5;
  -khtml-opacity: 0.5;
  opacity: 0.5;
}
.home_care_panel {
  position: relative;
}

.care_hn_page_btn {
  position: absolute;
  width: 18px;
  height: 38px;
  bottom: 200px;
  margin-top: -40px;
  /*-moz-opacity: 0.5;
  -khtml-opacity: 0.5;
  opacity: 0.5;*/
  transition: opacity .3s ease-in;
}
.hn_prev {
  left: 37px;
}
.hn_next {
  right: 13px;
}
.care_hn_page_btn img {
  width: 18px;
  height: 38px;
}
.care_hnp_guang {
  width: 710px;
  height: 430px;
  background: url('/images/employeeCare/guang.png') no-repeat left top;
  background-size: 100%;
  z-index: 1;
}
.care_hnp_dangao,
.care_hnp_dangao img {
  width: 380px;
  height: 400px;
}
.care_hnp_dangao {
  margin-left: 176px;
  position: absolute;
  z-index: 5;
  top: 95px;
}
.girl-card-body-wrap {
  width: 558px;
  height: 378px;
  background: url('/images/employeeCare/xinfeng.png') no-repeat left top;
  background-size: 100%;
  margin-left: 86px;
  z-index: 8;
  position: relative;
  margin-top: -94px;
}

.girl-card-head-wrap {
  position: relative;
  left: 45px;
  width: 90px;
  top: -25px;
}

.girl-card-head-tou {
  width: 90px;
  height: 90px;
  border-radius: 50%!important;
  border: 2px solid #FFADC2;
  background: #FFADC2;
}

.girl-card-tou-img {
  width: 86px;
  height: 86px;
  border-radius: 50%!important;
}

.girl-card-head-mao {
  position: absolute;
  left: -19px;
  top: -35px;
}
.girl-card-mao-img {
  width: 66px;
  height: 70px;
}
.girl-card-title-wrap {
  width: 100%;
  text-align: center;
  margin-top: -38px;
}
.girl-card-title-name {
  font-size: 18px;
  line-height: 18px;
  font-family: Source Han Sans CN;
  font-weight: 800 !important;
  color: #E22C57;
  margin: 13px auto 0 auto;
}
 .girl-card-title-name:first-child {
    margin-top: 0;
  }
.girl-card-content-wrap {
  text-align: center;
  margin-top: .195rem;
  padding: 0 59px 0 60px;
}
.girl-which-year-txt {
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #E22C57;
  line-height: 34px;
  text-align: left;
  
}
.girl-which-year-txt:first-child {
  margin-top: 28px;
}
.card-time-box {
  width: 100%;
  text-align: center;
  line-height: 16px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #E22C57;
  margin-top: 14px;
}
.care_hnp_dangao_box {
  position: relative;
}

.care_hnp_close {
  position: absolute;
  right: 80px;
  bottom: 132px;
}
.care_hnp_close,
.care_hnp_close img {
  width: 36px;
  height: 36px;
}

.home_care_box .hn_pager {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  line-height: 36px;
  color: white;
  font-size: 24px;
  text-align: center;
}



/** ��Ч*/
.yah {
  display: block;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 9998;
}

.care_hnp_box {
  z-index: 9;
  opacity: 0;
}
.care_hnp_one {
  width: 46px;
  height: 40px;
  position: absolute;
  left: 173px;
  top: 14px;
  background: url('/images/employeeCare/huang.png') no-repeat center center;
  background-size: 100%;
  animation: oneMove 2s linear 1;
}

.care_hnp_two {
  width: 40px;
  height: 46px;
  position: absolute;
  right: 172px;
  top: 0;
  background: url('/images/employeeCare/lan.png') no-repeat center center;
  background-size: 100%;
  animation: twoMove 1.8s linear 1;
}

.care_hnp_three {
  width: 40px;
  height: 46px;
  position: absolute;
  left: 38px;
  top: 103px;
  background: url('/images/employeeCare/hong.png') no-repeat center center;
  background-size: 100%;
  animation: threeMove 2s linear 1;
}

.care_hnp_four {
  width: 34px;
  height: 26px;
  position: absolute;
  right: 77px;
  top: 106px;
  background: url('/images/employeeCare/hong_san.png') no-repeat center center;
  background-size: 100%;
  animation: fourMove 1.8s linear 1;
}

.care_hnp_five {
  width: 32px;
  height: 42px;
  position: absolute;
  left: 26px;
  top: 244px;
  background: url('/images/employeeCare/huang_san.png') no-repeat center center;
  background-size: 100%;
  animation: fiveMove 1.5s linear 1;
}

.care_hnp_six {
  width: 46px;
  height: 40px;
  position: absolute;
  right: 0px;
  top: 205px;
  background: url('/images/employeeCare/huang.png') no-repeat center center;
  background-size: 100%;
  animation: sixMove 1.5s linear 1;
}

@keyframes oneMove {
  from {
    top: 14px;
    opacity: 1;
  }

  to {
    top: calc(100% - 50px);
    opacity: 0;
  }
}

@keyframes twoMove {
  from {
    top: 0;
    opacity: 1;
  }

  to {
    top: calc(100% - 50px);
    opacity: 0;
  }
}

@keyframes threeMove {
  from {
    top: 103px;
    opacity: 1;
  }

  to {
    top: calc(100% - 50px);
    opacity: 0;
  }
}

@keyframes fourMove {
  from {
    top: 106px;
    opacity: 1;
  }

  to {
    top: calc(100% - 50px);
    opacity: 0;
  }
}

@keyframes fiveMove {
  from {
    top: 244px;
    opacity: 1;
  }

  to {
    top: calc(100% - 50px);
    opacity: 0;
  }
}

@keyframes sixMove {
  from {
    top: 205px;
    opacity: 1;
  }

  to {
    top: calc(100% - 50px);
    opacity: 0;
  }
}

/**
  23.10.30�İ���ʽ
*/
.tip-box {
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #E87E04;
  line-height: 20px;
}
.tip-icon-img {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}
.jt-box {
  width: 12px;
  height: 12px;
  margin-left: 4px;
  cursor: pointer;
}
.jt-box-rote {
  transform: rotate(180deg);
}
.popover-data-wrap {
  border: 1px solid rgba(41,127,204,0.5);
  /*max-height: 202px;*/
  max-height: 255px;
  border-radius: 4px!important;
  overflow-y: auto;
  padding: 12px 12px 10px 12px;
  box-shadow: none;
}
.com-flex {
  display: flex;
}
.justify-be {
  justify-content: space-between;
}
.justify-ce {
  justify-content: center;
}
.justify-end {
  justify-content: flex-end;
}
.flex-flow {
  flex-flow: column;
}
.align-ce {
  align-items: center;
}
.po-data-li.po-data-line {
  line-height: 21px;
  margin-bottom: 2px;
}
.po-data-li.po-data-line1 {
  line-height: 17px;
  margin-bottom: 6px;
}
.po-data-li:last-child {
  margin-bottom: 0;
}
.po-data-li.po-data-line .po-data-num,
.po-data-li.po-data-line .po-data-txt {
  line-height: 21px;
}
.po-data-li.po-data-line1 .po-data-num,
.po-data-li.po-data-line1 .po-data-txt {
  line-height: 17px;
}
.po-data-num {
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
  font-size: 12px;
  display: inline-block;
  margin-right: 5px;
  width: 18px;
  text-align: center;
}

.yysj-svg {
  margin-right: 4px;
}
.caption-new .uppercase {
  font-size: 18px !important;
}
.num-img {
  width: 16px;
  height: 15px;
}
.num-img1{
  width: 13px;
  height: 15px;
  margin-left: 3px;
}
.po-data-txt {
  font-size: 12px;
  width: 72px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}
.po-data-p {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #297FCC;
  line-height: 17px;
}
.po-data-unit {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
  line-height: 17px;
}
.jt-box:focus {
  outline: none;
}

/**���������岿�� */
.popover-data-wrap::-webkit-scrollbar,
.lianhe-left::-webkit-scrollbar {
  width: 4px;
  position: fixed;
}
/**�����������С���飬�����������ƶ��������������ƶ���ȡ�����Ǵ�ֱ����������ˮƽ�������� */
.popover-data-wrap::-webkit-scrollbar-thumb,
.lianhe-left::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0,0.2);
  box-shadow: inset 0 0 3px rgba(0, 0, 0,0.2);
  background: #E3E3E3;
}
/** �������Ĺ��������װ��Thumb�� */
.popover-data-wrap::-webkit-scrollbar-track,
.lianhe-left::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0,0.2);
  box-shadow: inset 0 0 3px rgba(0, 0, 0,0.2);
  border-radius: 0;
  background: #fff;
}
.lianhe-left {
  border-right: 1px solid #E3E3E3;
}
.portlet-mb0 {
  margin-bottom: 0;
}
.portlet-mb15 {
  margin-bottom: 15px;
}

.lianhe-moddle-line {
  border-top: 1px solid #E3E3E3;
}
.lianhe-moddle-line ul .moddle-list-li:first-child {
  border-top: 0;
}
.item-one-left,
.item-one-right {
  width: 100px;
}
.lianhe-right {
  width: 296px;
  margin-left: 8px;
}
.lianhe-new-box .list {
  max-height: 370px;
}
.lianhe-left {
  flex: 1;
  max-height: 450px;
  overflow: auto;
  box-sizing: border-box;
  padding-bottom: 16px;
}
.lianheTab-boss .lianhe-left {
  max-height: 630px;
}
  .lianhe-header-img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }
.lianhe-right-header {
  font-size: 18px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #2A7FCC;
  line-height: 26px;
  padding: 16px 0;
  text-align: center;
  border-bottom: 1px solid #EEEEEE;
}
.lianhe-item {
  padding: 14px 16px 16px;
  border-bottom: 1px dashed #eee;
}
.lianhe-item-one {
  margin-bottom: 10px;
}
.ong-left-img {
  width: 32px;
  height: 32px;
  border-radius: 50%!important;
  margin-right: 10px;
}
.lianhe-name {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ong-center-img {
  width: 24px;
  height: 24px;
}
.lianhe-item-txt {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 17px;
}
.lianhe-item-bu {
  margin-left: 4px;
  color: #1C68AE;
  font-weight: 600;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.lianhe-item-amount {
  margin-left: 4px;
  color: #FF4D4F;
  font-weight: 600;
}

.btn-left-yy {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #1C68AE;
  line-height: 20px;
  position: relative;
  padding-left: 28px;
}
.btn-left-yy:first-child {
  margin-right: 12px;
}
.btn-left-yy svg {
  position: absolute;
  left: 0;
  bottom: -3px;
  width: 24px;
  height: 24px;
  margin-right: 4px;
  cursor: pointer;
}
.btn-left-yy .btn-gif {
  width: 24px;
  height: 50px;
  position: absolute;
  left: 0;
  bottom: -3px;
}
.r-tb-tuijian1 {
  border-width: 0;
  border-top-width: 1px;
}
.r-tb-tuijian1 tbody {
 /* display: block;
  width: calc(100%);
  max-height: 216px;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;*/
}
.r-tb-tuijian1 tr {
  box-sizing: border-box;
  table-layout: fixed;
  display: table;
  width: 100%;
}
.r-tb-tuijian0 tbody td:nth-child(1),
.r-tb-tuijian0 tbody td:nth-child(3),
.r-tb-tuijian0 tbody td:nth-child(4) {
  width: 20%;
}
.r-tb-tuijian0 tbody td:nth-child(2) {
  width: 40%;
}
.r-tb-tuijian1 td.r-status {
  font-weight: bold;
}
.r-tb-tuijian1 tbody td {
  border: 0 none !important;
}
.r-tb-tuijian1 th, .r-tb-tuijian1 td {
  overflow: hidden;
  white-space: nowrap;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}
.r-tb-tuijian1 thead th {
  border-bottom: 1px solid #ddd !important;
}

.r-tb-tuijian1 .r-status,
.r-tb-tuijian1 .r-salay {
  width: 55px;
}
.r-tb-tuijian1 .r-staff {
  width: 65px;
}
.r-tb-tuijian1 .r-tuijianstaff,
.r-tb-tuijian1 .r-salay1 {
  width: 72px;
}
.r-tb-tuijian1 .r-city {
  width: 86px;
}
@keyframes sliderdata {
  0% {
    transform: translateY(0%);
  }

  100% {
    transform: translateY(-50%);
  }
}

@-webkit-keyframes sliderdata {
  0% {
    transform: translateY(0%);
  }

  100% {
    transform: translateY(-50%);
  }
}
.r-tuijian-new {
  padding: 6px!important;
  font-size: 0!important;
}
.r-tuijian-new .r-tuijian-new-a {
  display: inline-block;
  max-width: 100%;
  line-height: 24px;
  padding: 0 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.r-tuijian-new .r-tuijian-new-a:focus {
  text-decoration: none;
  color: #fff;
}
.tuijian-body {
  position: relative;
}
.r-tb-tuijian-am,
.r-tb-shishi-am,
.r-tb-tuijian-am1,
.r-tb-shishi-am1 {
  position: absolute;
  top: 0;
  left: 0;
  /*animation: sliderdata11 5s linear infinite normal;*/
}
/*@keyframes sliderdata11 {
  0% {
    top: 0;
  }

  100% {
    top: -300px;
  }
}*/
/**23.10.30�İ���ҳbaseҳ��ʽ*/
.index-top-new {
  background: #fff;
}
.gw-head-left {
  width: 187px;
  background-color: #F9F9F9;
  border-radius: 8px 0 0 8px!important;
}
.gw-head-img {
  width: 70px;
  height: 70px;
  border-radius: 50% !important;
  margin: 20px 60px 13px 60px;
}
.gw-head-name,
.gw-head-comp,
.gw-head-detail {
  font-size: 14px;
  line-height: 20px;
  width: 100%;
  box-sizing: border-box;
}
.gw-head-name {
  font-weight: 500;
  color: #2A7FCC;
  text-align: center;
}
.gw-head-comp {
  font-weight: 400;
  color: #333333;
  text-align: center;
}
.gw-head-xian {
  width: 100%;
  height: 1px;
  background-color: #DDDDDD;
  margin: 16px 0px 10px;
}
.gw-head-detail {
  font-weight: 400;
  color: #333333;
  padding: 6px 12px;
}
.gw-head-right {
  width: 293px;
  padding: 27px 0 24px 19px;
  box-sizing: border-box;
}
.mb-26 {
  margin-bottom: 26px;
}
.gw-head-title {
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 20px;
  margin-bottom: 14px;
  margin-top: 0;
}
.gw-my-data-box {
  border-right: 1px solid #EEEEEE;
}
.gw-left-xian {
  width: 2px;
  height: 10px;
  background: #2A7FCC;
  display: inline-block;
  margin-right: 4px;
  line-height: 10px;
}
.gw-data-item {
  width: 33.33%;
  display: flex;
  flex-flow: column;
}
.gw-item-num {
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  line-height: 22px;
  margin-bottom: 10px;
}
.gw-item-txt {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
}
.gw-item-b {
  color: #1C68AE;
}
.gw-item-r {
  color: #DF0909;
}


.gw-index-head-right {
  flex: 1;
  padding: 13px 16px 19px 30px;
}
.gw-floor-item {
  width: 20%;
  display: flex;
  flex-flow: column;
}
.gw-time-sel {
  margin-bottom: 16px;
}
.gw-time-item {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
  line-height: 20px;
  padding: 6px 16px;
  border: 1px solid #DDDDDD;
  cursor: pointer;
}
.gw-time-item:not(:first-child) {
  margin-left: -1px;
}
.gw-time-item:first-child {
  border-top-left-radius: 4px !important;
  border-bottom-left-radius: 4px !important;
}
.gw-time-item:last-child {
  border-top-right-radius: 4px !important;
  border-bottom-right-radius: 4px !important;
}
.curren-time {
  border: 1px solid #2A7FCC;
  color: #2A7FCC;
  background: #F5FAFF;
  z-index: 1;
}
.j-img {
  width: 14px;
  height: 14px;
  margin-left: 2px;
}
.gw-floor-bz {
  margin-top: -2px;
  margin-right: 14px;
  text-align: center;
}
.gw-floor-bz img {
  width: 429px;
  height: 69px;
  background: #FFFFFF;
}
.floor-mt {
  margin-top: -7px;
}
.index-paihangbang {
  width: 100%;
}
.index-paihangbang .index-paihangbang-left {
  width: calc(50% - 7px);
}

.actions > ul > li {
  margin-left: -1px;
}
.current_li {
  border: 1px solid #2A7FCC !important;
  border-radius: 0px!important;
}
.actions > ul > li:first-child {
  border-top-right-radius: 2px !important;
  border-bottom-right-radius: 2px !important;
}
.actions > ul > li:last-child {
  border-top-left-radius: 2px !important;
  border-bottom-left-radius: 2px !important;
}

.portlet-body {
  position: relative;
}
.table-head-box {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 999;
}
.huanfu-po-box {
  position: relative;
}
.el-radio__input.is-checked .el-radio__inner {
  border-radius: 50%!important;
}
.kuaijie-wrap-new {
  margin-left: -15px;
  margin-right: -15px;
  overflow: hidden;
  position: relative;
}
.kuaijie-wrap-new1 {
  overflow: hidden;
  position: relative;
}
.page-head-new {
  margin: 0;
}
.mt15 {
  margin-top: 15px;
  position: relative;
}
.page-head-height0 {
  height: 25px;
}
@-webkit-keyframes slidehide {
  0% {
    height: 180px;
  }
  100% {
    height: 25px;
    /*max-height: 25px;*/
    overflow: hidden;
  }
}
@-webkit-keyframes slideshow {
  0% {
     height: 25px;
  }
   100% {
     height: 180px;
  }
}
.page-head-icon {
  position: absolute;
  top: 0;
  left: calc(50% - 80px);
  width: 160px;
  height: 25px;
  background: url("/images/index/toge.png") no-repeat;
  background-size: 100%;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #297FCC;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.page-dead-icon {
  width: 12px;
  height: 6px;
  margin-left: 6px;
  transform: rotate(-180deg);
}
.page-head-new-m {
  margin-top: 15px!important;
}
.page-head-new-m .portlet.light {
  padding: 12px 20px 0;
}
.kehuhuikuan {
  margin-top: 0;
}
.page-no-kuaijie {
  position: relative;
  height: 25px;
}
.page-kuaijie-icon {
  position: absolute;
  top: 0px;
  left: calc(50% - 110px);
  width: 220px;
  height: 25px;
  background: url("/images/index/toge1.png") no-repeat;
  background-size: 100%;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #297FCC;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.page-icon1 {
  width: 12px;
  height: 6px;
  margin-left: 6px;
}
.page-icon2 {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

/**��������������ʽ*/
.lianhe-item-box {
  padding-top: 10px;
  margin-right: 10px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(227, 227, 227, 0.99);
}
.lianhe-title {
  align-items: center;
  width: 878px;
  height: 70px;
  background: url("/images/index/jiaohu_bg.png") no-repeat left top;
  background-size: 100%;
  font-size: 18px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #1C68AE;
  border-top-left-radius: 8px !important;
}
.lianhe-title-img {
  width: 28px;
  height: 28px;
  margin: 0 10px 0 20px;
}
.go-position-btn {
  font-size: 14px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  color: #1C68AE;
  line-height: 20px;
  display: inline-block;
  padding: 6px 16px;
  border-radius: 16px !important;
  opacity: 0.99;
  border: 1px solid #1C68AE;
  margin-right: 21px;
}
.go-position-btn:hover {
  text-decoration: none;
}
.go-position-btn svg {
  margin-left: 8px;
}
.lianhe-content {
  padding: 0px 10px 0 20px;
}

.one-head-img {
  width: 40px;
  height: 40px;
  border-radius: 50%!important;
  background: #fff;
  z-index: 2;
}
.one-head-level {
  background: rgba(255, 139, 0, 0.99);
  border-radius: 0px 8px 8px 0px !important;
  padding: 0 8px;
  color: #fff;
  font-size: 12px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 16px;
  margin-left: -5px;
}
.one-head-name {
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #1C68AE;
  line-height: 20px;
  margin-left: 16px;
  display: inline-block;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.one-head-company {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
  margin-left: 8px;
  display: inline-block;
}

.lianhe-one-right {
  padding-right: 21px;
}
.one-btn-box {
  background: rgba(28, 104, 174, 0.99);
  padding: 6px 16px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 20px;
  border-radius: 2px !important;
  cursor: pointer;
}
.one-btn-box svg {
  margin-left: 8px;
}

.lianhe-two-left {
  width: 420px;
}
.lianhe-two-right {
  flex: 1;
}
.lianhe-two-left .job-item-job-title-name {
  color: #2A7FCC;
  font-weight: bold;
  font-size: 16px;
  max-width: 200px;
  display: inline-block;
  margin-right: 4px;
}
.lianhe-two-left .job-item-job-publish {
  display: inline;
  font-size: 14px;
  color: #666;
  font-weight: initial;
  margin-left: 10px;
  max-width: 140px;
}
.lianhe-two-box1 {
  margin-bottom: 14px;
}
.lianhe-two-right .job-item-company-title-name {
  font-weight: 600;
  font-size: 16px;
  max-width: 250px;
  display: inline-block;
}
.lianhe-two-right .company-industry-msg {
  max-width: 180px;
}
.lianhe-two-box .label-item {
  font-size: 12px;
  padding: 3px 4px;
  border: solid 1px;
  line-height: 1;
  margin-right: 10px;
  border-radius: 2px !important;
}
.lianhe-two-box2 {
  margin-bottom: 13px;
}
.lianhe-two-box3 .job-intro-sep {
  background-color: #DDDDDD;
  height: 12px;
  width: 1px;
  margin-left: 13px;
  margin-right: 13px;
}
.lianhe-two-box3 #newList3 {
  font-size: 16px;
  color: #F94444;
}
.lianhe-three-box {
  height: 37px;
  margin-top: 15px;
}
.lianhe-three-left-line {
  width: 122px;
  height: 1px;
  background: linear-gradient(270deg, rgba(41,127,204,0.5) 0%, rgba(41,127,204,0) 100%);
  border-radius: 1px !important;
  opacity: 0.99;
  margin-right: 14px;
}
.lianhe-three-right-line {
  width: 122px;
  height: 1px;
  background: linear-gradient(90deg, rgba(41,127,204,0.5) 0%, rgba(41,127,204,0) 100%);
  border-radius: 1px !important;
  opacity: 0.99;
  margin-left: 14px;
}
.lianhe-three-txt {
  font-size: 12px;
  color: #333;
}
.lianhe-amount-r {
  font-weight: bold;
  color: #F94444;
  font-size: 15px;
  margin-right: 10px;
}
.lianhe-tidian-box {
  display: inline-block;
  border: solid 1px #ccc;
  font-size: 12px;
  padding: 3px 7px;
  margin-left: 10px;
  border-radius: 2px!important;
}
.lianhe-tidian-rate-y {
  color: #FF8800;
  font-weight: 600;
}
.lianhe-tidian-rate-b {
  color: #2A7FCC;
  font-weight: 600;
}

.job-item-com-img {
  height: 17px;
  margin-right: 3px;
}
.item-content-new {
  cursor: pointer;
}
.operational-data-item {
  display: flex;
}
.pop-new-box {
  flex: 1;
}
.operational-data-item .item-content-new {
  width: 100%;
}
.el-popper[x-placement^=bottom] {
  margin-top: 16px;
}

.head-11-wrap {
  background: transparent;
  display: flex;
  justify-content: space-between;
}
.head-11-wrap .index-top-left {
  border-radius: 8px !important;
  flex: 1;
}
.head-11-wrap .top-left-left {
  border-radius: 8px 0 0 8px !important;
}
.head-11-wrap .top-left-right {
  border-radius: 8px !important;
  width: calc(100% - 190px);
}
.head-11-wrap .index-top-right {
  margin-left: 14px;
  border-radius: 8px !important;
}
.JobScreen-cont {
  padding: 20px 22px 0;
}
.JobScreen-div {
  width: 100%;
  display: flex;
  align-items: center;
}
.JobScreen-title {
  font-size: 14px;
  font-weight: 600;
  display: inline-block;
  width: 14%;
  color: #333;
  position: relative;
  top: -10px;
}
.JobScreen-right{
  width:86%;

}
.JobScreen-100 {
  width: 100%;
  
}
  .JobScreen-100 .el-input__inner {
    height: 32px;
    border: 1px solid #E4E4E4;
    border-radius: 4px !important;
  }
    .JobScreen-100 .el-input__inner::-webkit-input-placeholder {
      color: #666;
      font-size: 12px;
    }

.project-input {
  width: 100% !important;
  height: 32px !important;
  border: 1px solid #E4E4E4 !important;
  padding: 8px 12px !important;
  cursor: pointer !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  border-radius: 4px !important;
  display: flex;
  align-items: center;
}

.project-input-occupation {
  width: 100%;
  min-height: 32px;
  border: 1px solid #E4E4E4;
  padding: 8px 10px;
  cursor: pointer;
  border-radius: 4px !important;
  line-height: 12px;
}

  .project-input-occupation .occupations-span:nth-child(2) {
    margin-bottom: 4px;
  }

.subscription-industry {
  padding: 4px 8px 4px 10px;
  background: #E8F4FF;
  font-size: 12px;
  color: #2A7FCC;
  border-radius: 12px !important;
  margin-right: 6px;
  display: inline-block;
}

.subscription-industry-delete {
  width: 12px;
  height: 12px;
  display: inline-block;
  background: url(../images/subscriptionJob/subscription-delete-icon.png) no-repeat;
  background-size: 100% 100%;
  position: relative;
  top: 2px;
}
.JobScreen-right{
  margin-bottom:20px;
}
  .JobScreen-right .el-radio-group .el-radio {
    margin-right: 22px;
  }
  .JobScreen-right .el-radio-group .el-radio .el-radio__input .el-radio__inner {
    border-radius: 50% !important;
  }
.JobScreen-right .el-radio-group .el-radio .el-radio__input .radio {
  display: none;
}
  .JobScreen-right .el-radio-group .el-radio .el-radio__label {
    font-size: 14px;
    color: #333;
    padding-left: 4px;
  }
.JobScreenSaralyInput .el-input .el-input__inner {
  height:28px;
  line-height:28px;
  border-radius:4px !important;
}
.JobScreenSaralyInput .el-input .el-input__suffix{
  line-height:28px;
  font-size:14px;
  color:#333;
}
.JobScreenSaralyInput .el-input--suffix .el-input__inner {
  padding-right: 20px;
}
.JobScreenMultipleChoice {
  font-size: 14px;
  color: #333;
}
.JobScreen-right .filter-tag .icheck {
  border: 1px solid #A2A5B4;

}
.JobScreen-bottom {
  padding: 12px 20px;
  border-top: 1px solid #DCDFE6;
  text-align:end;
}

.JobScreen-bottom-left-btn {
  display: inline-block;
  padding: 6.5px 28px;
  border: 1px solid #DDDDDD;
  border-radius: 4px !important;
  color: #666666;
  font-size:12px;
  cursor:pointer;
}
.JobScreen-bottom-right-btn {
  display: inline-block;
  padding: 7.5px 28px;
  background: #2A7FCC;
  border-radius: 4px !important;
  color: #fff;
  font-size: 12px;
  cursor: pointer;
}
.project-select-span {
  color: #666;
  font-size: 12px;
}










