/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
function getEmail() {
	$(".again-sendemail").die("click").live("click", function () {
		DialogAlert.msgStopClose();
		DialogAlert.msgClose();
		emailedit();
	});
	function collection(el) {
		return {
			emailcategory: el.find(".emailcategory").val(),
			emailtitle: el.find(".emailtitle").val(),
			emailcontent: el.find(".emailcontent").val()
		};
	}
	function verification(data) {
		if (data.emailcategory.length == 0 || data.emailcategory == null) {
			beautAlert.done(setRnssLanguage("请选择邮件类别"), "hits");
			return false;
		}
		if (data.recipients.length == 0) {
			beautAlert.done(setRnssLanguage("请输入邮件收件人"), "hits");
			return false;
		}
		for (var i = 0; i < data.recipients.length; i++) {
			if (!/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(data.recipients[i].address)) {
				beautAlert.done(setRnssLanguage("收件人邮箱不合法"), "hits");
				return false;
			}
		}
		
		if (data.emailtitle.length == 0 || data.emailtitle == null) {
			beautAlert.done(setRnssLanguage("请输入邮件标题"), "hits");
			return false;
		}
		if (data.emailcontent.length == 0 || data.emailcontent == null) {
			beautAlert.done(setRnssLanguage("请输入邮件内容"), "hits");
			return false;
		}
		return true;
	}

	var datarecipients = [], categoryselected;
	var emailedit = function (recipients, category) {
		DialogAlert.msgClose();
		datarecipients = recipients || datarecipients || [];
		categoryselected = category || categoryselected || 0;
		$.ajaxPost("/services/staffactionhandler.ashx?action=getdefaultemail", {}, function (result) {
			if (result.Status == 1) {
				$(this).emailEditor({
					Category: result.Data.Category, Content: result.Data.Body, CategorySelected: categoryselected, DataRecipients: datarecipients, DialogCallBack: function (el) {
						el.find(".emailcontent").scrollTop(0);
					}, Submit: function (el, recipients) {
						var data = $.extend({}, collection(el), { recipients: recipients });
						if (!verification(data)) {
							return;
						}
						$.ajaxPost("/services/emailactionhandler.ashx?action=sendemail", $.extend({}, collection(el), { recipients: JSON.stringify(recipients) }), function (result) {
							if (result.Status == 1) {
								DialogAlert.msgClose();
								var html = '<div class="fill-out-con cf"><div class="fill-con-title"><span class="icon-fill-out icon-111 r-icon-fill-out r-icon-111"></span><h3>' + setRnssLanguage('邮件发送成功') + '</h3></div><div class="fill-con-link cf"><a href="javascript:;" class="again-sendemail">' + setRnssLanguage('再发一条') + '</a><a href="/apps/manageemail.aspx">' + setRnssLanguage('返回邮件管理') + '</a></div></div>';
								DialogAlert.msgOpen({ msgTitle: setRnssLanguage("操作成功"), msgBody: html, msgIsClose: true, msgWidth: "500", msgPanelWidth: "490", msgDragDrop: true });
							}
							else {
								beautAlert.done(result.Data, "hits");
							}
						});
					}
				});
			}
			else {
				beautAlert.done(result.Data, "hits");
				DialogAlert.msgClose();
				$(document).EmailSettingConfig({
					callback: function () {
						DialogAlert.msgClose();
						emailedit();
					}
				});
			}
		})
	}
	return {
		Show: emailedit
	};
}

$(".btn-setconfig").die("click").live("click", function () {
	DialogAlert.msgClose();
	$(document).EmailSettingConfig({
		callback: function () {
			DialogAlert.msgClose();
			//emailedit();
      getEmail().Show();
		}
	});
});
$(".btn-setsendmail").die("click").live("click", function () {
	DialogAlert.msgClose();
	getEmail().Show();
});
