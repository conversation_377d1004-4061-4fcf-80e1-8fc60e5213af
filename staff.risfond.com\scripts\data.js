var occupations = [{ value: '10', text: '经营管理类', children: [{ value: '1001', text: '首席执行官CEO/总裁/总经理' }, { value: '1003', text: '首席运营官COO' }, { value: '1005', text: '首席财务官CFO' }, { value: '1004', text: '首席技术官CTO/首席信息官CIO' }, { value: '1007', text: '总监/事业部总经理' }, { value: '1008', text: '分公司经理/分支机构经理/办事处经理' }, { value: '1012', text: '副总裁/副总经理' }, { value: '1006', text: '合伙人' }, { value: '1002', text: '总裁助理/总经理助理' }, { value: '1009', text: '部门经理' }, { value: '1010', text: '厂长/副厂长' }, { value: '1011', text: '店长' }, { value: '1013', text: '其他职位(经营管理类)'}] }, { value: '03', text: '财务/审计/统计类', children: [{ value: '0318', text: '首席财务官CFO' }, { value: '0305', text: '财务总监' }, { value: '0301', text: '财务主管/总帐主管' }, { value: '0306', text: '财务经理' }, { value: '0307', text: '会计经理/会计主管' }, { value: '0308', text: '会计' }, { value: '0309', text: '财务分析经理/财务分析主管' }, { value: '0311', text: '成本经理/成本主管' }, { value: '0313', text: '审计经理/审计主管' }, { value: '0315', text: '税务经理/税务主管' }, { value: '0304', text: '统计' }, { value: '0317', text: '投融资经理/投融资主管' }, { value: '0302', text: '出纳' }, { value: '0303', text: '财务助理/会计助理' }, { value: '0319', text: '其他职位(财务/审计/统计类)'}] }, { value: '02', text: '人力资源类', children: [{ value: '0215', text: '人力资源总监' }, { value: '0201', text: '人力资源经理/人力资源主管' }, { value: '0205', text: '培训经理/培训主管' }, { value: '0203', text: '招聘经理/招聘主管' }, { value: '0214', text: '人事主管/人事专员' }, { value: '0208', text: '薪资福利经理/主管' }, { value: '0210', text: '绩效考核经理/绩效考核主管' }, { value: '0212', text: '员工关系经理/员工关系主管' }, { value: '0207', text: '培训师' }, { value: '0216', text: '猎头顾问' }, { value: '0202', text: '人力资源专员/人力资源助理' }, { value: '0217', text: '其他职位(人力资源类)'}] }, { value: '01', text: '销售类', children: [{ value: '0110', text: '销售总监' }, { value: '0103', text: '渠道总监/分销总监' }, { value: '0102', text: '销售经理/销售主管' }, { value: '0105', text: '售前支持经理/售前支持主管' }, { value: '0106', text: '商务经理/商务主管' }, { value: '0108', text: '业务拓展(BD)经理' }, { value: '0109', text: '电话销售' }, { value: '0111', text: '销售代表/客户经理' }, { value: '0112', text: '渠道分销经理/渠道分销主管' }, { value: '0115', text: '医药代表' }, { value: '0114', text: '保险代理人/经纪人/客户经理' }, { value: '0113', text: '售前支持工程师' }, { value: '0116', text: '区域销售管理' }, { value: '0117', text: '医疗器械销售' }, { value: '0118', text: '其他职位(销售类)'}] }, { value: '04', text: '计算机/网络/技术类', children: [{ value: '0414', text: '首席技术官CTO/首席信息官CIO' }, { value: '0425', text: '技术总监/技术经理' }, { value: '0417', text: '高级软件工程师' }, { value: '0418', text: '互联网软件开发工程师' }, { value: '0420', text: '通信技术工程师' }, { value: '0436', text: '架构师' }, { value: '0432', text: '软件工程师' }, { value: '0431', text: '硬件工程师' }, { value: '0430', text: '网络工程师' }, { value: '0429', text: 'UI设计/网页设计与制作' }, { value: '0423', text: '网站营运管理' }, { value: '0405', text: '产品经理' }, { value: '0404', text: '数据库管理员/数据库开发工程师' }, { value: '0402', text: '语音/视频/多媒体开发' }, { value: '0401', text: '系统集成工程师' }, { value: '0406', text: '软件测试' }, { value: '0407', text: '硬件测试' }, { value: '0408', text: '游戏设计与开发' }, { value: '0409', text: 'ERP技术应用顾问/ERP实施工程师' }, { value: '0410', text: '网站策划' }, { value: '0411', text: '网站编辑' }, { value: '0412', text: '文档工程师' }, { value: '0413', text: '研发工程师' }, { value: '0415', text: '项目经理/项目主管' }, { value: '0416', text: '项目执行/协调人员' }, { value: '0419', text: '高级硬件工程师' }, { value: '0422', text: '品质经理/质量工程师' }, { value: '0426', text: '信息技术经理/信息技术主管' }, { value: '0428', text: '系统管理员/网管' }, { value: '0433', text: '网络与信息安全工程师' }, { value: '0434', text: '计算机辅助设计工程师' }, { value: '0435', text: '工程与项目实施' }, { value: '0421', text: '技术支持/维护经理' }, { value: '0427', text: '技术支持/维护工程师' }, { value: '0437', text: '其他职位(计算机/网络/技术类)'}] }, { value: '25', text: '金融类(银行/基金/证券/期货/投资/典当)', children: [{ value: '2502', text: '行长/副行长/高级管理' }, { value: '2511', text: '投资银行业务' }, { value: '2501', text: '投资管理/研究分析/顾问' }, { value: '2503', text: '证券/外汇/期货经纪人' }, { value: '2504', text: '融资项目管理' }, { value: '2508', text: '信贷管理/信用管理' }, { value: '2515', text: '股票/期货操盘手' }, { value: '2514', text: '证券分析师' }, { value: '2513', text: '国际结算/外汇交易' }, { value: '2509', text: '资金管理/财务管理/清算/结算' }, { value: '2507', text: '资产管理/资产评估/交易管理' }, { value: '2506', text: '客户经理/金融产品营销管理' }, { value: '2505', text: '风险管理/稽核/法律' }, { value: '2512', text: '银行卡/电子银行/新业务开拓' }, { value: '2516', text: '拍卖师' }, { value: '2517', text: '鉴定/估价师' }, { value: '2518', text: '其他职位(金融类(银行/基金/证券/期货/投资/典当))'}] }, { value: '12', text: '教育/培训类', children: [{ value: '1201', text: '教学/教务管理人员' }, { value: '1203', text: '教师' }, { value: '1202', text: '幼儿教育' }, { value: '1208', text: '教育产品开发' }, { value: '1204', text: '讲师' }, { value: '1207', text: '健身顾问/教练' }, { value: '1209', text: '其他职位(教育/培训类)'}] }, { value: '07', text: '建筑/房地产/装饰装修/物业管理类', children: [{ value: '0701', text: '物业管理' }, { value: '0702', text: '房地产开发/策划' }, { value: '0704', text: '建筑师' }, { value: '0709', text: '结构工程师/土木土建工程师' }, { value: '0710', text: '建筑设计/建筑制图' }, { value: '0711', text: '建筑工程管理' }, { value: '0725', text: '物业招商/租赁/租售' }, { value: '0715', text: '城市规划与设计' }, { value: '0716', text: '房地产中介/交易' }, { value: '0717', text: '公路桥梁设计/公路桥梁预算师' }, { value: '0729', text: '高级建筑工程师/总工' }, { value: '0708', text: '室内外装潢设计' }, { value: '0707', text: '园艺设计/园林设计/景观设计' }, { value: '0706', text: '路桥/隧道/港口/航道工程' }, { value: '0705', text: '给排水/强电/弱电/制冷暖通' }, { value: '0703', text: '房地产评估' }, { value: '0724', text: '物业顾问' }, { value: '0726', text: '物业设施管理' }, { value: '0730', text: '其他职位(建筑/房地产/装饰装修/物业管理类)'}] }, { value: '22', text: '市场/公关/媒介类', children: [{ value: '2218', text: '市场总监' }, { value: '2217', text: '市场营销经理/市场营销主管' }, { value: '2205', text: '产品经理/品牌经理/主管' }, { value: '2201', text: '广告创意/策划/设计或文案' }, { value: '2202', text: '市场调研与分析' }, { value: '2206', text: '会务经理/会务主管' }, { value: '2208', text: '市场企划经理/市场企划主管' }, { value: '2210', text: '广告客户经理' }, { value: '2212', text: '促销经理/促销主管' }, { value: '2215', text: '公关媒介经理/公关媒介主管' }, { value: '2219', text: '市场经理/市场主管' }, { value: '2220', text: '其他职位(市场/公关/媒介类)'}] }, { value: '24', text: '美术/设计/创意类', children: [{ value: '2406', text: '平面设计' }, { value: '2407', text: '媒体广告设计' }, { value: '2410', text: '动画设计' }, { value: '2411', text: '展示/装潢设计' }, { value: '2412', text: '创意/策划/文案' }, { value: '2413', text: '服装打样/服装制板' }, { value: '2414', text: '服装/纺织品设计师' }, { value: '2409', text: '多媒体设计' }, { value: '2408', text: '造型设计' }, { value: '2405', text: '家具设计' }, { value: '2404', text: '工艺品/珠宝设计' }, { value: '2403', text: '工业/产品设计' }, { value: '2402', text: '美术/图形设计' }, { value: '2401', text: '设计管理人员' }, { value: '2415', text: '其他职位(美术/设计/创意类)'}] }, { value: '06', text: '电子/电器/通信技术类', children: [{ value: '0601', text: '电子工程师/电路工程师' }, { value: '0602', text: '电器工程师' }, { value: '0603', text: '电信工程师/通讯工程师' }, { value: '0604', text: '电声工程师' }, { value: '0605', text: '数码产品开发工程师' }, { value: '0606', text: '无线电工程师' }, { value: '0607', text: '半导体工程师' }, { value: '0608', text: '电子元器件工程师' }, { value: '0609', text: '电子/电器维修' }, { value: '0610', text: '研发工程师' }, { value: '0611', text: '光源与照明工程师' }, { value: '0612', text: '测试工程师' }, { value: '0613', text: '技术文档工程师' }, { value: '0614', text: '工艺工程师' }, { value: '0615', text: '集成电路(IC)芯片开发' }, { value: '0616', text: '机械工程师' }, { value: '0617', text: '电气工程师' }, { value: '0618', text: '嵌入式系统软件开发' }, { value: '0619', text: '工程与项目实施' }, { value: '0620', text: '移动通信工程师/无线通信工程师' }, { value: '0621', text: '有线传输工程师' }, { value: '0622', text: '增值产品研发' }, { value: '0623', text: '通信电源工程师' }, { value: '0624', text: '数据通信工程师' }, { value: '0625', text: '激光/光电子技术' }, { value: '0626', text: '版图设计工程师' }, { value: '0627', text: '电池/电源开发' }, { value: '0628', text: '现场应用工程师/FAE' }, { value: '0629', text: '其他职位(电子/电器/通信技术类)'}] }, { value: '30', text: '生物/制药/化工/环保类', children: [{ value: '3003', text: '医药研发/化学制剂研发' }, { value: '3001', text: '生物工程/生物制药' }, { value: '3002', text: '临床试验/药品注册' }, { value: '3004', text: '环保技术' }, { value: '3005', text: '药品销售/推广/业务咨询' }, { value: '3006', text: '药品生产/质量管理' }, { value: '3007', text: '化工技术' }, { value: '3008', text: '医药招商' }, { value: '3009', text: '涂料开发工程师' }, { value: '3010', text: '塑料制品研发' }, { value: '3011', text: '日用化工产品研发' }, { value: '3012', text: '其他职位(生物/制药/化工/环保类)'}] }, { value: '09', text: '客户服务类', children: [{ value: '0904', text: '客户服务总监' }, { value: '0902', text: '客户关系管理' }, { value: '0906', text: '客户服务经理/客户服务主管' }, { value: '0905', text: '售后支持经理/售后支持主管' }, { value: '0903', text: '售后支持工程师' }, { value: '0908', text: '其他职位(客户服务类)'}] }, { value: '26', text: '贸易/物流/采购/运输类', children: [{ value: '2616', text: '采购经理/主管' }, { value: '2617', text: '物流经理/物流主管' }, { value: '2606', text: '运输经理/主管' }, { value: '2602', text: '国内贸易经理/主管/专员/助理' }, { value: '2601', text: '外贸经理/主管/专员/助理' }, { value: '2605', text: '仓库管理' }, { value: '2608', text: '海陆空交通运输' }, { value: '2614', text: '物料管理' }, { value: '2618', text: '司机' }, { value: '2619', text: '供应链管理' }, { value: '2621', text: '海关事务管理' }, { value: '2623', text: '集装箱业务' }, { value: '2622', text: '其他职位(贸易/物流/采购/运输类)'}] }, { value: '20', text: '机械/仪器仪表类', children: [{ value: '2001', text: '机电工程师' }, { value: '2002', text: '精密机械/仪器仪表工程师/技术员' }, { value: '2003', text: '机械工程师' }, { value: '2004', text: '模具工程师' }, { value: '2005', text: '机械设计师' }, { value: '2007', text: '铸造/锻造工程师' }, { value: '2008', text: '注塑工程师' }, { value: '2009', text: 'CNC工程师' }, { value: '2010', text: '冲压工程师' }, { value: '2011', text: '夹具工程师' }, { value: '2012', text: '锅炉工程师' }, { value: '2013', text: '焊接工程师' }, { value: '2014', text: '汽车/摩托车工程师' }, { value: '2015', text: '船舶工程师' }, { value: '2016', text: '飞行器设计与制造' }, { value: '2017', text: '机械维修工程师' }, { value: '2018', text: '包装/印刷机械' }, { value: '2019', text: '食品机械' }, { value: '2020', text: '纺织机械' }, { value: '2021', text: '设备修理' }, { value: '2022', text: '其他职位(机械/仪器仪表类)'}] }, { value: '28', text: '酒店/餐饮/旅游/服务类', children: [{ value: '2802', text: '娱乐或餐饮管理' }, { value: '2805', text: '厨师/调酒师' }, { value: '2808', text: '宾馆或酒店管理' }, { value: '2809', text: '营养师' }, { value: '2812', text: '美容美发' }, { value: '2813', text: '司机' }, { value: '2806', text: '导游/计调' }, { value: '2807', text: '健身教练' }, { value: '2819', text: '其他职位(酒店/餐饮/旅游/服务类)'}] }, { value: '34', text: '保险类', children: [{ value: '3401', text: '精算/产品研发/投资/稽核/法律' }, { value: '3402', text: '核保/理赔/契约管理/受理台' }, { value: '3403', text: '组训/培训/人员管理/业务推动' }, { value: '3404', text: '保险代理人/经纪人/客户经理' }, { value: '3405', text: '客户服务/续期管理' }, { value: '3406', text: '保险内勤' }, { value: '3407', text: '其他职位'}] }, { value: '14', text: '咨询/顾问类', children: [{ value: '1405', text: '咨询总监' }, { value: '1403', text: '培训师' }, { value: '1406', text: '咨询经理' }, { value: '1404', text: '涉外咨询师' }, { value: '1401', text: '企管顾问/专业顾问/策划师' }, { value: '1408', text: '其他职位(咨询/顾问类)'}] }, { value: '17', text: '电气/能源/动力类', children: [{ value: '1705', text: '电路工程师' }, { value: '1704', text: '变压器与磁电工程师' }, { value: '1703', text: '光源与照明工程' }, { value: '1702', text: '电气工程师' }, { value: '1701', text: '智能大厦/综合布线/弱电' }, { value: '1706', text: '电力工程师' }, { value: '1708', text: '水利/水电工程师' }, { value: '1709', text: '核力/火力工程师' }, { value: '1710', text: '空调/热能工程师' }, { value: '1711', text: '石油天然气技术人员' }, { value: '1712', text: '自动控制' }, { value: '1713', text: '制冷/暖通' }, { value: '1714', text: '燃气轮机工程师' }, { value: '1715', text: '其他职位(电气/能源/动力类)'}] }, { value: '13', text: '医疗卫生/美容保健类', children: [{ value: '1301', text: '医疗管理人员' }, { value: '1302', text: '医疗技术人员' }, { value: '1303', text: '医生/医师' }, { value: '1304', text: '心理医生' }, { value: '1305', text: '医药检验' }, { value: '1306', text: '护士/护理人员' }, { value: '1308', text: '美容/整形师' }, { value: '1309', text: '兽医/宠物医生' }, { value: '1310', text: '药库主任/药剂师' }, { value: '1311', text: '针灸推拿' }, { value: '1314', text: '医药代表' }, { value: '1315', text: '药学技术与管理人员' }, { value: '1317', text: '营养师' }, { value: '1316', text: '中医' }, { value: '1307', text: '疾病控制/公共卫生' }, { value: '1313', text: '健身顾问/教练' }, { value: '1319', text: '其他职位(医疗卫生/美容保健类)'}] }, { value: '11', text: '法律类', children: [{ value: '1101', text: '律师' }, { value: '1102', text: '法律顾问' }, { value: '1104', text: '法务人员' }, { value: '1105', text: '知识产权/专利顾问' }, { value: '1106', text: '其他职位(法律类)'}] }, { value: '29', text: '商业零售类', children: [{ value: '2902', text: '店长' }, { value: '2903', text: '营运' }, { value: '2907', text: '其他职位(商业零售类)'}] }, { value: '21', text: '文体/影视/写作/媒体类', children: [{ value: '2103', text: '总编/副总编' }, { value: '2101', text: '编辑/记者' }, { value: '2105', text: '发行总监/经理/主管' }, { value: '2109', text: '艺术总监/设计总监' }, { value: '2110', text: '影视策划/影视制作' }, { value: '2106', text: '出版' }, { value: '2104', text: '美术编辑' }, { value: '2111', text: '导演/编导' }, { value: '2112', text: '摄影/摄像' }, { value: '2113', text: '录音/音效师' }, { value: '2114', text: '化妆师/造型师' }, { value: '2115', text: '演员/配音/模特' }, { value: '2116', text: '主持人/播音员/DJ' }, { value: '2117', text: '演艺或体育经纪人' }, { value: '2102', text: '作家/撰稿人' }, { value: '2118', text: '其他职位(文体/影视/写作/媒体类)'}] }, { value: '33', text: '行政/后勤类', children: [{ value: '3301', text: '行政总监' }, { value: '3302', text: '行政经理/行政主管/办公室主任' }, { value: '3305', text: '前台/总机/接待' }, { value: '3304', text: '经理助理/秘书/文员' }, { value: '3303', text: '行政专员/助理' }, { value: '3308', text: '后勤' }, { value: '3309', text: '其他职位(行政/后勤类)'}] }, { value: '32', text: '质量管理类', children: [{ value: '3201', text: '质量保证(QA)/质量管理/质量督导' }, { value: '3202', text: '质量控制(QC)/质量检验' }, { value: '3203', text: '新产品开发测试' }, { value: '3204', text: '供应商管理/采购设备与材料质量管理' }, { value: '3205', text: '质量体系认证' }, { value: '3206', text: '其他职位(质量管理类)'}] }, { value: '27', text: '翻译类', children: [{ value: '2701', text: '英语' }, { value: '2702', text: '日语' }, { value: '2703', text: '法语' }, { value: '2704', text: '德语' }, { value: '2705', text: '俄语' }, { value: '2706', text: '西班牙语' }, { value: '2707', text: '韩语' }, { value: '2708', text: '阿拉伯语翻译' }, { value: '2709', text: '其他语种翻译(翻译类)'}] }, { value: '19', text: '工厂生产类', children: [{ value: '1903', text: '总工程师/副总工程师' }, { value: '1902', text: '厂长/副厂长' }, { value: '1904', text: '采购管理' }, { value: '1905', text: '物料或物流管理' }, { value: '1908', text: '产品开发' }, { value: '1909', text: '维修工程师' }, { value: '1910', text: '技术工程师' }, { value: '1911', text: '技术或工艺设计经理' }, { value: '1912', text: '质量管理(QA/QC)' }, { value: '1913', text: '化验/检验' }, { value: '1914', text: '生产经理/车间主任' }, { value: '1915', text: '组长/拉长' }, { value: '1917', text: '产品或生产工艺工程师(PE/ME)' }, { value: '1918', text: '工业工程师(IE)' }, { value: '1919', text: '制造工程师' }, { value: '1907', text: '安全/健康/环境管理' }, { value: '1906', text: '工程或设备管理' }, { value: '1901', text: '仓库管理' }, { value: '1921', text: '其他职位(工厂生产类)'}] }, { value: '18', text: '技工类', children: [{ value: '1810', text: '产品或工艺工程师' }, { value: '1811', text: '模具工程师' }, { value: '1812', text: '其他职位(技工类)'}] }, { value: '05', text: '项目管理类', children: [{ value: '0501', text: '项目总监' }, { value: '0502', text: '项目经理' }, { value: '0503', text: '其他职位(项目管理类)'}] }, { value: '31', text: '其他职业类', children: [{ value: '3107', text: '农林牧渔' }, { value: '3106', text: '气象' }, { value: '3105', text: '地质矿产冶金' }, { value: '3104', text: '测绘技术' }, { value: '3103', text: '声光学技术/激光技术' }, { value: '3102', text: '安全消防' }, { value: '3101', text: '航空航天' }, { value: '3108', text: '其他类别职位(其他类)'}]}];
var industries = [{ value: '04', text: '房地产开发/建筑与工程' }, { value: '16', text: '金融业(投资/保险/证券/银行/基金)' }, { value: '07', text: '互联网/电子商务' }, { value: '08', text: '环保' }, { value: '09', text: '机械制造/机电/重工' }, { value: '10', text: '计算机' }, { value: '05', text: '广告/会展/公关' }, { value: '21', text: '耐用消费品(服饰/纺织/皮革/家具)' }, { value: '28', text: '通信(设备/运营/增值服务)' }, { value: '30', text: '网络游戏' }, { value: '39', text: '制药/生物工程' }, { value: '03', text: '电子/微电子' }, { value: '02', text: '船舶制造' }, { value: '01', text: '办公设备/用品' }, { value: '06', text: '航空/航天研究与制造' }, { value: '15', text: '教育/培训/科研/院校' }, { value: '14', text: '交通/运输/物流' }, { value: '12', text: '家居/室内设计/装潢' }, { value: '11', text: '家电业' }, { value: '17', text: '快速消费品(食品/饮料/日化/烟酒等)' }, { value: '18', text: '旅游/酒店' }, { value: '19', text: '贸易/进出口' }, { value: '20', text: '媒体/出版/文化传播' }, { value: '22', text: '能源(电力/石油)/水利' }, { value: '26', text: '汽车/摩托车(制造/维护/配件/销售/服务)' }, { value: '31', text: '物业管理/商业中心' }, { value: '27', text: '石油/化工/矿产/采掘/冶炼/原材料' }, { value: '29', text: '玩具/工艺品/收藏品/奢侈品' }, { value: '33', text: '医疗设备/器械' }, { value: '32', text: '医疗/保健/美容/卫生服务' }, { value: '34', text: '仪器/仪表/工业自动化/电气' }, { value: '37', text: '原材料及加工(金属/木材/橡胶/塑料/玻璃/陶瓷/建材)' }, { value: '35', text: '印刷/包装/造纸' }, { value: '36', text: '娱乐/运动/休闲' }, { value: '40', text: '中介服务' }, { value: '41', text: '专业服务(咨询/人力资源/财会/法律等)' }, { value: '42', text: '保险' }, { value: '43', text: '餐饮服务' }, { value: '44', text: '外包服务' }, { value: '45', text: '影视/媒体/艺术/文化传播' }, { value: '46', text: '新能源' }, { value: '47', text: '电子技术/半导体/集成电路' }, { value: '48', text: '多元化业务集团公司' }, { value: '49', text: '生活服务' }, { value: '23', text: '农/林/牧/渔' }, { value: '24', text: '批发/零售' }, { value: '13', text: '检验/检测/认证' }, { value: '38', text: '政府/非营利机构' }, { value: '25', text: '其他行业'}];
var locations = [{ value: '01', text: '北京' }, { value: '09', text: '上海' }, { value: '02', text: '天津' }, { value: '19', text: '广东', children: [{ value: '1901', text: '广州' }, { value: '1902', text: '韶关' }, { value: '1903', text: '深圳' }, { value: '1904', text: '珠海' }, { value: '1905', text: '汕头' }, { value: '1906', text: '佛山' }, { value: '1907', text: '江门' }, { value: '1908', text: '湛江' }, { value: '1909', text: '茂名' }, { value: '1910', text: '肇庆' }, { value: '1911', text: '惠州' }, { value: '1912', text: '梅州' }, { value: '1913', text: '汕尾' }, { value: '1914', text: '河源' }, { value: '1915', text: '阳江' }, { value: '1916', text: '清远' }, { value: '1917', text: '东莞' }, { value: '1918', text: '中山' }, { value: '1919', text: '潮州' }, { value: '1920', text: '揭阳' }, { value: '1921', text: '云浮' }] }, { value: '11', text: '浙江', children: [{ value: '1101', text: '杭州' }, { value: '1102', text: '宁波' }, { value: '1103', text: '温州' }, { value: '1104', text: '嘉兴' }, { value: '1105', text: '湖州' }, { value: '1106', text: '绍兴' }, { value: '1107', text: '金华' }, { value: '1108', text: '衢州' }, { value: '1109', text: '舟山' }, { value: '1110', text: '台州' }, { value: '1111', text: '丽水' }] }, { value: '10', text: '江苏', children: [{ value: '1001', text: '南京' }, { value: '1002', text: '无锡' }, { value: '1003', text: '徐州' }, { value: '1004', text: '常州' }, { value: '1005', text: '苏州' }, { value: '1006', text: '南通' }, { value: '1007', text: '连云港' }, { value: '1008', text: '淮安' }, { value: '1009', text: '盐城' }, { value: '1010', text: '扬州' }, { value: '1011', text: '镇江' }, { value: '1012', text: '泰州' }, { value: '1013', text: '宿迁' }] }, { value: '22', text: '重庆' }, { value: '03', text: '河北', children: [{ value: '0301', text: '石家庄' }, { value: '0302', text: '唐山' }, { value: '0303', text: '秦皇岛' }, { value: '0304', text: '邯郸' }, { value: '0305', text: '邢台' }, { value: '0306', text: '保定' }, { value: '0307', text: '张家口' }, { value: '0308', text: '承德' }, { value: '0309', text: '沧州' }, { value: '0310', text: '廊坊' }, { value: '0311', text: '衡水' }] }, { value: '15', text: '山东', children: [{ value: '1501', text: '济南' }, { value: '1502', text: '青岛' }, { value: '1503', text: '淄博' }, { value: '1504', text: '枣庄' }, { value: '1505', text: '东营' }, { value: '1506', text: '烟台' }, { value: '1507', text: '潍坊' }, { value: '1508', text: '济宁' }, { value: '1509', text: '泰安' }, { value: '1510', text: '威海' }, { value: '1511', text: '日照' }, { value: '1512', text: '莱芜' }, { value: '1513', text: '临沂' }, { value: '1514', text: '德州' }, { value: '1515', text: '聊城' }, { value: '1516', text: '滨州' }, { value: '1517', text: '菏泽' }] }, { value: '23', text: '四川', children: [{ value: '2301', text: '成都' }, { value: '2302', text: '自贡' }, { value: '2303', text: '攀枝花' }, { value: '2304', text: '泸州' }, { value: '2305', text: '德阳' }, { value: '2306', text: '绵阳' }, { value: '2307', text: '广元' }, { value: '2308', text: '遂宁' }, { value: '2309', text: '内江' }, { value: '2310', text: '乐山' }, { value: '2311', text: '南充' }, { value: '2312', text: '眉山' }, { value: '2313', text: '宜宾' }, { value: '2314', text: '广安' }, { value: '2315', text: '达州' }, { value: '2316', text: '雅安' }, { value: '2317', text: '巴中' }, { value: '2318', text: '资阳' }, { value: '2319', text: '阿坝州' }, { value: '2320', text: '甘孜州' }, { value: '2321', text: '凉山州' }] }, { value: '18', text: '湖南', children: [{ value: '1801', text: '长沙' }, { value: '1802', text: '株洲' }, { value: '1803', text: '湘潭' }, { value: '1804', text: '衡阳' }, { value: '1805', text: '邵阳' }, { value: '1806', text: '岳阳' }, { value: '1807', text: '常德' }, { value: '1808', text: '张家界' }, { value: '1809', text: '益阳' }, { value: '1810', text: '郴州' }, { value: '1811', text: '永州' }, { value: '1812', text: '怀化' }, { value: '1813', text: '娄底' }, { value: '1814', text: '湘西州' }] }, { value: '13', text: '福建', children: [{ value: '1301', text: '福州' }, { value: '1302', text: '厦门' }, { value: '1303', text: '莆田' }, { value: '1304', text: '三明' }, { value: '1305', text: '泉州' }, { value: '1306', text: '漳州' }, { value: '1307', text: '南平' }, { value: '1308', text: '龙岩' }, { value: '1309', text: '宁德' }] }, { value: '12', text: '安徽', children: [{ value: '1201', text: '合肥' }, { value: '1202', text: '芜湖' }, { value: '1203', text: '蚌埠' }, { value: '1204', text: '淮南' }, { value: '1205', text: '马鞍山' }, { value: '1206', text: '淮北' }, { value: '1207', text: '铜陵' }, { value: '1208', text: '安庆' }, { value: '1209', text: '黄山' }, { value: '1210', text: '滁州' }, { value: '1211', text: '阜阳' }, { value: '1212', text: '宿州' }, { value: '1213', text: '巢湖' }, { value: '1214', text: '六安' }, { value: '1215', text: '亳州' }, { value: '1216', text: '池州' }, { value: '1217', text: '宣城' }] }, { value: '16', text: '河南', children: [{ value: '1601', text: '郑州' }, { value: '1602', text: '开封' }, { value: '1603', text: '洛阳' }, { value: '1604', text: '平顶山' }, { value: '1605', text: '安阳' }, { value: '1606', text: '鹤壁' }, { value: '1607', text: '新乡' }, { value: '1608', text: '焦作' }, { value: '1609', text: '濮阳' }, { value: '1610', text: '许昌' }, { value: '1611', text: '漯河' }, { value: '1612', text: '三门峡' }, { value: '1613', text: '南阳' }, { value: '1614', text: '商丘' }, { value: '1615', text: '信阳' }, { value: '1616', text: '周口' }, { value: '1617', text: '驻马店' }, { value: '1618', text: '河南省直辖县' }] }, { value: '14', text: '江西', children: [{ value: '1401', text: '南昌' }, { value: '1402', text: '景德镇' }, { value: '1403', text: '萍乡' }, { value: '1404', text: '九江' }, { value: '1405', text: '新余' }, { value: '1406', text: '鹰潭' }, { value: '1407', text: '赣州' }, { value: '1408', text: '吉安' }, { value: '1409', text: '宜春' }, { value: '1410', text: '抚州' }, { value: '1411', text: '上饶' }] }, { value: '17', text: '湖北', children: [{ value: '1701', text: '武汉' }, { value: '1702', text: '黄石' }, { value: '1703', text: '十堰' }, { value: '1704', text: '宜昌' }, { value: '1705', text: '襄阳' }, { value: '1706', text: '鄂州' }, { value: '1707', text: '荆门' }, { value: '1708', text: '孝感' }, { value: '1709', text: '荆州' }, { value: '1710', text: '黄冈' }, { value: '1711', text: '咸宁' }, { value: '1712', text: '随州' }, { value: '1713', text: '恩施州' }, { value: '1714', text: '湖北省直辖县' }] }, { value: '27', text: '陕西', children: [{ value: '2701', text: '西安' }, { value: '2702', text: '铜川' }, { value: '2703', text: '宝鸡' }, { value: '2704', text: '咸阳' }, { value: '2705', text: '渭南' }, { value: '2706', text: '延安' }, { value: '2707', text: '汉中' }, { value: '2708', text: '榆林' }, { value: '2709', text: '安康' }, { value: '2710', text: '商洛' }] }, { value: '04', text: '山西', children: [{ value: '0401', text: '太原' }, { value: '0402', text: '大同' }, { value: '0403', text: '阳泉' }, { value: '0404', text: '长治' }, { value: '0405', text: '晋城' }, { value: '0406', text: '朔州' }, { value: '0407', text: '晋中' }, { value: '0408', text: '运城' }, { value: '0409', text: '忻州' }, { value: '0410', text: '临汾' }, { value: '0411', text: '吕梁' }] }, { value: '07', text: '吉林省', children: [{ value: '0701', text: '长春' }, { value: '0702', text: '吉林' }, { value: '0703', text: '四平' }, { value: '0704', text: '辽源' }, { value: '0705', text: '通化' }, { value: '0706', text: '白山' }, { value: '0707', text: '松原' }, { value: '0708', text: '白城' }, { value: '0709', text: '延边州' }] }, { value: '20', text: '广西', children: [{ value: '2001', text: '南宁' }, { value: '2002', text: '柳州' }, { value: '2003', text: '桂林' }, { value: '2004', text: '梧州' }, { value: '2005', text: '北海' }, { value: '2006', text: '防城港' }, { value: '2007', text: '钦州' }, { value: '2008', text: '贵港' }, { value: '2009', text: '玉林' }, { value: '2010', text: '百色' }, { value: '2011', text: '贺州' }, { value: '2012', text: '河池' }, { value: '2013', text: '来宾' }, { value: '2014', text: '崇左' }] }, { value: '25', text: '云南', children: [{ value: '2501', text: '昆明' }, { value: '2502', text: '曲靖' }, { value: '2503', text: '玉溪' }, { value: '2504', text: '保山' }, { value: '2505', text: '昭通' }, { value: '2506', text: '丽江' }, { value: '2507', text: '普洱' }, { value: '2508', text: '临沧' }, { value: '2509', text: '楚雄州' }, { value: '2510', text: '红河州' }, { value: '2511', text: '文山州' }, { value: '2512', text: '西双版纳州' }, { value: '2513', text: '大理州' }, { value: '2514', text: '德宏州' }, { value: '2515', text: '怒江州' }, { value: '2516', text: '迪庆州' }] }, { value: '24', text: '贵州', children: [{ value: '2401', text: '贵阳' }, { value: '2402', text: '六盘水' }, { value: '2403', text: '遵义' }, { value: '2404', text: '安顺' }, { value: '2405', text: '铜仁地区' }, { value: '2406', text: '黔西南州' }, { value: '2407', text: '毕节地区' }, { value: '2408', text: '黔东南州' }, { value: '2409', text: '黔南州' }] }, { value: '06', text: '辽宁', children: [{ value: '0601', text: '沈阳' }, { value: '0602', text: '大连' }, { value: '0603', text: '鞍山' }, { value: '0604', text: '抚顺' }, { value: '0605', text: '本溪' }, { value: '0606', text: '丹东' }, { value: '0607', text: '锦州' }, { value: '0608', text: '营口' }, { value: '0609', text: '阜新' }, { value: '0610', text: '辽阳' }, { value: '0611', text: '盘锦' }, { value: '0612', text: '铁岭' }, { value: '0613', text: '朝阳' }, { value: '0614', text: '葫芦岛' }] }, { value: '21', text: '海南' }, { value: '08', text: '黑龙江', children: [{ value: '0801', text: '哈尔滨' }, { value: '0802', text: '齐齐哈尔' }, { value: '0803', text: '鸡西' }, { value: '0804', text: '鹤岗' }, { value: '0805', text: '双鸭山' }, { value: '0806', text: '大庆' }, { value: '0807', text: '伊春' }, { value: '0808', text: '佳木斯' }, { value: '0809', text: '七台河' }, { value: '0810', text: '牡丹江' }, { value: '0811', text: '黑河' }, { value: '0812', text: '绥化' }, { value: '0813', text: '大兴安岭地区' }] }, { value: '28', text: '甘肃', children: [{ value: '2801', text: '兰州' }, { value: '2802', text: '嘉峪关' }, { value: '2803', text: '金昌' }, { value: '2804', text: '白银' }, { value: '2805', text: '天水' }, { value: '2806', text: '武威' }, { value: '2807', text: '张掖' }, { value: '2808', text: '平凉' }, { value: '2809', text: '酒泉' }, { value: '2810', text: '庆阳' }, { value: '2811', text: '定西' }, { value: '2812', text: '陇南' }, { value: '2813', text: '临夏州' }, { value: '2814', text: '甘南州' }] }, { value: '29', text: '青海', children: [{ value: '2901', text: '西宁' }, { value: '2902', text: '海东地区' }, { value: '2903', text: '海北州' }, { value: '2904', text: '黄南州' }, { value: '2905', text: '海南州' }, { value: '2906', text: '果洛州' }, { value: '2907', text: '玉树州' }, { value: '2908', text: '海西州' }] }, { value: '30', text: '宁夏', children: [{ value: '3001', text: '银川' }, { value: '3002', text: '石嘴山' }, { value: '3003', text: '吴忠' }, { value: '3004', text: '固原' }, { value: '3005', text: '中卫' }] }, { value: '05', text: '内蒙', children: [{ value: '0501', text: '呼和浩特' }, { value: '0502', text: '包头' }, { value: '0503', text: '乌海' }, { value: '0504', text: '赤峰' }, { value: '0505', text: '通辽' }, { value: '0506', text: '鄂尔多斯' }, { value: '0507', text: '呼伦贝尔' }, { value: '0508', text: '巴彦淖尔' }, { value: '0509', text: '乌兰察布' }, { value: '0510', text: '兴安盟' }, { value: '0511', text: '锡林郭勒盟' }, { value: '0512', text: '阿拉善盟' }] }, { value: '31', text: '新疆' }, { value: '26', text: '西藏' }, { value: '33', text: '香港' }, { value: '32', text: '台湾' }, { value: '34', text: '澳门' }, { "value": "81", "text": "亚洲", "children": [{ "value": "8102", "text": "蒙古" }, { "value": "8103", "text": "朝鲜" }, { "value": "8104", "text": "韩国" }, { "value": "8105", "text": "日本" }, { "value": "8106", "text": "菲律宾" }, { "value": "8107", "text": "越南" }, { "value": "8108", "text": "老挝" }, { "value": "8109", "text": "柬埔寨" }, { "value": "8110", "text": "缅甸" }, { "value": "8111", "text": "泰国" }, { "value": "8112", "text": "马来西亚" }, { "value": "8113", "text": "文莱" }, { "value": "8114", "text": "新加坡" }, { "value": "8115", "text": "印度尼西亚" }, { "value": "8116", "text": "东帝汶" }, { "value": "8117", "text": "尼泊尔" }, { "value": "8118", "text": "不丹" }, { "value": "8119", "text": "孟加拉" }, { "value": "8120", "text": "印度" }, { "value": "8121", "text": "巴基斯坦" }, { "value": "8122", "text": "斯里兰卡" }, { "value": "8123", "text": "马尔代夫" }, { "value": "8124", "text": "哈萨克斯坦" }, { "value": "8125", "text": "吉尔吉斯" }, { "value": "8126", "text": "塔吉克斯坦" }, { "value": "8127", "text": "乌兹别克" }, { "value": "8128", "text": "土库曼斯坦" }, { "value": "8129", "text": "阿富汗" }, { "value": "8130", "text": "伊拉克" }, { "value": "8131", "text": "伊朗" }, { "value": "8132", "text": "叙利亚" }, { "value": "8133", "text": "约旦" }, { "value": "8134", "text": "黎巴嫩" }, { "value": "8135", "text": "以色列" }, { "value": "8136", "text": "巴勒斯坦" }, { "value": "8137", "text": "沙特阿拉伯" }, { "value": "8138", "text": "巴林" }, { "value": "8139", "text": "卡塔尔" }, { "value": "8140", "text": "科威特" }, { "value": "8141", "text": "阿联酋" }, { "value": "8142", "text": "阿曼" }, { "value": "8143", "text": "也门" }, { "value": "8144", "text": "格鲁吉亚" }, { "value": "8145", "text": "亚美尼亚" }, { "value": "8146", "text": "阿塞拜疆" }, { "value": "8147", "text": "土耳其" }, { "value": "8148", "text": "塞浦路斯" }] }, { "value": "82", "text": "北美洲", "children": [{ "value": "8202", "text": "加拿大" }, { "value": "8203", "text": "美国" }, { "value": "8204", "text": "墨西哥" }, { "value": "8205", "text": "格陵兰" }, { "value": "8206", "text": "危地马拉" }, { "value": "8207", "text": "伯利兹" }, { "value": "8208", "text": "萨尔瓦多" }, { "value": "8209", "text": "洪都拉斯" }, { "value": "8210", "text": "尼加拉瓜" }, { "value": "8211", "text": "哥斯达黎加" }, { "value": "8212", "text": "巴拿马" }, { "value": "8213", "text": "巴哈马" }, { "value": "8214", "text": "古巴" }, { "value": "8215", "text": "牙买加" }, { "value": "8216", "text": "海地" }, { "value": "8217", "text": "多米尼加" }, { "value": "8218", "text": "安提瓜" }, { "value": "8219", "text": "圣基茨" }, { "value": "8220", "text": "多米尼克" }, { "value": "8221", "text": "圣卢西亚" }, { "value": "8222", "text": "圣文森特" }, { "value": "8223", "text": "格林纳达" }, { "value": "8224", "text": "巴巴多斯" }, { "value": "8225", "text": "特立尼达" }, { "value": "8226", "text": "波多黎各" }, { "value": "8227", "text": "英属维尔京" }, { "value": "8228", "text": "美属维尔京" }, { "value": "8229", "text": "安圭拉" }, { "value": "8230", "text": "蒙特塞拉特" }, { "value": "8231", "text": "瓜德罗普" }, { "value": "8232", "text": "马提尼克" }, { "value": "8233", "text": "安的列斯" }, { "value": "8234", "text": "阿鲁巴" }, { "value": "8235", "text": "特克斯" }, { "value": "8236", "text": "开曼群岛" }, { "value": "8237", "text": "百慕大" }] }, { "value": "85", "text": "南美洲", "children": [{ "value": "8502", "text": "哥伦比亚" }, { "value": "8503", "text": "委内瑞拉" }, { "value": "8504", "text": "圭亚那" }, { "value": "8505", "text": "法属圭亚那" }, { "value": "8506", "text": "苏里南" }, { "value": "8507", "text": "厄瓜多尔" }, { "value": "8508", "text": "秘鲁" }, { "value": "8509", "text": "玻利维亚" }, { "value": "8510", "text": "巴西" }, { "value": "8511", "text": "智利" }, { "value": "8512", "text": "阿根廷" }, { "value": "8513", "text": "乌拉圭" }, { "value": "8514", "text": "巴拉圭" }] }, { "value": "86", "text": "大洋洲", "children": [{ "value": "8602", "text": "澳大利亚" }, { "value": "8603", "text": "新西兰" }, { "value": "8604", "text": "巴布亚" }, { "value": "8605", "text": "所罗门群岛" }, { "value": "8606", "text": "瓦努阿图" }, { "value": "8608", "text": "马绍尔群岛" }, { "value": "8609", "text": "帕劳群岛" }, { "value": "8610", "text": "瑙鲁" }, { "value": "8611", "text": "基里巴斯" }, { "value": "8612", "text": "图瓦卢" }, { "value": "8613", "text": "萨摩亚" }, { "value": "8614", "text": "斐济群岛" }, { "value": "8615", "text": "汤加" }, { "value": "8616", "text": "库克群岛" }, { "value": "8617", "text": "关岛" }, { "value": "8619", "text": "波利尼西亚" }, { "value": "8620", "text": "皮特凯恩岛" }, { "value": "8621", "text": "瓦利斯" }, { "value": "8622", "text": "纽埃" }, { "value": "8623", "text": "托克劳" }, { "value": "8624", "text": "美属萨摩亚" }, { "value": "8625", "text": "北马里亚纳" }, { "value": "8607", "text": "密克罗尼西亚" }, { "value": "8618", "text": "新喀里多尼亚" }] }, { "value": "83", "text": "欧洲", "children": [{ "value": "8302", "text": "芬兰" }, { "value": "8303", "text": "瑞典" }, { "value": "8304", "text": "挪威" }, { "value": "8305", "text": "冰岛" }, { "value": "8306", "text": "丹麦" }, { "value": "8307", "text": "法罗群岛" }, { "value": "8308", "text": "爱沙尼亚" }, { "value": "8309", "text": "拉脱维亚" }, { "value": "8310", "text": "立陶宛" }, { "value": "8311", "text": "白俄罗斯" }, { "value": "8312", "text": "俄罗斯" }, { "value": "8313", "text": "乌克兰" }, { "value": "8314", "text": "摩尔多瓦" }, { "value": "8315", "text": "波兰" }, { "value": "8316", "text": "捷克" }, { "value": "8345", "text": "斯洛伐克" }, { "value": "8317", "text": "匈牙利" }, { "value": "8318", "text": "德国" }, { "value": "8319", "text": "奥地利" }, { "value": "8320", "text": "瑞士" }, { "value": "8321", "text": "列支敦士登" }, { "value": "8322", "text": "英国" }, { "value": "8323", "text": "爱尔兰" }, { "value": "8324", "text": "荷兰" }, { "value": "8325", "text": "比利时" }, { "value": "8326", "text": "卢森堡" }, { "value": "8327", "text": "法国" }, { "value": "8328", "text": "摩纳哥" }, { "value": "8329", "text": "罗马尼亚" }, { "value": "8330", "text": "保加利亚" }, { "value": "8331", "text": "塞尔维亚" }, { "value": "8332", "text": "马其顿" }, { "value": "8333", "text": "阿尔巴尼亚" }, { "value": "8334", "text": "希腊" }, { "value": "8335", "text": "斯洛文尼亚" }, { "value": "8336", "text": "克罗地亚" }, { "value": "8337", "text": "波墨" }, { "value": "8338", "text": "意大利" }, { "value": "8339", "text": "梵蒂冈" }, { "value": "8340", "text": "圣马力诺" }, { "value": "8341", "text": "马耳他" }, { "value": "8342", "text": "西班牙" }, { "value": "8343", "text": "葡萄牙" }, { "value": "8344", "text": "安道尔" }] }, { "value": "84", "text": "非洲", "children": [{ "value": "8402", "text": "埃及" }, { "value": "8403", "text": "利比亚" }, { "value": "8404", "text": "苏丹" }, { "value": "8405", "text": "突尼斯" }, { "value": "8406", "text": "阿尔及利亚" }, { "value": "8407", "text": "摩洛哥" }, { "value": "8408", "text": "亚速尔群岛" }, { "value": "8409", "text": "马德拉群岛" }, { "value": "8410", "text": "埃塞俄比亚" }, { "value": "8411", "text": "厄立特里亚" }, { "value": "8412", "text": "索马里" }, { "value": "8413", "text": "吉布提" }, { "value": "8414", "text": "肯尼亚" }, { "value": "8415", "text": "坦桑尼亚" }, { "value": "8416", "text": "乌干达" }, { "value": "8417", "text": "卢旺达" }, { "value": "8418", "text": "布隆迪" }, { "value": "8419", "text": "塞舌尔" }, { "value": "8420", "text": "乍得" }, { "value": "8421", "text": "中非" }, { "value": "8422", "text": "喀麦隆" }, { "value": "8423", "text": "赤道几内亚" }, { "value": "8424", "text": "加蓬" }, { "value": "8425", "text": "刚果" }, { "value": "8426", "text": "圣普" }, { "value": "8427", "text": "毛里塔尼亚" }, { "value": "8428", "text": "西撒哈拉" }, { "value": "8429", "text": "塞内加尔" }, { "value": "8430", "text": "冈比亚" }, { "value": "8431", "text": "马里" }, { "value": "8432", "text": "布基纳法索" }, { "value": "8433", "text": "几内亚" }, { "value": "8434", "text": "几内亚比绍" }, { "value": "8435", "text": "佛得角" }, { "value": "8436", "text": "塞拉利昂" }, { "value": "8437", "text": "利比里亚" }, { "value": "8438", "text": "科特迪瓦" }, { "value": "8439", "text": "加纳" }, { "value": "8440", "text": "多哥" }, { "value": "8441", "text": "贝宁" }, { "value": "8442", "text": "尼日尔" }, { "value": "8443", "text": "加那利群岛" }, { "value": "8444", "text": "赞比亚" }, { "value": "8445", "text": "安哥拉" }, { "value": "8446", "text": "津巴布韦" }, { "value": "8447", "text": "马拉维" }, { "value": "8448", "text": "莫桑比克" }, { "value": "8449", "text": "博茨瓦纳" }, { "value": "8450", "text": "纳米比亚" }, { "value": "8451", "text": "南非" }, { "value": "8452", "text": "斯威士兰" }, { "value": "8453", "text": "莱索托" }, { "value": "8454", "text": "马达加斯加" }, { "value": "8455", "text": "科摩罗" }, { "value": "8456", "text": "毛里求斯" }, { "value": "8457", "text": "留尼旺" }, { "value": "8458", "text": "圣赫勒拿" }, { "value": "8459", "text": "尼日利亚" }] }];
var joblocations = [{ value: '01', text: '北京' }, { value: '09', text: '上海' }, { value: '1901', text: '广州' }, { value: '1903', text: '深圳' }, { value: '2301', text: '成都' }, { value: '1101', text: '杭州' }, { value: '1001', text: '南京' }, { value: '1005', text: '苏州' }, { value: '02', text: '天津' }, { value: '1701', text: '武汉' }, { value: '0602', text: '大连' }, { value: '1502', text: '青岛' }, { value: '22', text: '重庆' }, { value: '0601', text: '沈阳' }, { value: '1801', text: '长沙' }, { value: '1501', text: '济南' }, { value: '33', text: '香港' }, { value: '1102', text: '宁波' }, { value: '1002', text: '无锡' }, { value: '0801', text: '哈尔滨' }, { value: '1302', text: '厦门' }, { value: '1201', text: '合肥' }, { value: '1301', text: '福州' }, { value: '2701', text: '西安' }, { value: '1917', text: '东莞' }, { value: '1904', text: '珠海' }, { value: '1103', text: '温州' }, { value: '1305', text: '泉州' }, { value: '1601', text: '郑州' }, { value: '0701', text: '长春' }, { value: '1906', text: '佛山' }, { value: '81', text: '亚洲' }, { value: '82', text: '北美洲' }, { value: '83', text: '欧洲' }, { value: '84', text: '非洲' }];
var languages = [{ value: "01", text: "汉语" }, { value: "02", text: "英语" }, { value: "03", text: "日语" }, { value: "04", text: "法语" }, { value: "05", text: "德语" }, { value: "07", text: "俄语" }, { value: "06", text: "朝鲜语" }, { value: "09", text: "西班牙语" }, { value: "14", text: "葡萄牙语" }, { value: "12", text: "阿拉伯语" }, { value: "13", text: "意大利语"}];