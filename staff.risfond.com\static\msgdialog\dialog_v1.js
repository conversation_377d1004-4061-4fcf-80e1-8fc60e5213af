/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
DialogAlert =
  {
    msgBox: $("<div>", { "id": "Rs_MsgBox", "class": "Rs_MsgBox_Layer_Wrap" }),
    //msgTitle：弹出框标题，如：转移操作。
    //msgBody:弹出层的HTML块,如：<div></div>
    //msgShowOverlay:是否遮罩层,true为遮罩，false为不遮罩
    //msgIsClose:是否一定时间之后自动关闭，true为自动关闭，false为不关闭
    //msgdateClose:关闭时间，秒为单位
    //msgJumpUrl:是否存在倒计时跳转，null为不跳转，否则跳转到制定URL
    //msgWidth：弹出层的宽度
    //msgPanelWidth:弹出层内的块的宽度
    //msgPanelHeight:设置内块的高度，默认无高度。
    //msgPosition:是否定位，true为相对浏览器窗体定位，flase为绝对定位。
    //msgDragDrop:是否可以拖动，true可以拖动，false不能拖动
    //msgCloseIsRemove:关闭是否删除，true为删除，false为不删除且隐藏状态，页面有超过一个时，必须使用删除。
    //msgVertical相对文档是否垂直居中,true为是,false为否
    //msgCloseCallBack关闭对话框的回调函数
    //msgCloseCancel关闭按钮取消，关闭事件也取消，因为有的页面有取消按钮,默认false,即默认关闭按钮存在
    //msgOpenCallBack弹窗打开的时候触发事件
    //注意，msgDragDrop（拖拽）设置为true时，msgPosition设置为false（相对浏览器定位）将不可使用
    msgStopSetOutTimeClose: false,
    msgOpen: function (params) {
      var opts =
        {
          msgTitle: setRnssLanguage("提示"),
          msgBody: '<div style="height:100px;background-color:#000000;"></div>',
          msgShowOverlay: true,
          msgIsClose: false,
          msgdateClose: 5,
          msgJumpUrl: null,
          msgWidth: 434,
          msgPanelWidth: 370,
          msgPanelHeight: 0,
          msgPosition: true,
          msgDragDrop: true,
          msgCloseIsRemove: true,
          msgVertical: false,
          msgCloseCancel: false,
          msgStyle: null,
          msgOpenCallBack: function () { },
          msgCloseCallBack: function () { },
          msgOnSelectStart: true
        };
      this.params = $.extend({}, opts, params || {});
      DialogAlert.self = this.params;
      if (DialogAlert.self.msgStyle == null) {
        DialogAlert.msgLoadStyle("/static/msgdialog/msg_v1.css");
      }
      else if (DialogAlert.self.msgStyle.length > 0) {
        DialogAlert.msgLoadStyle(DialogAlert.self.msgStyle);
      }
      
      if ($(".msg-box-layer").length) {
        $(".msg-box-layer").show();
      }
      else {
        var autoCloseHtml = "";
        if (this.params.msgIsClose) {
          autoCloseHtml = '<div class="msg-box-footer" style="z-index:10000"><p>' + setRnssLanguage('弹出框将在{0}秒内自动关闭').format('  <b>5</b>  ') + '</p></div>';//自动关闭的HTML
        }
        var autoPanelHeight = "";

        if (this.params.msgPanelHeight != 0) {
          autoPanelHeight = "height:" + this.params.msgPanelHeight + "px";
        }
        var cancelClosehtml = "";
        if (!this.params.msgCloseCancel) {
          cancelClosehtml = '<a href="javascript:;" class="icon-Msg icon-close close"></a>';
        }
        this.msgBox.html('<div class="msg-box-layer cf" ' + (this.params.msgOnSelectStart === true ? 'onselectstart="return false;"' : '') + ' unselectable="on" id="msg_box_layer"><div class="msg-box-title cf"><h2>' + this.params.msgTitle + '</h2>' + cancelClosehtml + '</div><div class="msg-box-content cf"><div class="msg-box-content-autocenter cf" style="width:' + this.params.msgPanelWidth + 'px;' + autoPanelHeight + '"></div>' + autoCloseHtml + '</div>');
        if (typeof this.params.msgBody == "string") {
          this.msgBox.find('.msg-box-content-autocenter').html(this.params.msgBody);
        }
        else {
          this.msgBox.find('.msg-box-content-autocenter').append(this.params.msgBody);
        }
        this.msgBox.appendTo(document.body);//将消息的HTML加入页面
      }


      DialogAlert.msgConter("#msg_box_layer", this.params.msgWidth, this.params.msgPosition, this.params.msgDragDrop, this.params.msgVertical);//定位到中间
      if (this.params.msgShowOverlay) { this.msgShowOverlay(); }
      if (this.params.msgIsClose) {
        this.msgAutoClose(this.params.msgdateClose, this.params.msgJumpUrl);
      }
      if (this.params.msgDragDrop) {
        $("#msg_box_layer").css({ position: "absolute" }).find(".msg-box-title").css("cursor", "move");
        new dragDrop($(".msg-box-layer"), { trigger: ".msg-box-title" });
      }
      DialogAlert.msgCloseClick();
    },
    
  msgCloseClick: function () {
    $("#msg_box_layer").find(".close").click(function () {
      DialogAlert.msgCloseIsCancel(); $m("isclose", "false");
    });
    },
  msgCloseIsCancel: function () {
      $("#msg_box_layer").hide();
      if ($("#bgWrap").length && !$("#bgWrap").is(":hidden")) {
        DialogAlert.msgHideOverlay();
      }
      if (this.params.msgCloseIsRemove) {
        $(".msg-box-title").remove();
        $("#Rs_MsgBox").remove();
      }
      DialogAlert.self.msgCloseCallBack();
    },
    msgClose: function () {
      if (DialogAlert.self != null && !DialogAlert.self.msgCloseCancel) {
        DialogAlert.msgCloseIsCancel();
      }
    },
    msgStopClose: function () {
      this.msgStopSetOutTimeClose = true;
    },
    msgAutoClose: function (dataClose, dataJumpUrl) {
      $m("isclose", "true");
      $(".msg-box-footer>p>b").html(dataClose);
      var timeNum = dataClose;
      dataClock();
      var timeNameNum;
      function dataClock() {
        if (DialogAlert.self.msgStopSetOutTimeClose) { clearTimeout(timeNameNum); return; }
        if ($m("isclose") == "false") { clearTimeout(timeNameNum); return; }
        if (timeNum <= 0) {
          if (!DialogAlert.msgStopSetOutTimeClose) {
            DialogAlert.msgCloseIsCancel();
          }
          clearTimeout(timeNameNum);
          if (dataJumpUrl != null) {
            window.location.href = dataJumpUrl;
          }
        }
        else {
          $(".msg-box-footer>p>b").html(timeNum--);
          timeNameNum = setTimeout(dataClock, 1000);
        }
      }
    },
    msgLoadStyle: function (objSrc) {
      if ($("link[href='" + objSrc + "']").length == 0) {
        var oHead = document.getElementsByTagName('HEAD').item(0);
        var ocss = document.createElement("link");
        ocss.type = "text/css";
        ocss.rel = "stylesheet";
        ocss.href = objSrc;
        oHead.appendChild(ocss);
      }
    },
    msgConter: function ($el, msgWidth, msgPosition, msgDragDrop, msgVertical) {
      el = $($el);
      if (!msgDragDrop) {
        if (msgPosition) {
          //相对定位
          el.css({
            top: "50%",
            "margin-top": ($.browser.msie && parseInt($.browser.version) <= 6) ? -(el.outerHeight() / 2) + $(document).scrollTop() : -(el.outerHeight() / 2),
            left: "50%",
            display: "block",
            "margin-left": -(msgWidth / 2),
            position: ($.browser.msie && parseInt($.browser.version) <= 6) ? "absolute" : "fixed",
            width: msgWidth
          });
        }
        else {
          $(window).resize(function () {
            DragDropPsoition();
          });
          function DragDropPsoition() {
            //绝对定位
            if ($.browser.msie && parseInt($.browser.version) <= 6) {
              //如果是IE6及以下版本
              var left = ($(window).width() - msgWidth) / 2;
              var h = window.outerHeight || document.documentElement.clientHeight;
              var stlye;
              $(window).scroll(stlye = function () {
                el.css({
                  top: $(document).scrollTop() + (h - el.outerHeight()) / 2 - 5,
                  left: left,
                  width: msgWidth
                });
              });
              stlye();
            }
            else {
              //如果是IE6以上版本
              var left = ($(window).width() - msgWidth) / 2;
              var center = ((msgVertical ? document.body.scrollHeight : $(window).height()) - el.outerHeight()) / 2;
              topHeight = Math.abs(center > 0 ? center : 0) + $(document).scrollTop() || 0;
              el.css({
                "top": topHeight,
                "left": left,
                width: msgWidth,
                position: "absolute"
              });
            }
          }
          DragDropPsoition();
          el.css("display", "block").show();
        }
      }
      else {
        //拖拽时不兼容定位属性，所以当使用拖拽时，取消相对定位，使用绝对定位
        var left, top;
        var w = window.outerWidth || document.documentElement.clientWidth;
        var h = window.outerHeight || document.documentElement.clientHeight;
        left = ($(window).width() - msgWidth) / 2;
        var center = ((msgVertical ? document.body.scrollHeight : $(window).height()) - el.outerHeight()) / 2;
        //top = Math.abs(center > 0 ? center : 0) + $(document).scrollTop()|| 0;
        top = 200 + $(document).scrollTop() || 0;
        if (top < 76) {
          top = 76;
        }
        el.css({ left: left, top: top, width: msgWidth, "display": "block" });
      }
      DialogAlert.self.msgOpenCallBack.call(this, el)
    },
    msgShowOverlay: function () {
      var maxH = document.documentElement.scrollHeight;
      if ($("#bgWrap").length) {
        $("#bgWrap").css({
          "display": "block",
          "opacity": .4
        });
      }
      else {
        $("<div>", { id: "bgWrap" }).css({ "display": "block", "height": maxH }).appendTo($("#Rs_MsgBox"));
        /msie 6\.0/i.test(navigator.userAgent) && $("<iframe>").css({
          "background": "#fff",
          "left": 0,
          "top": 0,
          "opacity": .3,
          "position": "absolute",
          "zIndex": -1,
          "width": "100%",
          "height": maxH
        }).appendTo($("#Rs_MsgBox"));
      }
    },
    msgHideOverlay: function () { $("#bgWrap").css("display", "none"); }
  }
var dragDrop = (function ($) {
  var d = $(document),
    opts = {
      trigger: null,
      dragendCallBack: function () { },
      dragstartCallBack: function () {
      },
      dragCallBack: function () {
      }

    };
  var DrapDrop = function (el, params) {
    this.obj = typeof el == "string" ? $("#" + el) : el;
    this.params = $.extend({}, opts, params || {});
    this.trigger = this.params.trigger ? $(this.params.trigger) : this.obj;
    this.scope = this.obj.offsetParent()[0].tagName == "HTML" ? $("body") : this.obj.offsetParent();
    this.init();
  };
  DrapDrop.prototype = {
    init: function () {
      var self = this;
      this.trigger.mousedown(function (e) {
        e.stopPropagation();
        //var of = self.obj.position(),
        ////var of = self.obj.offset(),
        //  left = of.left,
        //  top = of.top,
        //  //x = e.clientX,
        //  //y = e.clientY,
        //  x = e.pageX,
        //  y = e.pageY,
        //  dx, dy;
        var isP = self.params.trigger ? true : false,
          of = isP ? self.obj.offset() : self.obj.position(),
          left = of.left,
          top = of.top,
          //x = e.clientX,
          //y = e.clientY,
          x = isP ? e.pageX : e.clientX,
          y = isP ? e.pageY : e.clientY,
          dx, dy;
       
        var scopeX = self.scope.outerWidth() - self.obj.outerWidth();
        var scopeY = self.scope.outerHeight() - self.obj.outerHeight();
        //if (scopeY <= 0) { scopeY = document.documentElement.scrollHeight - self.obj.outerHeight(); }
        self.params.dragstartCallBack.call(self.obj);
      
        d.bind("mousemove.drag", function (e) {
          //dx = e.clientx - x + left,
          //dy = e.clienty - y + top;
         
          //if (self.params.trigger == '.fill-out-title' || self.params.trigger == '.msg-box-title') {
          //  dx = e.pageX - x + left;
          //  dy = e.pageY - y + top;
          //} else {
          //  dx = e.pageX - x;
          //  dy = e.pageY - y;
          //}
          //dx = dx < 0 ? 0 : dx;
          //dy = dy < 0 ? 0 : dy;
          //dx = dx > scopeX ? scopeX : dx;
          //dy = dy > scopeY ? scopeY : dy;
          //if (dy < 0) { return; }
          //self.obj.css({ "left": dx, "top": dy }).data("left", dx).data("top", dy).data("height", $(window).height()).data("width", $(window).width());
          //self.params.dragCallBack(dx, dy);
          if (isP) {
            dx = e.pageX - x + left;
            dy = e.pageY - y + top;
          } else {
            dx = e.clientX - x + left;
            dy = e.clientY - y + top;
          }
          dx = dx < 0 ? 0 : dx;
          dy = dy < 0 ? 0 : dy;
          dx = dx > scopeX ? scopeX : dx;
          dy = dy > scopeY ? scopeY : dy;
          self.obj.css({ "left": dx, "top": dy }).data("height", $(window).height()).data("width", $(window).width());
          self.params.dragCallBack(dx, dy);
        }).bind("mouseup.drag", function () {
          self.params.dragendCallBack.call(self.obj);
          d.off("mousemove.drag");
          d.off("mouseup.drag");
        });
        return false;
      });
    }
  }
  return DrapDrop
})(jQuery);
var $m = (function () {
  var cache = {};
  return function (key, value) {
    if (typeof value == "undefined") {
      return cache[key];
    } else {
      if (value) {
        return cache[key] = value;
      } else {
        delete cache[key];
      }
    }
  }
})(jQuery);