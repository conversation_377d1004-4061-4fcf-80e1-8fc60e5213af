/// <reference path="../Scripts/jquery-1.8.2.js" />
/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
(function ($) {
	$.fn.SmsOperate = function (params) {
		var SmsRandom = parseInt(10000000 * Math.random());
		var SmsBox = "template_" + SmsRandom + "_smsbox";
		var SmsBoxCls = "." + SmsBox;
		var smsSearchBoxCls = "shortMessageKeySearch_" + SmsRandom;
		var smsSearchBox = "." + smsSearchBoxCls;
		var opts =
				{
					Box: SmsBoxCls + '.my-short-message #toAreaCtrl',
					Submit: SmsBoxCls + ' .btn-submit',
					WebServiceUrl: '/services/shortmessageactionHandler.ashx?action=sendmessage',
					WordkeySearchUrl: '/services/shortmessageactionhandler.ashx?action=getsuggestions',
					WebSignName: "/services/shortmessageactionhandler.ashx?action=getshortmessagesignature",
					DataNumber: [],
					DataContent: null,
					DataSign: null,
					DataType: 6,//6为其他短信，不设置短信类型，默认其他短信
					ShowCallBack: function () { },
					OnOkSendSMS: DefaultOkSendSMS,//点击确定发送短信事件
					SMSSuccessCallback: DefaultSMSSuccess,//短信发送成功回调
					Regex: /^\+?(?:\d*86)?(1\d{10})$/,
					IsShowTip: true,
				  CustomData: null//用于存储自定义数据
				}
		this.params = $.extend({}, opts, params || {});
		var self = this.params;
		var smssign = SmsBoxCls + ".my-short-message .shortmessage-sign",
			smscontent = SmsBoxCls + ".my-short-message .shortmessage-content",
			mysms = SmsBoxCls + ".my-short-message",
			smstype = SmsBoxCls + " .shortmessage-type",
			DialogBox = "#Rs_MsgBox";
		var modalityBox;
		var $this = this;
		function showSMS() {
			var html = $('<div class="my-short-message">' +
				'<div class="line cf">' +
				'<label>' + setRnssLanguage('类型：') + '</label>' +
				'<div class="selectBox-1 shortmessage-type-box">' +
				'<select class="shortmessage-type">' +
				'<option value="1">' + setRnssLanguage('客户短信') + '</option>' +
				'<option value="2">' + setRnssLanguage('人选短信') + '</option>' +
				'<option value="3">' + setRnssLanguage('同事短信') + '</option>' +
				'<option value="4">' + setRnssLanguage('应聘短信') + '</option>' +
				'<option value="5">' + setRnssLanguage('公司通知') + '</option>' +
				'<option value="6">' + setRnssLanguage('其他短信') + '</option>' +
				'<option value="7">' + setRnssLanguage('客户呼入') + '</option>' +
				'</select>' +
				'</div>' +
				(self.IsShowTip===true?'<span class="shortmessage-example-candidates">' + setRnssLanguage('人选短信示例') + '</span>' +
				'<span class="shortmessage-example-client">' + setRnssLanguage('客户短信示例') + '</span>': '') +
				'</div>' +
				'<div class="line cf">' +
				'<label>' + setRnssLanguage('收件人：') + '</label>' +
				'<div class="modalityboxes">' +
				'</div></div>' +
				'<div class="line shortmessage-textarea-box cf">' +
				'<label>' + setRnssLanguage('内容：') + '</label>' +
				'<textarea cols="80" rows="10" class="textarea-1 shortmessage-content"></textarea>' +
				'</div><div class="line shortmessage-tip-box cf">' +
				'<label class="shortmessage-tip">' + setRnssLanguage('提示：') + '</label>' +
				'<span class="shortmessage-tip">' + setRnssLanguage('短信尾部自带【锐仕方达】签名，请勿在内容中重复输入') + '</span>' +
				'<span class="tips"></span>' +
				'</div><div class="line cf">' +
				'<label>' + setRnssLanguage('签名：') + '</label>' +
				'<input type="text"  class="text-2 shortmessage-sign" />' +
				'</div></div><div class="dialog-footer">' +
				'<div class="my-dialog-form my-short-message">' +
				'<input type="button" class="btn-submit" value="' + setRnssLanguage('发送') + '" />' +
				'<input type="button" class="btn-cancel" value="' + setRnssLanguage('取消') + '" />' +
				'</div></div>');
			DialogAlert.msgOpen({ msgTitle: setRnssLanguage("发送短信"), msgWidth: "580", msgPanelWidth: "570", msgBody: html.addClass(SmsBox) });
			modalityBox = $(".modalityboxes").modalityboxes({ Regex: self.Regex });
			//添加数组到收件人
			$.each(self.DataNumber, function (index, value) {
				modalityBox.dataaddlist(value.info, value.number);
			});
			//绑定关键字搜索
			$(SmsBoxCls + '.my-short-message .addr_inputkey').SMSkeySearch({
				Url: self.WordkeySearchUrl,
				callback: function (name, number) {
					modalityBox.dataaddlist(name, number);
				},
				ElSearchBox: smsSearchBoxCls
			});
			//发送短信界面获取短信预制内容
			var username = "";
			if (self.DataContent == null) {
				username = setRnssLanguage("您好");//默认显示：您好
			}
			else {
				username = self.DataContent;
			}
			$(smscontent).val(username);

			//发送短信界面获取短信预制签名
			if (self.DataSign == null) {
				GetSMSSign(function (data) {
					if (data != null) {
						$(smssign).val(data);
					}
				});
			}
			else {
				$(smssign).val(self.DataSign);
			}

			//发送短信界面获取短信预制短信类型
			$(smstype).val(self.DataType);

			self.ShowCallBack.call(self);//打开完毕短信弹窗和加载完所有预制信息的回调

			//提示示例，短信内容输入长度提示
			$(smscontent).artTxtCount($('.my-short-message .tips'), 300, artTxtcallback);
			function artTxtcallback(state) {
				if (state == "off") {
					$(self.Submit).attr('disabled', true)//不可用
				}
				else {
					$(self.Submit).attr('disabled', false)//可用
				}
			}

			//获取短信签名，如果有缓存，直接去缓存，如果没有缓存，则请求短信签名
			function GetSMSSign(f) {
				if (!$m("smsSign")) {
					$.get(self.WebSignName, {}, function (result) {
						if (result.Status == "1") {
							$m("smsSign", result.Data);
							f.call(this, result.Data);
						}
						else {
							f.call(this, null);
						}
					});
				}
				else {
					f.call(this, $m("smsSign"));
				}
			}
			LoadTips();
		}
		$(".my-short-message .btn-cancel").live("click", function () {
			DialogAlert.msgClose();
		});
		//发送短信按钮
		$(self.Submit).die("click").live("click", function () {
			//如果输入框里还有号码，先执行添加号码事件，然后再提交
			if ($(SmsBoxCls + '.my-short-message .addr_inputkey').val().length > 0) {
				ImportAddToList();
			}
			$(this).data("smstype", smstype).data("smscontent", smscontent).data("smssign", smssign).data("modalityBox", modalityBox).data("self", self);
			self.OnOkSendSMS.call(this);
		});

		function DefaultOkSendSMS() {
			var type = $(smstype).val(), body = $(smscontent).val() + $(smssign).val(), recipientData = new Array();
			var recipient = modalityBox.getalldata();

			if (recipient.length == 0) {
				beautAlert.done(setRnssLanguage("请输入收件人号码"), 'hits'); return false;
			}
			if (body == "") {
				beautAlert.done(setRnssLanguage("请输入短信内容"), 'hits'); return false;
			}
			for (var i = 0; i < recipient.length; i++) {
				var name = recipient[i].contact.replace('|', '||'), number = recipient[i].address;
				if (!self.Regex.test(number)) {
					beautAlert.done(setRnssLanguage("收件人号码不合法,请输入正确的收件人号码"), 'hits');
					return false;
				}
				recipientData.push(name + "|" + number);
			}
			$(this).attr('disabled', true)//不可用
			$.ajaxPost(self.WebServiceUrl, { recipient: recipientData.toString(), type: type, messagebody: body }, function (result) {
				if (result.Status == "1") {
					self.SMSSuccessCallback.call(this);
				}
			}, "json");
		}

		function DefaultSMSSuccess() {
			DialogAlert.msgClose();
			var html = '<div class="fill-out-con cf"><div class="fill-con-title"><span class="icon-fill-out icon-111 r-icon-fill-out r-icon-111"></span><h3>' + setRnssLanguage('短信发送成功') + '</h3></div><div class="fill-con-link cf"><a href="javascript:;" class="again-shortmessage">' + setRnssLanguage('再发一条') + '</a><a href="/profile/mysmsmanage.aspx">' + setRnssLanguage('返回短信管理') + '</a></div></div>';
			DialogAlert.msgOpen({ msgTitle: setRnssLanguage("操作成功"), msgBody: html, msgIsClose: true, msgWidth: "500", msgPanelWidth: "490", msgDragDrop: true });
			return false;
		}

		var operationSMS = {
			SetContent: function (text) {
				$(smscontent).val(text);
			},
			SetSign: function (text) {
				$(smssign).val(text);
			}
		}

		//再发一条
		$(".again-shortmessage").die("click").live("click", function () {
			modalityBox.empty();
			DialogAlert.msgClose();
			DialogAlert.msgStopClose();
			showSMS.call($this);
		});

		function LoadTips() {
			//执行客户短信示例小提示
			referencesms({
				ele: ".my-short-message .shortmessage-example-client",
				title: setRnssLanguage("客户短信示例"),
				width: 500,
				left: -430,
				content: setRnssLanguage("王先生：你好，服务介绍和服务合同已发送到你的邮箱，请注意查收。{0}刘先生：你好，财务总监人选赵晓梅已发送到你的邮箱，请注意查收。{0}赵小姐：你好，关于猎头合作一事的相关资料已发送到你的邮箱，请注意查收。{0}Lucy zhao：你好，请款通知已发送到你的邮箱，望帮忙转交一下财务部门，谢谢！{0}Jack Ma：你好，看到贵公司发布了招聘CEO的职位需求，如需猎头合作，可联系我。{0}刘总：赵小飞已到贵公司入职2周，不知其表现如何，如有需要可随时联系我。{0}Jessic liu：你好，刘晓飞昨天来面试后感觉不错，不知贵司领导这边的反馈是怎样的。").format("<br />")
			});
			//执行人选短信示例小提示
			referencesms({
				ele: ".my-short-message .shortmessage-example-candidates",
				title: setRnssLanguage("人选短信示例"),
				width: 500,
				left: -430,
				content: setRnssLanguage("王先生：你好，很高兴认识，财务总监职位描述已发送到你邮箱，请注意查收。{0}刘先生：你好，今天去北京高兴公司面试总经理职位，不知你感觉如何？{0}赵小姐：你好，北京天力公司希望约你明天下午2点去面试，不知道你是否有时间？{0}Lucy zhao：你好，你已入职上海华龙2月，不知最近工作是否开心，如需帮助可联系我。{0}Jack Ma：你好，面试地点是：北京东四环大成国际中心A2座17A06{0}刘总：很高兴认识，职位信息已发送到你邮箱，如你有朋友考虑这样的机会可联系我。{0}Jessic liu：你好，今天是你入职成都华都集团的第一天，祝你工作愉快。").format("<br />")
			});
		}

		var setting = {
			Show: function () { showSMS(); },
			SetContent: operationSMS.SetContent,
			SetSign: operationSMS.SetSign,
			AddRecipients: function (text, number) {
				modalityBox.dataaddlist(text, number);
			}
		}
		return setting;
	}


	$.fn.SMSkeySearch = function (params) {
		var opts =
				{
					Url: '',
					Left: 10,
					Top: -21,
					width: 300,
					Zindex: 102,
					Type: '.shortmessage-type',
					callback: function (info, number) { },
					Box: ".shortmessage-content",
					DelayKeyTime: 300,
					ElSearchBox:''//主键索引
				}
		this.params = $.extend({}, opts, params || {});
		var self = this.params;
		var smsSearchBox = "."+self.ElSearchBox;
		var smsdelay;
		var keyBox = {
			box: $("<div>", { "class": "ajaxKeySearchName shortMessageKeySearch " + self.ElSearchBox + "" }),
			show: function () {
				$(self.Box).after(keyBox.box);
				$(this).unbind("keyup").bind("keyup", function (e) {
					keyBox.box.css({
						left: $(self.Box).position().left + self.Left,
						top: $(self.Box).position().top + self.Top,
						"z-index": self.Zindex,
						width: self.width
					});
					var $this = $(this);
					var currkey = e.which;
					if (currkey >= 37 && currkey <= 40 || currkey == 13) { keyBox.keyEvent.call(this, e, keyword); return; } else { curr = -1; keyword = $(this).val(); }
					if ($.trim(keyword) == "") {
						keyBox.box.removeClass("open"); return;
					}
					delayEvent = function () {
						clearTimeout(smsdelay);
						smsdelay = setTimeout(postData, self.DelayKeyTime);
					}
					delayEvent();
					function postData() {
						$.post(self.Url, { type: $(self.Type).val(), keywords: $.trim($this.val()) }).done(function (result) {
							var getTable = keyBox.box, html = ['<table cellspacing="0" cellpadding="2" id="st" class="keytb" unselectable="on">'];
							if (result.Status == 0 || result.Data.length == 0) {
								getTable.removeClass("open");
								return;
							}
							getTable.addClass("open");
							for (var i = 0, l = result.Data.length; i < l; i++) {
								html.push('<tr class="ml" data-info="' + $.trim(result.Data[i].Info) + '" data-number="' + $.trim(result.Data[i].Number) + '"  data-name="' + result.Data[i].Name + '" unselectable="on" key="' + i + '"><td class="phone-name"><span class="maxtext" title="' + $.trim(result.Data[i].Info) + '">' + keyBox.replace(result.Data[i].Info, $this.val()) + '</span></td><td class="phone-number"><span class="maxtext" title="' + result.Data[i].Number + '">' + keyBox.replace(result.Data[i].Number, $this.val()) + '</span></td></tr>');
							}
							html.push("</table>");
							html = html.join("");
							getTable.html(html);
							$(smsSearchBox).scrollTop(0);//初始化
						});
					}
				});
				$(".msg-box-layer,#bgWrap").live("click", function (e) {
					if (keyBox.box.hasClass("open")) {
						if ($(e.target).closest(smsSearchBox).size() == 0) {
							keyBox.box.removeClass("open");
						}
					}
				});
			},
			mouseEvent: function () {
				$(".keytb").find("tr").live("mouseenter", function () {
					$(this).addClass("mo").siblings().removeClass("mo");
					curr = $(this).index();
				}).live("mouseleave", function () {
					//$(this).removeClass("mo")
				}).die("click").live("click", function () {
					keyBox.box.removeClass("open");
					$('.my-short-message .addr_inputkey').val('');
					self.callback.call(self, $(this).attr("data-name"), $(this).attr("data-number"));
				});
			},
			replace: function (text, key) {
				var str = text.replace($.trim(key), '<span class="keyword">' + key.substring(0, 20) + '</span>').replace($.trim(key.toUpperCase()), '<span class="keyword">' + key.substring(0, 20).toUpperCase() + '</span>');
				return str;
			},
			keyEvent: function (e) {
				var $this = this;
				var key = e || event;
				var currkey = key.keyCode || key.charCode;
				//up 38 down 40 left 37 right 39 enter 13
				switch (currkey) {
					case 38:
						keyup();
						break;
					case 40:
						keydown();
						break;
					case 13:
						keyenter();
						break;
				}
				function keyup() { updown(false);/*false == current--*/ }
				function keydown() { updown(true);/*true == current++*/ }
				function keyleft() { updown(false); }
				function keyright() { updown(true); }
				function keyenter() {
					if ($(smsSearchBox).hasClass("open") && $(smsSearchBox).find(".keytb .mo").size() > 0) {
						self.callback.call(self, $(".keytb").find(".mo").attr("data-name"), $(".keytb").find(".mo").attr("data-number"));
						keyBox.box.removeClass("open");
					}
				}
				function updown(status) {
					if (!keyBox.box.hasClass("open")) { return false; }
					var tr = keyBox.box.find("tr");
					var totalcount = tr.size();
					status ? curr++ : curr--;
					if (curr < -1) { curr = parseInt(totalcount - 1); }
					if (curr >= totalcount) { curr = -1; }
					if (curr > -1) {
						tr.eq(curr).addClass("mo").siblings().removeClass("mo");
						focusscroll(status);
						$($this).val('');
					}
					else if (curr == -1) {
						if ($(smsSearchBox).hasClass("open") && $(smsSearchBox).find(".keytb .mo").size() > 0) {
							if (status) {
								$(smsSearchBox).scrollTop(0);
							}
							else {
								$(smsSearchBox).scrollTop($(".ml").size() * 25);
							}
						}
						$($this).val(keyword);
					}
				}
				function focusscroll(status) {
					if ($(smsSearchBox).hasClass("open") && $(smsSearchBox).find(".keytb .mo").size() > 0) {
						var index = parseInt($(".mo").attr("key")) || -2;
						var height = $(smsSearchBox).height() || 300;
						var top = $(".mo").position().top || null;
						if (status) {//下翻
							if (top >= height) {
								if (top % 25 != 0) { $(smsSearchBox).scrollTop(Math.ceil(top % 25) * 25); }
								if (top - 25 >= height) {//异常现象滚动到当前所选项
									$(smsSearchBox).scrollTop($(".mo").index() * 25); return;
								}
								$(smsSearchBox).scrollTop($(smsSearchBox).scrollTop() + height / 2);
							}
						} else {//上翻
							if (top < 0) {
								if (top % 25 != 0) { $(smsSearchBox).scrollTop(Math.floor(top % 25) * 25); }
								if (top + 25 < 0) {//异常现象滚动到当前所选项
									$(smsSearchBox).scrollTop($(".mo").index() * 25); return;
								}
								$(smsSearchBox).scrollTop($(smsSearchBox).scrollTop() - height / 2);
							}
						}
					}
				}
			}
		}
		var curr = -1;
		var keyword;
		keyBox.show.call(this);
		keyBox.mouseEvent();
	}

	function referencesms(arguments) {
		opts = $.extend({}, defaultArg, arguments);
		var html = '<div class="reference-wrap" style="z-index:103"><div class="reference-title"><h4>' + opts.title + '：</h4><div class="close"></div></div><div class="reference-con">' + opts.content + '</div><div class="reference-foot"></div></div>',
			style = {
				top: opts.top,
				left: opts.left,
				width: opts.width
			};
		if ($(opts.ele).hasClass("reference-1")) {
			$(opts.ele).live("click", function () {
				var num = $(".reference-wrap").length;
				if (num == 0) {
					$(opts.ele).addClass("reference-close");
				} else if ($(opts.ele).hasClass("reference-close")) {
					$(opts.ele).removeClass("reference-close");
					if (!$(opts.ele).hasClass("reference-close")) { $(".reference-wrap").remove(); return false; }
				}
				showCon($(opts.ele), html, style);
			});
		} else {
			$(opts.ele).live("click", function () {
				showCon($(opts.ele), html, style);
			});
		}
		$(".reference-wrap .close").live("click", function (e) {
			$(this).parents(".reference-wrap").remove();
			if ($(".reference-close").length > 0) {
				$(".reference-close").removeClass("reference-close");
			}
		});
		function showCon(ele, html, style) {
			var topNum = ele.offset().top,
				leftNum = ele.offset().left;
			if ($(".reference-wrap").length > 0) { return false }
			$(".main").append(html);
			$(".reference-wrap").offset({ top: topNum + style.top, left: leftNum + style.left }).css("width", style.width);
		}
	}

	var defaultArg = {
		ele: "",
		title: setRnssLanguage("小提示"),
		content: setRnssLanguage("信息提示内容"),
		top: 30,
		left: -500,
		width: 320
	};
})(jQuery);