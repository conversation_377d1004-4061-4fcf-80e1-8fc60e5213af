/// <reference path="../jquery-1.8.2.min.js" />
/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
/*
//公用筛选条件帮助类
 筛选条件的类型共分为3类
  1，点击后弹出一个对话框（高级选择对话框）
  2，点击后显示下拉列表
  3，点击后显示一个下级div
  4，时间
 */
function commonfilterhelper() {
  var self = this;
  this.submitform = "#selectform";
  //筛选条件
  this.addevent_sctitle = function () {
    $("body").delegate(".sx-panel .sc-title", "click", function (e) {
      var panel = $(this).siblings(".sx-item-panel");
      if (panel.hasClass("selected")) {
        panel.removeClass("selected");
      }
      else {
        panel.addClass("selected");
      }
      documentclick(this);
    });
    $("body").delegate(".sx-panel .sx-item-panel", "click", function (e) {
      e = e || event;
      if (e.target != $(this).find("input[type=submit]").get(0)) {
        e.stopPropagation();
      }
    });
    function documentclick(obj) {
      $(document).click(function (e) {
        e = e || event;
        if (e.target != obj && e.target != $(obj).closest(".sx-panel").get(0)) {
          $(obj).siblings(".sx-item-panel").removeClass("selected");
        }
      });
    }
    $(".list-order-panel .inputorder").change(function () {
      var form = $(this).closest("form");
      form.submit();
    });
  };
  this.submitselectform = function (name, val) {
    form = $(self.submitform);//"#selectform"
    if (typeof (val) == "object") {
      form.find("input[name=" + name + "]").remove();
      for (var i in val) {
        var d = val[i];
        var input = form.find("input[name=" + name + "]");
        //if (input.length > 0) {
        //	input.val(d.Id);
        //}
        //else {
        form.append("<input type='hidden' name='" + name + "' value='" + d.Id + "' />");
        //}
      }
    }
    else {
      var input = form.find("input[name=" + name + "]");
      if (input.length > 0) {
        input.val(val);
      }
      else {
        form.append("<input type='hidden' name='" + name + "' value='" + val + "' />");
      }
    }
    $.each(form.find("input[type=hidden]"), function () {
      if ($(this).val() == "") {
        $(this).remove();
      }
    });
    form.submit();
  };
  //高级选择辅助类
  this.helpSelect = function (el, obj, num, fn) {
    var sd = el.data("data");
    obj.setCheckedNum(num);
    obj.setSureCallBack(fn);
    if (sd) {
      obj.select(sd);
    }
    obj.show();
  };
  //1类筛选条件
  this.event_AdvancedSelect = function (as, obj, num, fn) {
    // obj = $(obj);
    $("body").delegate(obj, "click", function () {
      var dataname = $(this).attr("data-name");
      self.helpSelect($(this), as, num, function (data) {
        if (fn) {
          fn(data);
          return;
        }
        self.submitselectform(dataname, data);
      });
    });
  };
  //关键字
  this.event_KeyWords = function (itxt, btn, wm) {
    itxt = $(itxt);
    btn = $(btn);
    itxt.watermark(wm).keydown(function (e) {
      var e = e || event;
      e.stopPropagation();
      var evt = e.keyCode || e.which || e.charCode;
      if (evt == "13") {
        btn.triggerHandler("click");
        return false;
      }
    });
    btn.click(function () {
      var txt = itxt.val();
      if (!txt || txt == wm) {
        beautAlert.done(setRnssLanguage("关键词不能为空"), "hits", 1000);
        return;
      }
      if (txt.length > 50) {
        beautAlert.done(setRnssLanguage("关键词长度不能超过50"), "hits", 1000);
        return;
      }
      $(this).closest("form").submit();
    });
  };

  //2类筛选条件
  this.event_SelectFilter = function (obj) {
    obj = $(obj);
    obj.SelectFilter({
      showdialogevent: "click",
      callback: function () {
        var status = $(this).attr("data-value");
        var dataname = $(this).closest(".sx-panel").attr("data-name");
        self.submitselectform(dataname, status);
      }
    });
  };

  /**
   * 
   * 应用的旧插件，未来不建议使用，建议使用
   * @param {any} obj
   * @param {any} options
   */
  this.event_DateRangePicker = function (obj, options) {
    obj = $(obj);
    var datefrom = "datefrom", dateto = "dateto";
    if (options) {
      datefrom = options.datefrom;
      dateto = options.dateto;
    }
    obj.DateRangePicker({
      prepend: "body",//".page-content",
      From: {
        Name: datefrom,
        Value: (new Date()).dateformat()
      },
      To: {
        Name: dateto,
        Value: (new Date()).dateformat()
      },
      callbackfunc: function (dates) {
        if (dates && dates.length == 2) {
          var form = $(this).closest("form");
          if (!dates[0] || !dates[1]) {
            form.find("input[name=" + datefrom + "]").remove();
            form.find("input[name=" + dateto + "]").remove();
          }
          form.submit();
        }
      }
    });
    obj.click(function () {//再次点击时隐藏日期选择框
      var d = $(this).data("daterangepickerId");
      if ($("." + d + ":visible").height() > 0) {
        $("." + d + ":visible").find(".daterangepicker-close-box").trigger("click");
      }
    });
  };
    this.event_daterangeselect = function (obj, options, callback) {
        var $obj = $(obj);
        var datefrom = "datefrom", dateto = "dateto";
        if (options) {
            if (options.datefrom) {
                datefrom = options.datefrom;
            }
            if (options.dateto) {
                dateto = options.dateto;
            }
        }
        var $dateFrom = $("<input type='hidden' name='" + datefrom + "'/>");
        $dateFrom.insertAfter($obj);
        var $dateTo = $("<input type='hidden' name='" + dateto + "'/>");
        $dateTo.insertAfter($obj);
        var pluginOptions = {
            output: {
                plain: true,
                start: $dateFrom,
                end: $dateTo,
            },
            submitCallback: function (start, end) {
                var form = $obj.closest("form");
                if (form.length > 0) {
                    if (!start || !end) {
                        form.find("input[name=" + datefrom + "]").remove();
                        form.find("input[name=" + dateto + "]").remove();
                    }

                    form.submit();
                }

                if (callback) {
                    callback(start, end);
                }
            }
        };
        if (options && options.options) {
            pluginOptions = $.extend(pluginOptions, options.options);
        }

        $obj.DateRangeSelect(pluginOptions);
    },

        this.event_daterangeselectdelegate = function (obj, options, callback) {
            $("body").delegate(obj, "click", function () {
                var $obj = $(obj);
                var datefrom = "datefrom", dateto = "dateto";
                if (options) {
                    if (options.datefrom) {
                        datefrom = options.datefrom;
                    }
                    if (options.dateto) {
                        dateto = options.dateto;
                    }
                }
                var $dateFrom = $("<input type='hidden' name='" + datefrom + "'/>");
                $dateFrom.insertAfter($obj);
                var $dateTo = $("<input type='hidden' name='" + dateto + "'/>");
                $dateTo.insertAfter($obj);
                var pluginOptions = {
                    output: {
                        plain: true,
                        start: $dateFrom,
                        end: $dateTo,
                    },
                    submitCallback: function (start, end) {
                        var form = $obj.closest("form");
                        if (form.length > 0) {
                            if (!start || !end) {
                                form.find("input[name=" + datefrom + "]").remove();
                                form.find("input[name=" + dateto + "]").remove();
                            }

                            form.submit();
                        }

                        if (callback) {
                            callback(start, end);
                        }
                    }
                };
                if (options && options.options) {
                    pluginOptions = $.extend(pluginOptions, options.options);
                }

                $obj.DateRangeSelect(pluginOptions);

            });

        },
        this.event_selTeam = function (id, obj, userKey) {
        $("body").delegate(obj, "click", function () {
            var completeTeam = $(id).completeTeam({
                type: 0,
                userKey: userKey,
                maxCount: 30,
                width: 180,
                append: id,
            });
        });
        },
    this.addevent_keyword = function (keyobj, btnobj) {
      keyobj = keyobj || '#keywords';
      btnobj = btnobj || '#btnkeywords';
      $(keyobj).keydown(function (e) {
        var e = e || event;
        e.stopPropagation();
        var evt = e.keyCode || e.which || e.charCode;
        if (evt == "13") {
          $(btnobj).triggerHandler("click");
          return false;
        }
      }).focus();
      $(btnobj).click(function () {
        var txt = $(keyobj).val();
        if (!txt || txt.length == 0) {
          r_Layout_BeautAlert.done("关键词不能为空");
          return;
        }
        if (txt.length > 50) {
          r_Layout_BeautAlert.done("关键词长度不能超过50");
          return;
        }
        $(this).closest("form").submit();
      });
    };
}