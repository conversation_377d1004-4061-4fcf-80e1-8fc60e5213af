var WebIMAPI = {
  conn: undefined,
  db: undefined,
  init: function (options) {
    WebIM.config = config;
    this.initEmojiConf();
    // 主要是为了测试http:///undefined是不是和这个地方有关系，liuht，2019-12-27
    //if (!WebIM.config.xmppURL) {
    //  console.error('http:///undefined/: WebIM.config.xmppURL is undefined');
    //}
    this.conn = new WebIM.connection({
      appKey: WebIM.config.appkey,
      isMultiLoginSessions: WebIM.config.isMultiLoginSessions,
      https: typeof WebIM.config.https === 'boolean' ? WebIM.config.https : location.protocol === 'https:',
      url: WebIM.config.xmppURL,
      heartBeatWait: WebIM.config.heartBeatWait,
      autoReconnectNumMax: WebIM.config.autoReconnectNumMax,
      autoReconnectInterval: WebIM.config.autoReconnectInterval,
      apiUrl: WebIM.config.apiURL,
      isHttpDNS: true,//动态获取这个用户应该连接的地址
      useOwnUploadFun: true,
      isAutoLogin: true
    });

    this.setListener(options);
  },
  setListener: function (options) {
    var self = this;
    this.conn.listen({
      onOpened: function (message) {          
        // 如果isAutoLogin设置为false，那么必须手动设置上线，否则无法收消息
        // 手动上线指的是调用conn.setPresence(); 如果conn初始化时已将isAutoLogin设置为true
        // 则无需调用conn.setPresence();   
        console.log('连接成功')
        options && typeof options.callbackOnOpened === 'function' && options.callbackOnOpened(message);
        
      },//连接成功回调
      onClosed: function (message) {  
        console.log('连接关闭拉，也就是掉线拉');
        options && typeof options.callbackOnClosed === 'function' && options.callbackOnClosed(message);
      },//连接关闭回调
      onTextMessage: function (message) {
        options && typeof options.callbackOnTextMessage === 'function' && options.callbackOnTextMessage(message);
      },//收到文本消息
      onEmojiMessage: function (message) {     
        options && typeof options.callbackOnEmojiMessage === 'function' && options.callbackOnEmojiMessage(message);
      },//收到表情消息
      onCustomMessage: function (message) {
        options && typeof options.callbackOnCustomMessage === 'function' && options.callbackOnCustomMessage(message);
      },//收到自定义消息
      onPictureMessage: function (message) {
        options && typeof options.callbackOnPictureMessage === 'function' && options.callbackOnPictureMessage(message);
      }, //收到图片消息
      onCmdMessage: function (message) { },     //收到命令消息
      onAudioMessage: function (message) {
        options && typeof options.callbackOnAudioMessage === 'function' && options.callbackOnAudioMessage(message);
      },   //收到音频消息
      onLocationMessage: function (message) { },//收到位置消息
      onFileMessage: function (message) {    //收到文件消息
        options && typeof options.callbackOnTextMessage === 'function' && options.callbackOnFileMessage(message);
      },//收到文件信息
      onVideoMessage: function (message) {
        options && typeof options.callbackOnVideoMessage === 'function' && options.callbackOnVideoMessage(message);
      },   //收到视频消息
      onPresence: function (data) {       //处理“广播”或“发布-订阅”消息，如联系人订阅请求、处理群组、聊天室被踢解散等消息
        if (data.type === "leaveGroup") {
          options && typeof options.callbackOnLeaveGroup === 'function' && options.callbackOnLeaveGroup(data);
        }
      },
      onRoster: function (message) {         //处理好友申请
      },
      onInviteMessage: function (message) { },  //处理群组邀请
      onOnline: function (message) {
        console.log('网络链接成功拉');
        console.log(message);
      },                  //本机网络连接成功
      onOffline: function (message) {
        console.log('掉线拉');
        console.log(message);
      },                 //本机网络掉线
      onError: function (message) {
        console.log('onerror 回调');
        console.log(message);
        options && typeof options.callbackOnError === 'function' && options.callbackOnError(message);
      },          //失败回调
      onBlacklistUpdate: function (list) {       //黑名单变动
        // 查询黑名单，将好友拉黑，将好友从黑名单移除都会回调这个函数，list则是黑名单现有的所有好友信息
      },
      onReceivedMessage: function (message) { },    //收到消息送达服务器回执
      onDeliveredMessage: function (message) { },   //收到消息送达客户端回执
      onReadMessage: function (message) { 
        options && typeof options.callbackOnReadMessage === 'function' && options.callbackOnReadMessage(message);
      },//收到消息已读回执
      onChannelMessage: function (message) {
        options && typeof options.callbackOnChannelMessage === 'function' && options.callbackOnChannelMessage(message);
      }, //接收会话已读回执-对方点击进入聊天面板时触发
      onCreateGroup: function (message) { },        //创建群组成功回执（需调用createGroupNew）
      onMutedMessage: function (message) { }        //如果用户在A群组被禁言，在A群发消息会走这个回调并且消息不会传递给群其它成员
    });
  },
  initEmojiConf: function () {
    var emojiPackage = this.getEmojiConf();
    var arrayLikeEmojiObj = {};
    var collectFunc = function (elem) {
      var key = elem.name;
      var value = undefined;
      if (elem.gif) {
        value = elem.gif;
      } else {
        value = elem.img;
      }
      arrayLikeEmojiObj[key] = value;
    }
    emojiPackage.default.forEach(function (elem) {
      collectFunc(elem);
    });
    emojiPackage.tuzki.forEach(function (elem) {
      collectFunc(elem);
    });
    Easemob.im.Emoji = Easemob.im.EMOTIONS = WebIM.Emoji = {
      //path: '/images/chat/emoji/',  /*表情包路径*/
      path: '/images/chat/emoji/new/', /*表情包路径*/
      map: arrayLikeEmojiObj,
    };
  },
  exec: function (func) {
    if (typeof func === 'function') {
      func();
    }
  },

  getFriendsList: function (callback) {
    this.conn.getRoster({
      success: function (roster) {
      },
      error: function (e) {
        console.error(e);
        callback(false, undefined);
      }
    });
  },
  getToken: function () {
    return WebIM.utils.getCookie()['webim_user'];
  },
  setToken: function (token) {
    WebIM.utils.setCookie('webim_user', token, 1);// 失效时间应该和前端保持一致，其实1天也行，1天rnss登录状态肯定就失效了，其有效时间应该长于对应时间，但是应该测试，cookie还没到期，换了一个新的token，有效时间是否会重置
  },
  getTokenFromServer: function (userId, pwd, callback) {
    var self = this;
    var options = {
      apiUrl: WebIM.config.apiURL,
      user: String(userId),
      pwd: String(pwd),
      appKey: WebIM.config.appkey,
      success: function (data) {
        var token = data.access_token;
        self.conn.token = token;
        self.setToken(token);
        if (typeof callback === 'function') {
          callback(true, token);
        }
      },
      error: function (e) {
        console.error(e);
        callback(false, undefined);
      }
    };

    this.conn.open(options);
  },
  login: function (token, userId) {
    if (!token) {
      token = this.getToken();
    }
    var options = {
      apiUrl: WebIM.config.apiURL,
      user: userId,
      accessToken: token,
      appKey: WebIM.config.appkey,
    };
    this.conn.open(options);
  },
  // 获取好友列表
  getFriendList: function (callback) {
    this.conn.getRoster({
      success: function (resp) {
        typeof callback === 'function' && callback(true, resp);
      },
      error: function (e) {
        console.error(e);
        typeof callback === 'function' && callback(false, []);
      },
    });
  },
  // 获取群组列表
  getGroupList: function (callback) {
    var options = {
      success: function (resp) {
        typeof callback === 'function' && callback(true, resp.data);
      },
      error: function (e) {
        console.error(e);
        callback(false, []);
        typeof callback === 'function' && callback(true, []);
      }
    };
    this.conn.getGroup(options);
  },
  getGroupMember: function () {

  },
  getGroupInfo: function (gid, callback) {
    var options = {
      groupId: gid,                                //群组id
      success: function (resp) {
      },
      error: function (e) {
        console.error(e);
      }
    };
    this.conn.getGroupInfo(options);
  },
  // 解散群聊
  dissolveGroup: function (gid, callback) {
    var option = {
      groupId: gid,
      success: function (data) {
        callback(data);
      }
    };
    this.conn.dissolveGroup(option);
  },
  // 退出群聊
  quitGroup: function (gid, callback) {
    var option = {
      groupId: gid,
      success: function (resp) {
        callback(resp);
      },
      error: function (e) {
        callback(e);
      }
    };
    this.conn.quitGroup(option);
  },
  modifyGroup: function (gid, name, desc, callback) {
    var option = {
      groupId: gid,
      groupName: name,
      description: desc,
      success: function (resp) {
        callback(resp);
      }
    };
    this.conn.modifyGroup(option);
  },
  createGroupNew: function (data, func) {
    var options = {
      data: {
        groupname: data.groupname,                    // 群组名
        desc: data.desc,                          // 群组描述
        members: data.members,            // 用户名组成的数组
        public: data.public,                         // pub等于true时，创建为公开群
        approval: data.approval,                  // approval为true，加群需审批，为false时加群无需审批
        allowinvites: data.allowinvites,
      },
      success: function (resp) {
        func(resp);
      },
      error: function (resp) {
        func(resp);
      }
    };
    this.conn.createGroupNew(options);
  },
  // 发送文本消息
  sendChatMsg: function (type, data, toId, ext, func) {
    var id = this.conn.getUniqueId(); //生成本地消息id
    var msg = new WebIM.message('txt', id); //创建文本消息
    msg.set({
      ext: ext,// 扩展字段
      msg: data,//消息内容
      to: toId,// 接收消息对象(群组id)
      roomType: false,// 群聊类型，true时为聊天室，false时为群组
      chatType: 'singleChat',
      success: function (data, msgId) {//msgId为环信唯一标识
        func(msgId);
      },// 对成功的相关定义，sdk会将消息id登记到日志进行备份处理
      fail: function (e) {
      }// 对失败的相关定义，sdk会将消息id登记到日志进行备份处理
    })
    type === 'group' && msg.setGroup('groupchat');
    this.conn.send(msg.body);
  },
  //发送图片
  sendClipboardImg: function (toId, blob, chatType, option, ext, func) {
    //clipboardItem是通过e.clipboardData.items得到的单个item，然后再通过elem.getAsFile()取得blob，注意此item一定是图片格式的，也就是说，使用前，一定要使用/^image\/\w+$/正则来判断其type
    var url = window.URL.createObjectURL(blob);
    var id = this.conn.getUniqueId();             // 生成本地消息id
    var msg = new WebIM.message('img', id);  // 创建图片消息
    msg.set({
      apiUrl: WebIM.config.apiURL,
      file: { data: blob, url: url },
      to: toId,                      // 接收消息对象
      roomType: false,
      ext: ext,
      width: option.width,
      height: option.height,
      onFileUploadError: function (error) {
        // console.log('error');
      },
      onFileUploadComplete: function (resp, msgId) {
        //func(toId, id, resp);
      },
      success: function (id, msgId) {
        func(toId, id, msgId);
        //console.log(id);
      }
    });
    if (chatType === 'groupchat') {
      msg.setGroup('groupchat');
    }
    this.conn.send(msg.body);
  },
  //发送文件
  sendFile: function (toId, input, chatType, ext, func) {
    var id = this.conn.getUniqueId();                   // 生成本地消息id
    var msg = new WebIM.message('file', id);        // 创建文件消息
    var file = WebIM.utils.getFileUrl(input);      // 将文件转化为二进制文件
    var allowType = {
      'jpg': true,
      'gif': true,
      'png': true,
      'bmp': true,
      'zip': true,
      'txt': true,
      'doc': true,
      'pdf': true
    };
    if (true) {
      ext.file_length = file.data.size;
//    if (file.filetype.toLowerCase() in allowType) {
      var option = {
        apiUrl: WebIM.config.apiURL,
        ext: ext,
        file: file,
        to: toId,                       // 接收消息对象
        roomType: false,
        onFileUploadError: function (e) {      // 消息上传失败
          console.log(e);
        },
        onFileUploadComplete: function (resp) {   // 消息上传成功
          //func(file, toId, id, resp);
        },
        success: function (id, msgId) { // 消息发送成功
          func(file, toId, id, msgId);
        },
        flashUpload: WebIM.flashUpload
      };
      msg.set(option);
      if (chatType === 'groupchat') {
        msg.setGroup('groupchat');
      }
      this.conn.send(msg.body);
    }
  },
  //发送消息已读回执
  //在聊天页面，接收到消息时发送消息已读回执
  sendReadMessage: function (msgId, fromId, func) {
    var id = this.conn.getUniqueId();            // 生成本地消息id
    var msg = new WebIM.message('read', id); // 创建文本消息
    let option = {
      apiUrl: WebIM.config.apiUrl,
      chatType: 'singleChat', // 会话类型，设置为单聊。
      type: 'read', // 消息类型。
      id: msgId,//消息id
      to: fromId, // 接收消息对象的用户 ID。
      success: function (data, msgId) {
        func(msgId);
      },// 对成功的相关定义，sdk会将消息id登记到日志进行备份处理
      fail: function (e) {
      }// 对失败的相关定义，sdk会将消息id登记到日志进行备份处理
    };
    msg.set(option)
    this.conn.send(msg.body);
  },
  //接收方发送会话已读回执-消息接收方进入会话页面，会话已读回执
  sendReadChannelMessage: function (fromId) {
    var id = this.conn.getUniqueId();            // 生成本地消息id
    var msg = new WebIM.message('channel', id); // 创建文本消息
    let option = {
      apiUrl: WebIM.config.apiUrl,
      chatType: 'singleChat', // 会话类型，设置为单聊。
      type: 'channel', // 消息类型。
      to: fromId, // 接收消息对象的用户 ID。
      success: function (data, msgId) {
        func(msgId);
      },// 对成功的相关定义，sdk会将消息id登记到日志进行备份处理
      fail: function (e) {
      }// 对失败的相关定义，sdk会将消息id登记到日志进行备份处理
    };
    msg.set(option)
    this.conn.send(msg.body);
  },
  listGroups: function (func) {
    var limit = 1000000;
    var options = {
      limit: limit,                                            // 预期每页获取的记录数
      cursor: null,                                          // 游标
      success: function (resp) {
        func(resp);
      },
      error: function (e) { }
    };
    this.conn.listGroups(options);
  },
  //加入分组
  joinGroup: function (groupId, func) {
    var options = {
      groupId: groupId,                              // 群组ID
      success: function (resp) {
        func(resp);
      },
      error: function (e) {
        if (e.type == 17) {
          console.log("您已经在这个群组里了");
        }
      }
    };
    this.conn.joinGroup(options);
  },
  inviteToGroup: function (groupId, users, func) {
    var option = {
      users: users,
      groupId: groupId,
      success: function (resp) {
        func(resp);
      },
      error: function () {

      },
    };
    this.conn.inviteToGroup(option);
  },
  listGroupMember: function (groupId, pageObj, func) {
    var pageNum = pageObj.pageNum,
    pageSize = pageObj.pageSize;
    var options = {
      pageNum: pageNum,                                               // 页码
      pageSize: pageSize,                                             // 预期每页获取的记录数
      groupId: groupId,
      success: function (resp) {
        func(resp);
      },
      error: function (e) {
        console.error(e);
      }
    };
    this.conn.listGroupMember(options);
  },
  setAdmin: function (groupid, userid, func) {
    var options = {
      groupId: groupid,            // 群组id
      username: userid,              // 用户名
      success: function (resp) {
        func(resp);
      },
      error: function (e) {
        console.error(e);
      }
    };
    this.conn.setAdmin(options);
  },
  removeAdmin: function (groupId, userId, func) {
    var options = {
      groupId: groupId,             // 群组id
      username: userId,               // 用户名
      success: function (resp) {
        func(resp);
      },
      error: function (e) { }
    };
    this.conn.removeAdmin(options);
  },
  getGroupAdmin: function (groupid, func) {
    var options = {
      groupId: groupid,                 // 群组id
      success: function (resp) {
        func(resp);
      },
      error: function (e) { }
    };
    this.conn.getGroupAdmin(options);
  },
  // 移除单个群成员
  removeSingleGroupMember: function (groupId, userId, func) {
    var options = {
      groupId: groupId,
      username: userId,
      success: function (resp) {
        func(groupId, userId);
      },
      error: function (e) {
        console.error(e);
      },
    };
    this.conn.removeSingleGroupMember(options);
  },
  // 转让群主
  transferGroupOwner: function (groupId, userId, func) {
    var options = {
      groupId: groupId,
      username: userId,
      success: function (resp) {
        func(resp, userId);
      },
      error: function (e) {
        console.error(e);
      },
    };
    this.conn.transferGroupOwner(options);

  },
  getEmojiConf: function () {
    var defaultEmoji = [
      {
        name: '[):]',
        txt: '微笑',
        txtimg: '🙂',
        img: 'ee_1.png',
      },
      {
        name: '[:D]',
        txt: '咧嘴',
        txtimg: '😃',
        img: 'ee_2.png',
      },
      {
        name: '[;)]',
        txt: '眨眼',
        txtimg: '😉',
        img: 'ee_3.png',
      },
      {
        name: '[:-o]',
        txt: '流汗',
        txtimg: '😅',
        img: 'ee_4.png',
      },
      {
        name: '[:p]',
        txt: '调皮',
        txtimg: '😋',
        img: 'ee_5.png',
      },
      {
        name: '[(H)]',
        txt: '得意',
        txtimg: '😎',
        img: 'ee_6.png',
      },
      {
        name: '[:@]',
        txt: '生气',
        txtimg: '😡',
        img: 'ee_7.png',
      },
      {
        name: '[:s]',
        txt: '饥饿',
        txtimg: '🥴',
        img: 'ee_8.png',
      },
      {
        name: '[:$]',
        txt: '囧',
        txtimg: '😣',
        img: 'ee_9.png',
      },
      {
        name: '[:(]',
        txt: '难受',
        txtimg: '🙁',
        img: 'ee_10.png',
      },
      {
        name: "[:'(]",
        txt: '大哭',
        txtimg: '😭',
        img: 'ee_11.png',
      },
      {
        name: '[<o)]',
        txt: '冷汗',
        txtimg: '🥵',
        img: 'ee_12.png',
      },
      {
        name: '[(a)]',
        txt: '大笑',
        txtimg: '😆',
        img: 'ee_13.png',
      },
      {
        name: '[8o|]',
        txt: '坏笑',
        txtimg: '😁',
        img: 'ee_14.png',
      },
      {
        name: '[8-|]',
        txt: '阴险',
        txtimg: '🤨',
        img: 'ee_15.png',
      },
      {
        name: '[+o(]',
        txt: '吐',
        txtimg: '🤮',
        img: 'ee_16.png',
      },
      {
        name: '[|-)]',
        txt: '困',
        txtimg: '😪',
        img: 'ee_17.png',
      },
      {
        name: '[:|]',
        txt: '呆',
        txtimg: '😐',
        img: 'ee_18.png',
      },
      {
        name: '[*-)]',
        txt: '傲慢',
        txtimg: '🤔',
        img: 'ee_19.png',
      },
      {
        name: '[:-#]',
        txt: '闭嘴',
        txtimg: '🤫',
        img: 'ee_20.png',
      },
      {
        name: '[^o)]',
        txt: '偷笑',
        txtimg: '🥱',
        img: 'ee_21.png',
      },
      {
        name: '[:-*]',
        txt: '委屈',
        txtimg: '😒',
        img: 'ee_22.png',
      },
      {
        name: '[8-)]',
        txt: '囧',
        txtimg: '😧',
        img: 'ee_23.png',
      },
      {
        name: '[(|)]',
        txt: '爱心',
        txtimg: '💗',
        img: 'ee_24.png',
      },
      {
        name: '[(u)]',
        txt: '心碎',
        txtimg: '💔',
        img: 'ee_25.png',
      },
      {
        name: '[(S)]',
        txt: '月亮',
        txtimg: '🌜',
        img: 'ee_26.png',
      },
      {
        name: '[(*)]',
        txt: '星星',
        txtimg: '⭐',
        img: 'ee_27.png',
      },
      {
        name: '[(#)]',
        txt: '太阳',
        txtimg: '🌞',
        img: 'ee_28.png',
      },
      {
        name: '[(R)]',
        txt: '彩虹',
        txtimg: '🌈',
        img: 'ee_29.png',
      },
      {
        name: '[({)]',
        txt: '色',
        txtimg: '😍',
        img: 'ee_30.png',
      },
      {
        name: '[(})]',
        txt: '亲亲',
        txtimg: '😚',
        img: 'ee_31.png',
      },
      {
        name: '[(k)]', 
        txt: '嘴唇',
        txtimg: '👄',
        img: 'ee_32.png',
      },
      {
        name: '[(F)]',
        txt: '玫瑰',
        txtimg: '🌹',
        img: 'ee_33.png',
      },
      {
        name: '[(W)]',
        txt: '凋谢',
        txtimg: '🥀',
        img: 'ee_34.png',
      },
      {
        name: '[(D)]',
        txt: '点赞',
        txtimg: '👍',
        img: 'ee_35.png',
      },
    ];
    var tuzkiEmoji = [
      {
        name: '[哭]',
        img: 'tuzki_1_cover.png',
        gif: 'tuzki_1.gif',
      },
      {
        name: '[得瑟]',
        img: 'tuzki_2_cover.png',
        gif: 'tuzki_2.gif',
      },
      {
        name: '[跳舞]',
        img: 'tuzki_3_cover.png',
        gif: 'tuzki_3.gif',
      },
      {
        name: '[耸肩]',
        img: 'tuzki_4_cover.png',
        gif: 'tuzki_4.gif',
      },
      {
        name: '[bye]',
        img: 'tuzki_5_cover.png',
        gif: 'tuzki_5.gif',
      },
      {
        name: '[升天]',
        img: 'tuzki_6_cover.png',
        gif: 'tuzki_6.gif',
      },
      {
        name: '[痴迷]',
        img: 'tuzki_7_cover.png',
        gif: 'tuzki_7.gif',
      },
      {
        name: '[晚安]',
        img: 'tuzki_8_cover.png',
        gif: 'tuzki_8.gif',
      },
      {
        name: '[爱你]',
        img: 'tuzki_9_cover.png',
        gif: 'tuzki_9.gif',
      },
      {
        name: '[摆脱]',
        img: 'tuzki_10_cover.png',
        gif: 'tuzki_10.gif',
      },
      {
        name: '[no]',
        img: 'tuzki_11_cover.png',
        gif: 'tuzki_11.gif',
      },
      {
        name: '[生日快乐]',
        img: 'tuzki_12_cover.png',
        gif: 'tuzki_12.gif',
      },
      {
        name: '[哼]',
        img: 'tuzki_13_cover.png',
        gif: 'tuzki_13.gif',
      },
      {
        name: '[抓狂]',
        img: 'tuzki_14_cover.png',
        gif: 'tuzki_14.gif',
      },
      {
        name: '[扭一扭]',
        img: 'tuzki_15_cover.png',
        gif: 'tuzki_15.gif',
      },
      {
        name: '[尬舞]',
        img: 'tuzki_16_cover.png',
        gif: 'tuzki_16.gif',
      },
    ];

    return {
      default: defaultEmoji,
      tuzki: tuzkiEmoji,
    };
  },
};
