/* 兼容小屏幕样式问题 */
@media (max-width: 1440px) and (min-width: 1200px) {
    #scheduleReminderDom, .workSummaryOfYesterday {
        right: 6% !important;
    }
    .ZhiNengXiaoDa_job_body {
        max-height: 458px !important;
    }
    #ZhiNengXiaoDa .modal-content {
        margin-top: 180px !important;
    }
}
@media (min-width:1100px) and (max-width:1200px) {
    .ZhiNengXiaoDa_job_body {
        max-height: 366px !important;
    }
    #ZhiNengXiaoDa .modal-content {
        margin-top: 175px !important;
    }
}

@media (min-width:1440px) and (max-width:1920px) {

}

/* 2020-12-18 智能小达  start */
.xiaodajob_status {
    padding: 2px 6px;
    background: #12C77D;
    color: #FFF;
    border-radius: 2px !important;
    font-size: 12px;
    margin-left: 10px;

}
.xiaodaJobType_title {
 display:flex;
 align-items:center
}
.xiaodajob_deliveryRate {
    padding: 1px 6px;
    background: #FFF;
    color: #0EB90E;
    border-radius: 2px !important;
    font-size: 12px;
    margin-left: 10px;
    border: 1px solid #0EB90E;
}
.xiaoda_type2_bottom {
    display: flex;
}

    .xiaoda_type2_bottom .job-intro-sep {
        background-color: #DDDDDD;
        height: 12px;
        width: 1px;
        position: relative;
        top: 4px;
        margin-left: 13px;
        margin-right: 13px;
    }

.ZhiNengXiaoDa_kong_table {
    padding-top: 48px;
    padding-bottom: 48px;
    display: flex;
    flex-flow: column;
    align-items: center;
    border: 1px solid #DDD;
    margin-bottom: 12px;
    border-radius: 4px!important;
}

.ZhiNengXiaoDa_kong_icon {
    width: 70px;
    height: 70px;
}

.ZhiNengXiaoDa_kong_tips {
    color: #999999;
    font-size: 14px;
    margin-top: 14px;
}

#ZhiNengXiaoDa {
    /*font-family: PingFangSC-Medium, PingFang SC !important;*/
    /* 系统大鲨鱼是9997以上的层级 最后显示 所以层级降低*/
    z-index:8888;
}
    #ZhiNengXiaoDa .ZhiNengXiaoDa_modal-body {
        position: relative;
        padding: 24px;
    }
#examsDialog, #TrainDialog {
  /*font-family: PingFangSC-Medium, PingFang SC !important;*/
  /* 系统大鲨鱼是9997以上的层级 最后显示 所以层级降低*/
  z-index: 8887;
}

.ZhiNengXiaoDa_modal-header {
    position: absolute;
    top: -150px;
    left: 0;
    width: 100%;
}

.zhiNengXiaoDa_hea_tips {
    background: #3A64F6;
    color: #fff;
    width: 100%;
    position: absolute;
    bottom: 60px;
    padding-left: 219px;
    height: 60px;
    font-size: 18px;
    line-height: 60px;
    font-weight: 400;
    border-top-left-radius: 8px !important;
    border-top-right-radius: 8px !important;
    display:flex;
    justify-content: space-between;
    padding-right: 24px;
}

    .zhiNengXiaoDa_hea_tips svg {
        cursor: pointer;
    }
    .zhiNengXiaoDa_hea_tips svg:hover {
        
    }

    .ZhiNengXiaoDa_hea_body {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 17px;
    }

.ZhiNengXiaoDa_hea_img {
    width: 173px;
    height: 195px;
    /*margin-left: 18px;*/
    background-image: url('../../images/opt/header_icon.png');
    background-size: 173px 195px;
    /*-webkit-animation: ZhiNengXiaoDa_hea_img 4s infinite;
    -moz-animation: ZhiNengXiaoDa_hea_img 4s infinite;
    -o-animation: ZhiNengXiaoDa_hea_img 4s infinite;
    -ms-animation: ZhiNengXiaoDa_hea_img 4s infinite;
    animation: ZhiNengXiaoDa_hea_img 4s infinite;*/
    border: none;
    z-index: 1;
}

@keyframes ZhiNengXiaoDa_hea_img {
    0% {
        background-image: url('../../images/opt/icon_201218_xd_1.png');
        border: none;
    }

    25% {
        background-image: url('../../images/opt/icon_201218_xd_2.png');
        border: none;
    }

    50% {
        background-image: url('../../images/opt/icon_201218_xd_1.png');
        border: none;
    }

    75% {
        background-image: url('../../images/opt/icon_201218_xd_2.png');
        border: none;
    }
}

@keyframes ZhiNengXiaoDa_hea_tips {
    0% {
        opacity: 1
    }

    25% {
        opacity: 1
    }

    50% {
        opacity: 1
    }

    75% {
        opacity: 1
    }
}

#jobDetailPanel.adda .job-detail-panel {
    /*z-index: 11111 !important;*/
}

.ZhiNengXiaoDa_hea_tips {
    width: 720px;
    background: #FFFFFF;
    border-radius: 38px !important;
    padding: 14px 34px;
    margin-bottom: 41px;
    -webkit-animation: ZhiNengXiaoDa_hea_tips 4s infinite;
    -moz-animation: ZhiNengXiaoDa_hea_tips 4s infinite;
    -o-animation: ZhiNengXiaoDa_hea_tips 4s infinite;
    -ms-animation: ZhiNengXiaoDa_hea_tips 4s infinite;
    animation: ZhiNengXiaoDa_hea_tips 4s infinite;
}
.ZhiNengXiaoDa_buttom_but {
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #2A7FCC;
    padding: 3px 8px;
    border: 1px solid #2A7FCC;
    cursor:pointer;
}
.ZhiNengXiaoDa_hea_tips_1 {
    color: #2A7FCC;
    font-size: 16px;
}

.ZhiNengXiaoDa_hea_tips_2 {
    color: #333333;
    font-size: 16px;
}

.ZhiNengXiaoDa_hea_close {
    width: 36px;
    height: 36px;
    margin-bottom: 44px;
    cursor: pointer;
    margin-right: 8px;
}

.ZhiNengXiaoDa_title {
    display: flex;
    justify-content: space-between;
    /*padding-bottom: 14px;*/
    position: relative;
}

.ZhiNengXiaoDa_link {
    cursor: pointer;
    text-align: right;
    height: 22px;
    font-size: 16px;
    font-weight: 400;
    color: #0063F5;
    line-height: 22px;
    margin-bottom: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
}

.ZhiNengXiaoDa_job_body {
    max-height: 520px;
    overflow-y: scroll;
}


.hd_position_list_wrap {
    /*max-height: 470px;
    overflow-y: scroll;*/
}

.hd_position_tips {
    cursor: pointer;
    text-align: right;
    height: 22px;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #0063F5;
    line-height: 22px;
    margin-bottom: 16px;
}

.hd_position_item {
    background: #FFFFFF;
    border: 1px solid #DCDFE6;
    margin-top: 16px!important;
    position: relative;
    border-radius: 4px!important;
}

    .hd_position_item:first-child {
        margin: 0!important;
    }

.hd_position_one {
    display: flex;
    border-bottom: 1px dashed #EBEEF5;
    margin: 0 24px;
}

.hd_one_left {
    margin-top: 16px;
    width: 425px;
}

.hd_left_one {
    display: flex;
    align-items: center;
}

    .hd_left_one .hd_left_name {
        height: 25px;
        font-size: 18px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #33383D;
        line-height: 25px;
        max-width: 240px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
    }

.hd_left_city {
    height: 20px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #0063F5;
    line-height: 20px;
    margin-left: 16px;
}

.hd_left_time {
    height: 20px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #BDBDBD;
    line-height: 20px;
    margin-left: 16px;
}

.tag_new {
    width: 66px;
    height: 24px;
    font-size: 12px;
    font-weight: 400;
    line-height: 24px;
    text-align: center;
}

.hdjx_tag {
    background: url(../../images/opt/jsjf_tag.png) no-repeat center center;
    color: #61481a;
    background-size: 100% 100%;
}

.jsjf_tag {
    background: url(../../images/opt/gfc_tag.png) no-repeat center center;
    color: #2B0F6C;
    background-size: 100% 100%;
  }

.yzzw_tag {
    background: url(../../images/opt/ycx_tag.png) no-repeat center center;
    background-size: 100% 100%;
    color: #fff;
  }

.jsfk_tag {
    background: url(../../images/opt/jsfk_tag.png) no-repeat center center;
    background-size: 100% 100%;
    color: #ffffff;
}

.ptzw_tag {
    background: url(../../images/opt/ptzw_tag.png) no-repeat center center;
    background-size: 100% 100%;
    color: #4f5b6e;
}

.hd_left_tow .hd_left_salary {
    height: 22px;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #F3555B;
    margin-top: 8px;
    line-height: 22px;
    display: inline-block;
}

.hd_left_txt {
    height: 18px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #4F5B6E;
    line-height: 18px;
    display: inline-block;
    margin-left: 16px;
    margin-top: 2px;
    position: relative;
}

.content1_tow_line {
    line-height: 18px;
    border-left: 1px solid;
    position: absolute;
    top: 0;
    left: -8px;
    height: 18px;
}

.hd_one_center {
    /* margin-top: 24px; */
    width: 365px;
}

.center_company_name {
    height: 25px;
    font-size: 18px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #33383D;
    line-height: 25px;
    max-width: 280px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.center_industry_box {
    line-height: 17px;
    height: 17px;
    margin-top: 8px;
}

.box_industry {
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #4F5B6E;
    line-height: 17px;
    height: 17px;
    display: inline-block;
    max-width: 280px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.box_content2_position {
    height: 17px;
    font-size: 12px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #969BA4;
    line-height: 17px;
    margin-top: 7px;
    margin-bottom: 20px;
}

.position_didian_shuzi {
    color: #0063f5;
}

.hd_one_right {
    margin-top: 2px;
    position: relative;
}

.right_wg {
    position: absolute;
    right: 1px;
    width: 26px;
    height: 23px;
    top: -12px;
}

.right_header_pic {
    width: 56px;
    height: 56px;
    border-radius: 41px!important;
    border: 1px solid #F1F2F7;
}

.right_txt {
    position: absolute;
    top: 48px;
    width: 60px;
    height: 18px;
    background: #E5A10A;
    border-radius: 9px!important;
    font-size: 12px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 18px;
    text-align: center;
    display: inline-block;
    right: 0;
}

.hd_position_tow {
    display: flex;
    justify-content: space-between;
    padding: 7px 24px 8px;
}

.orders_immediately_back {
    width: 84px;
    height: 32px;
    background: #0063F5;
    border-radius: 4px!important;
    display: inline-block;
    line-height: 32px;
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF;
}
.orders_immediately_back:hover {
    text-decoration-line: none;
    color: #FFFFFF;
    background: #5d9bf8;
}

.box_yj_left {
    line-height: 32px;
    font-size: 14px;
}

.yj_ml {
    margin-left: 24px;
}

.yj_color {
    color: #FF7326;
}

.yongjin_rules {
    color: #0063F5;
}


#ZhiNengXiaoDa .modal-content {
    border-radius: 8px !important;
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
}

.page-header.navbar.navbar-fixed-top {
    z-index: 100 !important;
}

[v-cloak] {
    display: none;
}

.page-deliverycenter {
  /*  font-family: '微软雅黑';*/
    margin-bottom: 15px;
    margin-top: -17px;
}

.layout-top {
    margin-top: 20px;
}

.placeholder-img {
    width: 100%;
    border-radius: 4px !important;
}

.topnav-panel {
    display: flex;
    margin-top: 10px;
    align-items: baseline;
    margin-bottom: 10px;
}

.topnav-link {
    color: #2A7FCC;
    cursor: pointer;
}

.topnav-sep {
    height: 12px;
    margin-left: 7px;
    margin-right: 8px;
}

.topnav-text {
}

.layout-body {
    display: flex;
}

.layout-left {
    flex: auto;
    margin-right: 14px;
}

.layout-right {
    width: 286px;
}

.flex-between {
    display: flex;
    justify-content: space-between;
}
/* 通用样式 start */
.btn {
    border-radius: 2px !important;
}

.btn-primary {
    background-color: #2A7FCC;
    border-color: #2A7FCC;
}

.btn.btn-outline.btn-primary {
    border-color: #2A7FCC;
    color: #2A7FCC;
    background: 0 0;
}

    .btn.btn-outline.btn-primary:hover {
        border-color: #2A7FCC;
        color: #FFF;
        background-color: #2A7FCC;
    }

.btn.default:not(.btn-outline) {
    color: #fff;
    background-color: #D0D0D0;
    border-color: #D0D0D0;
}

    .btn.default:not(.btn-outline):hover {
        color: #fff;
        background-color: #D0D0D0 !important;
        border-color: #D0D0D0 !important;
    }

.overflow-dot {
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: bottom;
    white-space: nowrap;
}

.sep-panel {
    border-bottom: dashed 1px #ddd;
}

.color-orange {
    color: #FF8800;
}

.color-blue {
    color: #2A7FCC;
}

.color-lightblue {
    color: #3B87FF;
}

.color-darkblue {
    color: #2764F7;
}

.color-green {
    color: #0FAF46;
}

.color-red {
    color: #F94444;
}

.empty-panel {
    text-align: center;
    padding-top: 40px;
    padding-bottom: 40px;
    background-color: #fff;
    margin-bottom: 10px;
}

    .empty-panel .empty-img {
        height: 100px;
    }

    .empty-panel .empty-msg {
        color: #999;
        margin-top: 10px;
    }

.label-list {
    display: flex;
    margin-bottom: 13px !important;
}

    .label-list .label-item {
        font-size: 12px;
        padding: 3px 4px;
        border: solid 1px;
        line-height: 1;
        margin-right: 10px !important;
        border-radius: 2px !important;
    }

        .label-list .label-item.color-orange {
            border-color: initial;
        }

        .label-list .label-item.color-blue {
            border-color: initial;
        }

        .label-list .label-item.color-green {
            border-color: initial;
        }



    .job-intro-list {
        display: flex;
        margin-top: 9px;
    }

    .job-intro-list .job-intro-item {
        max-width: 100px;
        white-space: nowrap;
        overflow: hidden;
    }

        .job-intro-list .job-intro-item.no-width {
            max-width: initial;
            white-space: initial;
            overflow: auto;
        }

    .job-intro-list .job-intro-sep {
        background-color: #DDDDDD;
        height: 12px;
        width: 1px;
        position: relative;
        top: 4px;
        margin-left: 13px;
        margin-right: 13px;
    }

    .job-intro-list .company-industry-msg {
        max-width: 200px;
    }

.showCard-item {
    cursor: pointer;
}
/* 通用样式 end */
/* 表格通用样式 start */
.light-table {
    border: solid 1px #DDDDDD;
}

    .light-table thead {
        background-color: #F9F9F9;
    }

        .light-table thead th {
            border-bottom-width: 0 !important;
            border-right: solid 1px #ddd;
        }

    .light-table th, .light-table td {
        text-align: center;
    }
/* 表格通用样式 end */
/* 通用盒子面板 start */
.box-panel {
    background: #fff;
    padding-left: 20px;
    padding-right: 20px;
    margin-bottom: 14px;
}

.box-header {
    padding-top: 14px;
    padding-bottom: 14px;
    border-bottom: solid 1px #EEEEEE;
}

.box-title {
    font-weight: 600;
    font-size: 15px;
    display: flex;
    align-items: center;
}

.box-title-icon {
    height: 18px;
    margin-right: 8px;
}

.font-weight-600 {
    font-weight: 600;
}

.font-weight-500 {
    font-weight: 500;
}

.box-body {
}
/* 通用盒子面板 end */
/* 通用弹窗 start */
.modal-light .modal-header {
    padding: 15px 20px;
}

.modal-light .modal-title {
    font-weight: bold;
    letter-spacing: 1px;
}

.modal-light .modal-header .close {
    margin-top: 7px !important;
}

.modal-light .modal-body {
    padding-left: 20px;
    padding-right: 20px;
}

.modal-light .modal-footer {
    padding-left: 20px;
    padding-right: 20px;
}

    .modal-light .modal-footer .btn {
        border-radius: 2px !important;
        min-width: 90px;
    }
/* 通用弹窗 end */
/* 我的职位管理 start */
.jobmanage-panel {
    padding-bottom: 20px;
}

    .jobmanage-panel .jobmanage-publishjob {
        width: 100%;
        margin-top: 18px;
        padding-top: 9px;
        padding-bottom: 9px;
        letter-spacing: 1px;
    }

    .jobmanage-panel .jobmanage-btnicon {
        margin-right: 2px;
        height: 16px;
    }

    .jobmanage-panel .jobmanage-btnlist {
        display: flex;
        justify-content: space-between;
        margin-top: 13px;
    }

    .jobmanage-panel .jobmanage-btnitem {
        width: 76px;
        letter-spacing: 1px;
        padding-left: 8px;
        position: relative;
    }

    .jobmanage-panel .jobmanage-btnpopup {
        position: absolute;
        font-size: 12px;
        color: #fff;
        background: #F94444;
        border-radius: 30px !important;
        padding: 1px 3px;
        top: -9px;
        right: -8px;
        min-width: 18px;
    }
/* 我的职位管理 end */
/* 职位列表 start */
.job-item {
    margin-top: 14px !important;
    border: solid 1px #DDDDDD;
    cursor: pointer;
}

    .job-item:hover {
        border-color: #2A7FCC;
    }

    .job-item .job-item-info {
        background-color: #fff;
        display: flex;
        padding: 15px 20px;
        justify-content: space-between;
    }

    .job-item .job-item-job {
        width: 500px;
    }

    .job-item .job-item-job-title {
        margin-bottom: 14px;
    }

    .job-item .job-item-job-title-name {
        color: #2A7FCC;
        font-weight: bold;
        font-size: 16px;
        max-width: 300px;
        display: inline-block;
    }

    .job-item .job-item-job-publish {
        display: inline;
        font-size: 14px;
        color: #999999;
        font-weight: initial;
        margin-left: 10px;
    }


    .job-item .job-item-company {
        width: 300px;
    }

    .job-item .job-item-company-title {
        margin-bottom: 14px;
    }

    .job-item .job-item-company-title-name {
        font-weight: 600 !important;
        font-size: 16px;
        max-width: 250px;
        display: inline-block;
    }

    .job-item .job-item-company-level {
        height: 17px;
        margin-right: 3px;
        position: relative;
        top: -2px;
    }

    .job-item .job-item-owner {
        width: 60px;
    }

    .job-item .job-item-owner-img-wrapper {
        position: relative;
    }

    .job-item .job-item-owner-feedback {
        position: absolute;
        height: 20px;
        left: 31px;
        top: -3px;
    }

    .job-item .job-item-owner-img {
        height: 60px;
        border-radius: 60px !important;
    }

    .job-item .job-item-owner-level-wrapper {
        text-align: center;
        margin-top: 10px;
    }

    .job-item .job-item-owner-level {
        background-color: #FF8800;
        text-align: center;
        color: #fff;
        border-radius: 2px !important;
        font-size: 12px;
        width: 38px;
        display: inline-block;
    }

    .job-item .job-item-analytics {
        background-color: #F8F9FC;
        display: flex;
        justify-content: space-between;
        padding: 10px 20px;
        align-items: baseline;
    }

    .job-item .job-item-bonus {
        font-weight: bold;
        color: #F94444;
        font-size: 15px;
        margin-right: 10px;
    }

    .job-item .job-item-analytics-label {
        display: inline-block;
        border: solid 1px #ccc;
        font-size: 12px;
        padding: 3px 7px;
        margin-left: 10px;
    }

    .job-item .job-item-analytics-optitem {
        font-size: 12px;
        line-height: 1 !important;
        width: 60px;
        height: 24px;
        padding: 0;
    }

        .job-item .job-item-analytics-optitem.disabled {
            padding-top: 5px;
        }

    .job-item .job-item-analytics-opticon {
        width: 16px;
        margin-right: 2px;
    }

    .job-item .icon-follow .job-item-analytics-opticon {
        height: 15px;
        width: 15px;
        background-size: 16px 16px;
        display: inline-block;
        position: relative;
        top: 2px;
        background-repeat: no-repeat;
        background-image: url(/images/opt/icon-heart.png)
    }

    .job-item .icon-follow:hover .job-item-analytics-opticon {
        background-image: url(/images/opt/white/icon-heart.png)
    }

.job-empty-panel {
    margin-top: 14px;
}

    .job-empty-panel .empty-panel {
        height: 310px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    .job-empty-panel .empty-img {
        height: 72px;
    }

    .job-empty-panel.job-empty-without-selected .empty-panel {
        height: 367px;
    }

.yb-pager {
    background-color: transparent;
}
/* 职位列表 end */
/* 2020-12-18 智能小达  end */
#scheduleReminderDom {
    position: fixed;
    right: 14.5%;
    font-size: 14px;
    transition: all 0.5s;
    z-index: 101;
}

.scheduleReminderDom_bottom {
    bottom: 80px;
    transition: all 1s;
}

#scheduleReminderDom.scheduleReminderDom_bottom_new {
    right: -1000px !important;
    transition: all 1s;
    bottom: 80px;
}

.scheduleReminder__body {
    width: 430px;
    /* max-height: 376px;*/
    border-radius: 3px !important;
    overflow: hidden;
    box-shadow: 0px 2px 20px 0px #C1CADF;
    font-family: PingFangSC-Medium, PingFang SC;
}

.workSummaryOfYesterday__middle_span {
    flex: 1;
    text-align: center
}

.workSummaryOfYesterday__middle_line {
    height: 25px;
    width: 1px;
    background-color: #DDD
}

.scheduleReminder__title {
    width: 430px;
    background-color: #2A7FCC;
    display: flex;
    height: 50px;
    color: #FFFFFF;
    align-items: center;
    justify-content: space-between;
}

.scheduleReminder__title_new {
    padding: 0 15px;
}

.schetitle_new_text {
    font-size: 14px;
    margin-right: 36px;
    cursor: pointer;
    height: 34px;
    line-height: 34px;
    color: #CBE5FC;
    position: relative;
}

.sche_rc_link {
    color: #2A7FCC;
}

.schetitle_new_addText {
    color: #EEE;
    border-bottom: 2px solid #EEE;
}

.scheduleReminder__newColor {
    background-color: #E4EFF9;
    color: #333;
    font-size: 14px;
    height: 40px;
    justify-content: left;
    font-weight: 400 !important;
}

.scheduleReminder__title_new .scheduleReminder__top_buttom {
    height: 24px;
    align-items: center;
    display: flex;
}

.schetitle_new_body {
    display: flex
}



.scheduleReminder__text {
    font-weight: 600;
}

.scheduleReminder__title_left {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding-left: 16px;
}

.scheduleReminder__title_right {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding-right: 16px;
}

.workSummaryOfYesterday__title .scheduleReminder__icon {
    margin-right: 12px;
}


.scheduleReminder__icon {
    width: 14px;
    height: 14px;
    margin-right: 5px;
    cursor: pointer;
}

.scheduleReminder__tips_num {
    width: auto;
    padding: 0px 7px;
    background: #F83D3D;
    border-radius: 8px !important;
    margin-right: 5px;
    height: 16px;
    line-height: 16px;
    color: #FFF;
    font-size: 12px;
    position: absolute;
    top: 0px;
    left: 56px;
}

.scheduleReminder__href a {
    color: inherit !important;
}

.scheduleReminder__top_buttom {
    border-radius: 2px !important;
    border: 1px solid #FFFFFF;
    padding: 0px 7px;
    margin-left: 15px;
    cursor: pointer;
    font-size: 12px;
}

.scheduleReminder__midden {
    background-color: #FFFFFF;
}

.scheduleReminder__midden_row {
    padding: 13px 10px;
}

.scheduleReminder_first_line {
    display: flex;
    align-items: center;
}

.scheduleReminder_first_title {
    color: #2A7FCC;
    font-weight: 600;
    margin-right: 5px;
}

.scheduleReminder_first_text {
    margin-left: 20px;
    color: #333333;
}

.scheduleReminder__link {
    color:#000;
}
    .scheduleReminder__link :hover {
        color: #337ab7 !important;
    }


    .scheduleReminder_margin_bottom_5 {
        margin-bottom: 5px;
    }

.scheduleReminder_second_line {
    margin-left: 7px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.deliverList__margin-5 {
    margin-left: 7px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

.deliverList__margin-10 {
    margin-right: 10px;
}


.scheduleReminder_third_line {
    height: 24px;
    margin-top: 10px;
}

.scheduleReminder_third_buttom {
    border-radius: 2px !important;
    line-height: 22px;
    height: 24px;
    padding: 0 11px;
    float: right;
    font-size: 12px;
    text-align: center;
    cursor: pointer;
    display: inline-block;
}

.scheduleReminder_buttom_color_blue {
    border: 1px solid #2A7FCC;
    background-color: #2A7FCC;
    color: #FFFFFF;
}

.scheduleReminder_buttom_color_while {
    border: 1px solid #2A7FCC;
    color: #2A7FCC;
}

.scheduleReminder_margin_left_10 {
    margin-left: 10px;
}

.scheduleReminder__row_line {
    border-top: 1px dashed #DDDDDD;
}

.workSummaryOfYesterday {
    position: fixed;
    right: 14.5%;
    font-size: 14px;
    transition: all 0.5s;
    width: 420px;
    background-color: #FFFFFF;
    box-shadow: 0px 2px 20px 0px #C1CADF;
    border-radius: 3px !important;
    padding-bottom: 16px;
    z-index: 100;
}

.workSummaryOfYesterday__title {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
}

.workSummaryOfYesterday__title_left {
    display: flex;
    align-items: center;
    padding-left: 16px;
}

.workSummaryOfYesterday__title_right {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.workSummaryOfYesterday__middle {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    padding: 17px 0;
    border-top: 1px solid #EEEEEE;
}

.workSummaryOfYesterday__middle_num {
    color: #2A7FCC;
    font-size: 20px;
    text-align: center;
    font-weight: 600
}

.workSummaryOfYesterday__banner_1 {
    background: url('./1.png') no-repeat;
}

.workSummaryOfYesterday__banner_2 {
    background: url('./2.png') no-repeat;
}

.workSummaryOfYesterday__banner_3 {
    background: url('./3.png') no-repeat;
}

.workSummaryOfYesterday__banner_4 {
    background: url('./4.png') no-repeat;
}

.workSummaryOfYesterday__banner_5 {
    background: url('./5.png') no-repeat;
}

.workSummaryOfYesterday__banner {
    width: 390px;
    height: 140px;
    margin: 0 auto;
    color: #FFFFFF
}

.workSummaryOfYesterday__banner_title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 7px;
}

.workSummaryOfYesterday__banner_body {
    padding: 22px 17px;
}

.workSummaryOfYesterday__banner_text {
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 24px;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
}

.workSummaryOfYesterday__bottom_0 {
    bottom: 80px;
    transition: all 0.5s;
}

.workSummaryOfYesterday__bottom_1024 {
    bottom: -1024px;
    transition: all 0.5s;
}
/* 添加日程的model */
.addSchedule__body {
    width: 560px;
    background: #FFFFFF;
    position: absolute;
    top: 100px;
    z-index: 101;
    margin-left: 190px;
    font-size: 14px;
}

.addbg-cover {
    background: rgba(0, 0, 0, .4);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
    display: block
}

.addSchedule__title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    padding: 0 6px 0 20px;
    border-bottom: 1px solid #EEEEEE;
    font-weight: 600
}

.addSchedule__midden {
    margin: 21px 19px;
}

.addSchedule__row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.addSchedule__row_name {
    margin-right: 8px;
}

.addSchedule__row_input {
    width: 180px;
    height: 38px;
    background: #FFFFFF;
    border-radius: 2px !important;
    border: 1px solid #DDDDDD;
    margin-right: 10px;
    padding: 0 12px;
}

.addSchedule__row_textarea {
    width: 440px;
    height: 100px;
    background: #FFFFFF;
    border-radius: 2px !important;
    border: 1px solid #DDDDDD;
    padding: 8px 13px;
    box-sizing: border-box !important;
}

.addSchedule__row_left {
    position: relative;
}

    .addSchedule__row_left .scheduleReminder__icon {
        position: absolute;
        right: 11px;
        top: 11px;
    }

.addSchedule__row_detail {
    display: flex
}

.addSchedule__row_textarea_tips {
    text-align: right;
    color: #999999;
    font-size: 14px;
}

.addSchedule__buttom {
    border-top: 1px solid #EEEEEE;
    height: 64px;
}

.addSchedule__buttom_blue {
    width: 90px;
    float: right;
    height: 34px;
    line-height: 34px;
    text-align: center;
    background: #2A7FCC;
    border-radius: 2px !important;
    margin: 15px 20px 15px 10px;
    color: #FFFFFF;
    cursor: pointer;
}

.addSchedule__buttom_while {
    width: 90px;
    height: 34px;
    line-height: 34px;
    text-align: center;
    float: right;
    border-radius: 2px !important;
    border: 1px solid #DDDDDD;
    margin: 15px 0;
    cursor: pointer;
}

._overtime {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%);
    opacity: 0.6;
}
.examsTrain-content {
  border-radius: 4px !important;
  margin-top: 90px !important; 
}
.examsTrain-body{
  padding: 24px 16px;
}
.examsTrain-Title {
  color: #333;
  font-size: 16px;
  padding-bottom:24px;
  border-bottom:1px dashed #ddd;
}
.examsTrain-jieshao, .examsTrain-xuzhi {
  margin: 24px 0 16px;
}
.exams-jieshao {
  display: inline-block;
  width: 80px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  background: #2E81FF;
  color: #fff;
  border-radius: 2px !important;
  font-size: 16px;
}
.exams-cont {
  margin-top: 16px;
  font-size: 14px;
  color: #333;
  max-height: 120px;
  overflow-y: scroll;
  overflow:hidden;
}
.examsTrain-footer {
  text-align: center;
  padding-top: 16px;
  border-top: 1px dashed #ddd;
  margin-top:8px;
}
.examsTrain-btn-left {
  padding: 10px 35px;
  font-size: 14px;
  border: 1px solid #999999;
  color: #999999;
  border-radius: 4px !important;
  margin-top: 12px;
  display: inline-block;
  cursor:pointer;
}
.examsTrain-btn-right {
  padding: 10px 35px;
  font-size: 14px;
  color: #fff;
  background: #2A7FCC;
  border-radius: 4px !important;
  margin-left: 12px;
  margin-top: 12px;
  display: inline-block;
  cursor: pointer;
}

.ApprovalDialog {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1002;
  background: rgba(0,0,0,.4);
}
.ApprovalDialog-cont {
  width: 868px;
  height: 640px;
  background: #fff;
  border-radius: 8px !important;
  z-index: 1003;
  margin: 25px auto 0;
}

.ApprovalDialog-cont-top {
  padding: 14px 20px;
  border-bottom: 1px solid #DDDDDD;
  display:flex;
  justify-content:space-between;
}
.ApprovalDialog-cont-top-title{
  font-size:16px;
  color:#333;
  font-weight:600;
  display:inline-block;
}
.ApprovalDialog-cont-top-close {
  font-size: 22px;
  color: #333;
  cursor: pointer;
  display: inline-block;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
}
.ApprovalDialog-cont-cont {
  padding: 14px 20px 0;
}
.ApprovalDialog-line {
  width: 3px;
  height: 14px;
  background: #2A7FCC;
  display: inline-block;
  vertical-align: revert;
}
.ApprovalDialog-title {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}
.ApprovalDialog-cont-title {
  margin-bottom: 14px;
}
.ApprovalDialog-cont-img {
  position: relative;
  width: 54px;
  margin: 0 auto;
}
.ApprovalDialog-cont-cricle {
  position: absolute;
  width: 30px;
  height: 20px;
  border-radius: 12px !important;
  background: #FE5050;
  display: inline-block;
  padding: 2px 2px;
  font-size: 12px;
  color: #fff;
  border: 1px solid #fff;
  top: -2px;
  right: -14px;
  line-height: 14px;
  text-align: center;
}
.ApprovalDialog-cont-cricle1 {
  position: absolute;
  width: 22px;
  height: 22px;
  border-radius: 12px !important;
  background: #FE5050;
  display: inline-block;
  padding: 2px 2px;
  font-size: 12px;
  color: #fff;
  border: 1px solid #fff;
  top: -4px;
  right: -5px;
  line-height: 14px;
  text-align: center;
}
  .ApprovalDialog-ul {
    /*display: flex;*/
    /*justify-content: space-between;*/
    margin-bottom: 24px;
  }
    .ApprovalDialog-ul li {
      width: 70px;
      padding: 0 4px;
      display: inline-block;
      vertical-align: top;
      margin-right: 12px;
    }
.ApprovalDialog-cont-name {
  max-width: 56px;
  text-align: center;
  margin: auto;
}
.ApprovalDialog-cont-name a{
  color:#333;
  font-size:14px;
}
.ApprovalDialog-cont-name a:hover, .ApprovalDialog-cont-name a:focus, .ApprovalDialog-cont-name a:active {
  text-decoration: none;
}
.ApprovalDialog-cont-img .ApprovalImg{
  cursor:pointer;
}
@media screen and (max-width: 1466px) {
  .ApprovalDialog-cont-top {
    padding: 14px 20px;
  }
  .ApprovalDialog-cont-top-title {
    font-size: 14px;
  }

  .ApprovalDialog-cont-img .ApprovalImg {
    width: 50px !important;
    height: 50px !important;
  }

  .ApprovalDialog-ul {
    margin-bottom: 12px;
  }

  .ApprovalDialog-cont-name a {
    font-size: 12px;

  }
  .ApprovalDialog-img:hover a {
    color: #2A7FCC;
    font-weight: 600;
  }
    .ApprovalDialog-cont {
      height: 550px;
      overflow: auto;
    }
  .ApprovalDialog-cont {
    margin: 25px auto 0;
  }

    .ApprovalDialog-cont::-webkit-scrollbar {
      width: 3px;
      height: 10px;
    }
  .ApprovalDialog-cont-title {
    margin-bottom: 6px;
  }
  .ApprovalDialog-title{
    font-size:12px;
  }
}










