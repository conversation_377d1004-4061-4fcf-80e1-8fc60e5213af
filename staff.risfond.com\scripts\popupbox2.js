/// <reference path="jquery-1.8.2.js" />
/**
 * 功能：模态框
 * 依赖：[jquery.js]
 * 文档：无
 */
(function ($) {
	$.fn.popUpBox = function (arguments) {
		$thisEl = this;
		var opts = $.extend({}, $.fn.popUpBox.defaultArg, arguments);
		$(this).css({});
		var delay;
		//显示类型对象
		var showPositionType = {
			"centerBottom": function (el) {
				var $this = $(el);
				return $(".popUpBox").css({
					"position": "absolute",
					"top": $this.position().top + $this.height() + 5,
					"left": ($this.position().left - opts.width) + (opts.width / 2) + ($this.width() / 2) + opts.left,
					"width": opts.width
				});
			},
			"leftBottom": function (el) {
				var $this = $(el);
				return $(".popUpBox").css({
					"position": "absolute",
					"top": $this.position().top + $this.height() + 5,
					"left": ($this.position().left - opts.width) + (opts.width) + opts.left,
					"width": opts.width
				});
			},
			"rightBottom": function (el) {
				var $this = $(el);
				return $(".popUpBox").css({
					"position": "absolute",
					"top": $this.position().top + $this.height() + 5,
					"left": ($this.position().left - opts.width) + ($this.width()) + opts.left,
					"width": opts.width
				});
			},
			"left": function (el) {
				var $this = $(el);
				return $this.find(".popUpBox").css({
					"position": "absolute",
					"top": $this.position().top,
					"left": ($this.position().left - opts.width) + ($this.width() / 2) + opts.left,
					"width": opts.width
				});
			},
			"right": function (el) {
				var $this = $(el);
				$this.find(".popUpBoxJianTou").addClass("popUpBoxJianTouRight");
				return $this.find(".popUpBox").css({
					"position": "absolute",
					"top": $this.position().top,
					"left": $this.position().left + 10 + $this.outerWidth(true) + opts.left,
					"width": opts.width
				});
			}
		}
		/*
		positionType: 定位
		el:  当前容器
		return: 已修改定位属性的当前容器
		*/
		function setPopPositionByType(positionType, el) {
			var $this = $(el);
			var obj = null;
			switch (positionType) {
				case "left": {
					$this.find(".popUpBox").css({
						"position": "absolute",
						"top": $this.position().top,
						"left": ($this.position().left - opts.width) + ($this.width() / 2) + opts.left,
						"width": opts.width
					});
					break;
				}
				case "right": {
					$this.find(".popUpBox").css({
						"position": "absolute",
						"top": $this.position().top,
						"left": $this.position().left + 10 + $this.outerWidth(true) + opts.left,
						"width": opts.width
					});
					break;
				}
			}
			return $this;
		}
		var popupBoxEventType = {
			hover: function () {
				$($thisEl).hover(
				function () {
					var $this = this;
					function delayEvent() {
						clearTimeout(delay);
						delay = setTimeout(function () { mouseenter.call($this) }, opts.delay);
					}
					delayEvent();
					function mouseenter() {
						$(this).find(".popUpBox").live("click", function (e) { e.preventDefault(); });
						$(".popUpBox").hide(); $el = $(this).find(".popUpBox");
						if (opts.url != "") {
							if ($(this).text() == "0") { return false; }//验证消息条数书否为0，如果为0就不调用接口
							if ($(this).find(".popUpBox").size() == 0) {
								$(this).append(opts.box);
								$.post(opts.url, opts.ajaxData($(this)), function (result) {
									if (opts.resultType == "json") {
										if (result.Status == "1") {
											$($this).find(opts.addEleClass).html(opts.callbackhtml.call(this, result));
											showPositionType[opts.positionType]($this).hide().fadeIn(300);
										}
									}
									else {
										if (result.Status != 2 && result.Status != 0) {
											$($this).find(opts.addEleClass).html(result.Data);
											showPositionType[opts.positionType]($this).hide().fadeIn(300);
										}
									}
								}, "json");
							}
							else {
								showPositionType[opts.positionType]($this).hide().fadeIn(300);
							}
						}
						else {
							if (opts.resultType != "other") {
								if ($($this).attr("data-title") == "") { $(".popUpBox").hide(); return; }
								if ($(this).text() == "0") { return false; }
								if ($(this).find(".popUpBox").size() == 0) {
									$(this).append(opts.box);
									$($this).find(opts.addEleClass).html("<div class='popupTitle-box popup-showtitle'>" + $($this).attr("data-title") + "</div>");
									showPositionType[opts.positionType]($this).hide().fadeIn(300);
								} else {
									showPositionType[opts.positionType]($this).hide().fadeIn(300);
									//$(this).find(".popUpBox").fadeIn(300);
								}
							}
							else {
								if (opts.content == "") { $(".popUpBox").hide(); return; }
								if ($(this).text() == "0") { return false; }
								if ($(this).find(".popUpBox").size() == 0) {
									$(this).append(opts.box);
									$($this).find(opts.addEleClass).html("<div class='popupTitle-box popup-showtitle'></div>");
									if (typeof (opts.content) == "string") {
										$($this).find(opts.addEleClass).find(".popupTitle-box").html(opts.content);
									}
									else {
										opts.content.css("display", "block");
										$($this).find(opts.addEleClass).find(".popupTitle-box").append(opts.content);
									}
									showPositionType[opts.positionType]($this).hide().fadeIn(300);
								} else {
									showPositionType[opts.positionType]($this).hide().fadeIn(300);
								}
							}
						}
					}

				},
				function () {
					var $this = this;
					function delayEvent() {
						clearTimeout(delay);
						delay = setTimeout(function () { mouseleaver.call($this); }, opts.delay);
					}
					delayEvent();
					function mouseleaver() {
						if (opts.positionType != "centerBottom") {
							$(".popUpBox").hide();
						}
					}
				});
			},
			click: function () {
				$($thisEl).click(function (e) {
					var _event = e || event;
					var _target = _event.target;
					if ($(_target).hasClass("close") || $(_target).hasClass("showmore")) {
						return;
					}
					var $this = this;
					/*$.post(opts.url, opts.ajaxData($(this)), function (result) {
						if (result.Status != 2 && result.Status != 0) {
							$(".popUpBox").find(".popUpBoxCon").html(result.Data);
							showPositionType[opts.positionType]($this).hide().fadeIn(300);
						}
					}, "json");*/
					function delayEvent() {
						clearTimeout(delay);
						delay = setTimeout(function () { mouseenter.call($this) }, opts.delay);
					}
					delayEvent();
					function mouseenter() {
						//$(this).find(".popUpBox").live("click", function (e) { e.preventDefault(); });
						$(".popUpBox").hide(); $el = $(this).find(".popUpBox");
						if (opts.url != "") {
							if ($(this).text() == "0") { return false; }//验证消息条数书否为0，如果为0就不调用接口
							if ($(this).find(".popUpBox").size() == 0) {
								$(this).append(opts.box);
								$.post(opts.url, opts.ajaxData($(this)), function (result) {
									if (opts.resultType == "json") {
										if (result.Status == "1") {
											$($this).find(opts.addEleClass).html(opts.callbackhtml.call(this, result));
											showPositionType[opts.positionType]($this).hide().fadeIn(300);
										}
									}
									else {
										if (result.Status != 2 && result.Status != 0) {
											$($this).find(opts.addEleClass).html(result.Data);
											showPositionType[opts.positionType]($this).hide().fadeIn(300);
										}
									}
								}, "json");
							}
							else {
								showPositionType[opts.positionType]($this).hide().fadeIn(300);
							}
						}
						else {
							if (opts.resultType != "other") {
								if ($($this).attr("data-title") == "") { $(".popUpBox").hide(); return; }
								if ($(this).text() == "0") { return false; }
								if ($(this).find(".popUpBox").size() == 0) {
									$(this).append(opts.box);
									$($this).find(opts.addEleClass).html("<div class='popupTitle-box popup-showtitle'>" + $($this).attr("data-title") + "</div>");
									showPositionType[opts.positionType]($this).hide().fadeIn(300);
								} else {
									showPositionType[opts.positionType]($this).hide().fadeIn(300);
									//$(this).find(".popUpBox").fadeIn(300);
								}
							}
							else {
								if (opts.content == "") { $(".popUpBox").hide(); return; }
								if ($(this).text() == "0") { return false; }
								if ($(this).find(".popUpBox").size() == 0) {
									$(this).append(opts.box);
									$($this).find(opts.addEleClass).html("<div class='popupTitle-box popup-showtitle'></div>");
									if (typeof (opts.content) == "string") {
										$($this).find(opts.addEleClass).find(".popupTitle-box").html(opts.content);
									}
									else {
										opts.content.css("display", "block");
										$($this).find(opts.addEleClass).find(".popupTitle-box").append(opts.content);
									}
									showPositionType[opts.positionType]($this).show();
								} else {
									showPositionType[opts.positionType]($this).show();
								}
							}
						}
					}
				});/*.mouseleave(function () {
					var $this = this;
					function delayEvent() {
						clearTimeout(delay);
						delay = setTimeout(function () { mouseleaver.call($this); }, opts.delay);
					}
					delayEvent();
					function mouseleaver() {
						if (opts.positionType != "centerBottom") {
							$(".popUpBox").hide();
						}
					}
				});*/
			}
		}

		if (opts.eventFun == "hover") {
			popupBoxEventType.hover();
		}
		else if (opts.eventFun == "click") {
			popupBoxEventType.click();
		}
		$(".popUpBox .close").live("click", function (e) {
			$(".popUpBox").hide();
			if (event.stopPropagation) {
				event.stopPropagation();
			}
			else if (window.event) {
				window.event.cancelBubble = true;
			}
			return false;
		});
	}

	$.fn.popUpBox.defaultArg = {
		box: '<div class="popUpBox gouTongJiLu"><div class="popUpBoxWrpe"><div class="popUpBoxJianTou"></div><div class="popUpBoxCon"></div><div class="close">X</div></div></div>',
		url: "",
		eventHandling: "",
		showWrpeEle: ".popUpBox",
		positionType: "centerBottom",
		addEleClass: ".popUpBoxCon",
		eventFun: "click",
		delay: 1300,
		width: 630,
		left: 0,
		resultType: "html",
		callbackhtml: function () { },
		content: "",
		ajaxData: function (ele) {

		}
	};
})(jQuery)