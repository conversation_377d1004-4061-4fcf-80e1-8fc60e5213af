/* 主要样式文件 - 基于原始RISFOND网站 */

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "微软雅黑", "Microsoft YaHei", Arial, sans-serif !important;
  background-color: #f5f7fa;
  color: #333;
  line-height: 1.6;
}

/* 顶部导航栏样式 */
.page-header {
  background: linear-gradient(135deg, #2A7FCC 0%, #1e6bb8 100%);
  border-bottom: 1px solid #1e6bb8;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  height: 60px;
  z-index: 1000;
}

.page-header-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20px;
}

/* Logo区域 */
.page-logo {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo-placeholder {
  width: 40px;
  height: 40px;
  background: rgba(255,255,255,0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20px;
}

.menu-toggler {
  display: none;
  width: 30px;
  height: 30px;
  background: rgba(255,255,255,0.2);
  border-radius: 4px;
  cursor: pointer;
}

/* 换肤功能 */
.menu-icon-box {
  display: flex;
  align-items: center;
}

.huanfu-dropdown-link {
  color: #fff;
  text-decoration: none;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.huanfu-dropdown-link:hover {
  background: rgba(255,255,255,0.2);
}

/* 搜索表单 */
.search-form2 {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.search-form2 .input-group {
  display: flex;
  align-items: center;
  background: rgba(255,255,255,0.9);
  border-radius: 4px;
  overflow: hidden;
}

.search-form2 .btn {
  background: #fff;
  border: none;
  padding: 8px 12px;
  color: #333;
  border-right: 1px solid #ddd;
}

.search-form2 .form-control {
  border: none;
  padding: 8px 12px;
  width: 200px;
  outline: none;
}

.search-form2 .js-submit {
  background: #2A7FCC;
  color: #fff;
  padding: 8px 12px;
  text-decoration: none;
  border-left: 1px solid #ddd;
}

/* 顶部菜单 */
.top-menu .nav {
  display: flex;
  align-items: center;
  gap: 10px;
}

.top-menu .nav li {
  list-style: none;
}

.top-menu .nav li a {
  color: #fff;
  text-decoration: none;
  padding: 15px 10px;
  display: flex;
  align-items: center;
  position: relative;
  transition: background-color 0.3s;
}

.top-menu .nav li a:hover {
  background: rgba(255,255,255,0.2);
}

.top-menu .badge {
  position: absolute;
  top: 8px;
  right: 5px;
  background: #ff4757;
  color: #fff;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-danger2 {
  background: #ccc !important;
}

.badge-danger1 {
  background: #ff4757 !important;
}

.badge-primary {
  background: #2A7FCC !important;
}

/* 用户头像 */
.user-avatar-placeholder {
  width: 32px;
  height: 32px;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  margin-left: 10px;
}

.username {
  color: #fff;
  font-weight: bold;
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.show-score {
  color: #ffd700 !important;
  font-weight: bold;
  font-size: 12px;
}

/* 侧边栏样式 */
.page-sidebar-wrapper {
  position: fixed;
  left: 0;
  top: 60px;
  bottom: 0;
  width: 250px;
  background: #fff;
  border-right: 1px solid #e5e5e5;
  box-shadow: 2px 0 10px rgba(0,0,0,0.1);
  z-index: 999;
  overflow-y: auto;
}

.page-sidebar {
  height: 100%;
}

.page-sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.page-sidebar-menu .nav-item {
  border-bottom: 1px solid #f5f5f5;
}

.page-sidebar-menu .nav-link {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #333;
  text-decoration: none;
  transition: all 0.3s;
  position: relative;
}

.page-sidebar-menu .nav-link:hover {
  background: #f8f9fa;
  color: #2A7FCC;
}

.page-sidebar-menu .nav-item.active .nav-link {
  background: #e3f2fd;
  color: #2A7FCC;
  border-left: 3px solid #2A7FCC;
}

.page-sidebar-menu .nav-link i {
  width: 20px;
  margin-right: 10px;
  text-align: center;
}

.page-sidebar-menu .title {
  flex: 1;
}

.page-sidebar-menu .arrow {
  width: 0;
  height: 0;
  border-left: 4px solid #999;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  transition: transform 0.3s;
}

.page-sidebar-menu .nav-item.open .arrow {
  transform: rotate(90deg);
}

.page-sidebar-menu .selected {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #2A7FCC;
}

/* 子菜单 */
.page-sidebar-menu .sub-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  background: #f8f9fa;
  display: none;
}

.page-sidebar-menu .nav-item.open .sub-menu {
  display: block;
}

.page-sidebar-menu .sub-menu .nav-link {
  padding: 10px 20px 10px 50px;
  font-size: 14px;
  color: #666;
}

.page-sidebar-menu .sub-menu .nav-link:hover {
  background: #e9ecef;
  color: #2A7FCC;
}

/* 主内容区域 */
.page-content-wrapper {
  margin-left: 250px;
  margin-top: 60px;
  min-height: calc(100vh - 60px);
  background: #f5f7fa;
}

.page-content {
  padding: 20px;
  position: relative;
}

/* 四季装饰 */
.huanfu-base-head-img {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  background-size: cover;
  background-position: center;
  z-index: 1;
}

.huanfu-po-box {
  position: relative;
  z-index: 2;
}

/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* 通用工具类 */
.com-flex {
  display: flex;
  align-items: center;
}

.border-ra {
  border-radius: 8px;
}

.mt15 {
  margin-top: 15px;
}

/* 下拉菜单样式 */
.dropdown-menu {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  padding: 5px 0;
  min-width: 150px;
}

.dropdown-menu li {
  list-style: none;
}

.dropdown-menu li a {
  display: block;
  padding: 8px 15px;
  color: #333;
  text-decoration: none;
  transition: background-color 0.3s;
}

.dropdown-menu li a:hover {
  background: #f8f9fa;
  color: #2A7FCC;
}

/* Element UI 样式覆盖 */
.el-dropdown-menu__item {
  padding: 8px 15px !important;
}

.el-radio {
  color: #333 !important;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: #2A7FCC !important;
  background: #2A7FCC !important;
}

/* 四季换肤背景样式 */
.content-wrap-chun {
  background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%);
}

.content-wrap-xia {
  background: linear-gradient(135deg, #ffd3a5 0%, #fd9853 100%);
}

.content-wrap-qiu {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
}

.content-wrap-dong {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

/* 四季装饰图片 */
.huanfu-base-head-img.huanfu-base-head-chun {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><circle cx="20" cy="10" r="3" fill="%23a8e6cf"/><circle cx="40" cy="8" r="2" fill="%23dcedc1"/><circle cx="60" cy="12" r="2.5" fill="%23a8e6cf"/><circle cx="80" cy="9" r="2" fill="%23dcedc1"/></svg>');
}

.huanfu-base-head-img.huanfu-base-head-xia {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><circle cx="20" cy="10" r="3" fill="%23ffd3a5"/><circle cx="40" cy="8" r="2" fill="%23fd9853"/><circle cx="60" cy="12" r="2.5" fill="%23ffd3a5"/><circle cx="80" cy="9" r="2" fill="%23fd9853"/></svg>');
}

.huanfu-base-head-img.huanfu-base-head-qiu {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><circle cx="20" cy="10" r="3" fill="%23ffeaa7"/><circle cx="40" cy="8" r="2" fill="%23fab1a0"/><circle cx="60" cy="12" r="2.5" fill="%23ffeaa7"/><circle cx="80" cy="9" r="2" fill="%23fab1a0"/></svg>');
}

.huanfu-base-head-img.huanfu-base-head-dong {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><circle cx="20" cy="10" r="3" fill="%2374b9ff"/><circle cx="40" cy="8" r="2" fill="%230984e3"/><circle cx="60" cy="12" r="2.5" fill="%2374b9ff"/><circle cx="80" cy="9" r="2" fill="%230984e3"/></svg>');
}

/* 加载动画 */
.wait_loading_new {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.svg_wgs {
  width: 30px;
  height: 30px;
}

.g-circles circle {
  fill: #2A7FCC;
  opacity: 0.8;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-sidebar-wrapper {
    transform: translateX(-100%);
    transition: transform 0.3s;
  }

  .page-sidebar-wrapper.open {
    transform: translateX(0);
  }

  .page-content-wrapper {
    margin-left: 0;
  }

  .menu-toggler {
    display: block;
  }

  .search-form2 {
    display: none;
  }

  .page-header-inner {
    padding: 0 10px;
  }
}
