/// <reference path="/Scripts/jquery-1.12.0.js" />
/// <reference path="/Scripts/jquery-1.12.0.intellisense.js" />
/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
// 此函数事实上已被废弃并且没有被引用了   2019-07-23   刘海涛 
function textareaInputLimit(txt, len, errorpanelclass, numclass, watermark) {
	txt = $(txt);
	var message = "";
	txt.wrap('<div class="textarealimitpanel cf"></div>');
	txt.after('<div class="' + errorpanelclass + '"></div>');
	var errorpanel = txt.siblings("." + errorpanelclass);
	btxt(txt);
	txt.keyup(function () {
		btxt(this);
	}).change(function () {
		btxt(this);
	});
	function btxt(obj) {
		obj = $(obj);
		var text = obj.val();
		var tlen = text.length;
		if (watermark && text == watermark) {
			tlen = 0;
		}
		if (tlen <= len) {
			if (errorpanel.hasClass("error")) {
				errorpanel.removeClass("error");
			}
			message = setRnssLanguage('还可以输入{0}字。').format('<span class="' + numclass + '">' + (len - tlen) + '</span>');
			errorpanel.html(message);
		}
		else {
			if (!errorpanel.hasClass("error")) {
				errorpanel.addClass("error");
			}
			message = setRnssLanguage('最多输入{0}字，已超出{1}字，请缩减字数。').format('<span class="' + numclass + '">' + len + '</span>', (tlen - len));
			errorpanel.html(message);
		}
	}
}
function textareaInputLimit2(opts) {
	var self = this;
	var txt = $(opts.obj);
	var len = opts.len || 500;
	var errorpanelclass = opts.errorpanelclass || "errorpanel";
	var numclass = opts.numclass || "messpan";
	var watermark = opts.watermark || "";
	var istrigger = (!opts.istrigger || opts === true) ? true : false;
	var message = "";
	txt.wrap('<div class="textarealimitpanel cf"></div>');
	txt.after('<div class="' + errorpanelclass + '"></div>');
	var errorpanel = txt.siblings("." + errorpanelclass);
	this.btxt = function () {
		obj = $(txt);
		var text = obj.val();
		var tlen = text.length;
		if (watermark && text == watermark) {
			tlen = 0;
		}
		if (tlen <= len) {
			if (errorpanel.hasClass("error")) {
				errorpanel.removeClass("error");
			}
			message = setRnssLanguage('还可以输入{0}字。').format('<span class="' + numclass + '">' + (len - tlen) + '</span>');
			errorpanel.html(message);
		}
		else {
			if (!errorpanel.hasClass("error")) {
				errorpanel.addClass("error");
			}
			message = setRnssLanguage('最多输入{0}字，已超出{1}字，请缩减字数。').format('<span class="' + numclass + '">' + len + '</span>', (tlen - len));
			errorpanel.html(message);
		}
	}
	this.btxt(txt);
	if (istrigger) {
		txt.keyup(function () {
			self.btxt();
		}).change(function () {
			self.btxt();
		});
	}
}

//全选
function checkALL(params) {
	var opts =
		{
			AllButton: "#checkAll",
			ChildButton: ".input-radio",
			ParentBox: ".list-item-3"
		}
	this.params = $.extend({}, opts, params || {});
	var self = this.params;
	$(self.AllButton).live("change", function () {
	  $(self.ParentBox).find(self.ChildButton).not(":disabled").prop("checked", $(this).prop('checked'));
	  //$(self.ParentBox).find(self.ChildButton).not(":disabled").trigger("click");//引用的模板中会对input做样式上的优化，直接修改checked不可行
	});
	$(self.ParentBox).live("click", self.ChildButton, function () {
		if ($(self.ParentBox).find(self.ChildButton).filter(":checked").not(":disabled").size() == $(self.ParentBox).find(self.ChildButton).not(":disabled").size()) {
			$(self.AllButton).prop("checked", true);
		} else {
			$(self.AllButton).prop("checked", false);
		}
	});
}
function getMore(params) {
	var opts =
		{
			moreButton: '.term-list .js-term-list-more',
			dataBox: '.term-list'
		}
	this.params = $.extend({}, opts, params || {});
	var self = this.params;
	$(self.moreButton).click(function () {
		$(this).closest(self.dataBox).toggleClass("js-term-list-open");
	});
}
function getMore2(params) {
	var opts =
		{
			moreButton: '.expansion-btn',
			dataBox: '.expansion-box'
		}
	this.params = $.extend({}, opts, params || {});
	var self = this.params;
	$(self.moreButton).click(function () {
		$(this).closest(self.dataBox).toggleClass("expansion-more");
	});
}

function getMore3(params) {
	var opts =
		{
			moreButton: '.term-custom-list-more-btn',
			dataBox: '.term-custom-list-data-box',
			dataListBox: '.term-custom-list-data'
		}
	this.params = $.extend({}, opts, params || {});
	var self = this.params;
	$.each($(self.moreButton), function () {
	  var panel = $(this).closest(self.dataBox),
      dbh = panel.height(),
      listdata = panel.find(self.dataListBox);
	  if (listdata.height() <= dbh) {
	    $(this).parent().remove();
	  }
	});
	$(self.moreButton).unbind("click").click(function () {
	  var panel = $(this).closest(self.dataBox),
      dbh = panel.height();
		panel.toggleClass("term-open");
		$(this).parent().height(dbh);
	});
}
//鼠标经过的样式
function setupItemhoverbackground(params) {
	var opts =
		{
			box: '.listdata-text',
			item: ''
		}
	this.params = $.extend({}, opts, params || {});
	$(this.params.box)
		.find(this.params.item)
		.live("mouseenter", function () { $(this).addClass("hover"); })
		.live("mouseleave", function () { $(this).removeClass("hover"); });
}

function setupItemdeleteCheck(params) {
	var opts =
		{
			item: 'input[name=id]',
			clickEl: '#cmdDelete',
			url: "",
			form: "#form1",
			tip: setRnssLanguage("确认要删除所选的项吗？"),
			callback: function () { }
		}
	var self = this.params = $.extend({}, opts, params || {});
	$(self.clickEl).click(function () {
		$this = this;
		if ($(self.item).filter(":checked").size() == 0) {
			beautAlert.done(setRnssLanguage("请至少选择一个项"), 'hits');
			return;
		}

		if (!confirm(self.tip)) {
			return;
		}
		$.ajaxPost(self.url, $(self.form).serialize(), function (result) {
			self.callback.call($this, result);
		}, 'json');
	});
}
String.prototype.getQuery = function (name) {
	var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
	var r = this.substr(this.indexOf("/?") + 1).match(reg);
	if (r != null) return unescape(r[2]); return null;
}

$(document).ready(function () {
  $("#manageea-tab").find(".app_showmore").click(function () {
    $(this).closest(".r-tb-item").next(".r-tb-appdetails").toggle();
    var itemid = $(this).data("id");
    var appsStaffId = $(this).siblings().find("input").val(); //这是传入的id
    $(this).toggleClass("showmore_act");
    if ($(".app_showmore").hasClass("showmore_act")) {
      //alert("发起请求了");
      $(".App-attachment").html("");
      $.ajax({
        url: "/finance/_PartialEaDetail",
        type: "post",
        data: { id: itemid },
        success: function (data) {
          console.log(data);
          $(".application_box[data-id=" + itemid + "]").html(data);
        }
      });
    }
    
    if ($(this).html() == "+") {
      $(this).html("-")
      $(this).css("line-height", "1").css("font-size", "14px").css("font-weight", "bolder");
    } else {
      $(this).html("+");
    }

  })
});