
<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="utf-8" />
  <title>RISFOND RNSS V3.0</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta content="" name="description" />
  <meta content="" name="author" />
  <link rel="shortcut icon" href="/favicon.ico" />
  
  <link href="/css/bootstrap-star-rating.css?_v=20250318" rel="stylesheet"/>
  <link href="/js/schedulereminder/schedulereminder.css?_v=20250318" rel="stylesheet"/>
  <style>
    #newList {
      font-size: 16px;
      color: #F94444;
      position: relative;
      top: -3px;
    }

    #newList2 {
      font-size: 16px;
      color: #2A7FCC;
      position: relative;
      display: inline-block;
    }

    [v-cloak] {
      display: none !important;
    }

    #cnzz_stat_icon_1279333277 {
      display: none;
    }

    .label-default {
      background-color: #fff !important;
      color: #333 !important;
      height: 20px;
      width: 22.5px;
      display: inline-block !important;
      padding: 0 !important;
      line-height: 20px !important;
    }

    .rating-container .star {
      font-size: 16px;
    }

    .dp_con[v-cloak] {
      display: block;
    }

    .dp_con {
      display: none;
    }

    .img-circle {
      margin-top: -3px !important
    }

    li.dropdown-user ul {
      float: left;
    }

      li.dropdown-user ul li.username {
        color: #7FB0DA;
        font-weight: bolder;
        max-width: 100px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      li.dropdown-user ul li.show-score {
        padding: 0px 0px !important;
        color: #f3c200 !important;
        border-color: #f3c200;
        font-weight: bolder;
      }

    .page-header.navbar .top-menu .navbar-nav > li.dropdown-user .dropdown-toggle {
      padding: 20px 10px 18px 10px !important;
    }
    /*.page-container-bg-solid{
          background:red;
        }*/

    .item2 {
      margin-right: 20px;
      font-size: 14px;
      letter-spacing: 2px;
      line-height: 30px;
    }

    .aa {
      padding: 0 20px;
      margin-top: 90px;
    }

    .mod-footer {
      text-align: center !important;
      border-top: none !important;
      margin-top: 30px;
    }

      .mod-footer .but {
        border-radius: 5px !important;
        background: #ccc;
        border-color: #95A006;
      }
    /* 员工点评 新增样试 2020-03-06 赵刚 start*/

    #Staff_comments {
      z-index: 10086;
    }

    #Layout_BeautAlert {
      z-index: 100000;
    }

    #Staff_comments .modal-title {
      font-size: 14px;
      font-weight: bold;
    }

    #Staff_comments .close {
      margin-top: 5px !important;
      width: 12px;
      height: 12px;
    }

    .margin_right_12 {
      margin-right: 10px;
      margin-bottom: 12px;
      display: inline-block;
      width: 85px;
      text-align: right;
    }

    .PJBtn {
      float: left;
      width: 36px;
      height: 18px;
      line-height: 18px;
      text-align: center;
      font-size: 11px;
      background: #2A7FCC;
      color: #FFF;
      border-radius: 2px !important;
      cursor: pointer;
    }

    .rating-xs {
      font-size: 14px !important;
      display: inline-block !important;
      vertical-align: top;
    }

    .rating-container .rating-input {
      opacity: 0;
    }

    .text_color_000 {
      color: #FF6600;
      background: #FFF;
      font-size: 14px;
      display: inline-block;
      vertical-align: middle;
      margin-top: 2px;
      width: 30px;
    }

    .margin_left_5 {
      margin-left: 3px;
      width: 426px;
      height: auto;
      display: inline-block;
      vertical-align: top;
      font-size:12px;
    }

    .btn_custom_color {
      color: #FFF;
      background: #2A7FCC !important;
      margin-left: 24px;
      border-color: #2A7FCC !important;
      border-radius: 2px !important;
    }

    .but_color_ooo {
      display: inline-block;
      height: 34px;
      line-height: 34px;
    }

    #textareaValue {
      width: 574px;
      height: 130px;
      padding: 8px 16px;
      vertical-align: text-top;
      margin-bottom: 15px;
      position: relative;
    }

    .textarea_num {
      position: absolute;
      right: 30px;
      bottom: 37px;
      color: #999999;
      font-size: 14px;
    }

    .error_text {
      color: red;
      position: absolute;
      left: 90px;
      bottom: 6px;
    }
    /* 员工点评 新增样试 2020-03-06 赵刚 end*/
    .el-tab-pane {
      padding: 0 !important;
    }

    body {
      font-family: "微软雅黑" !important;
    }
    /**
      换肤样式
    */
    .content-wrap-chun {
      background: url('../images/index/chun_bg.png') no-repeat left 75px;
      background-size: 100%;
    }

    .content-wrap-xia {
      background: url('../images/index/xia_bg.png') no-repeat left 75px;
      background-size: 100%;
    }

    .content-wrap-qiu {
      background: url('../images/index/qiu_bg.png') no-repeat left 75px;
      background-size: 100%;
    }

    .content-wrap-dong {
      background: url('../images/index/dong_bg.png') no-repeat left 75px;
      background-size: 100%;
    }

    .page-header-fixed .page-container,
    .index-paihangbang {
      background-color: transparent !important;
    }

    .dropeown-huanfu .div.checker,
    .dropeown-huanfu div.radio {
      display: none !important;
    }

    .dropeown-huanfu .el-dropdown-menu__item {
      padding: 0 24px 0 10px !important;
    }

    .dropeown-huanfu .el-radio__inner {
      border-radius: 100% !important;
    }

    .dropeown-huanfu .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
      box-shadow: none !important;
    }

    .huanfu-dropdown-link:hover .huanfu-svg {
      fill: #2A7FCC;
    }

    .huanfu-dropdown-link:hover {
      color: #2A7FCC;
    }

    .dropeown-huanfu .el-dropdown-menu__item:focus, .el-dropdown-menu__item:not(.is-disabled):hover .el-radio__inner {
      border-color: #2A7FCC;
    }

    .dropeown-huanfu .el-radio__input {
      margin-top: 4px;
    }

    .dropeown-huanfu .el-radio {
      padding: 5px 0;
      margin-bottom: 0;
    }

    .dropeown-huanfu img {
      width: 80px;
      height: 36px;
    }
    .Approval {
      padding: 30px 12px 18px 12px !important;
    }
      .Approval:hover {
        background: #f9fafc !important;
      }
  </style>
  <link rel="stylesheet" type="text/css" href="/metronic/global/plugins/font-awesome/css/font-awesome.min.css?655f4a6b01b62de824c29de7025c4b21"/>
<link rel="stylesheet" type="text/css" href="/metronic/global/plugins/simple-line-icons/simple-line-icons.min.css?b87c13e541ea23867b47598da8a71957"/>
<link rel="stylesheet" type="text/css" href="/content/bootstrap.css?99e13ad8551767c806e6454bd9617470"/>
<link rel="stylesheet" type="text/css" href="/metronic/global/plugins/bootstrap-switch/css/bootstrap-switch.min.css?9349afd80c4f27801e8e93843f5b0d5d"/>
<link rel="stylesheet" type="text/css" href="/metronic/global/plugins/bootstrap-select/css/bootstrap-select.min.css?7c6e2a453e08a85a4151a65a1cfdd702"/>
<link rel="stylesheet" type="text/css" href="/metronic/global/plugins/bootstrap-toastr/toastr.min.css?a0e891adf614d5bb55df118d5ead7e70"/>
<link rel="stylesheet" type="text/css" href="/metronic/global/css/components.min.css?50747176be36a6854f98c4f185c2281e"/>
<link rel="stylesheet" type="text/css" href="/metronic/global/css/plugins.min.css?c5b8ad76ead0fc35080e9a08e402cb93"/>
<link rel="stylesheet" type="text/css" href="/metronic/layouts/layout4/css/layout-non-responsive.css?93a56ab8e7136ca6b3220f73c213751f"/>
<link rel="stylesheet" type="text/css" href="/metronic/layouts/layout4/css/themes/light.min.css?843d110e1cccf207d2dbb1a2808c5167"/>
<link rel="stylesheet" type="text/css" href="/metronic/layouts/layout4/css/custom.min.css?cf83e1357eefb8bdf1542850d66d8007"/>
<link rel="stylesheet" type="text/css" href="/Css/chat.css?fb77d608e8639c5ff45c4de336ba3d2e"/>

  <link rel="stylesheet" type="text/css" href="/static/QTip/jquery.qtip.css?daf82ac35cd7709fbc6e0ad80b189d7d"/>
<link rel="stylesheet" type="text/css" href="/Css/UI.css?ed75ecfb3b56a2c6ff2fff16932107a4"/>
<link rel="stylesheet" type="text/css" href="/Css/Common.css?d2b575c36b21e9acdd2137bb271f68a1"/>
<link rel="stylesheet" type="text/css" href="/Content/Site.css?d3a14eb667d3ef1a3c107a0108deba9a"/>

  
  <link href="/css/element-ui.css?_v=20250318" rel="stylesheet"/>
  <link href="/scripts/raselect/raselect.css?_v=20250318" rel="stylesheet"/>
  <link href="../Css/leo-def.css" rel="stylesheet" />
  <link href="/js/daterangeselect/daterangeselect.css" rel="stylesheet" type="text/css" />
  <link href="../Css/font/iconfont.css" rel="stylesheet" />
  <link href="/css/indexbase.css?_v=20250318" rel="stylesheet"/>
  <script src="/Css/font/iconfont.js"></script>


  <link href="/css/element-ui.css?_v=20250318" rel="stylesheet"/>
  <link href="/css/smallcard.css?_v=20250318" rel="stylesheet"/>
  <!--smallCard element-ui
    Create by Allen.sun on 2019/12/20
    Note: 新的前端监测模块运行 _layout
  -->
  
  <script src="/Scripts/jquery.min.js"></script>
  <script src="../Scripts/vue2.6.14.js"></script>
  <script src="/scripts/app/element_ui.js?_v=20250318" ></script>
  <script src="/static/i18next-1.10.3/i18next-1.10.3.min.js"></script>
  
</head>
<body class="page-container-bg-solid page-sidebar-closed-hide-logo page-boxed page-header-fixed ">
  
  <div id="jobDetailPanel" class="adda"></div>
  <div class="page-header navbar navbar-fixed-top" id="headHuanfu">
    <div class="page-header-inner container">
      <div class="page-logo com-flex">
        <a href="/">
          <img src="/static/images/logo-new3.png" style="" alt="logo" class="logo-default" />
        </a>
        <a href="/">
          <img src="/static/images/logo-new3.png" alt="logo" class="logo-default2" />
        </a>
        <div class="menu-toggler sidebar-toggler"></div>
      </div>

      <div class="menu-icon-box">

        <!-- 四季替换23.10.30-->
        <div class="huanfu-box">
          <el-dropdown trigger="click">
            <span class="huanfu-dropdown-link">
              <svg width="20px" height="18px" viewBox="0 0 20 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <title>换肤 (1)</title>
                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                  <g class="huanfu-svg" id="1.管理-春" transform="translate(-765.000000, -29.000000)" fill="#DBE2EC" fill-rule="nonzero">
                    <g id="换肤-(1)" transform="translate(765.000000, 29.000000)">
                      <path d="M15.0437183,18 L4.80078958,18 C4.37129523,18 4.02311634,17.6435142 4.02311634,17.203773 L4.02311634,9.9872189 C3.74464168,10.0805179 3.43858619,10.0056365 3.23139637,9.79351179 L0.227779278,6.71823412 C-0.075926426,6.40728257 -0.075926426,5.90314644 0.227779278,5.5921949 L5.43758246,0.258095753 C5.74128816,-0.0528557906 6.23367684,-0.0528557906 6.53738254,0.258095753 C6.84108824,0.569047296 6.84108824,1.07318343 6.53738254,1.38413497 L1.87746725,6.15522695 L3.78125996,8.10444052 L4.25087739,7.62361891 C4.47327142,7.39586925 4.80777853,7.32774096 5.09837111,7.45101182 C5.38896961,7.57424771 5.5784442,7.86458236 5.57843852,8.18662608 L5.57843852,16.4075459 L14.2660208,16.4075459 L14.2660208,8.00252346 C14.2660289,7.68048613 14.4555029,7.39016239 14.7460941,7.26692518 C15.0366853,7.14368796 15.3711693,7.21180571 15.5935819,7.43951629 L16.218734,8.07955843 L18.1225267,6.13034486 L13.4626357,1.35925287 C13.15893,1.04830133 13.15893,0.544165201 13.4626357,0.233213657 C13.7663171,-0.0777378858 14.2587301,-0.0777378858 14.5624115,0.233213657 L19.7722389,5.56731281 C20.0759204,5.87826435 20.0759204,6.38240048 19.7722389,6.69335202 L16.7686705,9.76860481 C16.5171871,10.0261108 16.1271252,10.0761201 15.8213915,9.89005432 L15.8213915,17.2037978 C15.8213915,17.6435142 15.4732127,18 15.0437183,18 L15.0437183,18 Z" id="路径"></path>
                      <path d="M10.0089645,4.15833929 C7.90725396,4.15841393 6.4572822,2.39641325 5.83370977,1.63875345 C5.81349026,1.61419482 5.79176402,1.58779492 5.77057242,1.56219124 C5.51282234,1.43253064 5.33539133,1.1610421 5.33539133,0.847154473 C5.33539133,0.490394993 5.54089149,0.183051354 5.85891124,0.0641398208 C6.04686514,-0.00615209803 6.41810691,-0.0635799736 6.79248367,0.341251715 C6.8586831,0.412837502 6.93124974,0.500994765 7.02311239,0.612615844 C7.54680241,1.24897543 8.6307088,2.56586031 10.0106414,2.56586031 C10.0256116,2.56586031 10.040436,2.56571102 10.0554791,2.56538755 C11.0605975,2.54458612 12.1151953,1.81481915 13.1899154,0.396390437 C13.4531821,0.0489368606 13.941731,-0.0141890148 14.2810396,0.255308958 C14.6203967,0.524856695 14.6820759,1.02503657 14.4188335,1.37246527 C13.0421332,3.18948026 11.584652,4.12651509 10.0869019,4.15749329 C10.0608742,4.15806558 10.0348221,4.15833929 10.0089645,4.15833929 Z" id="形状"></path>
                    </g>
                  </g>
                </g>
              </svg>换肤
            </span>

            <el-dropdown-menu class="dropeown-huanfu" slot="dropdown">
              <el-dropdown-item>
                <el-radio @input="changeSelVal" v-model="selFuVal" label="0">默认</el-radio>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-radio @input="changeSelVal" v-model="selFuVal" label="1">
                  <img src="/images/index/chun.png" alt="春" />
                </el-radio>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-radio @input="changeSelVal" v-model="selFuVal" label="2">
                  <img src="/images/index/xia.png" alt="夏" />
                </el-radio>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-radio @input="changeSelVal" v-model="selFuVal" label="3">
                  <img src="/images/index/qiu.png" alt="秋" />
                </el-radio>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-radio @input="changeSelVal" v-model="selFuVal" label="4">
                  <img src="/images/index/dong.png" alt="冬" />
                </el-radio>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>

      <div class="page-top">
        <form class="search-form2" id="r-search" method="GET" action="">
          <div class="input-group">
            <div class="input-group-btn">
              <button type="button" class="btn white dropdown-toggle search-form2-selected" data-toggle="dropdown" aria-expanded="false">
                <span class="js-currentOption">客户</span>
                <i class="fa fa-angle-down"></i>
              </button>
              <ul class="dropdown-menu js-options">
                <li>
                  <a href="javascript:;" data-value="1"> 客户 </a>
                </li>
                <li>
                  <a href="javascript:;" data-value="2"> 职位 </a>
                </li>
                <li>
                  <a href="javascript:;" data-value="3"> 简历 </a>
                </li>
              </ul>
            </div>
            <input type="text" class="form-control" name="keywords">
            <span class="input-group-btn">
              <a href="javascript:;" class="btn submit js-submit">
                <i class="icon-magnifier"></i>
              </a>
            </span>
          </div>
        </form>

        

        <div class="top-menu">
          <ul class="nav navbar-nav pull-right">

            <li class="separator hide"> </li>
            
            <li class="dropdown dropdown-extended dropdown-inbox dropdown-dark" v-if="IsGuwen=='False'">
              <a href="javascript:;" class="Approval" @click="ApprovalDialogOpenClick">
                <i style="width:20px;height:20px;display:inline-block;">
                  <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                      <g id="审批" transform="translate(-1164.000000, -29.000000)">
                        <g id="icon-审批-16" transform="translate(1164.000000, 29.000000)">
                          <rect id="矩形" x="0" y="0" width="20" height="20"></rect>
                          <path d="M10.0947882,0.5 C11.2159869,0.5 12.275567,0.908873935 13.0808468,1.65366273 C13.4659462,2.01072083 13.7692859,2.42541952 13.9823146,2.88511277 C14.1999367,3.35539797 14.3110255,3.85328434 14.3110255,4.36236158 C14.3110255,5.03246553 14.1273568,5.67699066 13.7955694,6.23850159 C13.4060236,6.89776185 12.8124729,7.44256808 12.072016,7.78177677 L12.6093448,10.9245908 L16.9195354,10.9245908 C17.1963335,10.9245908 17.4516606,11.0345682 17.6378472,11.2093131 C17.8120809,11.3728397 17.9277203,11.5954126 17.9277203,11.8400939 L17.9277203,14.456247 C17.9277203,14.7004374 17.8255724,14.9276031 17.6422072,15.0974895 C17.4504488,15.2763687 17.1934166,15.3717501 16.9195354,15.3717501 L3.08046461,15.3717501 C2.80366651,15.3717501 2.54833937,15.2617727 2.36215281,15.0870278 C2.1879191,14.9235012 2.07227973,14.7009282 2.07227973,14.456247 L2.07227973,11.8400939 C2.07227973,11.5959035 2.17442757,11.3687378 2.35779276,11.1988514 C2.54955123,11.0199722 2.80658345,10.9245908 3.08046461,10.9245908 L7.54231626,10.9245908 L8.07954781,7.78234575 C7.33896318,7.4414011 6.74525885,6.89633197 6.3556991,6.23704812 C6.02416073,5.6759586 5.84063553,5.03217097 5.84063553,4.36236158 C5.84063553,3.33162069 6.28274745,2.36634298 7.07433626,1.64149168 C7.88155866,0.904380141 8.9548394,0.5 10.0947882,0.5 Z M10.0926818,1.11561146 C9.1304447,1.11561146 8.25945524,1.47999921 7.6281901,2.0643729 C6.98259997,2.66200749 6.58684401,3.48844157 6.58684401,4.39817044 C6.58684401,4.99221447 6.7480305,5.55149608 7.04279153,6.03387144 C7.33560573,6.5130608 7.76084232,6.9170374 8.29507752,7.2009666 C8.47624621,7.29745128 8.6179183,7.4332047 8.70458995,7.5959253 C8.78455141,7.74604792 8.81599526,7.91796771 8.79268828,8.09589503 L8.26915769,10.719881 C8.24391234,10.962182 8.12010216,11.1767838 7.94345745,11.3308727 C7.76846032,11.4835244 7.54012631,11.5781175 7.29537795,11.5781175 L2.74687049,11.5781175 L2.74687049,14.7561387 L17.2552359,14.7561387 L17.2552359,11.5781175 L12.892092,11.5781175 C12.6469591,11.5781175 12.4179448,11.4832945 12.2423743,11.330362 C12.0655665,11.1763518 11.9413885,10.9619731 11.9162005,10.7198215 L11.392567,8.09954256 C11.3679807,7.91467101 11.4039354,7.73142254 11.4868808,7.57130794 C11.5742964,7.40256407 11.7137039,7.25933547 11.8909272,7.16481634 C12.4245212,6.88122854 12.8497578,6.47725194 13.142572,5.99806258 C13.437333,5.51568722 13.5985195,4.95640561 13.5985195,4.36236158 C13.5985195,3.46247195 13.2022271,2.64380377 12.5544474,2.05185934 C11.9238087,1.47557837 11.0538945,1.11561146 10.0926818,1.11561146 Z" id="形状" stroke="#C0CDDC" fill-rule="nonzero"></path>
                        </g>
                      </g>
                    </g>
                  </svg>
                </i>
                <span class="badge badge-danger2" v-if="WorkflowCountNum==0"> 0 </span>
                <span class="badge badge-danger1" v-else-if="WorkflowCountNum<99"> {{WorkflowCountNum}} </span>
                <span class="badge badge-danger1" v-else> 99+ </span>
              </a>
            </li>

            <li class="separator hide"> </li>
            
            <li class="dropdown dropdown-extended dropdown-inbox dropdown-dark" id="header_inbox_bar">
              <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
                <i class="icon-envelope-open"></i>
                <span class="badge badge-danger"> 0 </span>
              </a>
              <ul class="dropdown-menu">
                <li class="external">
                  <h3>您有<span class='bold'> 0 </span>条消息</h3>
                  <a href="/message/managemessages.aspx">查看所有</a>
                </li>
                <li>
                  <ul class="dropdown-menu-list scroller ms_content_box" style="height: 275px;" data-handle-color="#637283"></ul>
                </li>
              </ul>
            </li>
            <li class="separator hide"></li>
            
            <li class="dropdown dropdown-extended dropdown-tasks dropdown-dark" id="header_task_bar">
              
              <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
                <i class="icon-calendar"></i>
                <span class="badge badge-primary"> 0 </span>
              </a>
              <ul class="dropdown-menu extended tasks">
                <li class="external">
                  <h3>您有<span class="bold"> 0 </span>条日程提醒</h3>
                  <a href="/profile/taskmanage.aspx" target="_blank">查看所有</a>
                </li>
                <li>
                  <ul class="dropdown-menu-list scroller ms2_content_box" style="height: 275px;" data-handle-color="#637283"></ul>
                </li>
              </ul>
            </li>

            

<!-- BEGIN USER LOGIN DROPDOWN -->
<!-- DOC: Apply "dropdown-dark" class after below "dropdown-extended" to change the dropdown styte -->
<li class="dropdown dropdown-user dropdown-dark">
  <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
    
    <ul>
      <li title="顾开发ZJ1(Alvin)"
          class="username"> 顾开发ZJ1(Alvin) </li>

      <li class="btn-sm show-score">
        <i class="fa fa-money"></i>
        <span id="head_show_score"> 29856.00 </span> R币
      </li>
    </ul>
    <div class="circle-container CurrentBranchMyApprovalCount" style="top:-25px;right:-30px;display:none">
      <span class="circle"></span>
    </div>
    <!-- DOC: Do not remove below empty space(&nbsp;) as its purposely used -->
    
    <div style='width: 39px;
    height: 39px;
    background-repeat: round;
    border-radius: 50% !important;
    background-image: url("http://static2.risfond.com/photos/446fa3a4a0814c1eb933834fe81b256d.jpg?x-oss-process=image/resize,m_fixed,h_50,w_50");
    margin-left: 20px;
    display: inline-block;'></div>
  </a>
  <ul class="dropdown-menu dropdown-menu-default">
    <li>
      <a href="/staff/PersonalHomepage?id=18733">
        <i class="icon-user"></i> 个人主页
      </a>
    </li>
    <li>
      <a href="/company/CompanyMainPage?id=8">
        <i class="icon-user"></i> 公司主页
      </a>
    </li>
    <li>
      <a href="/profile/taskmanage.aspx">
        <i class="icon-calendar"></i> 我的日程
      </a>
    </li>
    <li>
      <a href="https://staff-hr.risfond.com/quitcontainer?ticket=afdf75bd13df4187987ddec84a359b01">
        <i class="icon-user"></i> 人事信息
      </a>
    </li>
    <li class="" id="transferR">
      <a href="javascript:;">
        <i class="fa fa-money"></i> R币转账
      </a>
    </li>
    <li class="">
      <a href="/ScoreLog/Index">
        <i class="fa fa-exchange"></i> R币记录
      </a>
    </li>
    <li>
      <a href="https://bms.risfond.com/?ticket=afdf75bd13df4187987ddec84a359b01" style="position:relative;display:none" id="BranchUrl" target="_blank">
        <i class="icon-user"></i>开业管理
        <div class="circle-container CurrentBranchMyApprovalCount" id="CurrentBranchMyApprovalCount" style="display:none">
          <span class="circle"></span>
        </div>
      </a>
    </li>
    <li class="divider">
    </li>
    <li>
      <a href="javascript:void(0)" onclick="LoginOut()"><i class="icon-key"></i> 退出登录</a>
    </li>
  </ul>
</li>
<!--BMS相关字段信息--开始--->
<input type="hidden" id="BMSModelData"
       data-ticket="afdf75bd13df4187987ddec84a359b01"
       data-branchurlticket="https://hrcenter-api.risfond.com/api/staff/GetTokenByTicket?ticket=afdf75bd13df4187987ddec84a359b01"
       data-branchapproveurl="https://bms-api.risfond.com/api/approvalManagement/getApprovePendingRequest"
       data-branchurl="https://bms.risfond.com" />
<!--BMS相关字段信息--结束--->
<!-- END USER LOGIN DROPDOWN -->
<script src="/js/layui/layer/layer.js" type="text/javascript"></script>
<script src="/js/bms/bms_index.js"></script>
<script type="text/javascript">
  //退出登录
  function LoginOut() {
    //调用hrsop验证接口
    $.ajax({
      type: "post",
      url: 'https://auth.risfond.com/api/Token/LoginOut',
      dataType: "json",
      contentType: "application/json", // 设置发送数据的类型为json
      data: JSON.stringify({
        StaffId:'18733',
        Ticket:'afdf75bd13df4187987ddec84a359b01'
      }),
      success: function (res) { }
    });
    window.location.href = "/signout.ashx";
  }
</script>
            <li class="dropdown dropdown-extended quick-sidebar-toggler" id="clickCompanyStaff">
              <span class="sr-only">Toggle Quick Sidebar</span>
              <i class="icon-logout"></i>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <div id="ApprovalDialog">
    <div class="ApprovalDialog" style="display:none;">
      <div class="ApprovalDialog-cont">
        <div class="ApprovalDialog-cont-top">
          <span class="ApprovalDialog-cont-top-title">审批中心</span>
          <span class="ApprovalDialog-cont-top-close" @click="ApprovalDialogCloseClick">×</span>
        </div>
        <div class="ApprovalDialog-cont-cont">

          <div v-for="(item,index) in WorkflowData" :key="index">
            <div class="ApprovalDialog-cont-title">
              <i class="ApprovalDialog-line"></i>
              <span class="ApprovalDialog-title">{{item.Title}}</span>
            </div>

            <ul class="ApprovalDialog-ul">
              <li v-for="(item1,index1) in item.Childs" :key="index1">
                <div class="ApprovalDialog-img">
                  <div class="ApprovalDialog-cont-img">
                    <img :src="item1.IconPath" class="ApprovalImg" @click="ApprovalClick(item1.Url)" width="50px" height="50px" alt="Alternate Text" />
                    <span class="ApprovalDialog-cont-cricle1" v-if="item1.IsDot">1</span>
                    <span class="ApprovalDialog-cont-cricle" v-else-if="item1.Count>99">99+</span>
                    <span class="ApprovalDialog-cont-cricle1" v-else-if="item1.Count>0&&item1.Count<99">{{item1.Count}}</span>
                  </div>
                  <div class="ApprovalDialog-cont-name">
                    <a :href="item1.Url" target="_blank">
                      {{item1.TypeStr}}
                    </a>
                  </div>
                </div>
              </li>
            </ul>
          </div>

        </div>
      </div>
    </div>
  </div>


  <script>
    var isAdmin = '0';
    var systemBGCode = sessionStorage.getItem("SystemBGCode") ?? '-1';
    var huanhu = new Vue({
      el: "#headHuanfu",
      data: {
        selFuVal: systemBGCode,
        WorkflowCountNum: 0,
        IsGuwen: 'True',
      },
      mounted() {
        let _this = this;
        setTimeout(function () {
          _this.setSJBG(_this.selFuVal, isAdmin == 1 ? 'boss' : "base", 0);
        }, 500)

        if (_this.IsGuwen == 'False') {
          this.StaffWorkflowData()
        }

      },
      methods: {
        ApprovalDialogOpenClick() {
          $(".ApprovalDialog").show()
          this.SetJobCandidate()

        },
        //数据埋点
        SetJobCandidate: function () {
          var _this = this
          $.ajax({
            type: "post",
            url: "/JobCandidate/SetJobCandidateSources?jobCandidateSourceEnum=11" ,
            dataType: "json",
            data: {},
            success: function (res) {
              if (res.Success) {
              } else {
                r_Layout_BeautAlert.done(res.message, "hits", 3000);
              }
            }
          });
        },
        StaffWorkflowData() {
          var _this=this
           $.ajax({
            type: "post",
            url: "/WorkflowCollection/GetStaffWorkflowData" ,
            dataType: "json",
            data: {
              staffId:18733,
              ticket: 'afdf75bd13df4187987ddec84a359b01',
              companyId:8,
                isGuwen:'True'
            },
            success: function (res) {
              if (res.Success) {
                ApprovalDialog.WorkflowData = res.Data.Parents
                _this.WorkflowCountNum = res.Data.Count
              } else {
                r_Layout_BeautAlert.done(res.message, "hits", 3000);
              }
            }
          });
        },
        changeSelVal() {
          this.setSJBG(this.selFuVal, isAdmin == 1 ? 'boss' : "base", 1);
        },
        setSJBG(selHuanfu, tag, isSave) {
          //console.log('设置', selHuanfu)
          //保存数据
          if (selHuanfu == '-1') {//根据当前时间算出是哪个季节
            const today = new Date();
            const currentMonth = today.getMonth() + 1;
            switch (currentMonth) {
              case 12:
              case 1:
              case 2:
                $("#huanfuBG").attr("class", "").addClass('content-wrap-dong');
                $(".huanfu-" + tag + "-head-img").attr("class", "huanfu-" + tag + "-head-img").addClass("huanfu-" + tag + "-head-dong");
                $(".huanfu-icon").attr("class", "huanfu-icon").addClass("huanfu-icon-dong");
                this.selFuVal = "4";
                break;
              case 3:
              case 4:
              case 5:
                $("#huanfuBG").attr("class", "").addClass('content-wrap-chun');
                $(".huanfu-" + tag + "-head-img").attr("class", "huanfu-" + tag + "-head-img").addClass("huanfu-" + tag + "-head-chun");
                $(".huanfu-icon").attr("class", "huanfu-icon").addClass("huanfu-icon-chun");
                this.selFuVal = "1";
                break;
              case 6:
              case 7:
              case 8:
                $("#huanfuBG").attr("class", "").addClass('content-wrap-xia');
                $(".huanfu-" + tag + "-head-img").attr("class", "huanfu-" + tag + "-head-img").addClass("huanfu-" + tag + "-head-xia");
                $(".huanfu-icon").attr("class", "huanfu-icon").addClass("huanfu-icon-xia");
                this.selFuVal = "2";
                break;
              case 9:
              case 10:
              case 11:
                $("#huanfuBG").attr("class", "").addClass('content-wrap-qiu');
                $(".huanfu-" + tag + "-head-img").attr("class", "huanfu-" + tag + "-head-img").addClass("huanfu-" + tag + "-head-qiu");
                $(".huanfu-icon").attr("class", "huanfu-icon").addClass("huanfu-icon-qiu");
                this.selFuVal = "3";
                break;
            }
          }
          else if (selHuanfu == '0') { //无
            $("#huanfuBG").attr("class", "");
            $(".huanfu-" + tag + "-head-img").attr("class", "huanfu-" + tag + "-head-img");
            $(".huanfu-icon").attr("class", "huanfu-icon");
          }
          else if (selHuanfu == '1') { //春
            $("#huanfuBG").attr("class", "").addClass('content-wrap-chun')
            $(".huanfu-" + tag + "-head-img").attr("class", "huanfu-" + tag + "-head-img").addClass("huanfu-" + tag + "-head-chun");
            $(".huanfu-icon").attr("class", "huanfu-icon").addClass("huanfu-icon-chun");
          } else if (selHuanfu == '2') { //夏
            $("#huanfuBG").attr("class", "").addClass('content-wrap-xia')
            $(".huanfu-" + tag + "-head-img").attr("class", "huanfu-" + tag + "-head-img").addClass("huanfu-" + tag + "-head-xia");
            $(".huanfu-icon").attr("class", "huanfu-icon").addClass("huanfu-icon-xia");
          } else if (selHuanfu == '3') { //秋
            $("#huanfuBG").attr("class", "").addClass('content-wrap-qiu')
            $(".huanfu-" + tag + "-head-img").attr("class", "huanfu-" + tag + "-head-img").addClass("huanfu-" + tag + "-head-qiu");
            $(".huanfu-icon").attr("class", "huanfu-icon").addClass("huanfu-icon-qiu");
          } else if (selHuanfu == '4') { //冬
            $("#huanfuBG").attr("class", "").addClass('content-wrap-dong')
            $(".huanfu-" + tag + "-head-img").attr("class", "huanfu-" + tag + "-head-img").addClass("huanfu-" + tag + "-head-dong");
            $(".huanfu-icon").attr("class", "huanfu-icon").addClass("huanfu-icon-dong");
          }
          {
            //保存数据
            if (isSave) {
              $.ajax({
                type: "post",
                url: "/Staff/UpdateSystemBGCode",
                dataType: "json",
                data: {
                  SystemBGCode: selHuanfu,
                },
                success: function (res) {
                  if (res.Success) {
                    sessionStorage.setItem("SystemBGCode", selHuanfu);
                    //保存成功
                  } else {
                    //保存失败
                  }
                }
              });
            }
          }
        }
      }
    })

    var ApprovalDialog = new Vue({
      el: "#ApprovalDialog",
      data: {
        WorkflowData:[],
      },
      mounted() {

      },
      methods: {
        ApprovalDialogCloseClick() {
          $(".ApprovalDialog").hide()
        },
        ApprovalClick(val) {
          window.open(val)
        },


      }
    })

  </script>

  
  <div class="" id="huanfuBG">
    <div class="container">
      <div class="page-container" id="page-container">
        
<!-- BEGIN SIDEBAR -->
<div class="page-sidebar-wrapper">
  <!-- BEGIN SIDEBAR -->
  <!-- DOC: Set data-auto-scroll="false" to disable the sidebar from auto scrolling/focusing -->
  <!-- DOC: Change data-auto-speed="200" to adjust the sub menu slide up/down speed -->
  <div class="page-sidebar navbar-collapse collapse border-ra">
    <!-- BEGIN SIDEBAR MENU -->
    <!-- DOC: Apply "page-sidebar-menu-light" class right after "page-sidebar-menu" to enable light sidebar menu style(without borders) -->
    <!-- DOC: Apply "page-sidebar-menu-hover-submenu" class right after "page-sidebar-menu" to enable hoverable(hover vs accordion) sub menu mode -->
    <!-- DOC: Apply "page-sidebar-menu-closed" class right after "page-sidebar-menu" to collapse("page-sidebar-closed" class must be applied to the body element) the sidebar sub menu mode -->
    <!-- DOC: Set data-auto-scroll="false" to disable the sidebar from auto scrolling/focusing -->
    <!-- DOC: Set data-keep-expand="true" to keep the submenues expanded -->
    <!-- DOC: Set data-auto-speed="200" to adjust the sub menu slide up/down speed -->
    <ul class="page-sidebar-menu  page-sidebar-menu-hover-submenu  " data-keep-expanded="false" data-auto-scroll="true" data-slide-speed="200">
      
      
          <li data-Id="1" data-Permission="0" class="nav-item  active open ">
            <a href="/" target="_self" class="nav-link nav-toggle">
                  <i class="icon-home"></i>

              <span class="title">首页</span>



                  <span class="selected"></span>

            </a>

          </li>
          <li data-Id="2" data-Permission="3" class="nav-item ">
            <a href="/client/manage" target="_self" class="nav-link nav-toggle">
                  <i class="icon-user"></i>

              <span class="title">客户管理</span>



                  <span class="arrow"></span>

            </a>
                <ul class="sub-menu" style="display: none;">
                        <li data-Id="101" data-Permission="3" class="nav-item " style="position: relative">
                          
                          <a href="/client/manage?guishu=1" target="_self" class="nav-link " style="position: relative">

                            <span class="title">我的客户</span>
                            
                          </a>
                        </li>
                        <li data-Id="102" data-Permission="3" class="nav-item " style="position: relative">
                          
                          <a href="/client/manage?guishu=3" target="_self" class="nav-link " style="position: relative">

                            <span class="title">合作客户</span>
                            
                          </a>
                        </li>
                        <li data-Id="103" data-Permission="3" class="nav-item " style="position: relative">
                          
                          <a href="/client/manage?guishu=2" target="_self" class="nav-link " style="position: relative">

                            <span class="title">其他客户</span>
                            
                          </a>
                        </li>
                        <li data-Id="104" data-Permission="3" class="nav-item " style="position: relative">
                          
                          <a href="/client/manage" target="_self" class="nav-link " style="position: relative">

                            <span class="title">客户管理</span>
                            
                          </a>
                        </li>
                        <li data-Id="105" data-Permission="4" class="nav-item " style="position: relative">
                          
                          <a href="/client/editclient" target="_self" class="nav-link " style="position: relative">

                            <span class="title">录入客户</span>
                            
                          </a>
                        </li>
                        <li data-Id="106" data-Permission="80" class="nav-item " style="position: relative">
                          
                          <a href="/apps/publicclients2.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">公共客户</span>
                            
                          </a>
                        </li>
                        <li data-Id="108" data-Permission="3" class="nav-item " style="position: relative">
                          
                          <a href="/apps/manageclientcontracts.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">合同管理</span>
                            
                          </a>
                        </li>
                        <li data-Id="109" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/apps/managepaymentorder.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">请款管理</span>
                            
                          </a>
                        </li>
                        <li data-Id="119" data-Permission="212" class="nav-item " style="position: relative">
                          
                          <a href="/Invitation/Index" target="_self" class="nav-link " style="position: relative">

                            <span class="title">招标商机</span>
                            
                          </a>
                        </li>
                        <li data-Id="114" data-Permission="3" class="nav-item " style="position: relative">
                          
                          <a href="/ClientLeads/ClientLeadsManage" target="_self" class="nav-link " style="position: relative">

                            <span class="title">客户线索</span>
                            
                          </a>
                        </li>
                        <li data-Id="115" data-Permission="3" class="nav-item " style="position: relative">
                          
                          <a href="/ClientAtlas/Index" target="_self" class="nav-link " style="position: relative">

                            <span class="title">行业图谱</span>
                            
                          </a>
                        </li>
                        <li data-Id="116" data-Permission="3" class="nav-item " style="position: relative">
                          
                          <a href="/ClientQuality/ClientFeedback" target="_self" class="nav-link " style="position: relative">

                            <span class="title">客户评价</span>
                            
                          </a>
                        </li>
                        <li data-Id="118" data-Permission="262" class="nav-item " style="position: relative">
                          
                          <a href="/Client/ClientInventory" target="_self" class="nav-link " style="position: relative">

                            <span class="title">客户盘点</span>
                            
                          </a>
                        </li>

                </ul>

          </li>
          <li data-Id="3" data-Permission="5" class="nav-item ">
            <a href="/job/managejob" target="_self" class="nav-link nav-toggle">
                  <i class="icon-doc"></i>

              <span class="title">职位管理</span>



                  <span class="arrow"></span>

            </a>
                <ul class="sub-menu" style="display: none;">
                        <li data-Id="201" data-Permission="5" class="nav-item " style="position: relative">
                          
                          <a href="/job/managejob?rt=1" target="_self" class="nav-link " style="position: relative">

                            <span class="title">我的职位</span>
                            
                          </a>
                        </li>
                        <li data-Id="202" data-Permission="5" class="nav-item " style="position: relative">
                          
                          <a href="/job/managejob?rt=2" target="_self" class="nav-link " style="position: relative">

                            <span class="title">合作职位</span>
                            
                          </a>
                        </li>
                        <li data-Id="203" data-Permission="5" class="nav-item " style="position: relative">
                          
                          <a href="/job/managejob?rt=3" target="_self" class="nav-link " style="position: relative">

                            <span class="title">其他职位</span>
                            
                          </a>
                        </li>
                        <li data-Id="204" data-Permission="5" class="nav-item " style="position: relative">
                          
                          <a href="/job/managejob" target="_self" class="nav-link " style="position: relative">

                            <span class="title">职位管理</span>
                            
                          </a>
                        </li>
                        <li data-Id="212" data-Permission="5" class="nav-item " style="position: relative">
                          
                          <a href="/job/managejob?rt=4" target="_self" class="nav-link " style="position: relative">

                            <span class="title">关注职位</span>
                            
                          </a>
                        </li>
                        <li data-Id="213" data-Permission="5" class="nav-item " style="position: relative">
                          
                          <a href="/job/managejob?rt=5" target="_self" class="nav-link " style="position: relative">

                            <span class="title">订阅职位</span>
                            
                          </a>
                        </li>
                        <li data-Id="211" data-Permission="5" class="nav-item " style="position: relative">
                          
                          <a href="/Fastjob" target="_self" class="nav-link " style="position: relative">

                            <span class="title">快速录入</span>
                            
                          </a>
                        </li>
                        <li data-Id="205" data-Permission="5" class="nav-item " style="position: relative">
                          
                          <a href="/deliverycenter/editjobinfo?jobsourcetype=0" target="_self" class="nav-link " style="position: relative">

                            <span class="title">完整录入</span>
                            
                          </a>
                        </li>
                        <li data-Id="206" data-Permission="219" class="nav-item " style="position: relative">
                          
                          <a href="/apps/managereward.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">悬赏猎聘</span>
                            
                          </a>
                        </li>
                        <li data-Id="207" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/apps/managecases.aspx?datefrom=2024-10-17&amp;dateto=2025-04-17" target="_self" class="nav-link " style="position: relative">

                            <span class="title">成功案例</span>
                            
                          </a>
                        </li>
                        <li data-Id="209" data-Permission="220" class="nav-item " style="position: relative">
                          
                          <a href="/cooperation/index" target="_self" class="nav-link " style="position: relative">

                            <span class="title">合作联盟</span>
                            
                          </a>
                        </li>
                        <li data-Id="210" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/cooperation/cooperationranklist" target="_self" class="nav-link " style="position: relative">

                            <span class="title">合作看板</span>
                            
                          </a>
                        </li>

                </ul>

          </li>
          <li data-Id="4" data-Permission="7" class="nav-item ">
            <a href="/resume/nsearchresume" target="_self" class="nav-link nav-toggle">
                  <i class="icon-emoticon-smile"></i>

              <span class="title">人选管理</span>



                  <span class="arrow"></span>

            </a>
                <ul class="sub-menu" style="display: none;">
                        <li data-Id="301" data-Permission="27" class="nav-item " style="position: relative">
                          
                          <a href="/profile/myrecommendmanage.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">推荐管理</span>
                            
                          </a>
                        </li>
                        <li data-Id="302" data-Permission="27" class="nav-item " style="position: relative">
                          
                          <a href="/profile/myrecommendmanage.aspx?status=8" target="_self" class="nav-link " style="position: relative">

                            <span class="title">我的offer</span>
                            
                          </a>
                        </li>
                        <li data-Id="323" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/PersonnelBank/Index" target="_self" class="nav-link " style="position: relative">

                            <span class="title">人才银行</span>
                            
                          </a>
                        </li>
                        <li data-Id="324" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/PersonnelBank/ResumeUploaderPage" target="_self" class="nav-link " style="position: relative">

                            <span class="title">上传简历</span>
                            
                          </a>
                        </li>
                        <li data-Id="303" data-Permission="27" class="nav-item " style="position: relative">
                          
                          <a href="/JobCandidate/entrymanagement" target="_self" class="nav-link " style="position: relative">

                            <span class="title">入职人选</span>
                            
                          </a>
                        </li>
                        <li data-Id="304" data-Permission="24" class="nav-item " style="position: relative">
                          
                          <a href="/profile/searchrecordmanage.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">寻访管理</span>
                            
                          </a>
                        </li>
                        <li data-Id="306" data-Permission="7" class="nav-item " style="position: relative">
                          
                          <a href="/resume/nsearchresume" target="_self" class="nav-link " style="position: relative">

                            <span class="title">搜索简历</span>
                            
                          </a>
                        </li>
                        <li data-Id="307" data-Permission="7" class="nav-item " style="position: relative">
                          
                          <a href="/apps/editresume.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">录入简历</span>
                            
                          </a>
                        </li>
                        <li data-Id="314" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/apps/managebackgroundreports.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">背景调查</span>
                            
                          </a>
                        </li>
                        <li data-Id="317" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/JobCandidate/CandidatePersonManagement" target="_self" class="nav-link " style="position: relative">

                            <span class="title">替补人选</span>
                            
                          </a>
                        </li>
                        <li data-Id="318" data-Permission="213" class="nav-item " style="position: relative">
                          
                          <a href="/talentmapping/searchcompanylist" target="_self" class="nav-link " style="position: relative">

                            <span class="title">人才地图</span>
                            
                          </a>
                        </li>
                        <li data-Id="321" data-Permission="181" class="nav-item " style="position: relative">
                          
                          <a href="/talentmapping/mappingcompanylist" target="_self" class="nav-link " style="position: relative">

                            <span class="title">Mapping</span>
                            
                          </a>
                        </li>
                        <li data-Id="305" data-Permission="7" class="nav-item " style="position: relative">
                          
                          <a href="/resume/manageresumes.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">我的简历</span>
                            
                          </a>
                        </li>
                        <li data-Id="310" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/resume/browseresume.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">浏览记录</span>
                            
                          </a>
                        </li>
                        <li data-Id="311" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/resumefolder/managerfolder" target="_self" class="nav-link " style="position: relative">

                            <span class="title">简历收藏</span>
                            
                          </a>
                        </li>
                        <li data-Id="312" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/JobCandidate/DeliveryRecords" target="_self" class="nav-link " style="position: relative">

                            <span class="title">锐仕精英</span>
                            
                          </a>
                        </li>

                </ul>

          </li>
          <li data-Id="12" data-Permission="0" class="nav-item ">
            <a href="/profile/myprofile.aspx" target="_self" class="nav-link nav-toggle">
                  <i class="icon-speech"></i>

              <span class="title">信息管理</span>



                  <span class="arrow"></span>

            </a>
                <ul class="sub-menu" style="display: none;">
                        <li data-Id="1206" data-Permission="214" class="nav-item " style="position: relative">
                          
                          <a href="/docmanage/index" target="_self" class="nav-link " style="position: relative">

                            <span class="title">锐仕云盘</span>
                            
                          </a>
                        </li>
                        <li data-Id="418" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/workorder/workorderlist" target="_self" class="nav-link " style="position: relative">

                            <span class="title">工单管理</span>
                            
                          </a>
                        </li>
                        <li data-Id="420" data-Permission="215" class="nav-item " style="position: relative">
                          
                          <a href="/BusinessCard/CardCase" target="_self" class="nav-link " style="position: relative">

                            <span class="title">名片管理</span>
                            
                          </a>
                        </li>
                        <li data-Id="401" data-Permission="29" class="nav-item " style="position: relative">
                          
                          <a href="/profile/mysmsmanage.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">短信管理</span>
                            
                          </a>
                        </li>
                        <li data-Id="402" data-Permission="216" class="nav-item " style="position: relative">
                          
                          <a href="/apps/manageemail.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">邮件管理</span>
                            
                          </a>
                        </li>
                        <li data-Id="403" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/message/managemessages.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">我的通知</span>
                            
                          </a>
                        </li>
                        <li data-Id="404" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/profile/taskmanage.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">我的日程</span>
                            
                          </a>
                        </li>
                        <li data-Id="405" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/profile/mysuggestionmanage.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">官网留言</span>
                            
                          </a>
                        </li>
                        <li data-Id="509" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/admin/managecommunications.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">留言管理</span>
                            
                          </a>
                        </li>
                        <li data-Id="406" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/profile/mycolleague.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">我的同事</span>
                            
                          </a>
                        </li>
                        <li data-Id="407" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/profile/accesslogmanage.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">登录日志</span>
                            
                          </a>
                        </li>
                        <li data-Id="408" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/profile/password.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">修改密码</span>
                            
                          </a>
                        </li>
                        <li data-Id="409" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/branchadmin/realtimedata.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">实时数据</span>
                            
                          </a>
                        </li>
                        <li data-Id="410" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/branchadmin/realtimedistribution.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">提成数据</span>
                            
                          </a>
                        </li>
                        <li data-Id="412" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/apps/managecalllogs.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">通话日志</span>
                            
                          </a>
                        </li>
                        <li data-Id="413" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/interaction/manage" target="_self" class="nav-link " style="position: relative">

                            <span class="title">目标管理</span>
                            
                          </a>
                        </li>
                        <li data-Id="414" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/rnsstools/mailcard" target="_self" class="nav-link " style="position: relative">

                            <span class="title">邮件名片</span>
                            
                          </a>
                        </li>
                        <li data-Id="415" data-Permission="218" class="nav-item " style="position: relative">
                          
                          <a href="/questionanswering/zhidao" target="_self" class="nav-link " style="position: relative">

                            <span class="title">点子大王</span>
                            
                          </a>
                        </li>
                        <li data-Id="416" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/notice/manage" target="_self" class="nav-link " style="position: relative">

                            <span class="title">系统公告</span>
                            
                          </a>
                        </li>
                        <li data-Id="421" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/rnsstools/staffcard" target="_self" class="nav-link " style="position: relative">

                            <span class="title">名片贺卡</span>
                            
                          </a>
                        </li>
                        <li data-Id="424" data-Permission="217" class="nav-item " style="position: relative">
                          
                          <a href="/SystemAppeal/index" target="_self" class="nav-link " style="position: relative">

                            <span class="title">申诉管理</span>
                            
                          </a>
                        </li>

                </ul>

          </li>
          <li data-Id="15" data-Permission="0" class="nav-item ">
            <a href="/Redirect/OfficeMenuRedirect?action=staffassessmentperformance" target="_self" class="nav-link nav-toggle">
                  <i class="icon-trophy"></i>

              <span class="title">绩效管理</span>



                  <span class="arrow"></span>

            </a>
                <ul class="sub-menu" style="display: none;">
                        <li data-Id="602" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/profile/performancerank.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">绩效排行</span>
                            
                          </a>
                        </li>
                        <li data-Id="603" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/Redirect/OfficeMenuRedirect?action=manageperassessmentperformance" target="_self" class="nav-link " style="position: relative">

                            <span class="title">绩效审核</span>
                            
                          </a>
                        </li>
                        <li data-Id="604" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/Redirect/OfficeMenuRedirect?action=staffassessmentperformance" target="_self" class="nav-link " style="position: relative">

                            <span class="title">我的绩效</span>
                            
                          </a>
                        </li>
                        <li data-Id="606" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/areaperformance/ranking" target="_self" class="nav-link " style="position: relative">

                            <span class="title">区域业绩</span>
                            
                          </a>
                        </li>
                        <li data-Id="607" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/Performance/YearPerformanceTarget" target="_self" class="nav-link " style="position: relative">

                            <span class="title">业绩目标</span>
                            
                          </a>
                        </li>
                        <li data-Id="2302" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/processanalysis/index" target="_self" class="nav-link " style="position: relative">

                            <span class="title">量化分析</span>
                            
                          </a>
                        </li>
                        <li data-Id="609" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/teamdata/index" target="_self" class="nav-link " style="position: relative">

                            <span class="title">团队数据</span>
                            
                          </a>
                        </li>

                </ul>

          </li>
          <li data-Id="16" data-Permission="17" class="nav-item ">
            <a href="/finance/incomemanage" target="_self" class="nav-link nav-toggle">
                  <i class="icon-credit-card"></i>

              <span class="title">财务管理</span>


                  <div class="circle-container">
                    <span class="circle" id="circleSalaryOneNum" style="display:none;"></span>
                  </div>

                  <span class="arrow"></span>

            </a>
                <ul class="sub-menu" style="display: none;">
                        <li data-Id="801" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/finance/incomemanage" target="_self" class="nav-link " style="position: relative">

                              <span class="title">公司回款</span>

                          </a>
                        </li>
                        <li data-Id="811" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/finance/reimbursemanage" target="_self" class="nav-link " style="position: relative">

                              <span class="title">报销申请</span>

                          </a>
                        </li>
                        <li data-Id="814" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/finance/ReceivableManage" target="_self" class="nav-link " style="position: relative">

                              <span class="title">收入确认</span>

                          </a>
                        </li>
                        <li data-Id="804" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/finance/managedistributions.aspx" target="_self" class="nav-link " style="position: relative">

                              <span class="title">提成管理</span>

                          </a>
                        </li>
                        <li data-Id="805" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/finance/managebonuses.aspx" target="_self" class="nav-link " style="position: relative">

                              <span class="title">我的提成</span>

                          </a>
                        </li>
                        <li data-Id="806" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/finance/manageperformances.aspx" target="_self" class="nav-link " style="position: relative">

                              <span class="title">我的业绩</span>

                          </a>
                        </li>
                        <li data-Id="807" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/finalsalary/ManageSalary" target="_self" class="nav-link " style="position: relative">

                              <span class="title">工资管理</span>
                                <span class="circle" style="position: absolute;top:8px;right:0;" id="circleSalaryNum">1</span>

                          </a>
                        </li>
                        <li data-Id="808" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/apps/manageinvoices.aspx" target="_self" class="nav-link " style="position: relative">

                              <span class="title">发票管理</span>

                          </a>
                        </li>
                        <li data-Id="810" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/apps/manageprofits.aspx" target="_self" class="nav-link " style="position: relative">

                              <span class="title">盈利中心</span>

                          </a>
                        </li>
                        <li data-Id="1209" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/Finalsalary/Bonus" target="_self" class="nav-link " style="position: relative">

                              <span class="title">超额奖励</span>

                          </a>
                        </li>
                        <li data-Id="816" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/Finance/Recheck" target="_self" class="nav-link " style="position: relative">

                              <span class="title">收入复核</span>

                          </a>
                        </li>

                </ul>

          </li>
          <li data-Id="18" data-Permission="35" class="nav-item ">
            <a href="javascript:void(0);" target="_self" class="nav-link nav-toggle">
                  <i class="icon-notebook"></i>

              <span class="title">行政管理</span>



                  <span class="arrow"></span>

            </a>
                <ul class="sub-menu" style="display: none;">
                        <li data-Id="701" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/administration/attendancemanage.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">考勤管理</span>
                            
                          </a>
                        </li>
                        <li data-Id="702" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/administration/businessactivitymanage.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">外出管理</span>
                            
                          </a>
                        </li>
                        <li data-Id="703" data-Permission="38" class="nav-item " style="position: relative">
                          
                          <a href="/administration/absencemanage.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">请假管理</span>
                            
                          </a>
                        </li>
                        <li data-Id="705" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/apps/manageregulation.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">制度大全</span>
                            
                          </a>
                        </li>
                        <li data-Id="708" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/Profile/PersonnelChanges.aspx" target="_self" class="nav-link " style="position: relative">

                            <span class="title">人事变动</span>
                            
                          </a>
                        </li>
                        <li data-Id="711" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="/Staff/WorkLoad" target="_self" class="nav-link " style="position: relative">

                            <span class="title">工时量表</span>
                            
                          </a>
                        </li>

                </ul>

          </li>
          <li data-Id="19" data-Permission="0" class="nav-item ">
            <a href="/NetworkSchool/index" target="_blank" class="nav-link nav-toggle">
                  <i class="icon-graduation"></i>

              <span class="title">达人网校</span>

                  <div class="circle-container">
                    <span class="circle" id="circleOneNum" style="display:none;"></span>
                  </div>


                  <span class="arrow"></span>

            </a>
                <ul class="sub-menu" style="display: none;">
                        <li data-Id="1005" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="https://staff-exam.risfond.com/" target="_blank" class="nav-link " style="position: relative">

                              <span class="title">学员中心</span>
                                <span class="circle" style="position: absolute;top:8px;right:0;" id="circleNum">1</span>

                          </a>
                        </li>
                        <li data-Id="1007" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/NetworkSchool/CourseList?advice=1" target="_blank" class="nav-link " style="position: relative">

                              <span class="title">课程专区</span>

                          </a>
                        </li>
                        <li data-Id="1008" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/Lecturer/Index?advice=2" target="_blank" class="nav-link " style="position: relative">

                              <span class="title">讲师专区</span>

                          </a>
                        </li>
                        <li data-Id="1009" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/NetworkSchool/NetSchoolFileCenter?Joy=DataArea&amp;advice=3" target="_blank" class="nav-link " style="position: relative">

                              <span class="title">资料专区</span>

                          </a>
                        </li>
                        <li data-Id="1010" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/NetworkSchool/NetSchoolFileCenter?Joy=helpCenter&amp;advice=4" target="_blank" class="nav-link " style="position: relative">

                              <span class="title">活动中心</span>

                          </a>
                        </li>
                        <li data-Id="1011" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/knowledgebank/index?advice=5" target="_blank" class="nav-link " style="position: relative">

                              <span class="title">锐仕智库</span>

                          </a>
                        </li>
                        <li data-Id="1012" data-Permission="0" class="nav-item " style="position: relative">

                          <a href="/NetworkSchool/NetworkManager?advice=1" target="_self" class="nav-link " style="position: relative">

                              <span class="title">课程管理</span>

                          </a>
                        </li>

                </ul>

          </li>
          <li data-Id="25" data-Permission="0" class="nav-item ">
            <a href="https://staff-hr.risfond.com?ticket=afdf75bd13df4187987ddec84a359b01" target="_blank" class="nav-link nav-toggle">
                  <i class="icon-user"></i>

              <span class="title">人事管理</span>
                  <div class="circle-container" id="CurrentMyApprovalCount">
                  </div>



                  <span class="arrow"></span>

            </a>

          </li>
          <li data-Id="14" data-Permission="11" class="nav-item ">
            <a href="javascript:void(0);" target="_self" class="nav-link nav-toggle">
                  <i class="icon-settings"></i>

              <span class="title">系统管理</span>



                  <span class="arrow"></span>

            </a>
                <ul class="sub-menu" style="display: none;">
                        <li data-Id="528" data-Permission="0" class="nav-item " style="position: relative">
                          
                          <a href="https://admin.risfond.com/index" target="_blank" class="nav-link " style="position: relative">

                            <span class="title">市场中心</span>
                            
                          </a>
                        </li>

                </ul>

          </li>

    </ul>
    <!-- END SIDEBAR MENU -->
  </div>
  <!-- END SIDEBAR -->
</div>
<!-- END SIDEBAR -->

<style type="text/css">
  .hasMenu__hasPowers {
    position: absolute;
    bottom: 20px;
    margin-left: 3px;
    border-radius: 10px !important;
    background-color: red;
    color: #FFF !important;
    height: 18px;
    min-width: 18px;
    text-align: center;
    line-height: 18px;
    font-size: 13px;
    padding: 0 5px;
    font-weight: 300;
  }

  .circle-container {
    position: absolute;
    top: 0px;
    right: 0px;
    height: 100%;
    width: 51px;
    display: flex;
    justify-content: center;
  }

  .circle {
    font-size: 12px;
    text-align: center;
    color: #fff;
    background: #f83d3d;
    position: relative;
    height: 18px;
    line-height: 18px;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 20px !important;
    padding: 0px 7px !important;
  }
</style>
<script>

  // 是否有内招管理权限
  var hasPowers = $(".page-sidebar-menu > li[data-Permission = 7] > ul > li[data-Permission = 186] a.nav-link").length == 1;
  // 是否有内招管理菜单
  var hasMenu = $(".page-sidebar-menu > li[data-id = 4] > ul > li[data-id = 322] a.nav-link").length == 1;
  // 添加Tips的一级父元素
  var pageItem_one = $(".page-sidebar-menu > li[data-id = 4] a.nav-toggle");
  // 添加Tips的二级父元素
  var pageItem_two = $(".page-sidebar-menu > li[data-id = 4] > ul > li[data-id = 322] a.nav-link");
  // Tips是否存在
  var hasTips = $(".page-sidebar-menu > li[data-id = 4] a.nav-toggle > a[data-open = true]").length >= 1;
  // hrsopApi
  var hrsopApi = 'https://hrcenter-api.risfond.com'
  // 获取Tips数量
  function hasTisAjax(callback) {
    $.ajax({
      url: '/ClientRecruitment/GetJobRecruitCount',
      type: 'get',
      success: function (res) {
        if (res.Success) {
          if (res.Data > 0) {
            callback && callback(res.Data);
          }
        }
      }
    })
  }

  function getJobCooperationCount() {
    $.ajax({
      url: '/cooperation/GetNotHandleJobCooperationCount',
      type: 'post',
      success: function (res) {
        if (res.success && res.data > 0) {
          $(".page-sidebar-menu .nav-item[data-id=209] .nav-link").append('<b data-open="true" title="您有' + res.data + '条未处理合作信息~"  class="hasMenu__hasPowers">' + res.data + '</b>');
        }
      }
    })
  }

  // 添加获取的Tips元素
  function addTipsItem(TipsNum) {
    // Tips元素
    var titleTip = '您有' + TipsNum + '未读内招管理信息~'
    var addTips = '<b onclick="openClientRecruitment()" data-open="true" title=' + titleTip + ' class="hasMenu__hasPowers">' + TipsNum + '</b>';
    pageItem_one.append(addTips);
    pageItem_two.append(addTips);
  }

  // 点击气泡跳转到内招管理
  function openClientRecruitment() {
    stopPropagation();
    window.open('/ClientRecruitment/Index');
  }

  // 阻止事件冒泡
  function stopPropagation(e) {
    e = e || window.event;
    if (e.stopPropagation) {
      e.stopPropagation();
    } else {
      e.cancelBubble = true; // IE 阻止冒泡方法
    }
  }
  //获取审批数量
  function getCurrentMyApprovalCount() {
    $.ajax({
      url: hrsopApi +"/api/staff/GetTokenByTicket?ticket="+'afdf75bd13df4187987ddec84a359b01',
      dataType: 'json',
      type: 'POST',
      data: {},
      success: (res) => {
        if (!res.Success)
          return;
        var token = res.Data;
        $.ajax({
          url: hrsopApi +"/api/Menu/menulist_rnss",
          dataType: 'json',
          type: 'POST',
          data: {},
          success: (resdata) => {
            if (resdata.Success) {
              var count = resdata.Data.ApproveCount + resdata.Data.ResumeFilterCount > 99 ? "99+" : resdata.Data.ApproveCount + resdata.Data.ResumeFilterCount;
              if (resdata.Data.ApproveCount + resdata.Data.ResumeFilterCount > 0) {
                $("#CurrentMyApprovalCount").append('<span class="circle">' + count + '</span>');
              }
              var menus = resdata.Data.Menus;
              if (menus.length > 0) {
                //加载菜单信息
                var menuHtml = "<ul class=\"sub-menu\" style=\"display: none;width:210px;\">";
                for (var i = 0; i < menus.length; i++) {
                  menuHtml += "<li data-id=\"0\" data-permission=\"0\" class=\"nav-item\" style='" + (i%2==0?'width:115px':'') +"'>";
                  menuHtml += "<a href=\"" + menus[i].MenuLink + "\" class=\"nav-link\" target='_blank'>";
                  menuHtml += "<span class=\"title\">" + menus[i].MenuName + "</span>";
                  if (menus[i].MenuIndex == 2 && resdata.Data.ApproveCount > 0) {
                    menuHtml += "<span class=\"circle\" style='margin-left:2px'>" + (resdata.Data.ApproveCount > 99 ? "99" : resdata.Data.ApproveCount) + "</span>";
                  }
                  if (menus[i].MenuIndex == 3 && resdata.Data.ResumeFilterCount > 0) {
                    menuHtml += "<span class=\"circle\" style='margin-left:2px'>" + (resdata.Data.ResumeFilterCount > 99 ? "99" : resdata.Data.ResumeFilterCount) + "</span>";
                  }
                  menuHtml += "</a >";
                  menuHtml += " </li > "
                }
                menuHtml += "</ul>";
                $("#CurrentMyApprovalCount").parent().after(menuHtml);
              }
            }
          },
          beforeSend: (request) => {
            request.setRequestHeader("Authorization", token);
            request.setRequestHeader("Content-Type", 'application/json');
            request.setRequestHeader('X-XSS-Protection', '0');
          },
          error: function (xhr, et) {
            if (et && et == "timeout") {
              //this.$message.error('服务器连接超时');
            } else if (xhr.status == "500") {
              //this.$message.error('服务器错误，稍后重试！');
            } else {
              //alert(xhr.responseText);
            }
          }
        });
      },
      error: function (xhr, et) {

      }
    })
  }

  var getApiUrl = 'https://staff-exam-api.risfond.com'
  //达人网校数量
  function funNoticeApi() {
 var staffid = '18733';
    $.ajax({
      url: 'https://staff-exam-api.risfond.com/api/RnssNotice/GetCountList/' + staffid,
      type: 'get',
      success: function (res) {
        if (res.succeeded) {
          var num = 0
          num = res.data.enrollmentCount + res.data.examCount + res.data.trainingCount
          if (num == 0) {
            $("#circleNum").hide()
            $("#circleOneNum").hide()
          } else {
            $("#circleNum").show()
            $("#circleOneNum").show()
            $("#circleNum").html(num)
            $("#circleOneNum").html(num)
          }
          //$('li[data-id="19"]')
        } else {
          $("#circleNum").hide()
          $("#circleOneNum").hide()
        }
      }
    })
  }
  funNoticeApi();

  //工资管理数量
  function funSalaryApi() {
    $.ajax({
      url: '/finalsalary/GetSalaryAuditCount',
      type: 'post',
      success: function (res) {
        if (res.Success) {
          var num = 0
          num = res.Data
          if (num == 0) {
            $("#circleSalaryNum").hide()
            $("#circleSalaryOneNum").hide()
          } else {
            $("#circleSalaryNum").show()
            $("#circleSalaryOneNum").show()
            $("#circleSalaryNum").html(num)
            $("#circleSalaryOneNum").html(num)
          }
          //$('li[data-id="19"]')
        } else {
          $("#circleSalaryNum").hide()
          $("#circleSalaryOneNum").hide()
        }
      }
    })
  }
  funSalaryApi()
  // 判断是否有权限 和 菜单
  //  if (hasMenu &&hasPowers && !hasTips) {
  //    hasTisAjax(addTipsItem);
  //  }
  //  setTimeout(function () {
  //    getJobCooperationCount();
  //  }, 2000);

  // 人才银行TIP 控制项
  if (window.localStorage.getItem("bankTip") == 1) {
    $("#banktip").hide()
  }
  $("#banktip").click(function () {
    $(this).hide();
    window.localStorage.setItem("bankTip", 1)
  })
  //
  var IsRedirtPwd='0';
  //
  if (IsRedirtPwd == "1") {
    var $eduQueryLink = $(".page-sidebar-menu .nav-link");
    //
    $eduQueryLink.on("click", function () {
      var href = $(this).attr("href");
      if (href && href.indexOf("/profile/password.aspx") == -1) {
        //type=1 密码到期修改 type=0 默认进入
        window.location.href = "/profile/password.aspx?type=1";
        return false;
      }
    })
  }
  function Init() {
    getCurrentMyApprovalCount();
  }
  // 获取审批数量
  $(function () {
    Init();
  })
</script>

        <div class="page-content-wrapper">
          <div class="page-content body" id="page-content" style="position: relative;">

                <!-- base四季顶部文件-->
                <div class="huanfu-base-head-img"></div>

            

<style>
  .xz_parent2 {
    z-index: 2200 !important;
  }

  .paihang-select .el-input .el-input__inner {
    height: 30px !important;
  }

  .paihang-select .el-input .el-select__caret {
    line-height: 30px;
  }

  .index-paihang-table-new {
    display: block;
  }
</style>
<!-- indexBase.css是当前页面的css -->
<!--后端:于云浩 当前页面接口文档 http://*************:8888/index.php?s=/3&page_id=1942  -> Office3 -> 新首页-->
<!--产品经理:段万军 UI:康松伟 当前页面原型地址 http://************:8008/rnss-index/#g=1&p=首页-顾问-->
<div id="concenter" class="huanfu-po-box" v-cloak>
  <!-- 四季顶部文件-->
  
  <div class="row" style="margin-top: -10px;">
    <!-- 顶部第一块开始 顶部第一块分成了左右两块 左边是用户信息 右边是客户提醒 简单的接口调用,23,11新增样式名head-11-warp,后边改版时可去掉-->
    <div class="index-top index-top-new com-flex border-ra head-11-wrap">
      <!-- 用户信息开始-->
      <!-- 右侧顾问统计23.11版本start-->
      
      <!-- 右侧顾问统计23.11版本end-->
      <!-- 用户信息开始 调了两个接口 涉及到的接口有 getPersonData getPersonData2-23.10.30改版-->
      <div class="index-top-left">
        <div class="top-left-left">
          <img alt="" class="demo-img" :src="prsonziliao.staffPictureUrl" />
          <p class="top-left-left-p">HI，{{prsonziliao.name}}</p>
          <p class="top-left-left-p2">{{prsonziliao.companyName}}</p>
          <div class="hengxian"></div>
          <p class="top-left-left-p3">未来的你,一定会感谢今天 努力的自己!</p>
        </div>
        <div class="top-left-right">
          <div class="top-left-right-div">
            <p class="left-right-div-p">{{personYj}}万</p>
            <p class="left-right-div-p2">业绩目标</p>
          </div>
          <div class="div-hengxian"></div>
          <div class="top-left-right-div">
            <p class="left-right-div-p">{{personJd}}%</p>
            <p class="left-right-div-p2">完成进度</p>
          </div>
          <div class="div-hengxian"></div>
          <div class="top-left-right-div">
            <p class="left-right-div-p" style="color: #D91E18;">{{personWwc}}万</p>
            <p class="left-right-div-p2">未完成</p>
          </div>
          <div class="div-hengxian"></div>
          <div class="top-left-right-div">
            <p class="left-right-div-p">{{personHk}}万</p>
            <p class="left-right-div-p2">年度回款</p>
          </div>
          <div class="top-left-right-div">
            <p class="left-right-div-p">{{personTj}}个</p>
            <p class="left-right-div-p2">年度推荐</p>
          </div>
          <div class="div-hengxian"></div>
          <div class="top-left-right-div">
            <p class="left-right-div-p">{{personMs}}个</p>
            <p class="left-right-div-p2">年度面试</p>
          </div>
          <div class="div-hengxian"></div>
          <div class="top-left-right-div">
            <p class="left-right-div-p">{{personOffer}}个</p>
            <p class="left-right-div-p2">年度offer</p>
          </div>
          <div class="div-hengxian"></div>
          <div class="top-left-right-div">
            <p class="left-right-div-p">{{personRz}}个</p>
            <p class="left-right-div-p2">年度入职</p>
          </div>
        </div>
      </div>
      <!-- 用户信息结束 -->
      <!-- 事项提醒开始 涉及到的接口有 getClientRemindMessage-23.10.30改版-->
      <div class="index-top-right">
        <div class="index-top-right-top">
          <img class="h-tixing" src="../images/h-tixing.svg" />
          <p class="index-top-right-top-p" style="color: #2A7FCC;font-weight: 600;font-size: 18px;">事项提醒</p>
          <el-select @change="getClientRemindMessage" class="paihang-select" v-model="monthChangeData" placeholder="请选择" style="margin-top: 21px;margin-left: 9px;width: 105px;">
            <el-option v-for="item in monthChange"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
          <div class="index-top-right-top-div">
            <div id="topChangeMonth1" @click="topChangeMonth1(1)" class="sxtj-left sxtj-active">
              <p class="sxtj-left-p">本月</p>
            </div>
            <div id="topChangeMonth2" @click="topChangeMonth1(2)" class="sxtj-left" style="border: 0;">
              <p class="sxtj-left-p">下月</p>
            </div>
          </div>
        </div>
        <div class="index-top-right-bottom">
          <div v-if="thingRemind.length !=0" v-for="(item,index) in thingRemind" :key="item.index" class="index-top-right-bottom-content">
            <div class="sxtx-qiu"></div>
            <p class="index-top-right-bottom-content-p" v-show="monthChangeData == 1" :title="item.dateTo">{{item.dateTo}}</p>
            <p class="index-top-right-bottom-content-p2" @click="jumpContactName(item.jobCandidateId)" :title="item.contactName" v-show="monthChangeData == 1">{{item.contactName}}</p>
            <p class="index-top-right-bottom-content-p3" @click="jumpjobTitle(item.jobId)" :title="item.jobTitle" v-show="monthChangeData == 1">{{item.jobTitle}}</p>
            <p class="index-top-right-bottom-content-p2" style="width: 190px;" @click="jumpPartyAName(item.clientId)" :title="item.partyAName" v-show="monthChangeData == 2">{{item.partyAName}}</p>
            <p class="index-top-right-bottom-content-p3" :title="item.dateTo" v-show="monthChangeData == 2">{{item.dateTo}}</p>
          </div>
          <div v-if="thingRemind.length ==0" class="noData" style="height: 100%;">
            <div class="noData-content">
              <img class="h-nodata" src="/images/h-nodata.png" />
              <p class="noData-content-p">暂无数据~</p>
            </div>
          </div>
        </div>
      </div>
      <!-- 事项提醒结束 -->
    </div>
    <!-- 顶部第一块结束 -->
    <!---------------------------快捷入口开始----------------------------->
    <!-- 快捷入口部分是吴冠圣写的 问他 获取的接口是 quickEntrance-->
    <div class="kuaijie-wrap-new1">
      <div v-if="isKuaijie" class="row page-head page-head-new border-ra mt15">
        <div class="page-head-icon" @click="toggerKuaiJie">收起<img src="/images/index/togekai.png" class="page-dead-icon" /></div>
        <div class="page-title page-mr-left">
          <div class="caption">
            <svg class="icon1 caption-icon" aria-hidden="true">
              <use xlink:href="#icon-kuaijierukou"></use>
            </svg>
            <span class="caption-subject font-blue-sharp bold uppercase caption-sharp">快捷入口</span>
          </div>
        </div>
        <div class="page-toolbar">
          <div class="dashboard-report-range2" data-toggle="modal" data-target="#customizeShortcuts">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-shezhi"></use>
            </svg>&nbsp;设置
          </div>
        </div>
        <div class="kuaijierukou">
          <div class="kuaijierukou-top"></div>
          <div class="kuaijierukou-content">
            <div class="kuaijierukou-content-item" v-for="(item,index) in yesAddDTO">
              <img :src="item.shortcutIconUrl" @click="toDetail(item)" />
              <p class="kuaijierukou-content-item-title">{{item.shortcutName}}</p>
            </div>
          </div>
          <div class="wait_loading" v-if="isKuaijieFlag">
            <svg class="svg_wgs" viewBox="0 0 120 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <g id="circle" class="g-circles g-circles--v1">
                <circle id="12" transform="translate(35, 16.698730) rotate(-30) translate(-35, -16.698730) " cx="35" cy="16.6987298" r="10"></circle>
                <circle id="11" transform="translate(16.698730, 35) rotate(-60) translate(-16.698730, -35) " cx="16.6987298" cy="35" r="10"></circle>
                <circle id="10" transform="translate(10, 60) rotate(-90) translate(-10, -60) " cx="10" cy="60" r="10"></circle>
                <circle id="9" transform="translate(16.698730, 85) rotate(-120) translate(-16.698730, -85) " cx="16.6987298" cy="85" r="10"></circle>
                <circle id="8" transform="translate(35, 103.301270) rotate(-150) translate(-35, -103.301270) " cx="35" cy="103.30127" r="10"></circle>
                <circle id="7" cx="60" cy="110" r="10"></circle>
                <circle id="6" transform="translate(85, 103.301270) rotate(-30) translate(-85, -103.301270) " cx="85" cy="103.30127" r="10"></circle>
                <circle id="5" transform="translate(103.301270, 85) rotate(-60) translate(-103.301270, -85) " cx="103.30127" cy="85" r="10"></circle>
                <circle id="4" transform="translate(110, 60) rotate(-90) translate(-110, -60) " cx="110" cy="60" r="10"></circle>
                <circle id="3" transform="translate(103.301270, 35) rotate(-120) translate(-103.301270, -35) " cx="103.30127" cy="35" r="10"></circle>
                <circle id="2" transform="translate(85, 16.698730) rotate(-150) translate(-85, -16.698730) " cx="85" cy="16.6987298" r="10"></circle>
                <circle id="1" cx="60" cy="10" r="10"></circle>
              </g>
              <use xlink:href="#circle" class="use" />
            </svg>
          </div>
        </div>
      </div>
      <div class="page-no-kuaijie" v-else>
        <div class="page-kuaijie-icon" @click="toggerKuaiJie"><img src="/images/index/kj.png" class="page-icon2" />快捷入口展开<img src="/images/index/togekai.png" class="page-icon1" /></div>
      </div>
    </div>
    <!---------------------------快捷入口结束----------------------------->
    <!---------------------------实时推荐榜、实时回款榜开始6.0.4版本----------------------------->
    <div class="page-head paihangbang index-paihangbang">
      <!--实时回款榜-->

      <div class="portlet border-ra light bordered index-paihangbang-left table-box-new">
        <div class="portlet-title" style="border:none;">
          <div class="caption">
            <svg class="icon1 portlet-title-svg" aria-hidden="true">
              <use xlink:href="#icon-qian1"></use>
            </svg>
            <span class="caption-subject font-blue-sharp bold uppercase base-title">实时回款榜</span>
          </div>
          <div class="kpbig-top-more" style="margin-top: 6px;">
            <a class="kpbig-link" href="/branchadmin/realtimedata.aspx" target="_blank">
              <p class="kpbig-top-more-p">更多</p> <img src="../images/h-more.svg" class="moreSvg">
            </a>
          </div>
        </div>

        <div class="portlet-body" style="margin-top:-6px;padding-top:0px">

          <div class="tbl-header shishi-header">
            <table class="table table-striped table-bordered table-hover r-tb-tuijian" style="margin-bottom: 0px">
              <thead>
                <tr>
                  <th width="20%" style="text-align:center">金额</th>
                  <th width="40%" style="text-align:center">支付单位</th>
                  <th class="tuijianstaff" width="20%" style="text-align:center">服务顾问</th>
                  <th width="20%" style="text-align:center">公司</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>
          <div class="tbl-body shishi-body">
            <table class="table table-striped table-bordered table-hover r-tb-tuijian r-tb-shishi-am" style="margin-bottom: 0px">
              <thead>
                <tr>
                  <th width="20%" style="text-align:center">金额</th>
                  <th width="40%" style="text-align:center">支付单位</th>
                  <th class="tuijianstaff" width="20%" style="text-align:center">服务顾问</th>
                  <th width="20%" style="text-align:center">公司</th>
                </tr>
              </thead>
              <tbody>
                <tr class="shishiTr" v-for="item in collectionList">
                  <td class="text-left text-color" :title="item.amount + '元'">{{item.amount}}元</td>
                  <td class="text-left" :title="item.clientExhibitionName">{{item.clientExhibitionName}}</td>
                  <td class="r-status showCard-item r-tuijian-new" :data-id="item.consultantStaffId" :title="item.consultantStaffName">
                    
                    <a href="javascript:void(0);" class="r-tuijian-new-a" :class="item.consultantStaffGender == 1 ? 'male':'female'">{{item.consultantStaffName}}</a>
                  </td>
                  <td class="text-center" :title="item.companyName">{{item.companyName}}</td>
                </tr>

              </tbody>

            </table>
          </div>


          <div class="wait_loading wait_loading_new" v-if="shishihuikuangbangIsflag">
            <svg class="svg_wgs" viewBox="0 0 120 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <g id="circle" class="g-circles g-circles--v1">
                <circle id="12" transform="translate(35, 16.698730) rotate(-30) translate(-35, -16.698730) " cx="35" cy="16.6987298" r="10"></circle>
                <circle id="11" transform="translate(16.698730, 35) rotate(-60) translate(-16.698730, -35) " cx="16.6987298" cy="35" r="10"></circle>
                <circle id="10" transform="translate(10, 60) rotate(-90) translate(-10, -60) " cx="10" cy="60" r="10"></circle>
                <circle id="9" transform="translate(16.698730, 85) rotate(-120) translate(-16.698730, -85) " cx="16.6987298" cy="85" r="10"></circle>
                <circle id="8" transform="translate(35, 103.301270) rotate(-150) translate(-35, -103.301270) " cx="35" cy="103.30127" r="10"></circle>
                <circle id="7" cx="60" cy="110" r="10"></circle>
                <circle id="6" transform="translate(85, 103.301270) rotate(-30) translate(-85, -103.301270) " cx="85" cy="103.30127" r="10"></circle>
                <circle id="5" transform="translate(103.301270, 85) rotate(-60) translate(-103.301270, -85) " cx="103.30127" cy="85" r="10"></circle>
                <circle id="4" transform="translate(110, 60) rotate(-90) translate(-110, -60) " cx="110" cy="60" r="10"></circle>
                <circle id="3" transform="translate(103.301270, 35) rotate(-120) translate(-103.301270, -35) " cx="103.30127" cy="35" r="10"></circle>
                <circle id="2" transform="translate(85, 16.698730) rotate(-150) translate(-85, -16.698730) " cx="85" cy="16.6987298" r="10"></circle>
                <circle id="1" cx="60" cy="10" r="10"></circle>
              </g>
              <use xlink:href="#circle" class="use" />
            </svg>
          </div>
          <div class="no_data_vp" v-show="hkDataLenFlag">
            <img src="../images/main/nodata.png" />
            <p>暂无数据</p>
          </div>
        </div>
      </div>

      <!--实时推荐榜-->
      <div class="portlet border-ra light bordered index-paihangbang-left table-box-new" style="float:right;">
        <div class="portlet-title" style="border:none;">
          <div class="caption">
            <svg class="icon1 portlet-title-svg" aria-hidden="true">
              <use xlink:href="#icon-tuijian"></use>
            </svg>
            <span class="caption-subject font-blue-sharp bold uppercase base-title">实时推荐榜</span>
          </div>
          <div class="kpbig-top-more" style="margin-top: 6px;">
            <a class="kpbig-link" href="/branchadmin/realtimedata.aspx" target="_blank">
              <p class="kpbig-top-more-p">更多</p> <img src="../images/h-more.svg" class="moreSvg">
            </a>
          </div>
        </div>
        <div class="portlet-body" style="margin-top:-6px;padding-top:0px">
          <div class="tbl-header tuijian-header">
            <table class="table table-striped table-bordered table-hover r-tb-tuijian" style="margin-bottom: 0px">
              <thead>
                <tr>
                  <th class="r-status" title="推荐状态">状态</th>
                  <th class="r-staff">人选</th>
                  <th class="r-job" title="推荐职位">推荐职位</th>
                  <th class="r-salay text-right" style="width:72px;">年薪</th>
                  <th class="r-tuijianstaff">推荐人</th>
                  <th class="r-city text-center">公司</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>
          <div class="tbl-body tuijian-body">
            <table class="table table-striped table-bordered table-hover r-tb-tuijian r-tb-tuijian-am" style="margin-bottom: 0px">
              <thead>
                <tr>
                  <th class="r-status" title="推荐状态">状态</th>
                  <th class="r-staff">人选</th>
                  <th class="r-job" title="推荐职位">推荐职位</th>
                  <th class="r-salay text-right" style="width:72px;">年薪</th>
                  <th class="r-tuijianstaff">推荐人</th>
                  <th class="r-city text-center">公司</th>
                </tr>
              </thead>
              <tbody>
                <tr class="tuijianTr" v-for="item in realTimeList">
                  <td class="r-status text-color" style="color:red" title="入职" v-if="item.stepStatus == 9">入职</td>
                  <td class="r-status text-color" style="color:RGB(252,223,18)" title="Offer" v-if="item.stepStatus == 8">Offer</td>
                  <td class="r-status" :title="item.contactName" style="color:#2A7FCC;">
                    
                    <span>{{item.contactName}}</span>
                  </td>
                  <td class="text-left" :title="item.jobTitle">{{item.jobTitle}}</td>
                  <td class="r-status text-color text-right" :title="item.jobSalary" style="color:red;">{{item.jobSalary}}万</td>
                  <td class="r-status showCard-item r-tuijian-new" :data-id="item.tuijianStaffId">
                    
                    <a href="javascript:void(0);" class="r-tuijian-new-a" :class="item.tuijianStaffGender == 1 ? 'male':'female'" :id='item.tuijianStaffId' :title="item.tuijianStaffName">{{item.tuijianStaffName}}</a>
                  </td>
                  <td class="text-left" :title="item.tuijianStaffCompanyName">{{item.tuijianStaffCompanyName}}</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="wait_loading wait_loading_new" v-if="shishituijaingbangIsflag">
            <svg class="svg_wgs" viewBox="0 0 120 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <g id="circle" class="g-circles g-circles--v1">
                <circle id="12" transform="translate(35, 16.698730) rotate(-30) translate(-35, -16.698730) " cx="35" cy="16.6987298" r="10"></circle>
                <circle id="11" transform="translate(16.698730, 35) rotate(-60) translate(-16.698730, -35) " cx="16.6987298" cy="35" r="10"></circle>
                <circle id="10" transform="translate(10, 60) rotate(-90) translate(-10, -60) " cx="10" cy="60" r="10"></circle>
                <circle id="9" transform="translate(16.698730, 85) rotate(-120) translate(-16.698730, -85) " cx="16.6987298" cy="85" r="10"></circle>
                <circle id="8" transform="translate(35, 103.301270) rotate(-150) translate(-35, -103.301270) " cx="35" cy="103.30127" r="10"></circle>
                <circle id="7" cx="60" cy="110" r="10"></circle>
                <circle id="6" transform="translate(85, 103.301270) rotate(-30) translate(-85, -103.301270) " cx="85" cy="103.30127" r="10"></circle>
                <circle id="5" transform="translate(103.301270, 85) rotate(-60) translate(-103.301270, -85) " cx="103.30127" cy="85" r="10"></circle>
                <circle id="4" transform="translate(110, 60) rotate(-90) translate(-110, -60) " cx="110" cy="60" r="10"></circle>
                <circle id="3" transform="translate(103.301270, 35) rotate(-120) translate(-103.301270, -35) " cx="103.30127" cy="35" r="10"></circle>
                <circle id="2" transform="translate(85, 16.698730) rotate(-150) translate(-85, -16.698730) " cx="85" cy="16.6987298" r="10"></circle>
                <circle id="1" cx="60" cy="10" r="10"></circle>
              </g>
              <use xlink:href="#circle" class="use" />
            </svg>
          </div>
          <div class="no_data_vp" v-show="tjDataLenFlag">
            <img src="../images/main/nodata.png" />
            <p>暂无数据</p>
          </div>
        </div>
      </div>


    </div>
    <!---------------------------实时推荐榜、实时回款榜结束----------------------------->
    <!-- 排行榜和实时信息互动结束 -->
    <!-- 联合交付和联合交付实时播报开始 -->
    <div class="lhjf-content border-ra">
      <!-- 联合交付开始 -->
      <!-- 联合交付要先获取条件 再通过条件获取数据 获取条件接口 getStaffAttentionJob-->
      <div class="lhjf-left">
        <div class="lhjf-left-top">
          <img class="h-hezuo" src="../images/h-hezuo.svg" />
          <p class="lhjf-left-top-p" style="color: #2A7FCC;font-weight: 600;font-size: 18px;">联合交付</p>
          <div class="lhjf-left-top-shezhi" @click="openZhiwei">
            <img class="h-shezhi" src="../images/h-shezhi.svg" />
            <p class="lhjf-left-top-shezhi-p">设置关注职位类型</p>

          </div>
          <div style="margin-top: 25px; height: 30px; float: left; " v-show="moreData==0">
            <span class="btn btn-sm  btn-outline btn-circle" style="margin-left: 12px; border-color: #2A7FCC !important; color: #2A7FCC !important; position: relative; " target="_blank" @click="JobScreenClick">
              <i style="width: 16px; height: 15px; display: inline-block; position: absolute; top: 4px;">
                <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <g id="联合交付-设置筛选条件" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g id="设置筛选条件" transform="translate(-628.000000, -108.000000)">
                      <g id="icon-筛选" transform="translate(628.000000, 108.000000)">
                        <rect id="矩形" x="0" y="0" width="20" height="20"></rect>
                        <g id="编组" transform="translate(2.142857, 2.857143)" fill="#2A7FCC" fill-rule="nonzero">
                          <path d="M11.7609013,0 C11.9853239,0 12.2034962,0.0739318328 12.3816688,0.210386972 C12.8288165,0.552873267 12.9134005,1.19268946 12.5705595,1.63939374 L8.3023573,7.20076104 L8.3023573,13.7157159 C8.3023575,13.8362093 8.27422618,13.9550386 8.22020316,14.0627427 L8.21098033,14.0805853 C8.00924863,14.4579587 7.53952273,14.6005401 7.16175911,14.3990213 L5.2662367,13.3878265 C4.80115769,13.1397146 4.51070945,12.6558175 4.51070945,12.1290874 L4.51070945,7.22248791 L0.211646243,1.64077717 C0.0786866241,1.46817026 0.00457510647,1.25747897 0.000195098355,1.03964366 L0,1.01924702 C0,0.456335053 0.456778459,0 1.02022251,0 L11.7609013,0 Z M15.1837601,12.3124266 C15.4767623,12.3124266 15.7142857,12.5497372 15.7142857,12.8424379 C15.7142857,13.1309707 15.4835021,13.3656562 15.1962818,13.3722896 L15.1837601,13.3724315 L10.1233167,13.3724315 C9.83031441,13.3724315 9.59280877,13.1351564 9.59280877,12.8424379 C9.59280877,12.5539052 9.82359239,12.3192196 10.1107949,12.3125685 L10.1233167,12.3124266 L15.1837601,12.3124266 L15.1837601,12.3124266 Z M15.1837601,9.45856337 C15.4767623,9.45856337 15.7142857,9.69583844 15.7142857,9.98855692 C15.7142857,10.2770896 15.4835021,10.5117752 15.1962818,10.5184263 L15.1837601,10.5185682 L10.1233167,10.5185682 C9.83031441,10.5185682 9.59280877,10.2812577 9.59280877,9.98855692 C9.59280877,9.70002419 9.82359239,9.4653386 10.1107949,9.45870526 L10.1233167,9.45856337 L15.1837601,9.45856337 L15.1837601,9.45856337 Z M15.1837601,6.60468236 C15.4767623,6.60468236 15.7142857,6.84197517 15.7142857,7.13469365 C15.7142857,7.42322638 15.4835021,7.65791196 15.1962818,7.66454531 L15.1837601,7.6646872 L10.1233167,7.6646872 C9.83031441,7.6646872 9.59280877,7.42739439 9.59280877,7.13469365 C9.59280877,6.84616092 9.82359239,6.61147533 10.1107949,6.60482425 L10.1233167,6.60468236 L15.1837601,6.60468236 Z" id="形状"></path>
                        </g>
                      </g>
                    </g>
                  </g>
                </svg>
              </i>
              <span style=" margin-left: 24px;">
                职位筛选
              </span>
            </span>
          </div>
          <div class="kpbig-top-more" style="margin-top: 25px;">
            <p class="kpbig-top-more-p" @click="getMore">更多</p>
            <img class="moreSvg" src="../images/h-more.svg" />
          </div>
        </div>
        <div class="lhjf-left-center com-flex">
          <p id="changeJf0" @click="changeJf(0)" class="lhjf-left-center-div1-p lhjf-left-center-div1-p2">交付职位</p>
          <p id="changeJf2" @click="changeJf(2)" class="lhjf-left-center-div1-p">收藏职位</p>
          <div class="lhjf-left-center-div3"></div>
        </div>
        <div v-if="positionList.length != 0"
             class="lhjf-left-bottom com-flex justify-be"
             v-for="(item,index) in positionList"
             :key="item.index">
          <!-- 23.10.30改版-->
          <div class="lhjf-left-box">
            <div class="lhjf-box-one com-flex align-ce">
              <p class="lhjf-left-bottom-top-p" @click="openJobDetailPanel(item.jobId, item.title)">{{item.title}}</p>
              <p class="lhjf-left-bottom-top-p2 lhjf-left-new-box">[{{item.jobLocation}}]</p>
              <p class="lhjf-left-bottom-top-p3" style="width: 110px;">{{item.publicTimeMonthDay}}</p>
            </div>
            <div class="lhjf-box-two com-flex align-ce">
              <p class="lhjf-left-bottom-p">{{item.salaryText}}</p>
              <div class="h-shuxian"></div>
              <p class="lhjf-left-bottom-p2">{{item.educationLevel}}</p>
              <div class="h-shuxian"></div>
              <p class="lhjf-left-bottom-p3">{{item.yearsExperience}}</p>
              <div class="h-shuxian"></div>
              <p class="lhjf-left-bottom-p4">招聘{{item.numberOfHiring}}人</p>
            </div>
          </div>
          <div class="lhjf-center-box">
            <div class="lhjf-box-one com-flex align-ce">
              <img v-if="item.clientLevel == 5" class="h-vv" src="../images/h-v5.svg" />
              <img v-if="item.clientLevel == 4" class="h-vv" src="../images/h-v4.svg" />
              <img v-if="item.clientLevel == 3" class="h-vv" src="../images/h-v3.svg" />
              <img v-if="item.clientLevel == 2" class="h-vv" src="../images/h-v2.svg" />
              <img v-if="item.clientLevel == 1" class="h-vv" src="../images/h-v1.svg" />
              <p class="lhjf-left-bottom-top-p4">{{item.clientName}}</p>
            </div>
            <div class="lhjf-box-two com-flex align-ce">
              <p class="lhjf-left-bottom-p2" style="margin-left: 0;">{{item.clientLocation}}</p>
              <div class="h-shuxian"></div>
              <p class="lhjf-left-bottom-p2" style="max-width: 160px;">{{item.clietnIndustry}}</p>
            </div>
          </div>
          <div class="lhjf-right-box">
            <div class="lhjf-box-one com-flex align-ce justify-ce">
              <p class="lhjf-left-bottom-top-p5">{{item.reckonIncomeTo}}元</p>
            </div>
            <div class="lhjf-box-two com-flex align-ce justify-ce">
              <p class="jjka-p">预计奖金最高可达</p>
            </div>
          </div>
        </div>
        <div class="wait_loading wait_loading_new" v-if="lhjfisLoading">
          <svg class="svg_wgs" viewBox="0 0 120 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <g id="circle" class="g-circles g-circles--v1">
              <circle id="12" transform="translate(35, 16.698730) rotate(-30) translate(-35, -16.698730) " cx="35" cy="16.6987298" r="10"></circle>
              <circle id="11" transform="translate(16.698730, 35) rotate(-60) translate(-16.698730, -35) " cx="16.6987298" cy="35" r="10"></circle>
              <circle id="10" transform="translate(10, 60) rotate(-90) translate(-10, -60) " cx="10" cy="60" r="10"></circle>
              <circle id="9" transform="translate(16.698730, 85) rotate(-120) translate(-16.698730, -85) " cx="16.6987298" cy="85" r="10"></circle>
              <circle id="8" transform="translate(35, 103.301270) rotate(-150) translate(-35, -103.301270) " cx="35" cy="103.30127" r="10"></circle>
              <circle id="7" cx="60" cy="110" r="10"></circle>
              <circle id="6" transform="translate(85, 103.301270) rotate(-30) translate(-85, -103.301270) " cx="85" cy="103.30127" r="10"></circle>
              <circle id="5" transform="translate(103.301270, 85) rotate(-60) translate(-103.301270, -85) " cx="103.30127" cy="85" r="10"></circle>
              <circle id="4" transform="translate(110, 60) rotate(-90) translate(-110, -60) " cx="110" cy="60" r="10"></circle>
              <circle id="3" transform="translate(103.301270, 35) rotate(-120) translate(-103.301270, -35) " cx="103.30127" cy="35" r="10"></circle>
              <circle id="2" transform="translate(85, 16.698730) rotate(-150) translate(-85, -16.698730) " cx="85" cy="16.6987298" r="10"></circle>
              <circle id="1" cx="60" cy="10" r="10"></circle>
            </g>
            <use xlink:href="#circle" class="use" />
          </svg>
        </div>
        <div v-if="!lhjfisLoading && positionList.length == 0" class="noData">
          <div class="noData-content">
            <img class="h-nodata" src="../images/h-nodata.png" />
            <p class="noData-content-p">暂无数据~</p>
          </div>
        </div>
      </div>
      <!-- 联合交付结束 -->
      <!-- 联合交付实时播报开始 涉及到的接口有getjoblist 这个有滚动效果 外部盒子是动态添加的高 滚动的部分看页面最底下，23.10.30首页改版去掉 -->
      
      <!-- 联合交付实时播报结束 -->
    </div>
    <!-- 联合交付和联合交付实时播报结束 -->
    <!---------------------------我有需求，找人才---联合交付开始----------------------------->

    <div class="lianheTab border-ra">
      <div class="lianhe-tab">

        <ul style="display:flex;">
          <li class="lianhe-list lianhe-list-on" id="3" onclick='addColor(this.id)' @click="lianheClick(3)">
            <div class="lianhe-list-title">资源大厅</div>
            <div class="lianhe-list-cont">快速发布资源，让优质的顾问来找你</div>
          </li>
          <li class="lianhe-list" id="1" onclick='addColor(this.id)' @click="lianheClick(1)">
            <div class="lianhe-list-title">我有需求，找人才</div>
            <div class="lianhe-list-cont">根据您的需求为您匹配有人才资源的顾问</div>
          </li>
          <li class="lianhe-list" id="2" onclick='addColor(this.id)' @click="lianheClick(2)">
            <div class="lianhe-list-title">我有人才，找需求</div>
            <div class="lianhe-list-cont">根据您的人才资源为您匹配有需求的顾问</div>
          </li>
        </ul>
      </div>

      <div class="lianhe-moddle" v-show="lianheListId==1||lianheListId==2">
        <div class="lianhe-moddle-any" v-show="lianheListId==1">
          <div style="font-size:14px;color:#333;">我的需求标签（可多选）</div>
          <div style="margin-top:14px;" v-if="jobTagsData">
            <el-checkbox-group v-model="checkList" class="jobTagsClass" id="jobTagsClass1">
              <el-checkbox v-for="(item,index) in jobTagsData" :key="index" :label="item.Name" class="lianhe-check" @change="jobTagsClick(item)"></el-checkbox>
            </el-checkbox-group>
            <p class="sm_open" id="sm_open1">展开<i></i></p>
            <p class="sm_close" id="sm_close1">收起<i></i></p>
          </div>
        </div>

        <div class="lianhe-moddle-any" v-show="lianheListId==2">
          <div style="font-size:14px;color:#333;">我的人选标签（可多选）</div>
          <div style="margin-top:14px;" v-if="resumeTagsData">
            <el-checkbox-group v-model="checkList" class="jobTagsClass" id="jobTagsClass2">
              <el-checkbox v-for="(item,index) in resumeTagsData" :key="index" :label="item.Name" class="lianhe-check" @change="resumeTagsClick(item)"></el-checkbox>
            </el-checkbox-group>
            <p class="sm_open" id="sm_open2">展开<i></i></p>
            <p class="sm_close" id="sm_close2">收起<i></i></p>
          </div>
        </div>

        <div>
          <div class="lianhe-renovate" style="display: flex; justify-content: space-between;">
            <span v-show="lianheListId==1" style="color:#333;">为你推荐有人选资源的顾问</span>
            <span v-show="lianheListId==2" style="color:#333;">为你推荐有职位需求的顾问</span>
            <span @click="changeBatchClick">
              <i class="renovate-icon">
                <svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g id="简化版-首页-联合交付信息展示" transform="translate(-1480.000000, -753.000000)" fill-rule="nonzero">
                      <g id="编组-3" transform="translate(1480.000000, 750.000000)">
                        <g id="换一批" transform="translate(0.000000, 3.000000)">
                          <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="14" height="14"></rect>
                          <path d="M8.32359248,11.7273883 C6.99213767,12.0234991 5.59823812,11.6996186 4.53374462,10.8467956 C3.46925111,9.9939726 2.84910518,8.70429296 2.84764126,7.34030933 C2.84449152,6.0852475 3.36932639,4.88672581 4.29378718,4.03786636 L4.29378718,5.49043037 C4.30186994,5.71833109 4.48896464,5.8989079 4.71700865,5.8989079 C4.94505265,5.8989079 5.13214736,5.71833109 5.14023011,5.49043037 L5.14023011,3.19890588 C5.1402302,3.08663887 5.09559591,2.97897779 5.01616125,2.89964295 C4.9367266,2.82030812 4.82900947,2.77580924 4.71674256,2.77595017 L2.42521806,2.77595017 C2.19162626,2.77595017 2.0022627,2.96531408 2.0022627,3.19890588 C2.0022627,3.43249768 2.19162626,3.62186125 2.42521806,3.62186125 L3.50876847,3.62186125 C1.86399005,5.31607469 1.52710497,7.88934709 2.68032512,9.94986608 C3.83354528,12.0103851 6.20307512,13.0689458 8.50730102,12.5530018 C8.73212231,12.4994244 8.8723084,12.2753034 8.82211046,12.0497035 C8.77191252,11.8241036 8.54992018,11.6805705 8.32359248,11.7273777 L8.32359248,11.7273883 Z M12.2578076,11.0566393 L11.1779825,11.0566393 C12.6705695,9.51634671 13.0968135,7.23252468 12.2603051,5.25753836 C11.4237966,3.28255205 9.48686408,1.99965706 7.34202905,2.00000007 C6.94903381,2.00000007 6.55721453,2.04293125 6.17357465,2.12817037 C6.02188956,2.15718134 5.89802344,2.2664634 5.85033594,2.41335079 C5.80264844,2.56023818 5.8387015,2.72143856 5.94441934,2.83401628 C6.05013717,2.946594 6.20875579,2.9926973 6.35834755,2.9543266 C7.68995442,2.65729493 9.08440979,2.98060944 10.1494409,3.83331887 C11.214472,4.68602829 11.8349902,5.97598769 11.8364381,7.34031997 C11.8404993,8.59614623 11.3153267,9.79558135 10.38976,10.6443701 L10.38976,9.18805956 C10.3816773,8.96015885 10.1945826,8.77958203 9.96653857,8.77958203 C9.73849457,8.77958203 9.55139986,8.96015885 9.54331711,9.18805956 L9.54331711,11.4801162 C9.5436103,11.7135823 9.73279575,11.9027725 9.96626184,11.9030716 L12.2583292,11.9030716 C12.4862299,11.8949889 12.6668067,11.7078941 12.6668067,11.4798501 C12.6668067,11.2518061 12.4862299,11.0647114 12.2583292,11.0566287 L12.2578076,11.0566393 Z" id="形状" fill="#2A7FCC"></path>
                        </g>
                      </g>
                    </g>
                  </g>
                </svg>
              </i>
              换一批
            </span>
          </div>
          <div class="lianhe-listData" v-loading="ESLoading" element-loading-text="正在加载中">
            <ul class="lianhe-ul" v-if="ResumeESData.length !=0">
              <li class="lianhe-li" v-for="(item,index) in ResumeESData">
                <div class="lianhe-list-top">
                  <div class="lianhe-img showCard-item" :data-id="item.StaffId">
                    <img :src="item.SmallPictureUrl" style="border-radius:50% !important;" alt="暂无数据" />
                    &nbsp;&nbsp;&nbsp;<i class="img-grade">V{{item.Level}}</i>
                  </div>
                  <div class="lianhe-right">
                    <p class="lianhe-right-name showCard-item" :data-id="item.StaffId">{{item.Name}} <span v-if="item.EnFirstname">({{item.EnFirstname}})</span></p>
                    <p class="lianhe-right-cont">{{item.PositionName}} | <span :title="item.ComapnyName">{{item.ComapnyName}}</span></p>
                  </div>
                </div>

                <div class="lianhe-list-num">
                  <div class="list-num-left" v-show="lianheListId==1">
                    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g id="简化版-首页-联合交付信息展示" transform="translate(-570.000000, -894.000000)">
                          <g id="编组-21" transform="translate(570.000000, 892.000000)">
                            <g id="编组" transform="translate(0.000000, 2.000000)">
                              <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                              <g id="编组-6" transform="translate(2.000000, 1.000000)" fill-rule="nonzero">
                                <path d="M0,6.92414135 L0,9.99090782 C0,10.2652742 0.146208407,10.5197851 0.384473959,10.6587734 L5.707543,13.73276 C5.94580855,13.8699432 6.24003041,13.8699432 6.47829596,13.73276 L11.801365,10.6587734 C12.0396306,10.5215902 12.185839,10.2670793 12.185839,9.99090782 L12.185839,6.92414135 C12.185839,6.64977496 12.0396306,6.39526403 11.801365,6.25627579 L6.47829596,3.1840942 C6.24003041,3.046911 5.94580855,3.046911 5.707543,3.1840942 L0.386279001,6.25627579 C0.148013449,6.39526403 0,6.64977496 0,6.92414135 Z" id="路径" fill="#CEE8FF"></path>
                                <path d="M6.09201696,0.770752961 L11.415086,3.84473959 L11.415086,9.99090782 L6.09201696,13.0630894 L0.768947919,9.99090782 L0.768947919,3.84473959 L6.09201696,0.770752961 M6.09201696,0 C5.95844385,0 5.82667578,0.0342957992 5.707543,0.102887398 L0.384473959,3.17687403 C0.146208407,3.31405723 0,3.56856816 0,3.84473959 L0,9.99090782 C0,10.2652742 0.146208407,10.5197851 0.384473959,10.6587734 L5.707543,13.73276 C5.82667578,13.8013516 5.96024889,13.8356474 6.09201696,13.8356474 C6.22378503,13.8356474 6.35735814,13.8013516 6.47649092,13.73276 L11.79956,10.6587734 C12.0378255,10.5215902 12.1840339,10.2670793 12.1840339,9.99090782 L12.1840339,3.84293455 C12.1840339,3.56856816 12.0378255,3.31405723 11.79956,3.17506899 L6.47649092,0.102887398 C6.35735814,0.0342957992 6.22559007,0 6.09201696,0 Z" id="形状" fill="#2A7FCC"></path>
                                <path d="M11.9926995,4.06495472 L6.46205058,7.25987918 C6.27793629,7.36637666 6.04147578,7.30320019 5.93678334,7.11908589 C5.83028586,6.9349716 5.89346233,6.69851109 6.07757662,6.59381865 L11.6100305,3.40069925 C11.7941448,3.29420176 12.0306053,3.35737824 12.1352978,3.54149253 C12.2399902,3.72199673 12.1768138,3.95845724 11.9926995,4.06495472 L11.9926995,4.06495472 Z M11.9963095,7.42594305 L7.6172775,9.95480698 C7.43316321,10.0613045 7.1967027,9.99812798 7.09201026,9.81401369 C6.98551278,9.6298994 7.04868925,9.39343889 7.23280354,9.28874645 L11.6136406,6.75988252 C11.7977549,6.65338504 12.0342154,6.71656151 12.1389079,6.90067581 C12.2436003,7.08298505 12.1804238,7.31944556 11.9963095,7.42594305 L11.9963095,7.42594305 Z" id="形状" fill="#2A7FCC"></path>
                                <path d="M6.09201696,13.261644 C5.88082704,13.261644 5.707543,13.08836 5.707543,12.8771701 L5.707543,7.02341867 C5.707543,6.81222874 5.88082704,6.63894471 6.09201696,6.63894471 C6.30320688,6.63894471 6.47649092,6.81222874 6.47649092,7.02341867 L6.47649092,12.8771701 C6.47649092,13.08836 6.30320688,13.261644 6.09201696,13.261644 L6.09201696,13.261644 Z" id="路径" fill="#2A7FCC"></path>
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                    资源数 <span style="font-size: 14px; font-weight: 600;"> {{item.ResourceCount}}</span>
                  </div>
                  <div class="list-num-left" v-show="lianheListId==2">
                    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g id="简化版-首页-联合交付信息展示" transform="translate(-570.000000, -894.000000)">
                          <g id="编组-21" transform="translate(570.000000, 892.000000)">
                            <g id="编组" transform="translate(0.000000, 2.000000)">
                              <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                              <g id="编组-6" transform="translate(2.000000, 1.000000)" fill-rule="nonzero">
                                <path d="M0,6.92414135 L0,9.99090782 C0,10.2652742 0.146208407,10.5197851 0.384473959,10.6587734 L5.707543,13.73276 C5.94580855,13.8699432 6.24003041,13.8699432 6.47829596,13.73276 L11.801365,10.6587734 C12.0396306,10.5215902 12.185839,10.2670793 12.185839,9.99090782 L12.185839,6.92414135 C12.185839,6.64977496 12.0396306,6.39526403 11.801365,6.25627579 L6.47829596,3.1840942 C6.24003041,3.046911 5.94580855,3.046911 5.707543,3.1840942 L0.386279001,6.25627579 C0.148013449,6.39526403 0,6.64977496 0,6.92414135 Z" id="路径" fill="#CEE8FF"></path>
                                <path d="M6.09201696,0.770752961 L11.415086,3.84473959 L11.415086,9.99090782 L6.09201696,13.0630894 L0.768947919,9.99090782 L0.768947919,3.84473959 L6.09201696,0.770752961 M6.09201696,0 C5.95844385,0 5.82667578,0.0342957992 5.707543,0.102887398 L0.384473959,3.17687403 C0.146208407,3.31405723 0,3.56856816 0,3.84473959 L0,9.99090782 C0,10.2652742 0.146208407,10.5197851 0.384473959,10.6587734 L5.707543,13.73276 C5.82667578,13.8013516 5.96024889,13.8356474 6.09201696,13.8356474 C6.22378503,13.8356474 6.35735814,13.8013516 6.47649092,13.73276 L11.79956,10.6587734 C12.0378255,10.5215902 12.1840339,10.2670793 12.1840339,9.99090782 L12.1840339,3.84293455 C12.1840339,3.56856816 12.0378255,3.31405723 11.79956,3.17506899 L6.47649092,0.102887398 C6.35735814,0.0342957992 6.22559007,0 6.09201696,0 Z" id="形状" fill="#2A7FCC"></path>
                                <path d="M11.9926995,4.06495472 L6.46205058,7.25987918 C6.27793629,7.36637666 6.04147578,7.30320019 5.93678334,7.11908589 C5.83028586,6.9349716 5.89346233,6.69851109 6.07757662,6.59381865 L11.6100305,3.40069925 C11.7941448,3.29420176 12.0306053,3.35737824 12.1352978,3.54149253 C12.2399902,3.72199673 12.1768138,3.95845724 11.9926995,4.06495472 L11.9926995,4.06495472 Z M11.9963095,7.42594305 L7.6172775,9.95480698 C7.43316321,10.0613045 7.1967027,9.99812798 7.09201026,9.81401369 C6.98551278,9.6298994 7.04868925,9.39343889 7.23280354,9.28874645 L11.6136406,6.75988252 C11.7977549,6.65338504 12.0342154,6.71656151 12.1389079,6.90067581 C12.2436003,7.08298505 12.1804238,7.31944556 11.9963095,7.42594305 L11.9963095,7.42594305 Z" id="形状" fill="#2A7FCC"></path>
                                <path d="M6.09201696,13.261644 C5.88082704,13.261644 5.707543,13.08836 5.707543,12.8771701 L5.707543,7.02341867 C5.707543,6.81222874 5.88082704,6.63894471 6.09201696,6.63894471 C6.30320688,6.63894471 6.47649092,6.81222874 6.47649092,7.02341867 L6.47649092,12.8771701 C6.47649092,13.08836 6.30320688,13.261644 6.09201696,13.261644 L6.09201696,13.261644 Z" id="路径" fill="#2A7FCC"></path>
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                    需求数 <span style="font-size: 14px; font-weight: 600;">{{item.ResourceCount}}</span>
                  </div>
                  <div class="list-num-right" v-show="lianheListId==1">
                    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g id="简化版-首页-联合交付信息展示" transform="translate(-674.000000, -894.000000)">
                          <g id="编组-21" transform="translate(570.000000, 892.000000)">
                            <g id="编组-10" transform="translate(104.000000, 0.000000)">
                              <g id="编组" transform="translate(0.000000, 2.000000)">
                                <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                                <g id="编组-7" transform="translate(1.000000, 2.000000)" fill-rule="nonzero">
                                  <path d="M10.6493802,9.29496571 L10.9775022,11.0103143 C10.9775022,11.034012 10.9775022,11.0485952 10.9702106,11.0595326 L10.962919,11.0686471 L10.379591,11.6282774 C10.3540704,11.653798 10.326727,11.6574438 10.3012064,11.6392148 L10.2884461,11.6282774 L9.70511814,11.0686471 C9.69235785,11.0577097 9.69053495,11.0358349 9.69053495,11.0194288 L9.69053495,11.0084914 L10.0186569,9.29314281 L10.6493802,9.29314281 L10.6493802,9.29496571 Z M10.7168275,8.51111882 C10.7624,8.51111882 10.7788061,8.54028522 10.7788061,8.58403481 L10.671255,9.09991543 L9.9949592,9.09991543 L9.88740812,8.58403481 C9.88740812,8.55486842 9.90199131,8.51111882 9.94938671,8.51111882 L10.7168275,8.51111882 Z" id="形状" fill="#FAAD14"></path>
                                  <path d="M13.998047,11.7722864 C13.998047,10.2392278 13.0574307,8.86658427 11.6373919,8.32336015 C12.4394677,7.78195893 12.8259225,6.80123888 12.6089974,5.85879972 C12.397541,4.91636056 11.6228087,4.20360677 10.6657863,4.07053509 L10.6657863,1.3453 C10.6657863,0.603379809 10.0678752,0 9.3332466,0 L1.3325397,0 C0.596088211,0 0,0.603379809 0,1.3453 L0,10.7624 C0,11.5043202 0.596088211,12.1077 1.3325397,12.1077 L6.70097939,12.1077 C6.87780067,12.1131687 7.04915324,12.0420756 7.17311042,11.9144726 C7.29706761,11.7868696 7.362692,11.6136942 7.3535775,11.43505 C7.46659728,9.87282492 8.78455378,8.6824714 10.3358414,8.74444999 C11.1324486,8.74627289 11.8962436,9.06528034 12.4558738,9.63220215 C13.0191498,10.2027698 13.3345115,10.9720335 13.3345115,11.7722864 C13.343626,11.9491077 13.4912809,12.0876481 13.6681021,12.089471 C13.8449234,12.0876481 13.9907554,11.9491077 13.998047,11.7722864 Z M10.0004362,4.06871219 C9.04340554,4.20178387 8.27049606,4.91453766 8.05721679,5.85697682 C7.84029172,6.80123888 8.22674647,7.78013603 9.02882235,8.32336015 C7.72727194,8.82648048 6.82129078,10.0332401 6.6973336,11.43505 L1.3343626,11.43505 C0.964313955,11.43505 0.667173008,11.1342715 0.667173008,10.7624 L0.667173008,1.3453 C0.6653584,0.975251354 0.964313955,0.674472899 1.3343626,0.672649999 L9.3332466,0.672649999 C9.70329524,0.674472899 10.0022508,0.975251354 10.0004362,1.3453 L10.0004362,4.06871219 Z" id="形状" fill="#FAAD14"></path>
                                  <path d="M10.0004362,2.89841057 C10.0022508,2.52836192 9.70329524,2.22758347 9.3332466,2.22576057 L1.3325397,2.22576057 C0.962491056,2.22758347 0.6653584,2.53018482 0.6653584,2.89841057 L0.6653584,10.7587542 C0.6653584,11.1306257 0.964313955,11.4314042 1.3325397,11.4314042 L6.6973336,11.4314042 C6.82129078,10.0295943 7.72544904,8.82101178 9.02882235,8.31971435 C8.22674647,7.77831313 7.84029172,6.79759308 8.05721679,5.85333102 C8.26867316,4.91089186 9.04340554,4.19813807 10.0004362,4.06506639 L10.0004362,2.89841057 Z" id="路径" fill="#FAAD14" opacity="0.234584263"></path>
                                  <path d="M12.0001489,6.38744064 C12.0001489,5.45776178 11.2545829,4.70490419 10.3340185,4.70490419 C9.41345419,4.70490419 8.6660653,5.45776178 8.6660653,6.38744064 C8.6660653,7.3171195 9.41163129,8.06997709 10.3340185,8.06997709 C11.2545829,8.06997709 12.0001489,7.3171195 12.0001489,6.38744064 Z" id="路径" fill="#FEECC8"></path>
                                  <path d="M7.9733634,3.72053834 L2.53018482,3.72053834 C2.31690555,3.72053834 2.14190718,3.54553997 2.14190718,3.3322607 C2.14190718,3.11898143 2.31690555,2.94398306 2.53018482,2.94398306 L7.9751863,2.94398306 C8.18846557,2.94398306 8.36346395,3.11898143 8.36346395,3.3322607 C8.36346395,3.54553997 8.18846557,3.72053834 7.9733634,3.72053834 Z M6.41842994,6.05384999 L2.53018482,6.05384999 C2.31690555,6.05384999 2.14190718,5.87885162 2.14190718,5.66557235 C2.14190718,5.45229308 2.31690555,5.27729471 2.53018482,5.27729471 L6.41842994,5.27729471 C6.63170921,5.27729471 6.80670758,5.45229308 6.80670758,5.66557235 C6.80670758,5.87885162 6.63170921,6.05384999 6.41842994,6.05384999 Z M4.86349647,8.38716164 L2.53018482,8.38716164 C2.31690555,8.38716164 2.14190718,8.21216327 2.14190718,7.998884 C2.14190718,7.78560473 2.31690555,7.61060636 2.53018482,7.61060636 L4.86349647,7.61060636 C5.07677574,7.61060636 5.25177411,7.78560473 5.25177411,7.998884 C5.25177411,8.21216327 5.07677574,8.38716164 4.86349647,8.38716164 Z" id="形状" fill="#FAAD14"></path>
                                </g>
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                    面试率 <span style="font-size: 14px; font-weight: 600;">{{(item.RecommendInterview).toFixed(2)}}%</span>
                  </div>
                  <div class="list-num-right" v-show="lianheListId==2">
                    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g id="简化版-首页-联合交付信息展示" transform="translate(-674.000000, -894.000000)">
                          <g id="编组-21" transform="translate(570.000000, 892.000000)">
                            <g id="编组-10" transform="translate(104.000000, 0.000000)">
                              <g id="编组" transform="translate(0.000000, 2.000000)">
                                <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                                <g id="编组-7" transform="translate(1.000000, 2.000000)" fill-rule="nonzero">
                                  <path d="M10.6493802,9.29496571 L10.9775022,11.0103143 C10.9775022,11.034012 10.9775022,11.0485952 10.9702106,11.0595326 L10.962919,11.0686471 L10.379591,11.6282774 C10.3540704,11.653798 10.326727,11.6574438 10.3012064,11.6392148 L10.2884461,11.6282774 L9.70511814,11.0686471 C9.69235785,11.0577097 9.69053495,11.0358349 9.69053495,11.0194288 L9.69053495,11.0084914 L10.0186569,9.29314281 L10.6493802,9.29314281 L10.6493802,9.29496571 Z M10.7168275,8.51111882 C10.7624,8.51111882 10.7788061,8.54028522 10.7788061,8.58403481 L10.671255,9.09991543 L9.9949592,9.09991543 L9.88740812,8.58403481 C9.88740812,8.55486842 9.90199131,8.51111882 9.94938671,8.51111882 L10.7168275,8.51111882 Z" id="形状" fill="#FAAD14"></path>
                                  <path d="M13.998047,11.7722864 C13.998047,10.2392278 13.0574307,8.86658427 11.6373919,8.32336015 C12.4394677,7.78195893 12.8259225,6.80123888 12.6089974,5.85879972 C12.397541,4.91636056 11.6228087,4.20360677 10.6657863,4.07053509 L10.6657863,1.3453 C10.6657863,0.603379809 10.0678752,0 9.3332466,0 L1.3325397,0 C0.596088211,0 0,0.603379809 0,1.3453 L0,10.7624 C0,11.5043202 0.596088211,12.1077 1.3325397,12.1077 L6.70097939,12.1077 C6.87780067,12.1131687 7.04915324,12.0420756 7.17311042,11.9144726 C7.29706761,11.7868696 7.362692,11.6136942 7.3535775,11.43505 C7.46659728,9.87282492 8.78455378,8.6824714 10.3358414,8.74444999 C11.1324486,8.74627289 11.8962436,9.06528034 12.4558738,9.63220215 C13.0191498,10.2027698 13.3345115,10.9720335 13.3345115,11.7722864 C13.343626,11.9491077 13.4912809,12.0876481 13.6681021,12.089471 C13.8449234,12.0876481 13.9907554,11.9491077 13.998047,11.7722864 Z M10.0004362,4.06871219 C9.04340554,4.20178387 8.27049606,4.91453766 8.05721679,5.85697682 C7.84029172,6.80123888 8.22674647,7.78013603 9.02882235,8.32336015 C7.72727194,8.82648048 6.82129078,10.0332401 6.6973336,11.43505 L1.3343626,11.43505 C0.964313955,11.43505 0.667173008,11.1342715 0.667173008,10.7624 L0.667173008,1.3453 C0.6653584,0.975251354 0.964313955,0.674472899 1.3343626,0.672649999 L9.3332466,0.672649999 C9.70329524,0.674472899 10.0022508,0.975251354 10.0004362,1.3453 L10.0004362,4.06871219 Z" id="形状" fill="#FAAD14"></path>
                                  <path d="M10.0004362,2.89841057 C10.0022508,2.52836192 9.70329524,2.22758347 9.3332466,2.22576057 L1.3325397,2.22576057 C0.962491056,2.22758347 0.6653584,2.53018482 0.6653584,2.89841057 L0.6653584,10.7587542 C0.6653584,11.1306257 0.964313955,11.4314042 1.3325397,11.4314042 L6.6973336,11.4314042 C6.82129078,10.0295943 7.72544904,8.82101178 9.02882235,8.31971435 C8.22674647,7.77831313 7.84029172,6.79759308 8.05721679,5.85333102 C8.26867316,4.91089186 9.04340554,4.19813807 10.0004362,4.06506639 L10.0004362,2.89841057 Z" id="路径" fill="#FAAD14" opacity="0.234584263"></path>
                                  <path d="M12.0001489,6.38744064 C12.0001489,5.45776178 11.2545829,4.70490419 10.3340185,4.70490419 C9.41345419,4.70490419 8.6660653,5.45776178 8.6660653,6.38744064 C8.6660653,7.3171195 9.41163129,8.06997709 10.3340185,8.06997709 C11.2545829,8.06997709 12.0001489,7.3171195 12.0001489,6.38744064 Z" id="路径" fill="#FEECC8"></path>
                                  <path d="M7.9733634,3.72053834 L2.53018482,3.72053834 C2.31690555,3.72053834 2.14190718,3.54553997 2.14190718,3.3322607 C2.14190718,3.11898143 2.31690555,2.94398306 2.53018482,2.94398306 L7.9751863,2.94398306 C8.18846557,2.94398306 8.36346395,3.11898143 8.36346395,3.3322607 C8.36346395,3.54553997 8.18846557,3.72053834 7.9733634,3.72053834 Z M6.41842994,6.05384999 L2.53018482,6.05384999 C2.31690555,6.05384999 2.14190718,5.87885162 2.14190718,5.66557235 C2.14190718,5.45229308 2.31690555,5.27729471 2.53018482,5.27729471 L6.41842994,5.27729471 C6.63170921,5.27729471 6.80670758,5.45229308 6.80670758,5.66557235 C6.80670758,5.87885162 6.63170921,6.05384999 6.41842994,6.05384999 Z M4.86349647,8.38716164 L2.53018482,8.38716164 C2.31690555,8.38716164 2.14190718,8.21216327 2.14190718,7.998884 C2.14190718,7.78560473 2.31690555,7.61060636 2.53018482,7.61060636 L4.86349647,7.61060636 C5.07677574,7.61060636 5.25177411,7.78560473 5.25177411,7.998884 C5.25177411,8.21216327 5.07677574,8.38716164 4.86349647,8.38716164 Z" id="形状" fill="#FAAD14"></path>
                                </g>
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                    反馈效率 <span style="font-size:14px;font-weight:600;">{{item.RecommendInterview}}天</span>
                  </div>
                </div>

                <div class="hover-cont" style="position: relative;" v-show="lianheListId==1">
                  <div class="listData-cont">
                    拥有资源范围：<span v-for="item1 in item.ResumeResourceList">{{item1.Name}}({{item1.Count}})</span>
                  </div>
                  <div class="hoverCont">
                    拥有资源范围：<span v-for="item1 in item.ResumeResourceList">{{item1.Name}}({{item1.Count}})</span>
                  </div>
                </div>
                <div class="hover-cont" style="position: relative;" v-show="lianheListId==2">
                  <div class="listData-cont">
                    拥有需求范围：<span v-for="item1 in item.JobResourceList">{{item1.Name}}({{item1.Count}})</span>
                  </div>
                  <div class="hoverCont">
                    拥有需求范围：<span v-for="item1 in item.JobResourceList">{{item1.Name}}({{item1.Count}})</span>
                  </div>
                </div>

                <div class="list-btn">
                  <div class="list-btn-left">
                    <span class="list-span">企微直聊</span>
                    <span class="call-phone">
                      <img :src="item.WeComQrCode" width="80px" height="80px" alt="Alternate Text" />
                      <span style="display:block;color:#333;font-size:12px;">扫一扫联系顾问</span>
                    </span>
                  </div>
                  <div class="list-btn-moddle">
                    <a href="javascript:void(0);" class="rnss-callphone show-phone" :data-transfernumber="item.MobileNumber" data-usnumber="111" data-ustel="111" title="点击呼叫">
                      电话联系
                    </a>
                  </div>
                  <div class="list-btn-right">
                    <span class="list-span showTeam-item" :data-id="item.StaffId">加入团队</span>
                  </div>
                </div>
              </li>
            </ul>
            <div v-if="ResumeESData.length ==0" class="noData" style="height: 100%;">
              <div class="noData-content">
                <img class="h-nodata" src="../images/h-nodata.png" />
                <p class="noData-content-p">暂无数据~</p>
              </div>
            </div>
          </div>
        </div>

      </div>

      <div class="lianhe-moddle" v-show="lianheListId==3">
        <div class="lianhe-hall">
          <div class="hall-left">
            <ul class="hall-left-ul">
              <li class="hall-left-search hall-left-search-on" id="1" onclick='searchColor(this.id)' @click="tabSearchClick(1)" style="border-top-left-radius: 2px !important; border-bottom-left-radius: 2px !important; ">全部</li>
              <li class="hall-left-search" id="2" onclick='searchColor(this.id)' @click="tabSearchClick(2)">职位线索</li>
              <li class="hall-left-search" id="3" onclick='searchColor(this.id)' @click="tabSearchClick(3)">人选线索</li>
              <li class="hall-left-search" id="4" onclick='searchColor(this.id)' @click="tabSearchClick(4)" style="border-top-right-radius: 2px !important; border-bottom-right-radius: 2px !important; ">我发布的</li>
            </ul>
          </div>
          <div class="hall-right">
            <div style="width:436px;">
              <el-input placeholder="请输入内容" v-model="searchKeyInput" id="contText" class="input-with-select input-with-hall" style="height:40px;">
                
                <el-select v-model="select" slot="prepend" placeholder="发布时间" style="width:110px;">
                  <el-option label="一天内" value="1" onclick="choseDate(1)"></el-option>
                  <el-option label="一周内" value="2" onclick="choseDate(2)"></el-option>
                  <el-option label="一月内" value="3" onclick="choseDate(3)"></el-option>
                  <el-option label="一年内" value="4" onclick="choseDate(5)"></el-option>
                  <el-option label="自定义" value="5" @click.native="clueDateDialog"></el-option>
                  <el-option label="发布时间" value="6" onclick="choseDate(6)"></el-option>
                </el-select>
                <el-button slot="append" class="input-with-search" id="contButton" @click="searchInput">搜索</el-button>
              </el-input>
            </div>
          </div>
          <div>
            <el-button slot="append" class="input-with-fabu" @click="publishClick">发布线索</el-button>
          </div>
        </div>
      </div>

      <div class="lianhe-moddle-list lianhe-moddle-line com-flex" v-show="lianheListId==3">
        <!-- 左侧23.10.30-->
        <div class="lianhe-left" ref="Box" @scroll="divScroll">
          <ul class="moddle-list-ul" id="moddleListUl" v-show="GetPageInterData.length !=0">
            <li class="moddle-list-li" v-for="(item,index) in GetPageInterData" :key="index">
              <div class="moddle-list-left">
                <div class="list-left-img showCard-item com-flex flex-flow align-ce" :data-id="item.StaffId">
                  <img :src="item.StaffPictureImg" />
                  <p class="img-grade">LV{{item.Level}}</p>
                </div>
              </div>
              <div class="moddle-list-right">
                <div class="list-right-top">
                  <p class="right-top-left p">
                    <span class="top-left-name showCard-item" :data-id="item.StaffId">{{item.StaffName}}</span>
                    <span class="top-left-company">{{item.CompanyName}}</span>
                    <span class="top-left-status1" v-if="item.Category==1">职位线索</span>
                    <span class="top-left-status" v-if="item.Category==2">人选线索</span>
                  </p>
                  <p class="right-top-right p">{{item.PubDate}}</p>
                </div>
                <div class="list-right-cont">
                  <div class="poster-txt xg" id="view" style="position: relative;">
                    <p class="p test-p audiot_style" id="poster-pid" style="color:#333;" v-html="item.Content"></p>
                    <p class="audiotShow" v-if="item.Content.length>223" @click="thisClick()">...查看更多</p>
                    <p style="display: none;" class="audiotHide">收起</p>
                  </div>
                  <div v-for="item1 in item.ClueImg" style="display: inline-block; margin-right: 10px; width: 242px; height: 128px; margin-top: 8px; ">
                    <a :href="item1" target="_blank"><img :src="item1" width="100%" height="100%" /></a>
                  </div>
                  <div class="fileDiv" style="margin-top:20px;" v-if="item.ClueFileObj">
                    <div v-for="item2 in item.ClueFileObj" class="fileData">
                      <div class="fileCli" v-if="item2.FileType=='.doc'||item2.FileType=='.docx'">
                        <div class="fileCli-img">
                          <svg width="30px" height="30px" viewBox="0 0 30 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <title>word</title>
                            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                              <g id="画板" transform="translate(-112.000000, -427.000000)" fill="#6791FF" fill-rule="nonzero">
                                <g id="编组-37" transform="translate(92.000000, 414.000000)">
                                  <g id="word" transform="translate(20.000000, 13.000000)">
                                    <path d="M29.7932842,4.15142178 C29.8413198,3.79046529 29.7197336,3.42758226 29.4639267,3.16843009 C29.2081198,2.90927792 28.8468471,2.78298747 28.485297,2.82632958 C24.8674059,2.75608987 21.2458754,2.82632958 17.6207056,2.79466712 L17.6207056,0.0214722438 L15.594672,0.0214722438 C10.4023923,0.968070652 5.20683715,1.87245245 0.0145574534,2.80158191 L0.0145574534,27.0623059 C5.18208948,27.987796 10.3496215,28.8710695 15.5175175,29.8213073 L17.6210695,29.8213073 L17.6210695,27.0972438 L27.787995,27.0972438 C28.3662898,27.0623059 29.0046341,27.0972438 29.4988597,26.7467731 C29.8951863,26.1193469 29.7932842,25.3445264 29.828222,24.6432211 C29.7932842,17.824146 29.8529697,10.981051 29.7932842,4.15142178 L29.7932842,4.15142178 Z M11.3438956,19.956449 L9.39647224,19.956449 C8.93864033,18.0312257 8.17473797,15.2220012 7.86793964,13.7327737 L7.85520186,13.7327737 C7.53930512,15.279867 6.75465839,18.2062791 6.29063956,19.956449 L4.34467197,19.956449 L2.13157512,11.2623738 L4.13650039,11.2623738 C4.95135384,15.2569391 5.32220497,17.0289451 5.42410714,17.9853698 L5.43684491,17.9853698 C5.76729911,16.3884171 6.60690023,13.3622865 7.11386355,11.2623738 L8.80289208,11.2623738 C9.2498059,12.9302941 10.1290761,16.30362 10.4122186,17.8947496 L10.4249563,17.8947496 C10.7135578,15.9804445 11.5673525,12.6133055 11.8726951,11.2623738 L13.6985637,11.2623738 L11.3438956,19.956449 Z M28.7975543,26.0207201 L17.6207056,26.0207201 L17.6207056,23.2475252 L26.427237,23.2475252 L26.427237,21.8452785 L17.6207056,21.8452785 L17.6207056,20.127499 L26.427237,20.127499 L26.427237,18.7248884 L17.6207056,18.7248884 L17.6207056,17.0071089 L26.427237,17.0071089 L26.427237,15.6048622 L17.6207056,15.6048622 L17.6207056,13.8521448 L26.427237,13.8521448 L26.427237,12.5023049 L17.6207056,12.5023049 L17.6207056,10.7495875 L26.427237,10.7495875 L26.427237,9.38227873 L17.6207056,9.38227873 L17.6207056,7.64703028 L26.427237,7.64703028 L26.427237,6.26225252 L17.6207056,6.26225252 L17.6207056,3.83588898 L28.7975543,3.83588898 L28.7975543,26.0207201 Z" id="形状"></path>
                                  </g>
                                </g>
                              </g>
                            </g>
                          </svg>
                        </div>
                        <div class="fileCli-cont">
                          <p style="color: #333333; font-size: 14px; margin: 0;">{{item2.FileName}}</p>
                          <p style="color: #666666; font-size: 12px; margin: 2px 0 0; ">{{item2.FileSize}}</p>
                        </div>
                        <div>
                          <a :href="item2.FileUrl" target="_blank">查看</a>
                        </div>
                      </div>

                      <div class="fileCli" v-if="item2.FileType=='.pdf'">
                        <div class="fileCli-img">
                          <svg width="30px" height="30px" viewBox="0 0 30 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                              <g id="画板" transform="translate(-112.000000, -287.000000)" fill-rule="nonzero">
                                <g id="编组-35" transform="translate(92.000000, 274.000000)">
                                  <g id="PDF" transform="translate(20.000000, 13.000000)">
                                    <path d="M15.6761821,0 L17.7129946,0 L17.7129946,2.79254439 C21.3501598,2.81239659 24.9873251,2.75283995 28.6443293,2.81239662 C29.0066108,2.76927718 29.3686454,2.8947858 29.6266228,3.15293389 C29.8846002,3.41108198 30.0100258,3.77335621 29.9669349,4.13587736 C30.0231456,11.0113599 29.9669349,17.8901511 30,24.7656336 C29.9669349,25.4703871 30.0661303,26.2512408 29.6693486,26.8765854 C29.170065,27.2405427 28.5286014,27.2074556 27.9466549,27.2074556 L17.7196076,27.2074556 L17.7196076,30 L15.5835997,30 C10.3956795,29.0437852 5.19783976,28.1537444 0,27.2206904 L0,2.79916181 C5.22429185,1.86279917 10.4518902,0.952906137 15.6761821,0 Z" id="路径" fill="#FA272D"></path>
                                    <polygon id="路径" fill="#FFFFFF" points="17.7129946 3.8380942 28.9551416 3.8380942 28.9551416 26.1751406 17.7129946 26.1751406 17.7129946 23.3825962 26.5711452 23.3825962 26.5711452 21.986324 17.7129946 21.986324 17.7129946 20.2426381 26.5711452 20.2426381 26.5711452 18.8596007 17.7129946 18.8596007 17.7129946 17.0993714 26.5711452 17.0993714 26.5711452 15.7030991 17.7129946 15.7030991 17.7129946 13.9594133 26.5711452 13.9594133 26.5711452 12.5730672 17.7129946 12.5730672 17.7129946 10.8194552 26.5711452 10.8194552 26.5711452 9.42318297 17.7129946 9.42318297 17.7129946 7.67618836 26.5711452 7.67618836 26.5711452 25.1792213 17.7129946 25.1792213"></polygon>
                                    <path d="M18.9066461,15.5244292 L27.7647967,15.5244292 L27.7647967,16.9207015 L18.9066461,16.9207015 L18.9066461,15.5244292 Z M18.9066461,17.9232381 L27.7647967,17.9232381 L27.7647967,19.3195103 L18.9066461,19.3195103 L18.9066461,17.9232381 Z M18.9066461,20.322047 L27.7647967,20.322047 L27.7647967,21.7183192 L18.9066461,21.7183192 L18.9066461,20.322047 Z M17.4683126,22.7208558 L27.7647967,22.7208558 L27.7647967,24.1171281 L17.4683126,24.1171281 L17.4683126,22.7208558 Z" id="形状" fill="#FA272D"></path>
                                    <path d="M5.29042214,9.30076101 C7.0263419,9.38347856 9.12597819,8.59600749 10.5808443,9.92610566 C11.956354,11.6400132 11.5926375,14.7865887 9.55251846,15.8089776 C8.82839192,16.1894784 7.99515044,16.1398478 7.20820015,16.1100695 L7.20820015,19.9481637 L5.29042214,19.7860373 C5.26397002,16.2920481 5.25735701,12.7947502 5.29042214,9.30076101 Z" id="路径" fill="#FFFFFF"></path>
                                    <path d="M7.19497409,11.0709165 C7.82321171,11.0411382 8.60354899,10.9220249 9.02678277,11.5275174 C9.37584991,12.1972025 9.39175441,12.9917292 9.06976745,13.6748649 C8.70605092,14.3366053 7.88272899,14.283666 7.24457182,14.3597662 C7.17844156,13.2645859 7.18505457,12.1694055 7.19497409,11.0709165 L7.19497409,11.0709165 Z M25.9429075,11.1337818 C25.4220763,11.0970203 24.9264409,10.8953904 24.5277196,10.5580677 C23.7494109,10.7329284 22.9881345,10.9765209 22.2528381,11.2859821 C21.6576656,12.341458 21.1054778,12.8807764 20.6260333,12.8807764 C20.5247113,12.8846353 20.4245451,12.8581371 20.3383666,12.8046763 C20.1359477,12.7113159 20.0066861,12.508241 20.0077091,12.2852101 C20.0077091,12.1131576 20.0473934,11.6234697 21.8626695,10.8459248 C22.2753724,10.0856544 22.6152539,9.28801054 22.8777692,8.46365941 C22.6463132,8.00374985 22.1503362,6.87217381 22.4942136,6.29645968 C22.612101,6.08452118 22.8450072,5.96343399 23.0860796,5.98875042 C23.2816059,5.990995 23.4654319,6.08235971 23.5853632,6.23690304 C23.8333517,6.56777322 23.8135126,7.31223114 23.4894743,8.38755927 C23.7974147,8.96435011 24.2010408,9.48460031 24.6831258,9.92610566 C25.0793066,9.84448251 25.4822569,9.80017295 25.8866968,9.79375758 C26.7860686,9.81360978 26.9216356,10.2338149 26.9017965,10.4852763 C26.9017965,11.1470167 26.2702524,11.1470167 25.9462141,11.1470166 L25.9429075,11.1337818 Z M20.5235314,12.3679277 L20.5896617,12.3513842 C20.8715154,12.2694359 21.1181213,12.0959991 21.2906426,11.8583876 C20.9918799,11.9521197 20.7258584,12.1288198 20.5235314,12.3679277 Z M23.2580183,6.4982905 L23.1951945,6.4982905 C23.1661899,6.49536815 23.1370515,6.50236599 23.1125317,6.51814271 C23.0417052,6.85341354 23.0847615,7.20274945 23.2348727,7.51075327 C23.3547633,7.18520773 23.3629069,6.82898146 23.2580183,6.4982905 L23.2580183,6.4982905 Z M23.1455968,9.3073784 L23.1455968,9.34377413 L23.1257578,9.3239219 C22.9670451,9.74412703 22.7951064,10.1577148 22.5967155,10.5646851 L22.6297807,10.5448329 L22.6297807,10.587846 C23.0142751,10.4405329 23.4073752,10.3167791 23.8068996,10.2172714 L23.7870605,10.2007279 L23.8399647,10.2007279 C23.5797332,9.92650184 23.3471524,9.62727154 23.1455968,9.3073784 L23.1455968,9.3073784 Z M25.9164554,10.3264586 C25.7375665,10.3183065 25.5584118,10.335009 25.3841067,10.3760891 C25.5723304,10.4854888 25.782292,10.5521336 25.9991183,10.5713025 C26.1363721,10.5931761 26.2769889,10.5771603 26.4058195,10.5249807 C26.3925934,10.4488805 26.3099306,10.3264586 25.9032294,10.3264586 L25.9164554,10.3264586 Z" id="形状" fill="#FA272D"></path>
                                  </g>
                                </g>
                              </g>
                            </g>
                          </svg>
                        </div>
                        <div class="fileCli-cont">
                          <p style="color: #333333; font-size: 14px; margin: 0;">{{item2.FileName}}</p>
                          <p style="color: #666666; font-size: 12px; margin: 2px 0 0; ">{{item2.FileSize}}</p>
                        </div>
                        <div>
                          <a :href="item2.FileUrl" target="_blank">查看</a>
                        </div>
                      </div>

                      <div class="fileCli" v-if="item2.FileType=='.xlsx'||item2.FileType=='.xls'">
                        <div class="fileCli-img">
                          <svg width="30px" height="30px" viewBox="0 0 30 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                              <g id="画板" transform="translate(-112.000000, -357.000000)" fill="#5BA139" fill-rule="nonzero">
                                <g id="编组-36" transform="translate(92.000000, 344.000000)">
                                  <g id="文件,excel" transform="translate(20.000000, 13.000000)">
                                    <path d="M29.0625,2.46473509 L16.875,2.46473509 L16.875,1.8758679 C16.875,0.695203842 15.7939453,-0.192491471 14.6367188,0.0360241541 L1.51171875,2.63465697 C0.6328125,2.80750853 0,3.57801634 0,4.47450072 L0,24.8270398 C0,25.7235242 0.635742188,26.4969617 1.51757812,26.6668835 L14.6425781,29.2186413 C15.7998047,29.4442273 16.875,28.556532 16.875,27.3787976 L16.875,26.8397351 L29.0625,26.8397351 C29.5810547,26.8397351 30,26.4207898 30,25.9022351 L30,3.40223509 C30,2.8836804 29.5810547,2.46473509 29.0625,2.46473509 Z M3.95214844,19.0614148 L6.16113281,14.8983288 C6.23144531,14.7664929 6.234375,14.6082898 6.16992187,14.4735242 L4.13378906,10.284071 C4.06640625,10.143446 4.15136719,9.97645384 4.30371094,9.95008665 L5.62792969,9.71864134 C5.84179687,9.6805554 6.05273437,9.79481322 6.140625,9.99403197 L7.28027344,12.6483288 C7.38867187,12.9061413 7.48242187,13.190321 7.56152344,13.5067273 L7.58496094,13.5067273 C7.66699219,13.2137585 7.76953125,12.9032117 7.89257812,12.572157 L9.41894531,9.25575072 C9.48339844,9.11512572 9.61230469,9.01551634 9.76464844,8.98914915 L11.7304688,8.64344603 C11.9238281,8.60828978 12.0703125,8.8133679 11.9765625,8.98621947 L8.99121094,14.4207898 C8.91503906,14.5614148 8.91503906,14.7342663 8.99121094,14.8748913 L12.0615234,20.4120007 C12.1582031,20.5848523 12.0087891,20.7899304 11.8154297,20.7577038 L9.68554687,20.3827038 C9.53320312,20.3563367 9.40429687,20.2567273 9.33984375,20.1161023 L7.68457031,16.4920788 C7.62597656,16.3602429 7.56445312,16.1258679 7.5,15.7889538 L7.4765625,15.7889538 C7.44433594,15.9530163 7.37402344,16.1844617 7.265625,16.4891492 L5.87988281,19.3719617 C5.7890625,19.5623913 5.58398437,19.6678601 5.37597656,19.6297742 L4.11621094,19.4100476 C3.9609375,19.3748913 3.87597656,19.2020398 3.95214844,19.0614148 L3.95214844,19.0614148 Z M28.75,25.3553601 C28.75,25.4842663 28.0195312,25.5897351 27.890625,25.5897351 L16.875,25.5897351 L16.875,22.3866101 C16.875,22.2577038 16.9804688,22.1522351 17.109375,22.1522351 L19.921875,22.1522351 C20.0507812,22.1522351 20.15625,22.0467663 20.15625,21.9178601 L20.15625,20.5116101 C20.15625,20.3827038 20.0507812,20.2772351 19.921875,20.2772351 L17.109375,20.2772351 C16.9804688,20.2772351 16.875,20.1717663 16.875,20.0428601 L16.875,19.1053601 C16.875,18.9764538 16.9804688,18.8709851 17.109375,18.8709851 L19.921875,18.8709851 C20.0507812,18.8709851 20.15625,18.7655163 20.15625,18.6366101 L20.15625,17.2303601 C20.15625,17.1014538 20.0507812,16.9959851 19.921875,16.9959851 L17.109375,16.9959851 C16.9804688,16.9959851 16.875,16.8905163 16.875,16.7616101 L16.875,15.8241101 C16.875,15.6952038 16.9804688,15.5897351 17.109375,15.5897351 L19.921875,15.5897351 C20.0507812,15.5897351 20.15625,15.4842663 20.15625,15.3553601 L20.15625,13.9491101 C20.15625,13.8202038 20.0507812,13.7147351 19.921875,13.7147351 L17.109375,13.7147351 C16.9804688,13.7147351 16.875,13.6092663 16.875,13.4803601 L16.875,12.5428601 C16.875,12.4139538 16.9804688,12.3084851 17.109375,12.3084851 L19.921875,12.3084851 C20.0507812,12.3084851 20.15625,12.2030163 20.15625,12.0741101 L20.15625,10.6678601 C20.15625,10.5389538 20.0507812,10.4334851 19.921875,10.4334851 L17.109375,10.4334851 C16.9804688,10.4334851 16.875,10.3280163 16.875,10.1991101 L16.875,9.26161009 C16.875,9.13270384 16.9804688,9.02723509 17.109375,9.02723509 L19.921875,9.02723509 C20.0507812,9.02723509 20.15625,8.92176634 20.15625,8.79286009 L20.15625,7.38661009 C20.15625,7.25770384 20.0507812,7.15223509 19.921875,7.15223509 L17.109375,7.15223509 C16.9804688,7.15223509 16.875,7.04676634 16.875,6.91786009 L16.875,3.71473509 L27.890625,3.71473509 C28.0195313,3.71473509 28.75,3.82020384 28.75,3.94911009 L28.75,25.3553601 Z" id="形状"></path>
                                    <path d="M22.265625,7.15223509 C22.1367188,7.15223509 22.03125,7.25770384 22.03125,7.38661009 L22.03125,8.79286009 C22.03125,8.92176634 22.1367188,9.02723509 22.265625,9.02723509 L26.015625,9.02723509 C26.1445312,9.02723509 26.25,8.92176634 26.25,8.79286009 L26.25,7.38661009 C26.25,7.25770384 26.1445312,7.15223509 26.015625,7.15223509 L22.265625,7.15223509 Z M22.265625,10.4334851 C22.1367188,10.4334851 22.03125,10.5389538 22.03125,10.6678601 L22.03125,12.0741101 C22.03125,12.2030163 22.1367188,12.3084851 22.265625,12.3084851 L26.015625,12.3084851 C26.1445312,12.3084851 26.25,12.2030163 26.25,12.0741101 L26.25,10.6678601 C26.25,10.5389538 26.1445312,10.4334851 26.015625,10.4334851 L22.265625,10.4334851 Z M22.265625,13.7147351 C22.1367188,13.7147351 22.03125,13.8202038 22.03125,13.9491101 L22.03125,15.3553601 C22.03125,15.4842663 22.1367188,15.5897351 22.265625,15.5897351 L26.015625,15.5897351 C26.1445312,15.5897351 26.25,15.4842663 26.25,15.3553601 L26.25,13.9491101 C26.25,13.8202038 26.1445312,13.7147351 26.015625,13.7147351 L22.265625,13.7147351 Z M22.265625,16.9959851 C22.1367188,16.9959851 22.03125,17.1014538 22.03125,17.2303601 L22.03125,18.6366101 C22.03125,18.7655163 22.1367188,18.8709851 22.265625,18.8709851 L26.015625,18.8709851 C26.1445312,18.8709851 26.25,18.7655163 26.25,18.6366101 L26.25,17.2303601 C26.25,17.1014538 26.1445312,16.9959851 26.015625,16.9959851 L22.265625,16.9959851 Z M22.265625,20.2772351 C22.1367188,20.2772351 22.03125,20.3827038 22.03125,20.5116101 L22.03125,21.9178601 C22.03125,22.0467663 22.1367188,22.1522351 22.265625,22.1522351 L26.015625,22.1522351 C26.1445312,22.1522351 26.25,22.0467663 26.25,21.9178601 L26.25,20.5116101 C26.25,20.3827038 26.1445312,20.2772351 26.015625,20.2772351 L22.265625,20.2772351 Z" id="形状"></path>
                                  </g>
                                </g>
                              </g>
                            </g>
                          </svg>
                        </div>
                        <div class="fileCli-cont">
                          <p style="color: #333333; font-size: 14px; margin: 0;">{{item2.FileName}}</p>
                          <p style="color: #666666; font-size: 12px; margin: 2px 0 0; ">{{item2.FileSize}}</p>
                        </div>
                        <div>
                          <a :href="item2.FileUrl" target="_blank">查看</a>
                        </div>
                      </div>
                    </div>
                  </div>


                </div>
                <div class="list-right-tag">
                  <span class="right-tab-list" v-for="item2 in item.ClueLabel">
                    <svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g id="简化版-首页-联合交付信息展示备份-2" transform="translate(-638.000000, -830.000000)">
                          <g id="标签" transform="translate(628.000000, 825.000000)">
                            <g id="编组备份-3" transform="translate(10.000000, 5.000000)">
                              <rect id="矩形" x="0" y="0" width="14" height="14"></rect>
                              <g id="编组-9" transform="translate(2.000000, 2.000000)">
                                <path d="M9.70273413,6.03280616 L6.03791748,9.69762281 C5.84618617,9.88958128 5.58599157,9.99743014 5.31466566,9.99743014 C5.04333974,9.99743014 4.78317353,9.88958128 4.59141383,9.69762281 L0,5.1113203 L0,0 L5.1113203,0 L9.70273413,4.59141383 C10.0990886,4.99012521 10.0990886,5.63409478 9.70273413,6.03280616 Z" id="路径" stroke="#003683" stroke-linejoin="round"></path>
                                <path d="M2.98160351,3.6915091 C3.37367017,3.6915091 3.6915091,3.37367017 3.6915091,2.98160351 C3.6915091,2.58953684 3.37367017,2.27169791 2.98160351,2.27169791 C2.58953684,2.27169791 2.27169791,2.58953684 2.27169791,2.98160351 C2.27169791,3.37367017 2.58953684,3.6915091 2.98160351,3.6915091 Z" id="路径" fill="#003683"></path>
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                    <span v-html="item2"></span>
                  </span>
                </div>
                <div class="list-right-bottom">
                  <!-- 23.11.17首页改版先不做-->
                  
                  <div class="list-right-btn">
                    <div class="list-btn-right mr12">
                      <span class="list-span showTeam-item" :data-id="item.StaffId">加入团队</span>
                    </div>
                    <div class="list-btn-moddle mr12">
                      <a href="javascript:void(0);" class="rnss-callphone show-phone" :data-transfernumber="item.MobileNumber" data-usnumber="111" data-ustel="111" title="点击呼叫">
                        电话联系
                      </a>
                    </div>
                    <div class="list-btn-left">
                      <span class="list-span">企微直聊</span>
                      <span class="call-phone">
                        <img :src="item.WeComQrCode" width="80px" height="80px" alt="Alternate Text" />
                        <span style="display:block;color:#333;font-size:12px;">扫一扫联系顾问</span>
                      </span>
                    </div>
                  </div>
                  
                  
                </div>
              </div>
            </li>
          </ul>
          <div style="display:flex;align-items:center;justify-content:center; padding: 4px 0 10px; font-size: 14px;color:#666;" v-show="moddleListShow">
            正在加载中
            <img src="/images/loading4.gif" alt="Alternate Text" />
          </div>
          <div v-if="GetPageInterData.length ==0" class="noData" style="height: 544px;">
            <div class="noData-content">
              <img class="h-nodata" src="../images/h-nodata.png" />
              <p class="noData-content-p">暂无数据~</p>
            </div>
          </div>
          <div style="text-align: center; height: 45px; line-height: 45px; border-top: 1px solid #E3E3E3; color: #666; cursor: pointer; padding-top: 10px;display:none; " @click="InterDataPage">
            <i style="width:10px;height:5px;display:inline-block;">
              <svg width="10px" height="5px" viewBox="0 0 10 5" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="vertical-align: middle;">
                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
                  <g id="简化版-首页-联合交付信息展示备份-2" transform="translate(-990.000000, -1261.000000)" stroke="#666666">
                    <g id="编组-5" transform="translate(990.000000, 1253.000000)">
                      <g id="下拉备份" transform="translate(0.000000, 8.000000)">
                        <polyline id="路径" points="10 0 5 5 0 0"></polyline>
                      </g>
                    </g>
                  </g>
                </g>
              </svg>
            </i>
            点击查看更多～
          </div>
        </div>
        <div class="lianhe-right lianhe-new-box">
          <div class="lianhe-right-header com-flex align-ce justify-ce">
            <img src="../images/index/lb.png" class="lianhe-header-img" />联合交付实时播报
          </div>
          <div class="lhjf-right-cetent2 list" id="dome">
            <div id="dome1" class="cc rowup">
              <div class="rowup-ch">
                <div class="lhjf-right-cetent item1" v-for="(item,index) in lianhebobao">
                  <div class="lhjf-right-cetent-top">
                    <div class="lhjf-right-cetent-left showCard-item" :data-id="item.fromStaffId">
                      <img class="lhjf-right-cetent-touxiang" :src="item.fromStaffPhoto" />
                      <p class="lhjf-right-cetent-left-p">{{item.fromStaffName}}</p>
                    </div>
                    <img class="h-hezuo2" src="../images/index/wq.png" />
                    <div class="lhjf-right-cetent-right showCard-item" :data-id="item.toStaffId">
                      <img class="lhjf-right-cetent-touxiang" :src="item.toStaffPhoto" />
                      <p class="lhjf-right-cetent-left-p" style="width: 42px;">{{item.toStaffName}}</p>
                    </div>
                  </div>
                  <div class="lhjf-right-cetent-bottom">
                    <p class="lhjf-right-cetent-bottom-p">成功合作</p>
                    <p class="lhjf-right-cetent-bottom-p2">{{item.jobTitle}}</p>
                    <p class="lhjf-right-cetent-bottom-p4">{{item.bonusesAmount}}元</p>
                    <p class="lhjf-right-cetent-bottom-p3">分成</p>
                  </div>
                </div>
                <div class="lhjf-right-cetent item1" v-if="4 < lianhebobao.length < 12" v-for="(item,index) in lianhebobao">
                  <div class="lhjf-right-cetent-top">
                    <div class="lhjf-right-cetent-left showCard-item" :data-id="item.fromStaffId">
                      <img class="lhjf-right-cetent-touxiang" :src="item.fromStaffPhoto" />
                      <p class="lhjf-right-cetent-left-p">{{item.fromStaffName}}</p>
                    </div>
                    <img class="h-hezuo2" src="../images/index/wq.png" />
                    <div class="lhjf-right-cetent-right showCard-item" :data-id="item.toStaffId">
                      <img class="lhjf-right-cetent-touxiang" :src="item.toStaffPhoto" />
                      <p class="lhjf-right-cetent-left-p" style="width: 42px;">{{item.toStaffName}}</p>
                    </div>
                  </div>
                  <div class="lhjf-right-cetent-bottom">
                    <p class="lhjf-right-cetent-bottom-p">成功合作</p>
                    <p class="lhjf-right-cetent-bottom-p2">{{item.jobTitle}}</p>
                    <p class="lhjf-right-cetent-bottom-p4">{{item.bonusesAmount}}元</p>
                    <p class="lhjf-right-cetent-bottom-p3">分成</p>
                  </div>
                </div>
              </div>
            </div>
            <div v-show="lianhebobao.length == 0" class="noData">
              <div class="noData-content">
                <img class="h-nodata" src="../images/h-nodata.png" />
                <p class="noData-content-p">暂无数据~</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!---------------------------我有需求，找人才---联合交付结束----------------------------->
    <!-- 靠谱大客户开始 涉及到的接口有 getReliableKAPageList-->
    <!-- 点击弹框是怎么实现的? -->
    <!-- 外层上写 id="managenotice_tab" 点击弹框的地方class里面添加r_btn_yulan 标签上添加:data-id="id" id就是你当前这条数据的id 依赖<script src="../Scripts/App/r_notice2.js"></script> <script src="../Scripts/App/managenotice.js"></script> -->
    <div class="kpbig border-ra" style="display:none;">
      <div class="kpbig-top">
        <img class="gongsiSvg" src="../images/gongsi.svg" />
        <p class="kpbig-top-p" style="color: #2A7FCC;font-weight: 600;font-size: 18px;">靠谱大客户</p>
        <div class="kpbig-top-more" @click="getMorse('/Client/ReliableKA')">
          <p class="kpbig-top-more-p">更多</p>
          <img class="moreSvg" src="../images/h-more.svg" />
        </div>
      </div>
      <div class="kpbig-bottom" id="managenotice_tab">
        <div v-if="kpBig.length !=0" class="kpbig-bottom-content" v-for="(item,index) in kpBig" :key="item.index">
          <div class="kpbig-bottom-content-img r_btn_yulan" :data-id="item.rnssNoticeId">
            <img class="kpbig-bottom-content-imggg " :src="item.cover" />
            <p class="kpbig-bottom-content-img-p">{{item.logTitle}}</p>
          </div>
          <p class="kpbig-bottom-content-p r_btn_yulan" :data-id="item.rnssNoticeId">{{item.logTitle}}</p>
          <p class="kpbig-bottom-content-p2">{{item.publishTime}}</p>
          <p class="kpbig-bottom-content-p3" v-html="item.noticeContent"></p>
        </div>
        <div v-if="kpBig.length ==0" class="noData" style="height: 100%;">
          <div class="noData-content">
            <img class="h-nodata" src="../images/h-nodata.png" />
            <p class="noData-content-p">暂无数据~</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 靠谱大客户开始 -->
    <!-- 达人网校课程推荐开始 涉及到的接口有:getKechengList 跳转事件schoolTiao-->
    <div class="index-content5 border-ra">
      <div class="index-content5-top">
        <img class="h-school" src="../images/h-school.svg" />
        <p class="index-content5-top-p" style="color: #2A7FCC;font-weight: 600;font-size: 18px;">达人网校课程推荐</p>
        <div class="kpbig-top-more" @click="getMorse('/NetworkSchool/index')">
          <p class="kpbig-top-more-p">更多</p>
          <img class="moreSvg" src="../images/h-more.svg" />
        </div>
      </div>
      <div class="index-content5-bottom">
        <img class="index-content5-bottom-img" v-for="(item,index) in kechengList" :key="item.index" :src="item.kechengImg" v-if="kechengList.length != 0" @click="schoolTiao(item.id,item.defaultKeshiId)" />
        <div v-if="kechengList.length == 0" class="noData">
          <div class="noData-content">
            <img class="h-nodata" src="../images/h-nodata.png" />
            <p class="noData-content-p">暂无数据~</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 达人网校课程推荐结束 -->

    <div class="index-content4 mr-bt25">
      <!-- 排行榜开始 -->
      <!-- 排行榜有筛选条件 筛选条件不同头部第三条显示的title也不同 涉及到的接口getRankingList-->
      <!-- 点击名字弹框怎么实现的? -->
      <!-- 你点击需要弹框的class上添加showCard-item 标签上写:data-id="id" id就是你当前人的id 依赖什么js我忘了-->
      <div class="index-content4-left border-ra">
        <div class="index-paihang-top com-flex justify-be">
          <div class="index-paihang-top-left">
            <img class="h-paihang" src="../images/h-paihang.svg" />
            <p class="paihang-p" style="color: #2A7FCC;font-weight: 600;font-size: 18px;">排行榜</p>
          </div>
          <div class="index-paihang-top-right">
            <el-select style="margin-left: 39px;" class="paihang-select" @change="getRankingList" v-model="rankingLeftValue" placeholder="请选择">
              <el-option v-for="item in rankingLeft"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
            <el-select class="paihang-select" @change="getRankingList" v-model="rankingRightValue" placeholder="请选择">
              <el-option v-for="item in rankingRight"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
            <el-select style="width: 80px;" class="paihang-select" @change="getRankingList" v-model="rankingPearsonValue" placeholder="请选择">
              <el-option v-for="item in rankingPearson"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
            <el-select style="width: 70px;" class="paihang-select" @change="getRankingList" v-model="rankingDateValue" placeholder="请选择">
              <el-option v-for="item in rankingDate"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="index-paihang-bottom">
          <div class="index-paihang-tableHead com-flex">
            <div class="index-paihang-mc">
              <p class="index-paihang-mc-p">名次</p>
            </div>
            <div class="index-paihang-xm">
              <p class="index-paihang-xm-p">姓名</p>
            </div>
            <div class="index-paihang-yj">
              <p class="index-paihang-xm-p text-right index-paihang-right" v-show="rankingLeftValue == 100">所得业绩</p>
              <p class="index-paihang-xm-p text-right index-paihang-right" v-show="rankingLeftValue == 600">线索奖金额</p>
              <p class="index-paihang-xm-p text-right index-paihang-right" v-show="rankingLeftValue == 600">线索奖金额</p>
              <p class="index-paihang-xm-p text-right index-paihang-right" v-show="rankingLeftValue == 400">offer金额</p>
              <p class="index-paihang-xm-p text-right index-paihang-right" v-show="rankingLeftValue == 200">回款金额</p>
              <p class="index-paihang-xm-p" v-show="rankingLeftValue == 8 ||
                                        rankingLeftValue == 300 ||
                                        rankingLeftValue == 9 ||
                                        rankingLeftValue == 6">
                完成数
              </p>
            </div>
            <div class="index-paihang-gs">
              <p class="index-paihang-gs-p index-paihang-gs-p-new">归属公司</p>
            </div>
            <div class="index-paihang-dq">
              <p class="index-paihang-gs-p index-paihang-gs-p-new">归属大区</p>
            </div>
          </div>
          <div v-if="rankingData.length != 0" class="index-paihang-tableBody">
            <div class="index-paihang-tableBody index-paihang-table-new">
              <div class="index-paihang-box-new" :class="{'index-paihang-tableBody-content':item.rank==1||item.rank==3||item.rank==5,'index-paihang-tableBody-content2':item.rank==2||item.rank==4||item.rank==6}" v-for="(item,index) in rankingData" :key="item.index">
                <div class="index-paihang-mc">
                  <img v-if="item.rank == 1" class="h-No1" src="../images/h-No1.png" />
                  <img v-if="item.rank == 2" class="h-No1" src="../images/h-No2.png" />
                  <img v-if="item.rank == 3" class="h-No1" src="../images/h-No3.png" />
                  <p v-if="item.rank == 4 || item.rank == 5 || item.rank == 6" class="index-paihang-mc-p">{{item.rank}}</p>
                </div>
                <div class="index-paihang-xm">
                  <p :data-id="item.staffId" :class="{'index-paihang-xm2 showCard-item':item.staffGender ==1,'index-paihang-xm2-women showCard-item':item.staffGender ==2}">{{item.staffName}}</p>
                </div>
                <div class="index-paihang-yj">
                  <p class="index-paihang-yj-p2 text-right index-paihang-right index-paihang-bold" v-show="rankingLeftValue == 100 || rankingLeftValue == 200||rankingLeftValue == 400">
                    {{item.performanceAmount}}
                  </p>
                  <p class="index-paihang-yj-p2 text-right index-paihang-right index-paihang-bold" v-show="rankingLeftValue == 600">
                    {{item.performanceAmount}}元
                  </p>
                  <p class="index-paihang-yj-p2 text-right index-paihang-right index-paihang-bold" v-show="rankingLeftValue == 600">
                    {{item.performanceAmount}}元
                  </p>
                  <p class="index-paihang-yj-p2 index-paihang-bold" v-show="rankingLeftValue == 8 ||
                                        rankingLeftValue == 300 ||
                                        rankingLeftValue == 9 ||
                                        rankingLeftValue == 6">
                    {{item.performanceAmount}}
                  </p>
                </div>
                <div class="index-paihang-gs">
                  <p class="index-paihang-gs-p index-paihang-gs-p-new">{{item.companyName}}</p>
                </div>
                <div class="index-paihang-dq">
                  <p class="index-paihang-gs-p index-paihang-gs-p-new">{{item.regionName}}</p>
                </div>
              </div>
            </div>
          </div>
          <div v-if="rankingData.length == 0" class="noData" style="height: 240px;">
            <div class="noData-content">
              <img class="h-nodata" src="../images/h-nodata.png" />
              <p class="noData-content-p">暂无数据~</p>
            </div>
          </div>
          <div class="index-paihang-tableFoot">
            <p class="index-paihang-tableFoot-p" @click="getMorse('/branchadmin/realtimedata.aspx')" v-if="rankingLeftValue!=600">更多排行 ></p>
            <p class="index-paihang-tableFoot-p" @click="getMorse('/PersonnelBank/Index')" v-if="rankingLeftValue==600">更多排行 ></p>
          </div>
        </div>
      </div>
      <!-- 排行榜结束 -->
      <!-- 订阅公司新闻开始 涉及到的接口有:getGetCompanyNewsList-->
      <div class="index-content4-left border-ra" style="float: right;">
        <div class="index-content6-left-top">
          <img class="h-dingyue" src="../images/h-news.svg" />
          <p class="index-content6-left-top-p" style="color: #2A7FCC;font-weight: 600;font-size: 18px;">公司新闻</p>
          <a target="_blank" href="http://www.risfond.com/news " class="kpbig-top-more">
            <p class="kpbig-top-more-p">更多</p>
            <img class="moreSvg" src="../images/h-more.svg" />
          </a>
        </div>
        <div :class="{'index-content6-content':item.abc == 1,'index-content6-content2':item.abc == 2}" v-for="(item,index) in companyNews" :key="item.index">
          <div class="greenlaba" style="background-color: rgb(237,107,117);">
            <svg t="1627950440914" class="raddd" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1343" data-spm-anchor-id="a313x.7781069.0.i7" width="200" height="200"><path d="M999.**********.191304c-15.582609-7.791304-35.617391-5.565217-48.973913 4.452174L768 377.321739V278.26087c0-24.486957-15.582609-44.521739-40.069565-44.52174H44.521739c-24.486957 0-44.521739 20.034783-44.521739 44.52174v467.47826c0 24.486957 20.034783 44.521739 44.521739 44.52174h683.408696c24.486957 0 40.069565-20.034783 40.069565-44.52174v-99.060869l182.53913 134.678261c7.791304 5.565217 18.921739 8.904348 27.826087 8.904348 6.678261 0 14.469565-1.113043 20.034783-4.452174 15.582609-7.791304 25.6-23.373913 25.6-40.069566V278.26087c0-16.695652-10.017391-32.278261-24.486957-40.069566zM678.**********.217391H89.043478V322.782609h589.913044v378.434782z m256-44.521739L779.130435 538.713043v-53.426086L934.956522 367.304348v289.391304z" p-id="1344" fill="#FFFFFF"></path></svg>
          </div>
          <a :href="item.categoryUrl" class="index-content6-content-p" target="_blank" rel="noopener noreferrer">
            {{item.title}}
          </a>
          <p class="index-content6-content-timep">{{item.createdTime}}</p>
        </div>
      </div>
      <!-- 订阅公司新闻结束 -->
      <!-- 排行榜和实时信息互动开始 -->
      <!-- 实时信息互动开始 涉及到的接口有:getInteractionList获取数据 submitMessage发送数据-->
      <div class="index-content4-left" style="float: right;display:none;">
        <div class="messageinteraction-top">
          <img class="h-message" src="../images/h-message.svg" />
          <p class="messageinteraction-top-p" style="color: #2A7FCC;font-weight: 600;font-size: 18px;">实时信息互动</p>
          <div class="subscribeMessage" @click="openDingyue">
            <img class="h-wifi" src="../images/h-wifi.svg" />
            <p class="subscribeMessage-p">订阅信息</p>
          </div>
          <div class="kpbig-top-more" @click="getMorse('/interaction/manage')">
            <p class="kpbig-top-more-p">更多</p>
            <img class="moreSvg" src="../images/h-more.svg" />
          </div>
          <div class="subscribeMessage" @click="getInteractionList" style="width: 80px;border: 0;float: right;">
            <img class="h-wifi" src="../images/h-f5.svg" />
            <p class="subscribeMessage-p" style="width: 30px;">刷新</p>
          </div>
        </div>
        <div class="messageinteraction-center">
          <div v-for="(item,index) in timeMessage" :key="item.index" class="messageinteraction-center-content" id="scHudong">
            <img :data-id="item.staffId" :class="{'messageinteraction-touxing showCard-item':item.isMy =='in','showCard-item messageinteraction-touxing-right':item.isMy =='out'}" :src="item.staffPictureImg" />
            <div :class="{'messageinteraction-content':item.isMy =='in','messageinteraction-content-right':item.isMy =='out'}">
              <span :class="{'arrow-left':item.isMy =='in','arrow-right':item.isMy =='out'}"></span>
              <div class="message-content-top" v-if="item.isMy =='in'">
                <p :data-id="item.staffId" class="message-content-top-p showCard-item">{{item.staffName}}</p>
                <p class="message-content-top-p2">{{item.companyName}}</p>
                <p class="message-content-top-p3">{{item.pubDate}}</p>
              </div>
              <div class="message-content-top-right" v-else>
                <p class="message-content-top-p3">{{item.pubDate}}</p>
                <p class="message-content-top-p2">{{item.companyName}}</p>
                <p :data-id="item.staffId" class="message-content-top-p showCard-item">{{item.staffName}}</p>
              </div>
              <div class="message-content-content">
                <span :class="{'h-left':item.isMy =='in','h-right':item.isMy =='out'}">{{item.content}}</span>
              </div>
            </div>
            <div class="h-renren">
              <img v-if="item.category ==2" src="../Content/images/interaction/2.png" />
              <img v-else src="../Content/images/interaction/1.png" />
            </div>
          </div>
        </div>
        <div class="messageinteraction-bottom">
          <el-select v-model="submitValue" placeholder="请选择" style="width: 130px;float: left;">
            <el-option v-for="item in submitLeixing"
                       :key="item.value"
                       :label="item.text"
                       :value="item.value">
            </el-option>
          </el-select>
          <el-input v-model="submitInput" placeholder="请输入内容" style="width: 270px;float: left;"></el-input>
          <el-button @click="submitMessage" type="primary" style="float: left;height: 30px;padding: 7px 20px;">发送</el-button>
        </div>
      </div>
      <!-- 实时信息互动结束 -->
    </div>

    <!-- 订阅简历搜索动态和公司新闻开始 -->
    <div class="index-content4" style="margin-bottom: 20px;display:none;">
      <!-- 订阅简历搜索动态开始 涉及到的接口有:getSubscriptionResumeDynamic-->
      <div class="index-content4-left">
        <div class="index-content6-left-top">
          <img class="h-dingyue" src="../images/h-dingyue.svg" />
          <p class="index-content6-left-top-p" style="color: #2A7FCC;font-weight: 600;font-size: 18px;">订阅简历搜索动态</p>
          <div class="kpbig-top-more" @click="getMorse('/resume/nsearchresume')">
            <p class="kpbig-top-more-p">更多</p>
            <img class="moreSvg" src="../images/h-more.svg" />
          </div>
        </div>
        <div v-if="resumeDynamics.length != 0" class="wangleShipei">
          <div :class="{'index-content6-content':item.abc == 1,'index-content6-content2':item.abc == 2}" v-for="(item,index) in resumeDynamics" :key="item.index">
            <img class="greenlaba" src="../images/greenlaba.svg" />
            <a href="http://staff.risfond.com/resume/nsearchresume" class="index-content6-content-p" target="_blank" rel="noopener noreferrer">{{item.name}}</a>
            <a href="http://staff.risfond.com/resume/nsearchresume" target="_blank" rel="noopener noreferrer">
              <div class="newRuku" style="margin-left: 24px;">
                <p class="index-content6-content-p2">新更新：</p>
                <p class="index-content6-content-p2" style="color: #2A7FCC;margin-left: 5px;">{{item.newUpdateCount}}人</p>
              </div>
            </a>
          </div>
        </div>
        <div v-if="resumeDynamics.length == 0" class="noData" style="height: 330px;">
          <div class="noData-content">
            <img class="h-nodata" src="../images/h-nodata.png" />
            <p class="noData-content-p">暂无数据~</p>
          </div>
        </div>
      </div>
      <!-- 订阅简历搜索动态结束 -->

    </div>
    <!-- 订阅简历搜索动态和公司新闻结束 -->
    <!-- 订阅信息弹框开始 -->
    <!-- 获取数据的接口是:getStaffSubscriptionInteraction 添加订阅条件的事件:addCondition 删除订阅条件:delectDyData 添加关键词:addGuanjian 删除关键词:decectkeyWord 保存订阅条件:submitDyData-->
    <div class="dingyue-tankuang">
      <div class="subMessage">
        <div class="selectzhiwei-top">
          <p class="selectzhiwei-top-p" style="color: #2A7FCC;font-weight: 600;font-size: 18px;">订阅信息</p>
          <div class="closee" @click="closeDingyue"></div>
        </div>
        <div class="subMessage-center">
          <div class="selectzhiwei-center-red">
            <p class="selectzhiwei-center-red-p">
              设置订阅信息，有匹配的信息将会进行信息推送，不错过任何关键信息，可添加 3 条订阅。
            </p>
          </div>
          <div class="sac" v-for="(item,index) in subMessages" :key="item.index">
            <div class="selectzhiwei-center-content">
              <p class="selectzhiwei-center-content-p">信息类别</p>
              <el-select v-model="item.categoryID" placeholder="请选择" style="width: 388px;margin-left: 10px;float: left;">
                <el-option v-for="item in submitLeixing"
                           :key="item.value"
                           :label="item.text"
                           :value="item.value">
                </el-option>
              </el-select>
            </div>
            <div class="selectzhiwei-center-content" style="margin-top: 0;">
              <p class="selectzhiwei-center-content-p">关键词</p>
              <el-input maxlength="8" v-model="item.tjMessage" placeholder="请输入内容" style="float: left;width: 322px;height: 38px;margin-left: 9px;"></el-input>
              <p class="add-tianjia" @click="addGuanjian(index)">添加</p>
            </div>
            <div class="selectzhiwei-center-bottom">
              <div class="add-rongqi">
                <div class="add-forData" v-for="(itemm,index2) in item.keyword" :key="itemm.index2">
                  <p class="add-forData-p">{{itemm}}</p>
                  <img class="h-chacha" src="../images/h-chacha.svg" @click="decectkeyWord(index,index2)" />
                </div>
              </div>
              <img v-show="index !=0" class="h-delect" src="../images/h-delect.svg" @click="delectDyData(index)" />
            </div>
          </div>
          <div class="selectzhiwei-center-content" v-show="subMessages.length < 3">
            <p class="selectzhiwei-add" @click="addCondition">+ 添加订阅条件</p>
          </div>

        </div>
        <div class="selectzhiwei-bottom">
          <div class="selectzhiwei-bottom-ok" @click="submitDyData">
            <p class="selectzhiwei-bottom-okp">保存</p>
          </div>
          <div @click="closeDingyue" class="selectzhiwei-bottom-ok" style="background-color: #FFFFFF;border: 1px solid #DDDDDD;margin-right: 10px;">
            <p class="selectzhiwei-bottom-okp" style="color: #333333;">取消</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 订阅信息弹框结束 -->
    <!-- 设置关注交付中心职位类型弹框开始 -->
    <!-- 弹框这个比较复杂 获取数据反显的接口getStaffAttentionJob 里面的弹框要先通过接口getIndustry获取行业getOccupation获取职业类别getILocationTree获取工作地点 获取之后通过递归操作 最后数据变成什么样自己去递归操作里log一下 点击呼出弹框事件selectData 具体的写在那里面 保存事件:saveStaffAttentionJob-->
    <div class="zhilei-tankuang">
      <div class="selectzhiwei">
        <div class="selectzhiwei-top">
          <p class="selectzhiwei-top-p">设置关注交付中心职位类型</p>
          <div class="closee" @click="closeZhiwei"></div>
        </div>
        <div class="selectzhiwei-center">
          <div class="selectzhiwei-center-red">
            <p class="selectzhiwei-center-red-p">
              设置想要关注的职位类型，比如行业、类别等等，我们将会根据喜好进行数据推送相信息。
            </p>
          </div>
          <div class="selectzhiwei-center-content">
            <p class="selectzhiwei-center-content-p">职位行业</p>
            <div @click="selectData(1)" class="selectzhiwei-center-content-select">
              <p class="selectzhiwei-center-p" :class="{'selectzhiwei_p':industryData.length != 0}">{{followIndustry}}</p>
            </div>
          </div>
          <div class="selectzhiwei-center-content">
            <p class="selectzhiwei-center-content-p">职位类别</p>
            <div @click="selectData(2)" class="selectzhiwei-center-content-select">
              <p class="selectzhiwei-center-p" :class="{'selectzhiwei_p':occupationData.length != 0}">{{followOccupation}}</p>
            </div>
          </div>
          <div class="selectzhiwei-center-content">
            <p class="selectzhiwei-center-content-p">职位地点</p>
            <div @click="selectData(3)" id="locationsWrapper" class="selectzhiwei-center-content-select">
              <p class="selectzhiwei-center-p" :class="{'selectzhiwei_p':ilocationTreeData.length != 0}">{{followIlocationTree}}</p>
            </div>
          </div>
          <div class="selectzhiwei-center-content2">
            <div class="selectzhiwei-center-sx">
              <p class="selectzhiwei-center-sxp">标签筛选</p>
            </div>
            <div class="selectzhiwei-center-sx2">
              <el-checkbox v-model="checkeda">保用期短</el-checkbox>
              <el-checkbox v-model="checkedb">提点≥10%</el-checkbox>
              <el-checkbox v-model="checkedc">有成单</el-checkbox>
              <el-checkbox v-model="checkedd">有回款</el-checkbox>
              <el-checkbox v-model="checkede">反馈快</el-checkbox>
            </div>
          </div>
        </div>
        <div class="selectzhiwei-bottom">
          <div class="selectzhiwei-bottom-ok" @click="saveStaffAttentionJob">
            <p class="selectzhiwei-bottom-okp">保存</p>
          </div>
          <div @click="clearZhiwei" class="selectzhiwei-bottom-ok" style="background-color: #FFFFFF;border: 1px solid #DDDDDD;margin-right: 10px;">
            <p class="selectzhiwei-bottom-okp" style="color: #333333;">清除</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 设置关注交付中心职位类型弹框结束 -->
    <!---------------------------设置快捷入口开始----------------------------->
    <div id="customizeShortcuts" class="modal fade dlg-hhrzd" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header bg-white">
            <div class="titlewgs">
              <span class="modal-title text-left bg-black">自定义我的快捷方式</span>
            </div>
            <div class="closeed">
              <span data-dismiss="modal" aria-hidden="true" class="modal-title text-right bg-black">
                <span class="iconfont" style="font-size:22px;color:red;">
                  &#xe6ba;
                </span>
              </span>
            </div>
          </div>
          <div class="modal-body">
            <div class="custom_shortcut_sure">
              <div class="custom_shortcut_sure_desc"><span>拖曳可调整顺序，点击减号可移除</span></div>
              <div class="custom_shortcut_sure_item">
                <div class="custom_shortcut_sure_items" name="custom_shortcut_sure_items" v-for="(item,index) in editYesAddDto">
                  <div class="custom_shortcut_sure_items_div" :id="'div'+index+1" ondrop="drop(event,this)" ondragover="allowDrop(event)" draggable="true" ondragstart="drag(event, this)" style="">
                    <img class="custom_shortcut_sure_items-img" :src="item.shortcutIconUrl" />
                    <span class="iconfont closed" @click="removeShortcut(item)">&#xe6b5;</span>
                    <p name="shortCutNames" :value="item.id" class="kuaijierukou-content-item-title">{{item.shortcutName}}</p>
                  </div>
                </div>

              </div>
            </div>
          </div>
          <hr />
          <div class="modal-body">
            <div class="custom_shortcut_sure">
              <div class="custom_shortcut_sure_desc"><span>点击加号可添加至快捷方式</span></div>
              <div class="custom_shortcut_sure_item">

                <div class="custom_shortcut_sure_items" v-for="(item,index) in remainAddDTO">
                  <img class="custom_shortcut_sure_items-img" :src="item.shortcutIconUrl" />
                  <span class="iconfont add_add" @click="addShortcut(item)">&#xe6b6;</span>
                  <p class="kuaijierukou-content-item-title">{{item.shortcutName}}</p>
                </div>
              </div>
            </div>
          </div>
          <hr />
          <div class="modal-body">
            <a class="restore_default" @click="restoreDefault">恢复默认</a>
            <a data-dismiss="modal" aria-hidden="true" class="modal-title text-right bg-black save" @click="saveShortcut">保存</a>
            <a data-dismiss="modal" aria-hidden="true" class="modal-title text-right bg-black cancel">取消</a>
          </div>
          <div style="height:10px;"></div>
        </div>
      </div>
    </div>
    <!---------------------------设置快捷入口结束----------------------------->
    <!---------------------------职位偏好设置开始----------------------------->
    <el-dialog title="提示"
               :visible.sync="projectDialog"
               width="554px"
               class="projectSetting"
               :close-on-click-modal="false">

      <div class="project-top">职位偏好设置</div>

      <div class="project-cont">
        <p class="p" style="margin-bottom:14px;">尊敬的猎头，现在选择您偏好的做单方向，可立即获得100R币奖励！</p>
        <p class="p">系统将根据您填写的做单偏好来为您推荐职位或人选，您填写的行业、职类等信息越精确，为您推荐的职位或人选就越精确哦</p>
      </div>
      <div class="project-select">
        <div class="project-flex">
          <div class="goodFont"><i class="xingxing">*</i> 行业偏好：</div>
          <div class="project-input" v-on:click="editAbility('industry')">
            <span v-for="(item, index) in PreferenceData.GoodIndustrys" :key="index">
              <span v-if="index==0">{{item.industryName}}</span>
              <span v-else>、{{item.industryName}}</span>
            </span>
            <span class="project-select-span" v-if="!PreferenceData.GoodIndustrys">请选择</span>
          </div>
        </div>
        <div class="project-flex">
          <div class="goodFont"><i class="xingxing">*</i> 职位偏好：</div>
          <div class="project-input" v-on:click="editAbility('occupation')">
            <span v-for="(item, index) in PreferenceData.GoodOccupations" :key="index">
              <span v-if="index==0">{{item.occupationsName}}</span>
              <span v-else>、{{item.occupationsName}}</span>
            </span>
            <span class="project-select-span" v-if="!PreferenceData.GoodOccupations">请选择</span>
          </div>
        </div>
        <div class="project-flex">
          <div class="goodFont"><i class="xingxing">*</i> 偏好地区：</div>
          <div class="project-input" v-on:click="editAbility('location')">
            <span v-for="(item, index) in PreferenceData.GoodLocations" :key="index">
              <span v-if="index==0">{{item.LocationsName}}</span>
              <span v-else>、{{item.LocationsName}}</span>
            </span>
            <span class="project-select-span" v-if="!PreferenceData.GoodLocations">请选择</span>
          </div>
        </div>
        <div style="margin-bottom:20px;">
          <div class="project-salary">
            <div class="goodFont"><i class="xingxing">*</i> 偏好年薪：</div>
            <div>
              <el-input v-model="PreferenceData.GoodMinSalary" placeholder="最低" class="small-salary">
                <i slot="suffix" style="font-style:normal;">万</i>
              </el-input>
              <span style="margin:0 9px;">至</span>
              <el-input v-model="PreferenceData.GoodMaxSalary" placeholder="最高" class="big-salary">
                <i slot="suffix" style="font-style:normal;">万</i>
              </el-input>
            </div>
          </div>
          <div class="project-yearM">
            <span class="salary-num" id="1" onclick='salaryColor(this.id)' @click="salaryClick(1)">20～30万</span>
            <span class="salary-num" id="2" onclick='salaryColor(this.id)' @click="salaryClick(2)">30～50万</span>
            <span class="salary-num" id="3" onclick='salaryColor(this.id)' @click="salaryClick(3)">50～100万</span>
          </div>
        </div>

        <div class="project-flex">
          <div class="goodFont" style="width: 88px; height: 32px; line-height: 32px; padding-top: 0;"><i class="xingxing">*</i> 擅长环节：</div>
          <div class="goodTags">
            <div :class="item.IsSelected?'distag-on':''" class="tag-status" v-for="item in PreferenceData.GoodAtLinks" @click="AtLinksClick(item)">
              {{item.LinksName}}
            </div>
          </div>
        </div>
        <div class="project-flex">
          <div class="goodFont" style="width:88px;height:32px;line-height:32px;padding-top:0;"><i class="xingxing">*</i> 性格特点：</div>
          <div class="goodTags">
            <div :class="item.IsSelected?'distag-on':''" class="tag-status" v-for="item in PreferenceData.GoodDispositions" @click="DispositionClick(item)">
              {{item.DispositionsName}}
            </div>
          </div>
        </div>
        <div class="project-flex">
          <div class="goodFont" style="width: 88px; height: 32px; line-height: 32px; padding-top: 0;"><i class="xingxing">*</i> 个人能力：</div>
          <div class="goodTags">
            <div :class="item.IsSelected?'distag-on':''" class="tag-status" v-for="item in PreferenceData.GoodAbilitys" @click="AbilitysClick(item)">
              {{item.AbilitysName}}
            </div>
          </div>
        </div>
      </div>

      <div class="project-bot">
        <el-button type="primary" class="setting-btn" @click="NextSettingPreference">下次设置</el-button>
        <el-button type="primary" class="submit-btn" @click="submitProject">保存</el-button>
      </div>

    </el-dialog>
    <!---------------------------职位偏好设置结束----------------------------->

    <el-dialog title="提示"
               :visible.sync="dateDialog"
               width="554px"
               class="clueDialog"
               :close-on-click-modal="false">

      <div class="publishClue">
        <div class="publisHead">
          <span class="publisHead-font">选择时间</span>
          <span class="publisHead-close" @click="closeDateDialog">×</span>
        </div>

        <div class="publisTabs">
          <div style="text-align:center">

            <el-date-picker v-model="clueDateVal"
                            type="daterange"
                            align="right"
                            unlink-panels
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="yyyy-MM-dd"
                            :picker-options="pickerOptions">
            </el-date-picker>
          </div>

          <div style="padding:30px 0;text-align:end;">
            <el-button class="publisCubmit" @click="submitClueDate">确定</el-button>
          </div>
        </div>

      </div>



    </el-dialog>

    <!---------------------------发布线索开始----------------------------->
    <el-dialog title="提示"
               :visible.sync="clueDialog"
               width="554px"
               class="clueDialog"
               :close-on-click-modal="false">
      <div class="publishClue">
        <div class="publisHead">
          <span class="publisHead-font">快捷发布</span>
          <span class="publisHead-close" @click="closeClueDialog">×</span>
        </div>
        <div class="publisTabs" v-loading="clueLoading" element-loading-text="正在发布中">

          <div class="publisTabs-list publisTabs-on" id="1" onclick='publisColor(this.id)' @click="publisClick(1)">
            <p class="p font-p1 border-p">职位线索</p>
            <p class="p font-p2" style="padding:10px 14px;">我有职位线索可以联合交付</p>
          </div>
          <div class="publisTabs-list" id="2" onclick='publisColor(this.id)' @click="publisClick(2)">
            <p class="p font-p1 border-p">人选线索</p>
            <p class="p font-p2" style="padding:10px 14px;">我有优质人选正在看机会</p>
          </div>
          <div class="" style="margin-top:20px;">
            <el-input type="textarea"
                      :placeholder="placeholderText"
                      @paste.native.capture.prevent="pasting"
                      style="width:514px;"
                      rows="5"
                      v-model="TagsVal.Content"
                      maxlength="2000"
                      show-word-limit>
            </el-input>

            <div class="imgList" v-if="imgData.length>0">
              <ul>
                <li class="imgList-li" v-for="(item,index) in imgData" :key="index">
                  <div class="img-C" v-if="item.FileType=='.jpg'||item.FileType=='.jpeg'||item.FileType=='.png'">
                    <i class="deleteImg" @click="deleteImg(index)">
                      <svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                          <g id="画板备份" transform="translate(-1861.000000, -822.000000)">
                            <g id="编组-4" transform="translate(1763.000000, 522.000000)">
                              <g id="编组" transform="translate(98.000000, 300.000000)">
                                <path d="M7,13 C10.31371,13 13,10.31371 13,7 C13,3.68629 10.31371,1 7,1 C3.68629,1 1,3.68629 1,7 C1,10.31371 3.68629,13 7,13 Z" id="路径" fill-opacity="0.194356425" fill="#000000"></path>
                                <line x1="8.69701" y1="5.30296" x2="5.3029" y2="8.69707" id="路径" stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round"></line>
                                <line x1="5.30299" y1="5.30296" x2="8.6971" y2="8.69707" id="路径" stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round"></line>
                              </g>
                            </g>
                          </g>
                        </g>
                      </svg>
                    </i>
                    <img :src="item.url" width="100%" height="100%" style="border-radius:50% !important;" alt="Alternate Text" />
                  </div>
                  <span style="position: relative; display: inline-block;" v-else>
                    <i class="deleteImg" @click="deleteImg(index)">
                      <svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="vertical-align: top;">
                        <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                          <g id="画板备份" transform="translate(-1861.000000, -822.000000)">
                            <g id="编组-4" transform="translate(1763.000000, 522.000000)">
                              <g id="编组" transform="translate(98.000000, 300.000000)">
                                <path d="M7,13 C10.31371,13 13,10.31371 13,7 C13,3.68629 10.31371,1 7,1 C3.68629,1 1,3.68629 1,7 C1,10.31371 3.68629,13 7,13 Z" id="路径" fill-opacity="0.194356425" fill="#000000"></path>
                                <line x1="8.69701" y1="5.30296" x2="5.3029" y2="8.69707" id="路径" stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round"></line>
                                <line x1="5.30299" y1="5.30296" x2="8.6971" y2="8.69707" id="路径" stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round"></line>
                              </g>
                            </g>
                          </g>
                        </g>
                      </svg>
                    </i>

                    <span v-if="item.FileType=='.doc'||item.FileType=='.docx'">
                      <span class="img-C" @click="deleteImg(index)" style="display:flex;justify-content:center;align-items:center;cursor:pointer;">
                        <svg width="60px" height="60px" viewBox="0 0 60 60" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="vertical-align: top;">
                          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <g id="pdf_export" fill="#6791FF" fill-rule="nonzero">
                              <g id="word">
                                <path d="M59.5865683,8.30284356 C59.6826396,7.58093057 59.4394673,6.85516452 58.9278535,6.33686018 C58.4162397,5.81855584 57.6936941,5.56597493 56.9705939,5.65265916 C49.7348117,5.51217974 42.4917508,5.65265916 35.2414111,5.58933424 L35.2414111,0.0429444876 L31.1893439,0.0429444876 C20.8047845,1.9361413 10.4136743,3.74490489 0.0291149068,5.60316382 L0.0291149068,54.1246118 C10.364179,55.975592 20.699243,57.742139 31.0350349,59.6426145 L35.242139,59.6426145 L35.242139,54.1944876 L55.5759899,54.1944876 C56.7325796,54.1246118 58.0092682,54.1944876 58.9977193,53.4935462 C59.7903727,52.2386937 59.5865683,50.6890528 59.6564441,49.2864422 C59.5865683,35.6482919 59.7059394,21.9621021 59.5865683,8.30284356 L59.5865683,8.30284356 Z M22.6877911,39.9128979 L18.7929445,39.9128979 C17.8772807,36.0624515 16.3494759,30.4440023 15.7358793,27.4655474 L15.7104037,27.4655474 C15.0786102,30.5597341 13.5093168,36.4125582 12.5812791,39.9128979 L8.68934394,39.9128979 L4.26315023,22.5247477 L8.27300078,22.5247477 C9.90270769,30.5138781 10.6444099,34.0578901 10.8482143,35.9707395 L10.8736898,35.9707395 C11.5345982,32.7768342 13.2138005,26.724573 14.2277271,22.5247477 L17.6057842,22.5247477 C18.4996118,25.8605881 20.2581522,32.6072399 20.8244371,35.7894992 L20.8499127,35.7894992 C21.4271157,31.960889 23.134705,25.226611 23.7453901,22.5247477 L27.3971273,22.5247477 L22.6877911,39.9128979 Z M57.5951087,52.0414402 L35.2414111,52.0414402 L35.2414111,46.4950505 L52.854474,46.4950505 L52.854474,43.6905571 L35.2414111,43.6905571 L35.2414111,40.2549981 L52.854474,40.2549981 L52.854474,37.4497768 L35.2414111,37.4497768 L35.2414111,34.0142178 L52.854474,34.0142178 L52.854474,31.2097244 L35.2414111,31.2097244 L35.2414111,27.7042896 L52.854474,27.7042896 L52.854474,25.0046099 L35.2414111,25.0046099 L35.2414111,21.4991751 L52.854474,21.4991751 L52.854474,18.7645575 L35.2414111,18.7645575 L35.2414111,15.2940606 L52.854474,15.2940606 L52.854474,12.524505 L35.2414111,12.524505 L35.2414111,7.67177795 L57.5951087,7.67177795 L57.5951087,52.0414402 Z" id="形状"></path>
                              </g>
                            </g>
                          </g>
                        </svg>
                      </span>
                    </span>
                    <span v-if="item.FileType=='.pdf'">
                      <span class="img-C" @click="deleteImg(index)" style="display:flex;justify-content:center;align-items:center;">
                        <svg width="60px" height="60px" viewBox="0 0 60 60" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="vertical-align: top;">
                          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <g id="pdf备份_export" fill-rule="nonzero">
                              <g id="PDF">
                                <path d="M31.3523642,0 L35.4259892,0 L35.4259892,5.58508877 C42.7003196,5.62479317 49.9746501,5.50567991 57.2886587,5.62479323 C58.0132215,5.53855436 58.7372908,5.7895716 59.2532456,6.30586778 C59.7692003,6.82216396 60.0200516,7.54671242 59.9338697,8.27175473 C60.0462912,22.0227197 59.9338697,35.7803022 60,49.5312672 C59.9338697,50.9407743 60.1322605,52.5024815 59.3386973,53.7531709 C58.3401301,54.4810853 57.0572027,54.4149112 55.8933098,54.4149112 L35.4392153,54.4149112 L35.4392153,60 L31.1671994,60 C20.791359,58.0875703 10.3956795,56.3074887 0,54.4413809 L0,5.59832361 C10.4485837,3.72559835 20.9037805,1.90581227 31.3523642,0 Z" id="路径" fill="#FA272D"></path>
                                <polygon id="路径" fill="#FFFFFF" points="35.4259892 7.67618839 57.9102833 7.67618839 57.9102833 52.3502812 35.4259892 52.3502812 35.4259892 46.7651925 53.1422903 46.7651925 53.1422903 43.9726481 35.4259892 43.9726481 35.4259892 40.4852763 53.1422903 40.4852763 53.1422903 37.7192015 35.4259892 37.7192015 35.4259892 34.1987427 53.1422903 34.1987427 53.1422903 31.4061983 35.4259892 31.4061983 35.4259892 27.9188265 53.1422903 27.9188265 53.1422903 25.1461343 35.4259892 25.1461343 35.4259892 21.6389103 53.1422903 21.6389103 53.1422903 18.8463659 35.4259892 18.8463659 35.4259892 15.3523767 53.1422903 15.3523767 53.1422903 50.3584427 35.4259892 50.3584427"></polygon>
                                <path d="M37.8132922,31.0488585 L55.5295933,31.0488585 L55.5295933,33.8414029 L37.8132922,33.8414029 L37.8132922,31.0488585 Z M37.8132922,35.8464762 L55.5295933,35.8464762 L55.5295933,38.6390206 L37.8132922,38.6390206 L37.8132922,35.8464762 Z M37.8132922,40.644094 L55.5295933,40.644094 L55.5295933,43.4366384 L37.8132922,43.4366384 L37.8132922,40.644094 Z M34.9366252,45.4417117 L55.5295933,45.4417117 L55.5295933,48.2342561 L34.9366252,48.2342561 L34.9366252,45.4417117 Z" id="形状" fill="#FA272D"></path>
                                <path d="M10.5808443,18.601522 C14.0526838,18.7669571 18.2519564,17.192015 21.1616886,19.8522113 C23.9127081,23.2800265 23.185275,29.5731775 19.1050369,31.6179553 C17.6567838,32.3789567 15.9903009,32.2796956 14.4164003,32.220139 L14.4164003,39.8963274 L10.5808443,39.5720746 C10.52794,32.5840962 10.514714,25.5895004 10.5808443,18.601522 Z" id="路径" fill="#FFFFFF"></path>
                                <path d="M14.3899482,22.141833 C15.6464234,22.0822764 17.207098,21.8440498 18.0535655,23.0550347 C18.7516998,24.3944049 18.7835088,25.9834583 18.1395349,27.3497298 C17.4121018,28.6732106 15.765458,28.5673321 14.4891436,28.7195324 C14.3568831,26.5291717 14.3701091,24.3388111 14.3899482,22.141833 L14.3899482,22.141833 Z M51.8858151,22.2675637 C50.8441526,22.1940407 49.8528818,21.7907808 49.0554392,21.1161355 C47.4988217,21.4658569 45.9762689,21.9530419 44.5056762,22.5719643 C43.3153312,24.6829161 42.2109556,25.7615529 41.2520666,25.7615529 C41.0494227,25.7692707 40.8490902,25.7162742 40.6767332,25.6093526 C40.2718954,25.4226319 40.0133722,25.0164819 40.0154182,24.5704202 C40.0154182,24.2263152 40.0947867,23.2469394 43.725339,21.6918496 C44.5507447,20.1713087 45.2305078,18.5760211 45.7555384,16.9273188 C45.2926265,16.0074997 44.3006723,13.7443476 44.9884272,12.5929194 C45.224202,12.1690424 45.6900144,11.926868 46.1721592,11.9775008 C46.5632118,11.98199 46.9308638,12.1647194 47.1707264,12.4738061 C47.6667035,13.1355464 47.6270252,14.6244623 46.9789485,16.7751185 C47.5948295,17.9287002 48.4020817,18.9692006 49.3662515,19.8522113 C50.1586131,19.688965 50.9645138,19.6003459 51.7733936,19.5875152 C53.5721372,19.6272196 53.8432713,20.4676299 53.8035931,20.9705526 C53.8035931,22.2940333 52.5405048,22.2940333 51.8924281,22.2940333 L51.8858151,22.2675637 Z M41.0470627,24.7358553 L41.1793233,24.7027683 C41.7430308,24.5388718 42.2362427,24.1919982 42.5812851,23.7167751 C41.9837597,23.9042394 41.4517169,24.2576395 41.0470627,24.7358553 Z M46.5160366,12.996581 L46.3903891,12.996581 C46.3323798,12.9907363 46.274103,13.004732 46.2250634,13.0362854 C46.0834105,13.7068271 46.169523,14.4054989 46.4697454,15.0215065 C46.7095266,14.3704155 46.7258138,13.6579629 46.5160366,12.996581 L46.5160366,12.996581 Z M46.2911937,18.6147568 L46.2911937,18.6875483 L46.2515155,18.6478438 C45.9340902,19.4882541 45.5902128,20.3154295 45.1934311,21.1293702 L45.2595613,21.0896658 L45.2595613,21.1756921 C46.0285502,20.8810658 46.8147504,20.6335582 47.6137992,20.4345429 L47.574121,20.4014559 L47.6799295,20.4014559 C47.1594664,19.8530037 46.6943049,19.2545431 46.2911937,18.6147568 L46.2911937,18.6147568 Z M51.8329109,20.6529172 C51.475133,20.6366131 51.1168236,20.670018 50.7682134,20.7521782 C51.1446608,20.9709775 51.5645839,21.1042671 51.9982366,21.1426051 C52.2727442,21.1863522 52.5539778,21.1543207 52.811639,21.0499614 C52.7851868,20.8977611 52.6198612,20.6529172 51.8064587,20.6529172 L51.8329109,20.6529172 Z" id="形状" fill="#FA272D"></path>
                              </g>
                            </g>
                          </g>
                        </svg>
                      </span>
                    </span>
                    <span v-if="item.FileType=='.xlsx'||item.FileType=='.xls'">
                      <span class="img-C" @click="deleteImg(index)" style="display:flex;justify-content:center;align-items:center;">
                        <svg width="60px" height="59px" viewBox="0 0 60 59" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="vertical-align: top;">
                          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <g id="pdf_export备份-5" fill="#5BA139" fill-rule="nonzero">
                              <g id="文件,excel">
                                <path d="M58.125,4.92947018 L33.75,4.92947018 L33.75,3.75173581 C33.75,1.39040768 31.5878906,-0.384982942 29.2734375,0.0720483081 L3.0234375,5.26931393 C1.265625,5.61501706 0,7.15603268 0,8.94900143 L0,49.6540796 C0,51.4470483 1.27148438,52.9939233 3.03515625,53.3337671 L29.2851562,58.4372827 C31.5996094,58.8884546 33.75,57.1130639 33.75,54.7575952 L33.75,53.6794702 L58.125,53.6794702 C59.1621094,53.6794702 60,52.8415796 60,51.8044702 L60,6.80447018 C60,5.76736081 59.1621094,4.92947018 58.125,4.92947018 Z M7.90429687,38.1228296 L12.3222656,29.7966577 C12.4628906,29.5329858 12.46875,29.2165796 12.3398438,28.9470483 L8.26757812,20.5681421 C8.1328125,20.2868921 8.30273438,19.9529077 8.60742188,19.9001733 L11.2558594,19.4372827 C11.6835938,19.3611108 12.1054688,19.5896264 12.28125,19.9880639 L14.5605469,25.2966577 C14.7773437,25.8122827 14.9648438,26.3806421 15.1230469,27.0134546 L15.1699219,27.0134546 C15.3339844,26.4275171 15.5390625,25.8064233 15.7851562,25.1443139 L18.8378906,18.5115014 C18.9667969,18.2302514 19.2246094,18.0310327 19.5292969,17.9782983 L23.4609375,17.2868921 C23.8476563,17.2165796 24.140625,17.6267358 23.953125,17.9724389 L17.9824219,28.8415796 C17.8300781,29.1228296 17.8300781,29.4685327 17.9824219,29.7497827 L24.1230469,40.8240014 C24.3164062,41.1697046 24.0175781,41.5798608 23.6308594,41.5154077 L19.3710938,40.7654077 C19.0664062,40.7126733 18.8085938,40.5134546 18.6796875,40.2322046 L15.3691406,32.9841577 C15.2519531,32.7204858 15.1289062,32.2517358 15,31.5779077 L14.953125,31.5779077 C14.8886719,31.9060327 14.7480469,32.3689233 14.53125,32.9782983 L11.7597656,38.7439233 C11.578125,39.1247827 11.1679687,39.3357202 10.7519531,39.2595483 L8.23242188,38.8200952 C7.921875,38.7497827 7.75195312,38.4040796 7.90429687,38.1228296 L7.90429687,38.1228296 Z M57.5,50.7107202 C57.5,50.9685327 56.0390625,51.1794702 55.78125,51.1794702 L33.75,51.1794702 L33.75,44.7732202 C33.75,44.5154077 33.9609375,44.3044702 34.21875,44.3044702 L39.84375,44.3044702 C40.1015625,44.3044702 40.3125,44.0935327 40.3125,43.8357202 L40.3125,41.0232202 C40.3125,40.7654077 40.1015625,40.5544702 39.84375,40.5544702 L34.21875,40.5544702 C33.9609375,40.5544702 33.75,40.3435327 33.75,40.0857202 L33.75,38.2107202 C33.75,37.9529077 33.9609375,37.7419702 34.21875,37.7419702 L39.84375,37.7419702 C40.1015625,37.7419702 40.3125,37.5310327 40.3125,37.2732202 L40.3125,34.4607202 C40.3125,34.2029077 40.1015625,33.9919702 39.84375,33.9919702 L34.21875,33.9919702 C33.9609375,33.9919702 33.75,33.7810327 33.75,33.5232202 L33.75,31.6482202 C33.75,31.3904077 33.9609375,31.1794702 34.21875,31.1794702 L39.84375,31.1794702 C40.1015625,31.1794702 40.3125,30.9685327 40.3125,30.7107202 L40.3125,27.8982202 C40.3125,27.6404077 40.1015625,27.4294702 39.84375,27.4294702 L34.21875,27.4294702 C33.9609375,27.4294702 33.75,27.2185327 33.75,26.9607202 L33.75,25.0857202 C33.75,24.8279077 33.9609375,24.6169702 34.21875,24.6169702 L39.84375,24.6169702 C40.1015625,24.6169702 40.3125,24.4060327 40.3125,24.1482202 L40.3125,21.3357202 C40.3125,21.0779077 40.1015625,20.8669702 39.84375,20.8669702 L34.21875,20.8669702 C33.9609375,20.8669702 33.75,20.6560327 33.75,20.3982202 L33.75,18.5232202 C33.75,18.2654077 33.9609375,18.0544702 34.21875,18.0544702 L39.84375,18.0544702 C40.1015625,18.0544702 40.3125,17.8435327 40.3125,17.5857202 L40.3125,14.7732202 C40.3125,14.5154077 40.1015625,14.3044702 39.84375,14.3044702 L34.21875,14.3044702 C33.9609375,14.3044702 33.75,14.0935327 33.75,13.8357202 L33.75,7.42947018 L55.78125,7.42947018 C56.0390625,7.42947018 57.5,7.64040768 57.5,7.89822018 L57.5,50.7107202 Z" id="形状"></path>
                                <path d="M44.53125,14.3044702 C44.2734375,14.3044702 44.0625,14.5154077 44.0625,14.7732202 L44.0625,17.5857202 C44.0625,17.8435327 44.2734375,18.0544702 44.53125,18.0544702 L52.03125,18.0544702 C52.2890625,18.0544702 52.5,17.8435327 52.5,17.5857202 L52.5,14.7732202 C52.5,14.5154077 52.2890625,14.3044702 52.03125,14.3044702 L44.53125,14.3044702 Z M44.53125,20.8669702 C44.2734375,20.8669702 44.0625,21.0779077 44.0625,21.3357202 L44.0625,24.1482202 C44.0625,24.4060327 44.2734375,24.6169702 44.53125,24.6169702 L52.03125,24.6169702 C52.2890625,24.6169702 52.5,24.4060327 52.5,24.1482202 L52.5,21.3357202 C52.5,21.0779077 52.2890625,20.8669702 52.03125,20.8669702 L44.53125,20.8669702 Z M44.53125,27.4294702 C44.2734375,27.4294702 44.0625,27.6404077 44.0625,27.8982202 L44.0625,30.7107202 C44.0625,30.9685327 44.2734375,31.1794702 44.53125,31.1794702 L52.03125,31.1794702 C52.2890625,31.1794702 52.5,30.9685327 52.5,30.7107202 L52.5,27.8982202 C52.5,27.6404077 52.2890625,27.4294702 52.03125,27.4294702 L44.53125,27.4294702 Z M44.53125,33.9919702 C44.2734375,33.9919702 44.0625,34.2029077 44.0625,34.4607202 L44.0625,37.2732202 C44.0625,37.5310327 44.2734375,37.7419702 44.53125,37.7419702 L52.03125,37.7419702 C52.2890625,37.7419702 52.5,37.5310327 52.5,37.2732202 L52.5,34.4607202 C52.5,34.2029077 52.2890625,33.9919702 52.03125,33.9919702 L44.53125,33.9919702 Z M44.53125,40.5544702 C44.2734375,40.5544702 44.0625,40.7654077 44.0625,41.0232202 L44.0625,43.8357202 C44.0625,44.0935327 44.2734375,44.3044702 44.53125,44.3044702 L52.03125,44.3044702 C52.2890625,44.3044702 52.5,44.0935327 52.5,43.8357202 L52.5,41.0232202 C52.5,40.7654077 52.2890625,40.5544702 52.03125,40.5544702 L44.53125,40.5544702 Z" id="形状"></path>
                              </g>
                            </g>
                          </g>
                        </svg>
                      </span>
                    </span>
                  </span>
                </li>
                <li class="imgList-li-add" @click="F_Open_dialog">
                  <i class="addImg">
                    <svg width="22px" height="22px" viewBox="0 0 22 22" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linejoin="round">
                        <g id="画板备份" transform="translate(-1932.000000, -853.000000)" stroke="#D0D0D0">
                          <g id="编组-4" transform="translate(1763.000000, 522.000000)">
                            <g id="编组" transform="translate(170.000000, 332.000000)">
                              <path d="M10,20 C15.52285,20 20,15.52285 20,10 C20,4.47715 15.52285,0 10,0 C4.47715,0 0,4.47715 0,10 C0,15.52285 4.47715,20 10,20 Z" id="路径"></path>
                              <line x1="10" y1="6" x2="10" y2="14" id="路径" stroke-linecap="round"></line>
                              <line x1="6" y1="10" x2="14" y2="10" id="路径" stroke-linecap="round"></line>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </i>
                </li>
              </ul>
            </div>
            <input type="file" id="btn_file" style="display:none">
            <div style="margin-top:10px;">
              <span class="file-font" @click="F_Open_dialog">
                <i class="fileIcon">
                  <svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                      <g id="画板备份" transform="translate(-1219.000000, -412.000000)" fill-rule="nonzero">
                        <g id="编组-3" transform="translate(1199.000000, 117.000000)">
                          <g id="图片添加备份-3" transform="translate(20.000000, 295.000000)">
                            <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="14" height="14"></rect>
                            <path d="M11.8325,5.7411875 C10.5361875,5.7411875 9.8510625,6.980625 9.3006875,7.9768125 C8.947625,8.615125 8.58275,9.274875 8.2244375,9.31075 C7.8341875,9.2333125 7.566875,8.9935625 7.2584375,8.7179375 C6.8244375,8.329 6.33575,7.8936875 5.47475,7.925625 C4.42475,7.971125 3.475375,8.7144375 2.652875,10.1363125 C2.53182109,10.3454386 2.60321768,10.6131023 2.81234375,10.7341562 C3.02146982,10.8552102 3.28913359,10.7838136 3.4101875,10.5746875 C4.073,9.4284375 4.7804375,8.8316875 5.5119375,8.79975 C6.0168125,8.7800625 6.2906875,9.026375 6.6739375,9.369375 C7.0414375,9.6988125 7.4579375,10.0715625 8.121625,10.181375 C8.14518537,10.1853866 8.16903813,10.1874353 8.1929375,10.1875 C9.0775625,10.1875 9.5798125,9.2788125 10.0654375,8.4003125 C10.4530625,7.6985625 10.851625,6.9784375 11.394125,6.7176875 L11.394125,11.7760625 L1.875,11.7760625 L1.875,3.05625 L8.5490625,3.05625 C8.79068708,3.05625 8.9865625,2.86037458 8.9865625,2.61875 C8.9865625,2.37712542 8.79068708,2.18125 8.5490625,2.18125 L1.4375,2.18125 C1.19587542,2.18125 1,2.37712542 1,2.61875 L1,12.214 C1,12.4556246 1.19587542,12.6515 1.4375,12.6515 L11.8325,12.6515 C12.0741246,12.6515 12.27,12.4556246 12.27,12.214 L12.27,6.1786875 C12.27,5.93706292 12.0741246,5.7411875 11.8325,5.7411875 Z M2.5990625,5.69 C2.5990625,6.4823125 3.2439375,7.1271875 4.03625,7.1271875 C4.8285625,7.1271875 5.4734375,6.4823125 5.4734375,5.69 C5.4734375,4.8976875 4.8285625,4.25325 4.03625,4.25325 C3.2439375,4.25325 2.5990625,4.8976875 2.5990625,5.69 L2.5990625,5.69 Z M4.5984375,5.69 C4.5865262,5.99186384 4.33834875,6.23044155 4.03625,6.23044155 C3.73415125,6.23044155 3.4859738,5.99186384 3.4740625,5.69 C3.4859738,5.38813616 3.73415125,5.14955845 4.03625,5.14955845 C4.33834875,5.14955845 4.5865262,5.38813616 4.5984375,5.69 Z M13.25,2.276625 L12.410875,2.276625 L12.410875,1.4375 C12.410875,1.19587542 12.2149996,1 11.973375,1 C11.7317504,1 11.535875,1.19587542 11.535875,1.4375 L11.535875,2.2761875 L10.415,2.2761875 C10.1733754,2.2761875 9.9775,2.47206292 9.9775,2.7136875 C9.9775,2.95531208 10.1733754,3.1511875 10.415,3.1511875 L11.535875,3.1511875 L11.535875,4.2720625 C11.535875,4.51368708 11.7317504,4.7095625 11.973375,4.7095625 C12.2149996,4.7095625 12.410875,4.51368708 12.410875,4.2720625 L12.410875,3.151625 L13.25,3.151625 C13.4916246,3.151625 13.6875,2.95574958 13.6875,2.714125 C13.6875,2.47250042 13.4916246,2.276625 13.25,2.276625 Z" id="形状" fill="#2A7FCC"></path>
                          </g>
                        </g>
                      </g>
                    </g>
                  </svg>
                </i>
                图片

              </span>
              <span class="file-font" @click="F_Open_dialog">
                <i class="fileIcon">
                  <svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                      <g id="画板备份" transform="translate(-1285.000000, -412.000000)" fill-rule="nonzero">
                        <g id="编组-3" transform="translate(1199.000000, 117.000000)">
                          <g id="附件备份-6" transform="translate(86.000000, 295.000000)">
                            <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="14" height="14"></rect>
                            <path d="M11.4705686,5.89424257 L6.12278891,11.24124 C5.36759524,11.9960313 4.13902527,11.9964113 3.38384278,11.24124 C2.62866028,10.485644 2.62866028,9.25668282 3.38384278,8.50188031 L4.76568223,7.11963852 L8.73082899,3.15490528 C9.1818642,2.70467475 9.91391209,2.70426124 10.3641426,3.15450294 C10.8143731,3.60474464 10.8143731,4.33796603 10.3641426,4.78819655 L10.088718,5.06401235 L5.84659313,9.30577957 C5.7013034,9.4510693 5.46499524,9.45147164 5.318912,9.30619309 C5.17363345,9.16010985 5.17363345,8.92341053 5.318912,8.7781208 L8.73082899,5.36576794 L8.15286644,4.78780539 L4.74093828,8.20014707 C4.27712875,8.66356543 4.27712875,9.41874793 4.74093828,9.88376448 C4.96526562,10.108483 5.26461835,10.2326051 5.58313814,10.2326051 C5.90086443,10.2326051 6.20021716,10.108483 6.4245445,9.88376448 L6.97695841,9.33135057 L10.9420828,5.36621499 C11.7116378,4.59705115 11.7116378,3.34572658 10.9420828,2.57656274 C10.174126,1.80781242 8.92239909,1.80781242 8.15285526,2.57656274 L7.59963667,3.12939017 L2.80588023,7.92392894 C1.73137326,8.99724006 1.73137326,10.7446955 2.80588023,11.8191913 C3.34233462,12.3560592 4.04801802,12.6246776 4.75290791,12.6246776 C5.45860249,12.6246776 6.16349239,12.3560369 6.70074028,11.8191913 L12.04852,6.47220512 L11.4705686,5.89424257 Z" id="路径" fill="#2A7FCC"></path>
                          </g>
                        </g>
                      </g>
                    </g>
                  </svg>
                </i>
                附件
              </span>
            </div>

            <div style="margin-top:17px;">
              <el-tag :key="tag"
                      v-for="tag in TagsVal.dynamicTags"
                      closable
                      :disable-transitions="false"
                      class="newCardTags"
                      @close="handleClose(tag)">
                <i class="fileIcon">
                  <svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                      <g id="画板备份" transform="translate(-1219.000000, -449.000000)">
                        <g id="编组-3" transform="translate(1199.000000, 117.000000)">
                          <g id="编组备份-16" transform="translate(20.000000, 332.000000)">
                            <rect id="矩形" x="0" y="0" width="14" height="14"></rect>
                            <g id="编组-9" transform="translate(2.000000, 2.000000)">
                              <path d="M9.70273413,6.03280616 L6.03791748,9.69762281 C5.84618617,9.88958128 5.58599157,9.99743014 5.31466566,9.99743014 C5.04333974,9.99743014 4.78317353,9.88958128 4.59141383,9.69762281 L0,5.1113203 L0,0 L5.1113203,0 L9.70273413,4.59141383 C10.0990886,4.99012521 10.0990886,5.63409478 9.70273413,6.03280616 Z" id="路径" stroke="#2A7FCC" stroke-linejoin="round"></path>
                              <path d="M2.98160351,3.6915091 C3.37367017,3.6915091 3.6915091,3.37367017 3.6915091,2.98160351 C3.6915091,2.58953684 3.37367017,2.27169791 2.98160351,2.27169791 C2.58953684,2.27169791 2.27169791,2.58953684 2.27169791,2.98160351 C2.27169791,3.37367017 2.58953684,3.6915091 2.98160351,3.6915091 Z" id="路径" fill="#2A7FCC"></path>
                            </g>
                          </g>
                        </g>
                      </g>
                    </g>
                  </svg>
                </i>
                {{tag}}
              </el-tag>
              <el-input class="input-newCard"
                        v-if="inputVisible"
                        v-model="inputValue"
                        ref="saveTagInput"
                        size="small"
                        @keyup.enter.native="newCardHandleInput"
                        @blur="newCardHandleInput">
              </el-input>

              <span class="file-font" @click="newCardShowInput" style="margin: 5px 0; display: inline-block;" v-else>
                <i class="fileIcon">
                  <svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                      <g id="画板备份" transform="translate(-1219.000000, -449.000000)">
                        <g id="编组-3" transform="translate(1199.000000, 117.000000)">
                          <g id="编组备份-16" transform="translate(20.000000, 332.000000)">
                            <rect id="矩形" x="0" y="0" width="14" height="14"></rect>
                            <g id="编组-9" transform="translate(2.000000, 2.000000)">
                              <path d="M9.70273413,6.03280616 L6.03791748,9.69762281 C5.84618617,9.88958128 5.58599157,9.99743014 5.31466566,9.99743014 C5.04333974,9.99743014 4.78317353,9.88958128 4.59141383,9.69762281 L0,5.1113203 L0,0 L5.1113203,0 L9.70273413,4.59141383 C10.0990886,4.99012521 10.0990886,5.63409478 9.70273413,6.03280616 Z" id="路径" stroke="#2A7FCC" stroke-linejoin="round"></path>
                              <path d="M2.98160351,3.6915091 C3.37367017,3.6915091 3.6915091,3.37367017 3.6915091,2.98160351 C3.6915091,2.58953684 3.37367017,2.27169791 2.98160351,2.27169791 C2.58953684,2.27169791 2.27169791,2.58953684 2.27169791,2.98160351 C2.27169791,3.37367017 2.58953684,3.6915091 2.98160351,3.6915091 Z" id="路径" fill="#2A7FCC"></path>
                            </g>
                          </g>
                        </g>
                      </g>
                    </g>
                  </svg>
                </i>
                添加标签
              </span>
              <span style="display:block;margin-top:5px;">（至少添加一个，帮你更快匹配到资源）</span>
            </div>

          </div>
          <div style="padding:30px 0;text-align:end;">
            <el-button class="publisCubmit" @click="submitFabu">发布</el-button>
          </div>

        </div>
      </div>

    </el-dialog>
    <!---------------------------发布线索结束----------------------------->
    
    <el-dialog title="提示"
               :visible.sync="JobScreenDialog"
               width="530px"
               class="clueDialog"
               :close-on-click-modal="false">
      <div class="publishClue">
        <div class="publisHead">
          <span class="publisHead-font">设置筛选条件</span>
          <span class="publisHead-close" @click="closeJobScreen">×</span>
        </div>

        <div class="JobScreen-cont">

          <div class="JobScreen-div">
            <span class="JobScreen-title">职位名称</span>
            <div class="JobScreen-right">
              <el-input v-model="kewords" class="JobScreen-100" placeholder="请输入职位名称"></el-input>
            </div>
          </div>
          <div class="JobScreen-div">
            <span class="JobScreen-title">职位行业</span>
            <div class="JobScreen-right">
              <div class="subscription-public-input">
                <div class="project-input-occupation" v-on:click="editAbilityed('industry')">
                  <span v-if="GoodIndustrysed.length" style="width: 100%; ">
                    <span v-for="(item, index) in GoodIndustrysed" :key="index">
                      <span class="subscription-industry">
                        {{item.Text}}
                        <i v-on:click.stop="subscriptionIndustryDeleteClick(index)" class="subscription-industry-delete"></i>
                      </span>
                    </span>
                  </span>

                  <span class="project-select-span" v-else>请选择行业类别</span>
                </div>
              </div>
            </div>
          </div>
          <div class="JobScreen-div">
            <span class="JobScreen-title">职位地点</span>
            <div class="JobScreen-right">

              <div class="subscription-public-input">
                <div class="project-input" v-on:click="editAbilityed('location')">
                  
                  <span v-if="GoodLocationsed.length" style="width: 100%; display: flex; ">
                    <span v-for="(item, index) in GoodLocationsed" :key="index">
                      <span class="subscription-industry">
                        {{item.Text}}
                        <i v-on:click.stop="subscriptionGoodLocationsDeleteClick(index)" class="subscription-industry-delete"></i>
                      </span>
                    </span>
                  </span>

                  <span class="project-select-span" v-else>请选择工作地点</span>
                </div>
              </div>
            </div>
          </div>

          <div class="JobScreen-div">
            <span class="JobScreen-title" style="top: -24px;">薪资筛选</span>
            <div class="JobScreen-right">
              <el-radio-group v-model="JobScreenSaralyVal" @change="JobScreenSaralyChange">
                <el-radio v-for="(item,index) in JobScreenSaraly" :key="index" :label="item.id">
                  {{item.text}}
                  <span class="JobScreenSaralyInput" v-show="JobScreenSaralyInput&&index==5">

                    <el-input v-model="JobScreenSaralyFrom" style="width:70px;height:28px;">
                      <template slot="suffix">
                        万
                      </template>
                    </el-input>
                    至
                    <el-input v-model="JobScreenSaralyTo" style="width:70px;height:28px;">
                      <template slot="suffix">
                        万
                      </template>
                    </el-input>
                  </span>
                </el-radio>
              </el-radio-group>
            </div>
          </div>

          <div class="JobScreen-div">
            <span class="JobScreen-title">标签筛选</span>
            <div class="JobScreen-right">
              <label class="filter-tag checkbox-inline">
                <input class="icheck" type="checkbox" v-model="checkeda">
                <span class="JobScreenMultipleChoice">保用期短</span>
              </label>
              <label class="filter-tag checkbox-inline">
                <input class="icheck" type="checkbox" v-model="checkedb">
                <span class="JobScreenMultipleChoice">提点≥10%</span>
              </label>
              <label class="filter-tag checkbox-inline">
                <input class="icheck" type="checkbox" v-model="checkedc">
                <span class="JobScreenMultipleChoice">有成单</span>
              </label>
              <label class="filter-tag checkbox-inline">
                <input class="icheck" type="checkbox" v-model="checkedd">
                <span class="JobScreenMultipleChoice">有回款</span>
              </label>
              <label class="filter-tag checkbox-inline">
                <input class="icheck" type="checkbox" v-model="checkede">
                <span class="JobScreenMultipleChoice">反馈快</span>
              </label>
            </div>
          </div>


        </div>
        <div class="JobScreen-bottom">
          <span class="JobScreen-bottom-left-btn" @click="JobScreenClearClick">清除</span>
          <span class="JobScreen-bottom-right-btn" @click="JobScreenSubmitClick">确认</span>
        </div>

      </div>
    </el-dialog>
  </div>
</div>
<!-- indexBase.js是当前页面的js -->
          </div>
        </div>

        <a href="javascript:;" class="page-quick-sidebar-toggler">
          <i class="icon-login"></i>
        </a>
        <div class="page-quick-sidebar-wrapper" data-close-on-body-click="false">
          <div class="page-quick-sidebar">
            <ul class="nav nav-tabs">
              <li class="active">
                <a href="javascript:;" data-target="#quick_sidebar_tab_1" data-toggle="tab">
                  内部通讯录<span class="index-cgl-staffcount" id="index-cgl-staffcount"></span>
                </a>
              </li>
            </ul>
            <div class="tab-content">
              <div class="tab-pane active page-quick-sidebar-chat" id="quick_sidebar_tab_1">
                <div class="page-quick-sidebar-chat-users" id="lazyLoadContainer8" data-rail-color="#ddd" data-wrapper-class="page-quick-sidebar-list">
                  <div class="input-group index-searchgroup-staff">
                    <input type="text" class="blue-dark form-control" placeholder="输入中文名或英文名">
                    <span class="input-group-btn">
                      <button class="btn" type="button"><i class="icon-magnifier"></i></button>
                    </span>
                  </div>
                  <div class="index-searchgroup-list" style="display:none;">
                    <h3 class="list-heading">搜索结果</h3>
                    <ul class="media-list list-items index-sg-items"></ul>
                  </div>
                  <h3 class="list-heading">南京公司同事</h3>
                  <ul class="media-list list-items index-colleaguelist" data-companyid="8"></ul>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>

      


<div class="footer-box page-footer" style="height:50px;display:table;text-align:center;font-size:14px;color:#999;width:100%;">
    <div style="display:table-cell;vertical-align:middle">
        ©2008-<span id="footer_box_yearHtml">2021</span>RISFOND
    </div>
</div>
<script>

    let year = document.getElementById('footer_box_yearHtml');
    let yearDate = new Date().getFullYear();
    year.innerHTML = yearDate
</script>


      <div class="scroll-to-top">
        <i class="icon-arrow-up"></i>
      </div>

    </div>



  </div>

  <!-- 员工评价模态框 新增 2020-03-06 赵刚 start-->
  <div id="app_2">
    <div class="modal fade" id="Staff_comments" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false">
      <div class="modal-dialog" style="width:710px;">
        <div class="modal-content" style="width:710px;">
          <div class="modal-header">
            <button type="button" class="close" onclick="app_2.close_fn_app_2()" data-dismiss="modal" aria-hidden="true"></button>
            <h4 class="modal-title">员工点评</h4>
            <input type="hidden" class="staffid" value="" />
          </div>
          <div class="modal-body" style="margin-top:12px;">
            <div v-for="(item,index) in rateObj">
              <div>
                <span class="margin_right_12">{{item.Name}}</span>
                <input v-bind:id="'input_'+index" />
                <span class="margin_left_5">({{item.Description}})</span>
              </div>
            </div>
            <div>
              <span class="margin_right_12" style="margin-right:14px;margin-top:10px;">文字评价</span>
              <textarea oninput="app_2.onTextChange()" onBlur="app_2.onBlurFn()" id="textareaValue" placeholder="请输入200个字以内文字评价"></textarea>
              <span class="textarea_num">{{DescriptionLength}}/200</span>
            </div>
            <div id="error_tips" class="error_text"></div>
          </div>
          <div class="modal-footer">
            <span class="but_color_ooo">
              <input name="checkbox_name" id="checkbox_but" type="checkbox" value="" />
              <span>匿名评价</span>
            </span>
            <a href="#" class="btn btn-success btn_custom_color" onclick="app_2.subValue()">
              发表评价
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div id="examsTraining">
    <div class="modal fade bs-modal-sm r-reference new_modal_show hm_center" id="examsDialog" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
      <div class="modal-dialog" style="width:500px;">
        <div class="modal-content examsTrain-content">
          <div class="examsTrain-head" v-if="examsObj.type==2">
            <img src="../images/exams-TZ.png" alt="Alternate Text" width="100%" height="140px" />
          </div>
          <div class="examsTrain-head" v-if="examsObj.type==1">
            <img src="../images/train-TZ.png" alt="Alternate Text" width="100%" height="140px" />
          </div>
          <div class="examsTrain-body">
            <div class="examsTrain-Title">
              为了使新员工能更好的了解公司，融入公司，以及更加清楚公司的规章制度，企业概况和企业文化，增强新员工的工作信心和工作意识，使其尽快地投入到工作中。公司特制定新进员工的培训计划，希望你积极参与培训
            </div>

            <div class="examsTrain-jieshao" v-if="examsObj.type==1" v-show="examsObj.name">
              <div class="exams-jieshao">培训主题</div>
              <div class="exams-cont">{{examsObj.name}}</div>
            </div>
            <div class="examsTrain-jieshao" v-if="examsObj.type==1" v-show="examsObj.introduce">
              <div class="exams-jieshao">培训介绍</div>
              <div class="exams-cont">{{examsObj.introduce}}</div>
            </div>
            <div class="examsTrain-jieshao" v-if="examsObj.type==1" v-show="examsObj.target">
              <div class="exams-jieshao">培训目标</div>
              <div class="exams-cont">{{examsObj.target}}</div>
            </div>

            <div class="examsTrain-jieshao" v-if="examsObj.type==2" v-show="examsObj.name">
              <div class="exams-jieshao">考试名称</div>
              <div class="exams-cont">{{examsObj.name}}</div>
            </div>
            <div class="examsTrain-jieshao" v-if="examsObj.type==2" v-show="examsObj.target">
              <div class="exams-jieshao">考试介绍</div>
              <div class="exams-cont">{{examsObj.target}}</div>
            </div>
            <div class="examsTrain-xuzhi" v-if="examsObj.type==2" v-show="examsObj.notice">
              <div class="exams-jieshao">考试须知</div>
              <div class="exams-cont">{{examsObj.notice}}</div>
            </div>

            <div class="examsTrain-footer">
              <span class="examsTrain-btn-left" @click="nextOkClick()">下次参加</span>
              <span class="examsTrain-btn-right" @click="openExamsClick" v-if="examsObj.type==2">进入考试</span>
              <span class="examsTrain-btn-right" @click="openTrainClick" v-if="examsObj.type==1">进入培训</span>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>

  <!-- 员工评价模态框 新增 2020-03-06 赵刚 end-->
  <!-- 日程总结model位置 2020-09-21 start-->
  <div id="bottomMessageTip" v-cloak>
    <!-- 智能小达model -->
    <div class="modal fade bs-modal-sm r-reference new_modal_show hm_center" id="ZhiNengXiaoDa" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
      <div class="modal-dialog" style="width:940px;">
        <div class="modal-content">
          <div class="ZhiNengXiaoDa_modal-body">
            <div class="ZhiNengXiaoDa_modal-header">
              <div class="ZhiNengXiaoDa_hea_body">
                <span class="ZhiNengXiaoDa_hea_img"></span>
                <div class="zhiNengXiaoDa_hea_tips">
                  {{xiaodaJobType | xiaodaJobTypeFilters}}
                  <span>
                    <svg data-dismiss="modal" aria-hidden="true" width="12px" height="12px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <title>Mask</title>
                      <defs>
                        <path d="M891.696214,103.000466 L896.443303,98.253787 C896.730215,97.9666744 896.730215,97.502447 896.443303,97.2171988 C896.156391,96.9300862 895.692488,96.9300862 895.407439,97.2171988 L890.658487,101.962014 L885.909535,97.2153344 C885.622623,96.9282219 885.15872,96.9282219 884.873671,97.2153344 C884.586759,97.502447 884.586759,97.9666744 884.873671,98.2519226 L889.62076,102.998602 L884.873671,107.747145 C884.586759,108.034258 884.586759,108.498485 884.873671,108.783733 C885.017127,108.92729 885.205296,108.998136 885.391603,108.998136 C885.579772,108.998136 885.766079,108.92729 885.909535,108.783733 L890.658487,104.037054 L895.407439,108.785598 C895.550895,108.929154 895.737201,109 895.925371,109 C896.11354,109 896.299847,108.929154 896.443303,108.785598 C896.730215,108.498485 896.730215,108.034258 896.443303,107.74901 L891.696214,103.000466 Z" id="path-1"></path>
                      </defs>
                      <g id="海斗6.2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g id="首页" transform="translate(-913.000000, -2094.000000)">
                          <g id="头部" transform="translate(28.341513, 1997.000000)">
                            <mask id="mask-2" fill="white">
                              <use xlink:href="#path-1"></use>
                            </mask>
                            <use id="Mask" fill="#FFFFFF" fill-rule="nonzero" xlink:href="#path-1"></use>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </span>
                </div>
              </div>
            </div>

            <div class="ZhiNengXiaoDa_title">
              <div class="ZhiNengXiaoDa_img"></div>
              <div v-on:click="openJiaoFu" class="ZhiNengXiaoDa_link">
                {{xiaodaJobType | xiaodaJobTypelinkFilters}}
              </div>
            </div>
            <div class="ZhiNengXiaoDa_job_body">
              <div class="hd_position_list_wrap">
                <!-- 职位-->
                <div class="hd_position_item" v-for="(item, index) in jobsObjList" :key="index">
                  <!--职位类型-->
                  <div class="tag_new_box">
                    <div class="tag_new hdjx_tag" v-if="item.positionTags == 1">
                      海斗精选
                    </div>
                    <div class="tag_new jsjf_tag" v-else-if="item.positionTags == 2">
                      高分成
                    </div>
                    <div class="tag_new yzzw_tag" v-else-if="item.positionTags == 3">
                      一次性付款
                    </div>
                    <div class="tag_new jsfk_tag" v-else-if="item.positionTags == 4">
                      极速反馈
                    </div>
                    <div class="tag_new ptzw_tag" v-else>普通职位</div>
                  </div>
                  <div class="hd_position_one">
                    <div class="hd_one_left">
                      <div class="hd_left_one">
                        <span class="hd_left_name">{{ item.jobTitle }}</span>
                        <span class="hd_left_city" :title="item.locationList1">{{ item.locationList1 }}</span>
                        <span class="hd_left_time">{{ item.createdTimeStr }}</span>
                      </div>
                      <div class="hd_left_tow">
                        <span class="hd_left_salary" v-if="item.salaryFrom || item.salaryTo">{{ item.salaryFrom }}-{{ item.salaryTo }}W</span>
                        <span class="hd_left_txt" v-if="item.eduLevel">{{ setEduLevel(item.eduLevel)}}</span>
                        <span class="hd_left_txt" v-if="item.experienceFrom && item.experienceTo"><i class="content1_tow_line"></i>{{ item.experienceFrom }}-{{ item.experienceTo }}年</span>
                        <span class="hd_left_txt" v-if="(item.experienceFrom == 0 && item.experienceTo == 0) || (item.experienceFrom == '' && item.experienceTo == '')"><i class="content1_tow_line"></i>应届</span>
                        <span class="hd_left_txt" v-show="item.numberRecruit"><i class="content1_tow_line"></i>招聘{{ item.numberRecruit }}人</span>
                      </div>
                    </div>
                    <div class="hd_one_center">
                      <p class="center_company_name">{{ item.clientDisplayName }}</p>
                      <p class="center_industry_box"><span class="box_industry" :title="item.industries">{{ item.industries }}</span></p>
                      <!--职位内容区 -中 - 再招职位数-->
                      <p class="box_content2_position">
                        <span class="position_didian_shuzi1">
                          <span class="position_didian_shuzi" v-if="item.clientPositionNum">{{ item.clientPositionNum }}</span>个在招职位
                        </span>
                      </p>
                    </div>
                    <div class="hd_one_right">
                      <img v-show="item.brilliantAdvisorFlag" class="right_wg" src="../../images/opt/hg.png" alt="皇冠">
                      <img v-if="item.dispatcherHeadPhoto" class="right_header_pic" :src="`${item.dispatcherHeadPhoto}?x-oss-process=image/resize,m_fixed,h_150,w_150`" alt="顾问头像">
                      <img v-else class="right_header_pic" src="../../images/opt/<EMAIL>" alt="顾问头像">
                      <span v-show="item.brilliantAdvisorFlag" class="right_txt">优质顾问</span>
                    </div>
                  </div>
                  <div class="hd_position_tow">
                    <div class="box_yj_left">
                      <!-- 定额 -->
                      <div class="yongjin" v-show="item.commissionType == 0">
                        <p>
                          <span class="yongjin_data">定额分成：<span class="yj_color">{{ setAmount(item.commissionAmountFrom) }}k</span></span>
                          <span class="yj_ml yongjin_baoyongqi"
                                v-show="item.warrantyPeriod > 0">保用期：{{ item.warrantyPeriod }}个月</span>
                          <span class="yj_ml yongjin_baoyongqi"
                                v-show="item.warrantyPeriod == 0">保用期：无</span>
                          <span class="yj_ml chenruo">承诺：{{ item.feedbackCycle }}小时内反馈推荐简历</span>
                        </p>
                      </div>
                      <!-- 比列 -->
                      <div class="yongjin" v-show="item.commissionType == 1">
                        <p>
                          <span class="yongjin_data">
                            预估分成：<span class="yj_color">
                              {{ setAmount(item.commissionAmountFrom) }}k-{{
                    setAmount(item.commissionAmountTo)
                              }}k
                              <template v-if="item.commissionRealRatio">
                                ({{ item.commissionRealRatio }}%)
                              </template>
                            </span>
                          </span>
                          <span class="yj_ml yongjin_baoyongqi"
                                v-show="item.warrantyPeriod > 0">保用期：{{ item.warrantyPeriod }}个月</span>
                          <span class="yj_ml yongjin_baoyongqi"
                                v-show="item.warrantyPeriod == 0">保用期：无</span>
                          <span class="yj_ml chenruo">承诺：{{ item.feedbackCycle }}小时内反馈推荐简历</span>
                        </p>
                      </div>
                    </div>
                    <div class="box_jiedan_right">
                      <p class="orders_immediately_back_p">
                        <a class="orders_immediately_back orders_immediately_collection" v-on:click="goToPositionDetails(item)">去看看 >></a>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="jobsObjList.length==0" class="ZhiNengXiaoDa_kong_table">
                <img class="ZhiNengXiaoDa_kong_icon" src="/images/opt/icon_kong.png" />
                <span class="ZhiNengXiaoDa_kong_tips">暂无可合作的同类职位~</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 今日日程提醒model -->
      <div id="scheduleReminderDom"
           v-if="showAlertList.length > 0 || deliverList.length > 0  || workOrderList.length > 0"
           :class="[!showAlertModel?'scheduleReminderDom_bottom':'scheduleReminderDom_bottom_new']">
        <div class="bg-cover" id="" style="display: none;"></div>
        <div class="scheduleReminder__body">
          <div class="scheduleReminder__title scheduleReminder__title_new">
            <div class="schetitle_new_body">
              <span v-on:click="remindTabChange(2)"
                    :class="[remindTab==2?'schetitle_new_addText':'']"
                    class="schetitle_new_text">
                交付提醒
                <span class="scheduleReminder__tips_num">
                  {{deliverList.length > 99 ? '99+' :deliverList.length}}
                </span>
              </span>
              <span v-on:click="remindTabChange(1)"
                    :class="[remindTab==1?'schetitle_new_addText':'']"
                    class="schetitle_new_text">
                日程提醒
                <span class="scheduleReminder__tips_num">
                  {{showAlertList.length > 99 ?'99+':showAlertList.length}}
                </span>
              </span>
              <span v-on:click="remindTabChange(3)"
                    :class="[remindTab==3?'schetitle_new_addText':'']"
                    class="schetitle_new_text">
                工单提醒
                <span class="scheduleReminder__tips_num">
                  {{workOrderList.length > 99 ?'99+':workOrderList.length}}
                </span>
              </span>
            </div>
            <div v-on:click="openAlert(showAlertModel)">
              <img class="scheduleReminder__icon" src="/Content/images/close_btn.png" />
            </div>
          </div>
          <div v-show="(remindTab==1 && showAlertList.length > 3) || ( remindTab==2 && deliverList.length > 3 )" class="scheduleReminder__title scheduleReminder__newColor">
            <div class="scheduleReminder__title_left">
              <div>
                仅显示三条{{remindTab == 1?'"日程提醒"':'"交付提醒"'}}，更多{{remindTab == 1?'日程':'交付'}}信息，请到
              </div>
            </div>
            <div class="scheduleReminder__title_right">
              <div v-show="remindTab==1" class="scheduleReminder__href sche_rc_link">
                <a class="" target="_blank" href="/profile/taskmanage.aspx">日程提醒></a>
              </div>
              <div v-show="remindTab==2" class="scheduleReminder__href sche_rc_link">
                <a class="" target="_blank" href="/deliverycenter/index">交付中心></a>
              </div>
            </div>
          </div>
          <div class="scheduleReminder__midden">
            <!-- 原我的日程 -->
            <div v-on:click="messageOpeningCorrespondingDetails(item.id)" v-if="index < 3 && remindTab == 1" v-for="(item,index) in showAlertList" v-show="processedIndex[index]">
              <div class="scheduleReminder__midden_row" :class="[countDown[item.id] =='00:00'?'_overtime':'']">
                <div class="scheduleReminder_first_line scheduleReminder_margin_bottom_5">
                  <div class="scheduleReminder_first_title">【{{item.typeName}}】</div>
                  <img class="scheduleReminder__icon" src="/js/scheduleReminder/icon_time_blue.png" />
                  <div class="scheduleReminder_first_title">{{item.taskTime | filtersTime}}</div>
                  <div class="scheduleReminder_first_text">
                    剩余：{{item.taskTime | filtersTaskTime(index,item.id)}}
                  </div>
                </div>
                <div :title="item.taskContent" class="scheduleReminder_second_line scheduleReminder_margin_bottom_5">{{item.taskContent}}</div>
                <div class="scheduleReminder_third_line">
                  <div v-on:click="markProcessed(index,item.id)" class="scheduleReminder_third_buttom scheduleReminder_buttom_color_while">标记已处理</div>
                </div>
              </div>
              <div class="scheduleReminder__row_line" v-if="showAlertList.length !=1 && index!=2  && index !=showAlertList.length -1"></div>
            </div>
            <!-- 交付中心的日程 -->
            <div v-if="index < 3 && remindTab == 2" v-for="(item,index) in deliverList">
              <div class="scheduleReminder__midden_row">
                <div class="scheduleReminder_first_line scheduleReminder_margin_bottom_5">
                  <div class="scheduleReminder_first_title">【{{item.typeName}}】</div>
                </div>
                <div :title="item.taskContent | taskContentFilters" class="scheduleReminder_margin_bottom_5 deliverList__margin-5" v-html="item.taskContent">

                </div>
                <div class="scheduleReminder_third_line">
                  <div v-on:click="remindButChange(item.taskType,item.jobId,item.jobCandidateId,index,true)" class="scheduleReminder_third_buttom scheduleReminder_buttom_color_while">
                    {{item.taskType|taskTypeFilters}}
                  </div>

                  <div v-show="item.taskType==10" v-on:click="remindButChange(item.taskType,item.jobId,item.jobCandidateId,index,false)" class="deliverList__margin-10  scheduleReminder_third_buttom scheduleReminder_buttom_color_while">
                    不再提醒
                  </div>

                </div>
              </div>
              <div class="scheduleReminder__row_line" v-if="deliverList.length !=1 && index!=2  && index !=deliverList.length -1"></div>
            </div>
            <!-- 工单提醒 -->
            <div v-if="index < 3 && remindTab == 3" v-for="(item,index) in workOrderList">
              <div class="scheduleReminder__midden_row">
                <div class="scheduleReminder_first_line scheduleReminder_margin_bottom_5">
                  <div class="scheduleReminder_first_title">【{{item.typeName}}】</div>
                </div>
                <a class="scheduleReminder__link">
                  <div v-on:click="openworkOrder(item.id,index)" :title="item.taskContent" class="scheduleReminder_margin_bottom_5 deliverList__margin-5" v-html="item.taskContent">
                  </div>
                </a>
                <div class="scheduleReminder_third_line">
                  <div v-on:click="workIgnore(item.id,index)" class="scheduleReminder_third_buttom scheduleReminder_buttom_color_while">
                    忽略
                  </div>
                </div>
              </div>
              <div class="scheduleReminder__row_line" v-if="workOrderList.length !=1 && index!=2  && index !=workOrderList.length -1"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 昨日工作总结model -->
      <div class="workSummaryOfYesterday" :class="[showLeftAlert?'workSummaryOfYesterday__bottom_0':'workSummaryOfYesterday__bottom_1024']">
        <div class="workSummaryOfYesterday__title">
          <div class="workSummaryOfYesterday__title_left">
            <img class="scheduleReminder__icon" src="/js/scheduleReminder/icon_title.png" />
            <div>{{hasShowSummary?'昨日工作总结':''}}</div>
          </div>
          <div v-on:click="closeLeft" class="workSummaryOfYesterday__title_right">
            <img class="scheduleReminder__icon" src="/js/scheduleReminder/icon_close.png" />
          </div>
        </div>
        <div v-if="hasShowSummary" class="workSummaryOfYesterday__middle">
          <div class="workSummaryOfYesterday__middle_span">
            <div class="workSummaryOfYesterday__middle_num">
              {{showLeftList.summary.serch}}
            </div>
            <div>
              寻访量
            </div>
          </div>
          <div class="workSummaryOfYesterday__middle_line"></div>
          <div class="workSummaryOfYesterday__middle_span">
            <div class="workSummaryOfYesterday__middle_num">
              {{showLeftList.summary.recommend}}
            </div>
            <div>
              推给客户
            </div>
          </div>
          <div class="workSummaryOfYesterday__middle_line"></div>
          <div class="workSummaryOfYesterday__middle_span">
            <div class="workSummaryOfYesterday__middle_num">
              {{showLeftList.summary.interview}}
            </div>
            <div>
              客户面试
            </div>
          </div>
          <div class="workSummaryOfYesterday__middle_line"></div>
          <div class="workSummaryOfYesterday__middle_span">
            <div class="workSummaryOfYesterday__middle_num">
              {{showLeftList.summary.offer}}
            </div>
            <div>
              人选offer
            </div>
          </div>
        </div>
        <div :class="'workSummaryOfYesterday__banner_'+ week" class="workSummaryOfYesterday__banner">
          <div class="workSummaryOfYesterday__banner_body">
            <div class="workSummaryOfYesterday__banner_title">
              <span>亲爱的</span><span>{{showLeftList.motto.staffName}} ：</span>
            </div>
            <div class="workSummaryOfYesterday__banner_text">{{mottoContent[week]}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script>
       var HdUrl = 'https://www.haidou.com';
  </script>
  <!-- 日程总结model位置 2020-09-21 end-->
  
  <div class="modal fade bs-modal-sm index-staffcard staff_cards" id="staffSmallCard" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
    <div class="modal-dialog modal-sm">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
          <h4 class="modal-title"><i class="icon-info"></i>个人名片</h4>
          <input type="hidden" class="staffid" value="" />
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-8" style="padding-right:0;">
              <!-- 员工评价 修改结构 2020-03-06 赵刚 start-->
              <div class="rnss-line sc-line cf" style="width:230px">
                <span class="sc-title">姓名：</span>
                <a id="is_id_url" href="" class="sc-con sc-name" target="_blank" style="width: auto;max-width: 135px;margin-right:5px;"></a>
              </div>
              <!-- 员工评价 修改结构 2020-03-06 赵刚 end-->
              <div class="rnss-line sc-line cf">
                <span class="sc-title">手机：</span>
                <span class="sc-con sc-phone" style="display:none;">
                  <span class="sc-txt"></span>
                  <a style="display:inline-block;" href="javascript:void(0);" class="rnss-callphone show-phone" data-transfernumber="" data-usnumber="" data-ustel="" title="点击呼叫"><img class="icon-staffcard-mobile" src="/images/cooperation/icon-mobile.png" /></a>
                </span>
                <span class="sc-con sc-phone all-titphone" style="display:block;">
                  <span class="sc-txt-view" style="color:#2A7FCC;text-decoration: underline;cursor: pointer;">点击查看</span> <a><img class="icon-staffcard-mobile" src="/images/cooperation/icon-mobile.png" /></a>
                </span>
              </div>
              <div class="rnss-line sc-line cf">
                <span class="sc-title">座机：</span>
                <span class="sc-con sc-tel"></span>
              </div>
              <div class="rnss-line sc-line cf">
                <span class="sc-title">QQ：</span>
                <a class="sc-con sc-qq" href="javascript:void(0)"></a>
              </div>
              <div class="rnss-line sc-line2 cf">
                <span class="sc-title">邮箱：</span>
                <span class="sc-con sc-email"></span>
              </div>
            </div>
            <div class="col-md-4 staffcard-level-panel" style="padding-left:0;">
              
              <div class="sc-photo staffcard-userimg"></div>
              <img class="staffcard-level-img" src="/images/cooperation/img-userlevel0.png" />
              <div class="staffcard-level-name sc-level"></div>
            </div>
          </div>

          <div class="row md6-row">
            <div class="col-md-6">
              <div class="rnss-line cf">
                <span class="sc-title width-45">公司：</span>
                <span class="sc-con sc-company"></span>
              </div>
            </div>
            <div class="col-md-6">
              <div class="rnss-line cf">
                <span class="sc-title width-45">部门：</span>
                <span class="sc-con sc-department"></span>
              </div>
            </div>
          </div>
          <div class="row md6-row">
            <div class="col-md-6">
              <div class="rnss-line cf">
                <span class="sc-title width-45">职务：</span>
                <span class="sc-con sc-position"></span>
              </div>
            </div>
          </div>

          
          <div class="tstar-boxwrap">
            <div class="tstar-titlewrap">
              <p class="tstar-title"><span class="sc-name"></span>哪一点打动了你，为他点亮这颗星吧<a href="javascript:;" class="tstar-rules" id="seeRules">点亮规则</a></p>
              <hr />
            </div>
            <div class="tstar-maincontent cf">
              <dl class="tstar-box">
                <dt>
                  <img src="/Css/images/1star_0.png" class="tstar" data-tagid="1" id="starImg1" />
                  <span class="hit">+1</span>
                </dt>
                <dd class="star-tag">真诚</dd>
                <dd class="font-yellow-gold sbold">x<span id="starScore1"></span></dd>
              </dl>
              <dl class="tstar-box">
                <dt>
                  <img src="/Css/images/1star_0.png" class="tstar" data-tagid="2" id="starImg2" />
                  <span class="hit">+1</span>
                </dt>
                <dd class="star-tag">进取</dd>
                <dd class="font-yellow-gold sbold">x<span id="starScore2"></span></dd>
              </dl>
              <dl class="tstar-box">
                <dt>
                  <img src="/Css/images/1star_0.png" class="tstar" data-tagid="3" id="starImg3" />
                  <span class="hit">+1</span>
                </dt>
                <dd class="star-tag">分享</dd>
                <dd class="font-yellow-gold sbold">x<span id="starScore3"></span></dd>
              </dl>
              <dl class="tstar-box">
                <dt>
                  <img src="/Css/images/1star_0.png" class="tstar" data-tagid="4" id="starImg4" />
                  <span class="hit">+1</span>
                </dt>
                <dd class="star-tag">责任</dd>
                <dd class="font-yellow-gold sbold">x<span id="starScore4"></span></dd>
              </dl>
              <dl class="tstar-box">
                <dt>
                  <img src="/Css/images/1star_0.png" class="tstar" data-tagid="5" id="starImg5" />
                  <span class="hit">+1</span>
                </dt>
                <dd class="star-tag">创新</dd>
                <dd class="font-yellow-gold sbold">x<span id="starScore5"></span></dd>
              </dl>
            </div>
          </div>

          <div class="rnss-line cf">
            <span class="sc-title no-width">
              评价得分：
            </span>
            <span class="sc-con sc-evaluation-score"></span>
          </div>

          <div class="rnss-line cf">
            <span class="sc-title no-width">
              擅长行业：
            </span>
            <span class="sc-industries"></span>
          </div>

          <div class="modal-bottom-opt">
            <div class="btn btn-outline orange modal-bottom-btn" onclick="app_2.showModeFn()"><img class="modal-bottom-icon" src="/images/cooperation/icon-smile.png" />评价</div>
            <div class="btn btn-outline green modal-bottom-btn" onclick="app_2.sendIMMsg()"><img class="modal-bottom-icon" src="/images/cooperation/icon-chat.png" />发私信</div>
            <div class="btn btn-outline blue modal-bottom-btn btn-invite-cooperation btn-allow" onclick="inviteCoorporationVue.openInviteCooperation()"><img class="modal-bottom-icon" src="/images/cooperation/icon-hand.png" />邀合作</div>
            <div class="btn btn-outline blue modal-bottom-btn btn-invite-cooperation btn-disabled" onclick="inviteCoorporationVue.openInviteCooperation()"><img class="modal-bottom-icon" src="/images/cooperation/icon-hand-gray.png" />邀合作</div>
          </div>
        </div>

        
        <div class="rules-box" id="rulesBox">
          <span class="closeTips" id="closeTips">X</span>
          <h3>达人之星评选规则</h3>

          <h4>真诚</h4>
          <p>真实坦荡，让他人感受温暖</p>
          <p>诚实守信，对承诺全力以赴</p>
          <ul>
            <li>1、人选简历造假，如实告知客户自行选择</li>
            <li>2、不提供各项虚假信息，不隐瞒真实情况（系统虚假录入，虚构请假等）</li>
          </ul>

          <h4>进取</h4>
          <p>永不满足，不断挑战新高度</p>
          <p>与时俱进，时常保持饥饿感</p>
          <ul>
            <li>1、不断进步成长</li>
            <li>2、不断自我学习</li>
          </ul>

          <h4>分享</h4>
          <p>双赢思维，越分享越成长</p>
          <p>空杯心态，满遭损谦受益</p>
          <ul>
            <li>1、乐于分享知识技能，乐于教人成长</li>
            <li>2、合作共赢，热衷团队合作</li>
          </ul>

          <h4>责任</h4>
          <p>勇于担当，常思己过</p>
          <p>心怀感恩，乐于助人</p>
          <ul>
            <li>1、对客户对团队负责，全力以赴交付人选，带领团队出单</li>
            <li>2、具备主人翁意识，爱护公物，节约水电（随手关灯，及时断电）</li>
          </ul>

          <h4>创新</h4>
          <p>勇敢探索，快速低成本实验</p>
          <p>容忍失败，对未来保持敬畏</p>
          <ul>
            <li>1、为公司团队发展提供各种点子方案</li>
            <li>2、为系统改革主动提出各种改革需求建议</li>
          </ul>


        </div>
      </div>
    </div>
  </div>

  <div id="inviteCoorporationModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
          <div class="modal-title">邀请<span class="color-blue">{{ receiveStaffName }}</span>进行职位合作</div>
        </div>
        <div class="modal-body">
          <div class="invitecoorporation-notice"> 选择合作职位（预计分配提成金额只作为参考，不做实际分配依据）</div>
          <div class="invitecoorporation-item" v-for="(item, index) in selectedJobList">
            <div class="invitecoorporation-row">
              <div class="invitecoorporation-main">
                <div class="invitecoorporation-col-6">
                  <div class="invitecoorporation-form-group">
                    <div class="invitecoorporation-form-title">选择职位</div>
                    <div class="invitecoorporation-form-content">
                      <select class="form-control" v-model="item.jobId" v-on:change="changeJob(index)">
                        <option v-for="jobItem in getUnselectedJob(item.jobId)" :value="jobItem.jobId">{{ jobItem.jobTitle }}-{{ jobItem.clientName }}</option>
                      </select>
                    </div>
                  </div>

                </div>
                <div class="invitecoorporation-col-6">
                  <div class="invitecoorporation-form-group">
                    <div class="invitecoorporation-form-title">分配提点</div>
                    <div class="invitecoorporation-form-content">
                      <select class="form-control" v-model="item.ratio" v-on:change="changeRatio(item)">
                        <option :value="0.08">8%</option>
                        <option :value="0.09">9%</option>
                        <option :value="0.1">10%</option>
                        <option :value="0.11">11%</option>
                        <option :value="0.12">12%</option>
                        <option :value="0.13">13%</option>
                        <option :value="0.14">14%</option>
                        <option :value="0.15">15%</option>
                      </select>
                    </div>
                  </div>
                  <div class="invitecoorporation-form-notice">
                    <img class="invitecoorporation-form-notice-icon" src="/images/cooperation/icon-question.png" title="根据合同签约金额计和分配提成计算出预计分配金额，该金额仅供参考不作为实际分配依据" />
                    预计分配提成金额（元/人）:{{ item.reckonFrom || 0 }}～{{ item.reckonTo || 0 }}
                  </div>
                </div>

              </div>
              <div class="invitecoorporation-opt">
                <img v-if="index == 0 && selectedJobList.length < 5" class="invitecoorporation-opt-icon" src="/images/cooperation/icon-item-add.png" v-on:click="addJob" />
                <img v-if="index != 0" class="invitecoorporation-opt-icon" src="/images/cooperation/icon-item-remove.png" v-on:click="removeJob(index)" />
              </div>
            </div>
            <div class="invitecoorporation-row">
              <div class="invitecoorporation-main">
                <div class="invitecoorporation-form-group">
                  <div class="invitecoorporation-form-title">职位要求</div>
                  <div class="invitecoorporation-form-content">
                    <textarea class="form-control" v-model="item.remark" rows="3" placeholder="简单描述职位要求，便于合作者能快速了解职位需求0～100字，非必填" maxlength="100"></textarea>
                  </div>
                </div>
              </div>
              <div class="invitecoorporation-opt">
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default footer-btn" data-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary footer-btn" v-on:click="submit">确定合作</button>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div>

  
  <div class="modal fade" id="myModals" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-body back">
          <h3>【致新家人的一封信】</h3>
          <div class="back_content">
            <ul>
              <li style="font-weight:700;"><span id="userName">顾开发ZJ1 &nbsp;&nbsp;</span>欢迎加入锐仕方达</li>
              <li style="text-align:center;padding-left:30px;">锐仕方达梦想成为“世界一流的人才服务机构”，为客户在全球范围内提供</li>
              <li>人才解决方案，希望每位伙伴立足岗位，不断提升专业素质，成为受人尊敬的人</li>
              <li>才服务专家。</li>
              <li style="text-align:center;padding-left:15px;">“奋斗永不止步”代表着锐仕方达人的精神，如果有一天：我停止奋斗或满足</li>
              <li>现状，那么，我知道，我已经不再是锐仕方达人！</li>
              <li style="text-align:center;padding-left:23px;">在这里我们鼓励创新，多提想法，更重要的是要去行动，工作中我们要积极</li>
              <li>主动去沟通，推动业务向前发展。</li>
              <li style="text-align:center;padding-left:21px;">现在，公司正通过技术与创新，引领着人才服务行业不断前进，为个人和组</li>
              <li>织的梦想加速。</li>
              <li style="text-align:center;padding-left:23px">未来，我们将在全球范围内连接更多上下游，共建人才服务生态体系，也希</li>
              <li>望你能和我一道，不懈奋斗，做强人才产业，推动就业，为社会稳定做出贡献。</li>
              <li style="text-align:right; padding-right:15px; font-weight:700;">锐仕方达创始人 黄小平</li>
            </ul>
          </div>
          <p class="item2" style="font-size:12px;margin-top:-61px;">&nbsp;&nbsp;&nbsp;&nbsp;<input type="checkbox" name="name" value="" class="chech" /><span>我已阅读<a href="/apps/viewregulation.aspx?id=74" target="_Blank">《员工手册》</a></span></p>
          <button type="button" class="bot" style="border-radius:12px;"><span class="icona"></span><span>开启新的工作旅程</span></button>
        </div>
      </div>
    </div>
  </div>

  
  <div class="modal fade bs-modal-sm" id="Layout_BeautAlert" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
          <h4 class="modal-title"></h4>
        </div>
        <div class="modal-body"></div>
      </div>
    </div>
  </div>

  
  <div class="modal fade bs-modal-sm" id="Layout_Confirm" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
    <div class="modal-dialog modal-sm">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
          <h4 class="modal-title">确认信息</h4>
        </div>
        <div class="modal-body"></div>
        <div class="modal-footer">
          <button type="button" class="btn green btn_Save">确认</button>
          <button type="button" class="btn dark btn-outline btn_Cancel" data-dismiss="modal">取消</button>
        </div>
      </div>
      <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
  </div>

  
  <div class="tel-modal not-collapse-right-menu" id="telModal">
    <div class="tel-modal-header">
      <img src="/images/callbox-bg.jpg" class="tel-modal-header-bg" />
      <span class="tel-modal-close fr">&times;</span>
    </div>

    <div class="tel-line-box">
      <img src="/images/callbox-phone.png" class="tel-line-box-icon" alt="选择线路" />
      <div class="line-box">
        
        <ul class="lines">
          <li><a href="javascript:;" class="call_line" id="callLine1">线路1通话</a></li>
              <li><a href="javascript:;" class="call_line" id="callLine2">线路2通话</a></li>


          
        </ul>
      </div>
    </div>

    <div class="tel-modal-body">
      <input type="hidden" id="telModalData" data-type="0" date-rid="0" />

      <div class="tm-box">
        <h4 class="tm-label">对方号码</h4>
        <div class="tm-op-box">
          <select class="i-tips">
            <option selected="selected" value="86" data-codeid="1">中国大陆+86</option>
          </select>
          <span class="i-divider"></span>
          <input class="i-phone" type="text" placeholder='请输入电话号~' id="CallNumInput" />
        </div>
      </div>

      <a href="javascript:;" class="tm-notice-btn">
        注意事项
        <div class="tm-notice-box">
          <div class="popover confirmation top in">
            <div class="arrow"></div>
            <h3 class="popover-title">注意事项<span class="tm-notice-close pull-right" title='关闭提示'>&times;</span></h3>
            <div class="popover-content">
              <p>1.系统将分别呼叫双方电话，请先接听后再等待对方接听。</p>
              <p>2.主叫号码使用分机只能使用线路1通话。</p>
              <p>3.所有线路都已经支持通话录音功能。</p>
              <p>4.输入国内座机号码时，必须添加国内长途区号，格式：010-59626785，或 01059626785。</p>
            </div>
          </div>
        </div>
      </a>

      <div class="tm-om-box cf">
        <h4 class="tm-label">我的号码</h4>
            <span class="tm_num selected" id="MyMobileNum" data-calltype="1">15605286175</span>

      </div>

      <div class="tm-rd-box cf" id="telModalRecordBox">
        <h4 class="tm-label tm_wj font-red-soft">
          <i class="glyphicon glyphicon-exclamation-sign">
            
          </i>
          合作客户，严禁挖猎
          <span class="xs_tl1">(以下可能为签约客户公司员工)</span>
          
        </h4>
        <ul class="tm-rd-list" id="telRecordList"></ul>
      </div>

      <div class="tm-rd-box cf" id="telModalRecordBox2">
        <h4 class="tm-label">沟通记录<a href="/apps/managecalllogs.aspx" class="tm-rd-seemore" id="telRecordSeemore2" target="_blank" title='将跳转到通话日志查看相关记录'>查看更多<i class="fa fa-angle-double-right"></i></a></h4>
        <ul class="tm-rd-list" id="telRecordList2"></ul>
      </div>

    </div>

  </div>
  <div class="bg-cover not-collapse-right-menu" id="telModalCover"></div>


  <div style="width: 40px; height: 288px; border-radius: 4px !important; background: #fff; position: fixed; right: 24px; bottom: 80px;padding:4px;display:none; ">
    <div style="padding: 6px 6px 4px; border-bottom: 1px solid #E7E7E7;cursor:pointer;position:relative; ">
      <i style="width:20px;height:20px;display:inline-block;">
        <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="简化版-首页-联合交付信息展示备份" transform="translate(-1714.000000, -1291.000000)" fill-rule="nonzero">
              <g id="握手备份" transform="translate(1714.000000, 1291.000000)">
                <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="20" height="20"></rect>
                <path d="M15.5321262,12.5 C15.3962374,12.5 15.2603487,12.4451863 15.155816,12.335559 C15.0554647,12.231197 14.9990168,12.0891224 14.9990168,11.9409086 C14.9990168,11.7926948 15.0554647,11.6506202 15.155816,11.5462582 L17.0582583,9.55109659 C17.4868349,9.1016524 17.1523264,8.55351579 16.7864808,8.16984004 L12.8665988,4.05891484 C12.5530386,3.73007264 12.3335085,3.64235487 12.2080844,3.62042543 C12.1139974,3.60947065 11.9781276,3.60947065 11.7899536,3.80679586 L7.99553357,7.78614441 C8.54953421,8.15886538 9.17671154,8.20272426 9.57393206,7.84095807 C10.2533758,7.21609824 11.2254883,6.10890999 11.2359529,6.09793533 C11.4345537,5.86772591 11.7690432,5.85677113 11.9885733,6.06505111 C12.2080844,6.27333109 12.2185301,6.62412262 12.0199293,6.85435193 C11.9781087,6.89819093 10.9955315,8.01635383 10.2742671,8.68505267 C9.33351061,9.55109659 7.84919914,9.34281661 6.81433664,8.20272426 C6.61573586,7.98346961 6.61573586,7.63267809 6.82478233,7.4243981 L11.0268875,3.01749504 C11.7585976,2.25012366 12.7307291,2.33782154 13.6192192,3.26963391 L17.5391012,7.3805591 C18.4903224,8.37812002 18.5948551,9.51823225 17.8108787,10.3403974 L15.9084364,12.335559 C15.8039037,12.4451863 15.6680149,12.5 15.5321262,12.5 L15.5321262,12.5 Z" id="路径" fill="#2A7FCC"></path>
                <path d="M10.3747625,18.3333333 C10.3008432,18.3333333 10.2163913,18.3333333 10.142472,18.3226042 C9.67787188,18.2583069 9.23437513,18.0332471 8.88593939,17.6795733 L4.58841205,13.3069518 C3.87037972,12.5781847 3.53249568,11.9994511 3.22628587,11.495744 C3.046773,11.1956577 2.87783097,10.9063006 2.64554048,10.6062144 C2.58219201,10.5204782 2.50827269,10.4347421 2.43435338,10.3490059 C1.99087578,9.80244036 1.7163402,9.44876658 1.69521766,9.08436364 C1.69521766,9.06294418 1.69521766,9.04150528 1.68466597,9.02006638 C1.65299173,8.69856067 1.57907242,8.02336174 2.33933067,7.25173636 L5.77102536,3.76864442 C6.54181616,2.98628988 7.44991304,3.43640954 8.05178097,3.74720552 L8.14680368,3.79006388 C8.24184553,3.83294167 8.34743908,3.8865292 8.44246179,3.94011672 C9.04432972,4.24020298 9.80458797,4.60458649 10.4592335,5.18330066 C10.6809915,5.37621186 10.7126657,5.72988564 10.5120303,5.95494547 C10.3219657,6.18002474 9.97352999,6.21215393 9.75177205,6.00853299 C9.20272005,5.5262647 8.51638111,5.19402982 7.96730996,4.9260922 C7.86171641,4.87250467 7.75612285,4.81891715 7.6505293,4.77603935 L7.5555066,4.72247127 C6.87971935,4.37952665 6.70022563,4.34735859 6.51016107,4.54026979 L3.09956977,8.02336174 C2.69833726,8.43062304 2.73001149,8.6663926 2.75113403,8.9021816 C2.75113403,8.9236205 2.75113403,8.93433023 2.76168573,8.95576913 C2.8144825,9.0950928 3.11014062,9.44876658 3.26853095,9.64165835 C3.34245026,9.73812367 3.42690212,9.83456955 3.49026975,9.9203057 C3.77535702,10.2847086 3.96542158,10.6062338 4.15548614,10.9277201 C4.45114426,11.4314467 4.72567983,11.9030053 5.34865115,12.535307 L9.64619764,16.8972188 C9.82569136,17.0794008 10.058001,17.197305 10.2797398,17.2294536 C10.438111,17.2508925 10.5753979,17.2187245 10.6492981,17.1544272 C10.8604852,16.9400771 11.9375241,15.9755405 13.4368989,14.6358913 C14.281609,13.8856854 15.2319319,13.0390141 15.3269737,12.9318585 C15.3375254,12.9211294 15.8865966,12.2030721 15.3797705,11.6886552 L11.4095755,7.65899767 C11.1983692,7.44464757 11.1983692,7.10168352 11.4095755,6.88735286 C11.6207817,6.67302219 11.9586275,6.67300276 12.1698146,6.88735286 L16.1400096,10.9170103 C16.5835063,11.36713 16.7524292,11.9673025 16.6257323,12.5996236 C16.5307096,13.0711628 16.2878482,13.499863 16.087232,13.7034839 C15.9710676,13.8213687 15.3058512,14.4108315 14.1443604,15.4503945 C13.0145439,16.4578088 11.6101726,17.7117219 11.4095563,17.9153429 C11.1561433,18.1832805 10.7865659,18.3333333 10.3747625,18.3333333 L10.3747625,18.3333333 Z" id="路径" fill="#2A7FCC"></path>
                <path d="M14.4610253,13.839676 C14.3236464,13.839676 14.1862674,13.7869322 14.0805884,13.6814636 L12.1467426,11.7510612 C11.9354037,11.5400858 11.9354037,11.2025404 12.1467426,10.9915649 C12.3580815,10.7805895 12.6962582,10.7805895 12.9076163,10.9915649 L14.8414813,12.921929 C14.9429331,13.0223503 15,13.15906 15,13.3016772 C15,13.4442944 14.9429331,13.5810041 14.8414813,13.6814253 C14.7463817,13.7869131 14.6090027,13.8396378 14.4610444,13.8396378 L14.4610253,13.839676 Z M13.1083758,15.2004371 C12.9709969,15.2004371 12.833618,15.1476932 12.7279389,15.0422055 L10.794074,13.1118222 C10.5827159,12.9008659 10.5827159,12.5633014 10.794074,12.3523259 C11.005432,12.1413505 11.3435896,12.1413696 11.5549477,12.3523259 L13.4887935,14.2827283 C13.5902453,14.3831495 13.6473122,14.5198593 13.6473122,14.6624765 C13.6473122,14.8050936 13.5902453,14.9418034 13.4887935,15.0422246 C13.3831145,15.1477123 13.2457355,15.2004371 13.1083566,15.2004371 L13.1083758,15.2004371 Z M11.6394871,16.6666667 C11.5021081,16.6666667 11.3647292,16.6139228 11.2590502,16.5084542 L9.32518522,14.5780518 C9.11382715,14.3670764 9.11382715,14.029531 9.32518522,13.8185555 C9.53654328,13.6075801 9.87470086,13.6075801 10.0860589,13.8185555 L12.0199239,15.7489388 C12.1213757,15.84936 12.1784426,15.9860697 12.1784426,16.1286869 C12.1784426,16.2713041 12.1213757,16.4080139 12.0199239,16.5084351 C11.9142449,16.6139037 11.776866,16.6666667 11.6394871,16.6666667 L11.6394871,16.6666667 Z" id="形状" fill="#2A7FCC"></path>
              </g>
            </g>
          </g>
        </svg>
      </i>
      <div style="position: absolute; background: rgba(0,0,0,0.22);width:74px;height:24px;color:#fff;">
        联合交付
      </div>
    </div>
    <div style="padding: 6px 6px 4px; border-bottom: 1px solid #E7E7E7; ">
      <i style="width:20px;height:20px;display:inline-block;">
        <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="简化版-首页-联合交付信息展示备份" transform="translate(-1714.000000, -1327.000000)">
              <g id="编组-11" transform="translate(1714.000000, 1327.000000)">
                <rect id="矩形" x="0" y="0" width="20" height="20"></rect>
                <g id="编组备份-6" transform="translate(1.666667, 3.333333)" stroke="#666666" stroke-linecap="round">
                  <path d="M12.0833333,13.3333333 L8.5,13.3333333 C7.94771525,13.3333333 7.5,12.8856181 7.5,12.3333333 L7.5,10 L7.5,10 L13.3333333,10 L13.3333333,6.66666667 L15.6666667,6.66666667 C16.2189514,6.66666667 16.6666667,7.11438192 16.6666667,7.66666667 L16.6666667,12.3333333 C16.6666667,12.8856181 16.2189514,13.3333333 15.6666667,13.3333333 L14.5833333,13.3333333 L14.5833333,13.3333333 L13.3333333,14.5833333 L12.0833333,13.3333333 Z" id="路径" stroke-linejoin="round"></path>
                  <path d="M1,0 L12.3333333,0 C12.8856181,8.7960173e-16 13.3333333,0.44771525 13.3333333,1 L13.3333333,9 C13.3333333,9.55228475 12.8856181,10 12.3333333,10 L5.41666667,10 L5.41666667,10 L3.75,11.6666667 L2.08333333,10 L1,10 C0.44771525,10 6.76353751e-17,9.55228475 0,9 L0,1 C-6.76353751e-17,0.44771525 0.44771525,-1.20591542e-16 1,0 Z" id="路径" stroke-linejoin="round"></path>
                  <line x1="6.25" y1="5" x2="6.66666667" y2="5" id="路径"></line>
                  <line x1="9.16666667" y1="5" x2="9.58333333" y2="5" id="路径"></line>
                  <line x1="3.33333333" y1="5" x2="3.75" y2="5" id="路径"></line>
                </g>
              </g>
            </g>
          </g>
        </svg>
      </i>
    </div>
    <div style="padding: 6px 6px 4px; border-bottom: 1px solid #E7E7E7; ">
      <i style="width:20px;height:20px;display:inline-block;">
        <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="简化版-首页-联合交付信息展示备份" transform="translate(-1714.000000, -1363.000000)" fill="#666666" fill-rule="nonzero">
              <g id="提醒备份" transform="translate(1714.000000, 1363.000000)">
                <rect id="矩形" opacity="0" x="0" y="0" width="20" height="20"></rect>
                <path d="M17.9780128,14.1673958 C17.7710403,13.905 17.5369239,13.5921875 17.2790565,13.2278125 C17.0228639,12.8667153 16.7923142,12.4876363 16.5891482,12.0934375 C16.3709831,11.6789795 16.186766,11.2470779 16.0383526,10.8020833 C15.9059043,10.4132029 15.8386233,10.0046089 15.8392971,9.59322917 C15.8392971,8.94354167 15.7375073,8.33854167 15.5361898,7.80114583 C15.347965,7.27995973 15.078791,6.79257214 14.7388368,6.35739583 C14.3999523,5.92998772 13.9923575,5.56353117 13.5331938,5.2734375 C13.277726,5.11151466 13.0090815,4.97198036 12.7301858,4.85635417 L12.2936209,4.6546875 L12.1183163,4.61229167 C12.4610085,4.21010417 12.6758979,3.69333333 12.6758979,3.12270833 C12.6758979,1.85832143 11.6641813,0.833333333 10.4161656,0.833333333 C9.16814983,0.833333333 8.15643318,1.85832143 8.15643318,3.12270833 C8.15643318,3.70020833 8.37584663,4.22270833 8.72419377,4.62604167 C8.65633394,4.64322917 8.59978408,4.65927083 8.57829513,4.66614583 C8.09727383,4.82114911 7.63862744,5.03999527 7.21431252,5.31697917 C6.31432165,5.9035095 5.62529918,6.76875136 5.2497704,7.78395833 C5.03187778,8.36724431 4.92407176,8.98680652 4.93196019,9.61041667 C4.93196019,10.0366667 4.86523136,10.446875 4.73516668,10.825 C4.58352011,11.2646563 4.39940071,11.6921156 4.18437105,12.10375 C3.97739856,12.5047917 3.75459212,12.880625 3.50237974,13.2530208 C3.22528543,13.6597917 2.99003802,14.0276042 2.82038844,14.3026042 C2.36346557,14.9557292 2.46073133,15.616875 2.70728872,16.0202083 C3.04095056,16.5553125 3.55327462,16.875 4.07918831,16.875 L16.4059267,16.875 C16.7339159,16.875 17.3028075,16.648125 17.4464441,16.5713542 C17.7314554,16.428125 17.9282489,16.2046875 18.0402176,16.0477083 C18.2121292,15.8116667 18.3116569,15.5240625 18.3308839,15.2215625 C18.352859,14.8370795 18.2263075,14.4590198 17.9780128,14.1673958 L17.9780128,14.1673958 Z M10.4172966,1.97916652 C11.0422414,1.97948308 11.5486061,2.49300314 11.5482939,3.12614583 C11.5479814,3.75928852 11.0411104,4.27229558 10.4161656,4.27197931 C10.0118972,4.27177448 9.63844552,4.05308155 9.43648628,3.69827988 C9.23452704,3.34347821 9.23474261,2.90647062 9.43705178,2.55187363 C9.63936095,2.19727664 10.0130282,1.97896198 10.4172966,1.97916652 L10.4172966,1.97916652 Z M17.1354199,15.39 C17.0904898,15.4578014 17.0304348,15.5139337 16.9601153,15.5538542 C16.8673735,15.6042708 16.5473013,15.7154167 16.4398566,15.7291667 L4.03168643,15.7291667 C3.91971771,15.7291667 3.76590209,15.6065625 3.65958835,15.4335417 C3.62565844,15.3739583 3.58833553,15.2467708 3.75459212,15.0061458 C3.93555167,14.7059375 4.1640131,14.3495833 4.43092844,13.9519792 C4.71254674,13.5303125 4.96023512,13.1086458 5.19208955,12.6617708 C5.43186095,12.1988542 5.64222643,11.705 5.81639999,11.1939583 C5.99170456,10.6783333 6.08105334,10.1283333 6.08105334,9.5565625 C6.06077501,8.21003863 6.72338612,6.94701996 7.83636097,6.21072917 C8.19601808,5.978125 8.56019918,5.80166667 8.88818836,5.69625 L9.53398776,5.51635417 L9.54529773,5.5140625 L9.54303573,5.51520833 C9.81562373,5.45104167 10.1006173,5.41666667 10.3958076,5.41666667 C11.1671477,5.41666667 11.7484803,5.64583333 11.7993751,5.66416667 C12.240464,5.80166667 12.5978591,5.964375 12.9269793,6.17635417 C13.2762634,6.39822816 13.5856826,6.67876974 13.8419561,7.0059375 C14.1009544,7.33822917 14.3090579,7.71979167 14.4606115,8.14145833 C14.6144271,8.55395833 14.6913349,9.02375 14.6913349,9.539375 C14.6913349,10.0951042 14.7806837,10.6416667 14.9548573,11.161875 C15.1278999,11.6820833 15.3405273,12.1873958 15.5859537,12.65375 C15.8313801,13.128125 16.0937715,13.5646875 16.3663418,13.9485417 C16.6377811,14.338125 16.8866005,14.6738542 17.1139309,14.96375 C17.1783978,15.0416667 17.2078037,15.1161458 17.2032797,15.190625 C17.2002237,15.2623444 17.1766464,15.3316155 17.1354199,15.39 L17.1354199,15.39 Z M13.2447895,18.0208333 C13.5571058,18.0208333 13.8102881,18.2773369 13.8102881,18.59375 C13.8102881,18.9101631 13.5571058,19.1666667 13.2447895,19.1666667 L7.58980359,19.1666667 C7.27748734,19.1666667 7.02430499,18.9101631 7.02430499,18.59375 C7.02430499,18.2773369 7.27748734,18.0208333 7.58980359,18.0208333 L13.2447895,18.0208333 Z" id="形状"></path>
              </g>
            </g>
          </g>
        </svg>
      </i>
    </div>
    <div style="padding: 6px 6px 4px; border-bottom: 1px solid #E7E7E7; ">
      <i style="width:20px;height:20px;display:inline-block;">
        <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="简化版-首页-联合交付信息展示备份" transform="translate(-1714.000000, -1399.000000)">
              <g id="编组-12" transform="translate(1714.000000, 1399.000000)">
                <rect id="矩形" x="0" y="0" width="20" height="20"></rect>
                <g id="编组备份-5" transform="translate(2.500000, 4.166667)" stroke="#666666" stroke-linejoin="round">
                  <path d="M1,11.25 L14,11.25 C14.5522847,11.25 15,10.8022847 15,10.25 L15,5.625 L15,5.625 L15,1 C15,0.44771525 14.5522847,1.20591542e-16 14,0 L7.5,0 L7.5,0 L1,0 C0.44771525,1.01453063e-16 -2.8967998e-16,0.44771525 0,1 L0,5.625 L0,5.625 L0,10.25 C6.76353751e-17,10.8022847 0.44771525,11.25 1,11.25 Z" id="路径"></path>
                  <path d="M0,0 L6.9,5.175 C7.25555556,5.44166667 7.74444444,5.44166667 8.1,5.175 L15,0 L15,0" id="路径" stroke-linecap="round"></path>
                  <polyline id="路径" stroke-linecap="round" points="7.5 0 0 0 0 5.625"></polyline>
                  <polyline id="路径" stroke-linecap="round" points="15 5.625 15 0 7.5 0"></polyline>
                </g>
              </g>
            </g>
          </g>
        </svg>
      </i>
    </div>
    <div style="padding: 6px 6px 4px; border-bottom: 1px solid #E7E7E7; ">
      <i style="width:20px;height:20px;display:inline-block;">
        <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="简化版-首页-联合交付信息展示备份" transform="translate(-1714.000000, -1435.000000)">
              <g id="编组-13" transform="translate(1714.000000, 1435.000000)">
                <rect id="矩形备份-33" x="0" y="0" width="20" height="20"></rect>
                <g id="编组备份-7" transform="translate(2.500000, 1.666667)">
                  <path d="M7.91666667,15.8333333 C10.102775,15.8333333 12.0819417,14.9472208 13.5145812,13.5145813 C14.9472208,12.0819417 15.8333333,10.102775 15.8333333,7.91666667 C15.8333333,5.73055833 14.9472208,3.75139167 13.5145812,2.31873625 C12.0819417,0.886104583 10.102775,0 7.91666667,0 C5.73055833,0 3.75139167,0.886104583 2.31873625,2.31873625 C0.886104583,3.75139167 0,5.73055833 0,7.91666667 C0,10.102775 0.886104583,12.0819417 2.31873625,13.5145813 C3.75139167,14.9472208 5.73055833,15.8333333 7.91666667,15.8333333 Z" id="路径" stroke="#666666" stroke-linejoin="round"></path>
                  <path d="M7.91666667,9.74731667 L7.91666667,8.16398333 C9.22833958,8.16398333 10.2916667,7.10065625 10.2916667,5.78898333 C10.2916667,4.47731042 9.22833958,3.41398333 7.91666667,3.41398333 C6.60499375,3.41398333 5.54166667,4.47731042 5.54166667,5.78898333" id="路径" stroke="#666666" stroke-linecap="round" stroke-linejoin="round"></path>
                  <path d="M7.91666667,13.3098167 C8.46319375,13.3098167 8.90625,12.8667604 8.90625,12.3202333 C8.90625,11.7737063 8.46319375,11.33065 7.91666667,11.33065 C7.37013958,11.33065 6.92708333,11.7737063 6.92708333,12.3202333 C6.92708333,12.8667604 7.37013958,13.3098167 7.91666667,13.3098167 Z" id="路径" fill="#666666"></path>
                </g>
              </g>
            </g>
          </g>
        </svg>
      </i>
    </div>
    <div style="padding: 6px 6px 4px; border-bottom: 1px solid #E7E7E7; ">
      <i style="width:20px;height:20px;display:inline-block;">
        <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="简化版-首页-联合交付信息展示备份" transform="translate(-1714.000000, -1471.000000)">
              <g id="编组-14" transform="translate(1714.000000, 1471.000000)">
                <rect id="矩形备份-34" x="0" y="0" width="20" height="20"></rect>
                <g id="编组备份-4" transform="translate(2.500000, 3.333333)" stroke="#666666" stroke-linejoin="round">
                  <path d="M14.1111606,5.80291331 C14.2094379,5.14366252 14.1772101,4.4687449 14.0144773,3.81979612 C13.7995346,2.96251352 13.3568204,2.1506375 12.6864123,1.48022944 C12.0160043,0.809825267 11.2041282,0.367142132 10.3468845,0.152183922 C9.69793572,-0.010541161 9.02297923,-0.0427689535 8.36372844,0.0555005445" id="路径" stroke-linecap="round"></path>
                  <path d="M11.1605099,5.49070414 C11.2657848,4.78449175 11.0466047,4.03924834 10.5029697,3.4956133 C9.95929573,2.95197826 9.21405233,2.73275929 8.50787881,2.83803415" id="路径" stroke-linecap="round"></path>
                  <path d="M3.45767876,1.21079499 C3.74014817,1.21079499 4.00038079,1.36397225 4.13745581,1.61092499 L5.08854501,3.32409458 C5.21306325,3.54840623 5.21889457,3.81971837 5.10417296,4.04920047 L4.18795509,5.88163621 C4.18795509,5.88163621 4.45347478,7.24674952 5.56469218,8.35796691 C6.67590957,9.46918431 8.03643557,9.73011669 8.03643557,9.73011669 L9.86859918,8.81405432 C10.0982368,8.69921609 10.3697433,8.70516404 10.5941327,8.82991553 L12.3121578,9.78508665 C12.5588618,9.92223943 12.7118758,10.1823554 12.7118758,10.4646305 L12.7118758,12.4369791 C12.7118758,13.4414055 11.7789026,14.1668613 10.8271914,13.8457108 C8.87256991,13.1861878 5.83845347,11.9304313 3.91536006,10.007299 C1.99222777,8.08420562 0.736471254,5.05008919 0.076932785,3.09546774 C-0.24418665,2.14375654 0.481265236,1.21079499 1.48567219,1.21079499 L3.45767876,1.21079499 Z" id="路径"></path>
                </g>
              </g>
            </g>
          </g>
        </svg>
      </i>
    </div>
    <div style="padding: 6px 6px 4px; border-bottom: 1px solid #E7E7E7; ">
      <i style="width:20px;height:20px;display:inline-block;">
        <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="简化版-首页-联合交付信息展示备份" transform="translate(-1714.000000, -1507.000000)">
              <g id="编组-15" transform="translate(1714.000000, 1507.000000)">
                <rect id="矩形备份-35" x="0" y="0" width="20" height="20"></rect>
                <g id="编组备份-8" transform="translate(3.333333, 1.666667)" stroke="#666666" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M0.833333333,16.6666667 L12.5,16.6666667 C12.96025,16.6666667 13.3333333,16.2935833 13.3333333,15.8333333 L13.3333333,4.16666667 L9.16666667,4.16666667 L9.16666667,0 L0.833333333,0 C0.373095833,0 0,0.373095833 0,0.833333333 L0,15.8333333 C0,16.2935833 0.373095833,16.6666667 0.833333333,16.6666667 Z" id="路径"></path>
                  <line x1="9.16666667" y1="0" x2="13.3333333" y2="4.16666667" id="路径"></line>
                  <polygon id="路径" points="5.41666667 12.9166667 9.58333333 8.75 7.91666667 7.08333333 3.75 11.25 3.75 12.9166667"></polygon>
                </g>
              </g>
            </g>
          </g>
        </svg>
      </i>
    </div>
    <div style="padding: 6px 6px 4px; border-bottom: 1px solid #E7E7E7; ">
      <i style="width:20px;height:20px;display:inline-block;">
        <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="简化版-首页-联合交付信息展示备份" transform="translate(-1714.000000, -1543.000000)">
              <g id="编组-17" transform="translate(1714.000000, 1543.000000)">
                <g id="编组-16">
                  <rect id="矩形备份-36" x="0" y="0" width="20" height="20"></rect>
                  <polyline id="路径" stroke="#666666" stroke-linecap="round" stroke-linejoin="round" points="5 12.5 10 7.5 15 12.5"></polyline>
                </g>
                <g id="编组备份-3" transform="translate(5.000000, 7.500000)"></g>
              </g>
            </g>
          </g>
        </svg>
      </i>
    </div>
  </div>



  <div class="right-menu-container loading">
    <img class="right-expand-btn" src="/images/layout-right-expand-btn.png" />
    <div class="right-menu-panel">
      
      <!--<div class="call-box not-collapse-right-menu" id="CallBox">-->
      
      <!--<div class="bg-font-green-meadow index-iconinfo-box callphone-box" id="showTelModalBtn" data-usnumber="15605286175" data-ustel="">
        <a class="if-box" href="javascript:;">
          <div class="if-icon"><img src="~/Content/images/if-icon-tel.png" alt="" /></div>
          <div class="index-iconinfo-con">拨打电话</div>
        </a>
      </div>-->
      
      <!--<div class="callphone-pingjia-box" style="display:none;">
          <div class="cppj-title">点击评价</div>
          <div class="cppj-line cppj-fankui">报告问题</div>
          <div class="cppj-line cppj-callreload">再呼一个</div>
          <div class="cppj-zz">正在通话...</div>
        </div>
      </div>-->
      
      <!--<div onclick="bottomMessageTip.openZhiNengXiaoDa('1')" class="zhinengxiaoda">
      <a class="zhinengxiaoda-link">
        <span class="zhinengxiaodaimg">
          <span class="xiaoda_yanjin"></span>
          <span class="xiaoda_left"></span>
          <span class="xiaoda_right"></span>
        </span>
        <span class="xiaoda_bottom"></span>-->
      
      <!--</a>
        <span style="display:none" id="xiaoDaMessageNum" class="zhinengxiaoda-tips "></span>
      </div>-->
      
      <!--<div class="chat-box" data-toggle="modal" data-target="#chat-modal" data-backdrop="static" data-keyboard="false">
        <a class="chat-box-link" href="javascript:;">
          <img src="~/Content/images/chat.png" />
          <div>聊天通讯</div>
        </a>
        <div class="chat-box-unread"></div>
      </div>-->
      
      <!--<div onclick="bottomMessageTip.openAlert(bottomMessageTip.showAlertModel)" class="xiaoxi-box">
        <a class="xiaoxi-box-link">
          <img src="~/Content/images/icon_xiaoxi.png" />
          <div>系统提醒</div>
        </a>
        <span style="display:none" id="systemMessageNum" class="xiaoxi-tips"></span>
      </div>-->
      
      <!--<div class="help-box">
        <a class="help-box-link" href="/staff/help" target="_blank">
          <img src="~/Content/images/help-box-icon.png" />
          <div>帮助中心</div>
        </a>
      </div>-->
      
      <!--<div class="mail-box not-collapse-right-menu" id="mailBox">
      <a class="mail-box-link" href="javascript:;">
        <i class="fa fa-envelope"></i>
        <div>企业邮箱</div>
      </a>-->
      
      <!--</div>-->
      
      <!--<div class="feedback-box" id="layoutFeedbackModalID">
        <a class="help-box-link">
          <img src="~/Content/images/Common__icon_note.png" />
          <div>意见反馈</div>
        </a>
      </div>-->
      

<!-- 24.12.30新的右侧浮动-->
<div class="right_menu_container_new">
  <div class="right_menu_haidou_box" onclick="bottomMessageTip.openZhiNengXiaoDa('1')">
    <div class="right_menu_item">
      <div class="right_menu_tips">海斗<div class="popper_arrow_icon"></div></div>
      <div class="right_menu_icon_box right_menu_icon_box1">
        <img class="right_menu_img" src="/images/ai_assistant/haidou_icon.png" alt="" />
      </div>
    </div>
  </div>
  <div class="right_menu_list_box">
    <div class="right_menu_item" data-toggle="modal" data-target="#chat-modal" data-backdrop="static" data-keyboard="false">
      <div class="right_menu_tips">聊天通讯<div class="popper_arrow_icon"></div></div>
      <div class="right_menu_icon_box">
        <svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="RNSS-右侧导航栏修改" transform="translate(-1841.000000, -663.000000)">
              <g id="icon/聊天通讯" transform="translate(1841.000000, 663.000000)">
                <rect id="矩形" fill="#F1F1F1" opacity="0" x="0" y="0" width="40" height="40"></rect>
                <g id="聊天通讯" class="svg_icon_hover" transform="translate(10.000000, 10.000000)" fill="#333333">
                  <path d="M10.0047079,6.04145908e-06 C5.96605556,-0.00441912556 2.32172325,2.42246386 0.768699108,6.15057983 C-0.784325034,9.87869579 0.0592740827,14.1751198 2.90667343,17.0392165 L2.90667343,19.1503262 C2.90264581,19.3765025 2.99257476,19.5942252 3.15503928,19.751633 C3.31458716,19.9109171 3.53089791,20.0002628 3.75634607,20.000006 L10.0047079,20.000006 C15.5275517,20.000006 20.0047012,15.5228492 20.0047012,10.000006 C20.0047012,4.47716169 15.5275517,6.04145908e-06 10.0047079,6.04145908e-06 L10.0047079,6.04145908e-06 Z M10.0047079,18.3006536 L4.59948276,18.3006536 L4.59948276,16.6862755 C4.59992084,16.4511409 4.50290046,16.2263376 4.33150908,16.0653609 C2.64579712,14.5001332 1.69285987,12.3003179 1.70396222,10.0000055 C1.70396222,5.41568411 5.42038658,1.69935738 10.0047079,1.69935738 C14.5890293,1.69935738 18.305356,5.41568411 18.305356,10.0000055 C18.305356,14.5843268 14.5890293,18.3006536 10.0047079,18.3006536 Z" id="形状" fill-rule="nonzero"></path>
                  <circle id="椭圆形" cx="5.5" cy="10" r="1.5"></circle>
                  <circle id="椭圆形备份-19" cx="10" cy="10" r="1.5"></circle>
                  <circle id="椭圆形备份-20" cx="14.5" cy="10" r="1.5"></circle>
                </g>
              </g>
            </g>
          </g>
        </svg>
      </div>
    </div>
    <div class="right_menu_item" onclick="bottomMessageTip.openAlert(bottomMessageTip.showAlertModel)">
      <div class="right_menu_tips">系统提醒<div class="popper_arrow_icon"></div></div>
      <div class="right_menu_icon_box">
        <svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="RNSS-右侧导航栏修改" transform="translate(-1841.000000, -704.000000)">
              <g id="icon/通讯" transform="translate(1841.000000, 704.000000)">
                <rect id="矩形备份-12" stroke="#F5F5F5" fill="#FFFFFF" opacity="0" x="-0.5" y="-0.5" width="41" height="41"></rect>
                <g id="提醒" class="svg_icon_hover" transform="translate(11.000000, 7.800000)" fill="#333333" fill-rule="nonzero">
                  <path d="M16.8195447,13.6756709 L16.398893,13.1295618 C15.9998509,12.6001884 15.7847556,11.9549026 15.7863653,11.2919785 L15.7863653,9.82338784 C15.7863653,5.6168718 12.6056488,2.2 8.69432683,2.2 C4.7830049,2.2 1.60966824,5.6168718 1.60966824,9.82338784 L1.60966824,11.2550793 C1.61127793,11.9180033 1.39618264,12.5632892 0.997140463,13.0926626 L0.583868712,13.6756709 C0.20362501,14.1736622 -0.00134318548,14.7833777 0.000860348356,15.4099363 C-0.0363131809,16.8949639 1.13576023,18.129768 2.62070806,18.1700012 L14.797465,18.1700012 C16.2824128,18.129768 17.4544863,16.8949639 17.4173127,15.4099363 C17.4125443,14.7821231 17.2026217,14.1730884 16.8195447,13.6756709 Z M2.47132725,16.33 C2.19011736,16.33 2.04496357,16.2291895 1.90212049,16.0726692 C1.7592774,15.9161489 1.69572485,15.4059776 1.7123849,15.198389 C1.71018775,14.9962738 1.77681973,14.7690135 1.90212049,14.6066869 L2.33471763,14.0595745 C3.03506594,13.2021195 3.41707499,12.1442274 3.42000519,11.0541033 L3.42000519,9.63890578 C3.42000519,6.53130699 5.80308419,4 8.73260167,4 C11.6621192,4 14.0451982,6.53130699 14.0451982,9.63890578 L14.0451982,11.0541033 C14.0434832,12.1266152 14.4088421,13.1698934 15.0849492,14.0231003 L15.5175463,14.5702128 C15.6428471,14.7325393 15.7094791,14.9597998 15.7072819,15.161915 C15.7427769,15.5790012 15.7072819,16.2308099 14.9862867,16.33 L2.47132725,16.33 Z M12.5318502,18.5906528 C12.0872357,18.3073266 11.4973066,18.4358586 11.2108566,18.8784671 C10.6527295,19.7318102 9.72009563,20.2667031 8.70170668,20.3175384 C7.84941354,20.3396792 7.06582176,19.852111 6.70914645,19.0777231 C6.51105609,18.591444 5.95885122,18.3547519 5.47008473,18.5466231 C4.98131824,18.7384942 4.7376014,19.2876349 4.9232221,19.7788091 C5.58008794,21.2731714 7.06958895,22.227383 8.70170668,22.1994008 C10.3664131,22.144153 11.9023504,21.2896334 12.8270443,19.9042666 C12.964103,19.6914796 13.0100342,19.4325901 12.95454,19.1856411 C12.8990459,18.9386922 12.7467691,18.7243438 12.5318502,18.5906528 Z M8.71,3 C9.53842712,3 10.21,2.32842712 10.21,1.5 C10.21,0.671572875 9.53842712,0 8.71,0 C7.88157288,0 7.21,0.671572875 7.21,1.5 C7.21,2.32842712 7.88157288,3 8.71,3 Z" id="形状"></path>
                </g>
              </g>
            </g>
          </g>
        </svg>
      </div>
    </div>
    <div class="right_menu_item" id="mailBox">
      <div class="right_menu_tips">企业邮箱<div class="popper_arrow_icon"></div></div>
      <div class="right_menu_icon_box">
        <svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="RNSS-右侧导航栏修改" transform="translate(-1841.000000, -745.000000)">
              <g id="icon/邮箱" transform="translate(1841.000000, 745.000000)">
                <rect id="矩形备份-13" stroke="#F5F5F5" fill="#FFFFFF" opacity="0" x="-0.5" y="-0.5" width="41" height="41"></rect>
                <g class="svg_icon_hover" id="邮箱" transform="translate(10.000000, 13.000000)" fill="#333333">
                  <path d="M18.8,0 C19.4627417,-1.2174368e-16 20,0.537258322 20,1.20000005 L20,13.8 C20,14.4627417 19.4627417,15 18.8,15 L1.20000005,15 C0.537258322,15 -1.40882152e-16,14.4627417 0,13.8 L0,1.20000005 C-8.11624534e-17,0.537258322 0.537258322,1.0099221e-15 1.20000005,0 L18.8,0 Z M18.3,1.7 L1.7,1.7 L1.7,13.3 L18.3,13.3 L18.3,1.7 Z" id="形状结合"></path>
                  <path d="M18.4596493,0.444177383 C18.8437868,0.150974242 19.3928799,0.224691153 19.6860831,0.608828713 C19.9792862,0.992966272 19.9055693,1.5420594 19.5214318,1.83526254 L10.5334952,8.69554258 C10.2186293,8.93587228 9.78158557,8.9347057 9.46800716,8.69269854 L0.578866613,1.83241851 C0.196299771,1.53716885 0.125515204,0.987690016 0.420764858,0.605123174 C0.716014511,0.222556332 1.26549335,0.151771765 1.64806019,0.447021419 L10.0044634,6.89671996 L18.4596493,0.444177383 Z" id="路径-4" fill-rule="nonzero"></path>
                </g>
              </g>
            </g>
          </g>
        </svg>
      </div>
    </div>
    <div class="right_menu_item">
      <a href="/staff/help" target="_blank">
        <div class="right_menu_tips">帮助中心<div class="popper_arrow_icon"></div></div>
        <div class="right_menu_icon_box">
          <svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
              <g id="RNSS-右侧导航栏修改" transform="translate(-1841.000000, -786.000000)">
                <g id="icon/帮助中心" transform="translate(1841.000000, 786.000000)">
                  <rect id="矩形备份-14" stroke="#F5F5F5" fill="#FFFFFF" opacity="0" x="-0.5" y="-0.5" width="41" height="41"></rect>
                  <g id="反馈" class="svg_icon_hover" transform="translate(10.000000, 10.000000)" fill="#333333" fill-rule="nonzero">
                    <path d="M10,0 C4.4771525,0 0,4.4771525 0,10 C0,15.5228475 4.4771525,20 10,20 C15.5228475,20 20,15.5228475 20,10 C19.9963948,4.47864651 15.5213535,0.00360519442 10,0 L10,0 Z M10,18.3006536 C5.41567561,18.3006536 1.69934641,14.5843244 1.69934641,10 C1.69934641,5.41567561 5.41567561,1.69934641 10,1.69934641 C14.5843244,1.69934641 18.3006536,5.41567561 18.3006536,10 C18.2934559,14.5813397 14.5813397,18.2934559 10,18.3006536 Z" id="形状"></path>
                    <path d="M10,4.33986568 C8.08834854,4.343466 6.53954443,5.89227011 6.53594771,7.80392157 L8.23529412,7.80392157 C8.23529412,6.82930142 9.02537985,6.03921569 10,6.03921569 C10.9746201,6.03921569 11.7647059,6.82930142 11.7647059,7.80392157 C11.7647059,8.77854172 10.9746201,9.56862745 10,9.56862745 L9.1503268,9.56862745 L9.1503268,11.9607843 L10.8496732,11.9607843 L10.8496732,11.1633987 C12.5463727,10.7367756 13.658642,9.11198168 13.4424603,7.37587625 C13.2262785,5.63977082 11.7495115,4.33736648 10,4.33986568 L10,4.33986568 Z" id="路径"></path>
                    <path d="M8.69281046,13.9215686 C8.69281046,14.6435095 9.27805915,15.2287582 10,15.2287582 C10.7219408,15.2287582 11.3071895,14.6435095 11.3071895,13.9215686 C11.3071895,13.1996278 10.7219408,12.6143791 10,12.6143791 C9.27805915,12.6143791 8.69281046,13.1996278 8.69281046,13.9215686 Z" id="路径"></path>
                  </g>
                </g>
              </g>
            </g>
          </svg>
        </div>
      </a>
    </div>
    <div class="right_menu_item right_menu_phone" id="CallBox">
      <div class="callphone-box" id="showTelModalBtn" data-usnumber="<% =UserInfo.MobileNumber%>" data-ustel="<%=UserInfo.CallTelNumber%>">
        <div class="right_menu_tips">拨打电话<div class="popper_arrow_icon"></div></div>
        <div class="right_menu_icon_box">
          <svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
              <g id="RNSS-右侧导航栏修改" transform="translate(-1841.000000, -827.000000)">
                <g id="icon/拨打电话" transform="translate(1841.000000, 827.000000)">
                  <rect id="矩形备份-15" stroke="#F5F5F5" fill="#FFFFFF" opacity="0" x="-0.5" y="-0.5" width="41" height="41"></rect>
                  <g id="电话" transform="translate(11.000000, 10.602542)">
                    <g id="电话-(4)" class="svg_icon_hover" transform="translate(0.000000, 2.397458)" fill="#333333" fill-rule="nonzero">
                      <path d="M2.35366718,0.571783647 L2.19014056,0.681880795 C1.89270904,0.892264012 1.61764534,1.13260567 1.36926923,1.39913129 L1.16931338,1.62175418 L1.27293422,1.52946687 C0.352454849,2.31064004 -0.116103653,3.50057201 0.0246269237,4.69961692 C0.103520522,5.59664022 0.335144759,6.47358163 0.709495905,7.2925665 L0.877879775,7.64552498 C2.42152494,10.7965197 4.82429167,13.4470841 7.80914245,15.2916095 L8.12000496,15.4802318 L8.05281333,15.4365168 C9.37890496,16.339923 10.9202574,16.8767013 12.5206525,16.9924485 L12.6372259,16.9964961 L12.7853713,16.9964961 L12.6841791,16.9916389 C13.4321859,17.0494519 14.1765522,16.8409949 14.7857392,16.4031049 C15.3341067,15.9911071 15.8218948,15.5041365 16.2348119,14.9564609 L16.3465281,14.8010296 C17.2253295,13.6793253 17.2169489,12.1004188 16.3262897,10.9881066 L15.9344734,10.52667 L15.8421861,10.4262873 L15.6398016,10.2328078 C15.295067,9.91830649 14.9175456,9.64174696 14.5137345,9.40788878 L14.2975879,9.28726765 L14.2255391,9.24112399 C13.0211298,8.49964019 11.4739707,8.63032128 10.410997,9.56332003 L10.3316622,9.63617844 L10.2911853,9.60703508 C9.23357902,8.83982447 8.29704672,7.9183007 7.51285161,6.87322592 L7.38251603,6.6951276 L7.4100403,6.66841285 C8.27898505,5.74522325 8.5058728,4.38932931 7.98481216,3.23354394 L7.88200085,2.99634937 C7.50452587,2.19029108 6.96107101,1.47304101 6.28721138,0.891551092 L6.16092348,0.787930241 C5.13852649,-0.14161811 3.61666857,-0.261501885 2.46133572,0.496496632 L2.35366718,0.570974122 L2.35366718,0.571783647 Z M3.08710843,2.03138031 L3.28382612,1.89780657 C3.78474005,1.52633816 4.47015986,1.52798423 4.96928383,1.90185427 L5.07209513,1.98685574 C5.70647695,2.49108878 6.20116659,3.14937437 6.50902473,3.89898402 C6.78575826,4.51338737 6.62307021,5.23673873 6.1099226,5.6734909 C6.00172082,5.78286731 5.89827419,5.89684765 5.79986961,6.01511584 L5.65658142,6.19564278 L5.39510072,6.55345848 L5.60153286,6.93879848 C5.70920139,7.12903987 5.82820345,7.31280495 5.95934857,7.48928418 C6.97152688,8.92685251 8.23399139,10.1706707 9.68646061,11.1613477 C9.77955746,11.2261107 9.87508291,11.2876356 9.97222745,11.3459223 L10.1203729,11.4301142 L10.5186655,11.6503085 L10.8764812,11.3613035 L11.0351506,11.2244916 L11.1857246,11.0787748 L11.3031076,10.9622014 C11.8000472,10.3623623 12.6562858,10.2066204 13.3326189,10.5930521 L13.5171935,10.7080065 C13.880676,10.8974383 14.2182532,11.1322043 14.5218299,11.4058281 L14.6999282,11.5742119 L15.0099812,11.9385039 C15.4503698,12.4298934 15.5013707,13.1511916 15.1476026,13.6960105 L15.0715061,13.8028695 C14.7322304,14.2940265 14.3168566,14.7279827 13.8410086,15.0884155 C13.5787184,15.2770378 13.2662368,15.3790396 12.9464694,15.3822778 L12.8088479,15.3774205 L12.6372259,15.3774205 C11.3215954,15.2822544 10.0545017,14.8409919 8.96435288,14.0983508 C5.99863663,12.3509281 3.63372656,9.74379608 2.18285472,6.62226919 C1.87953601,5.95532478 1.69332035,5.24113296 1.632369,4.5109946 C1.5612806,3.90383881 1.77356193,3.29774725 2.20795038,2.86763286 L2.32128568,2.76401203 L2.55443257,2.50253131 C2.71634013,2.32848069 2.89443845,2.17062081 3.08710843,2.03138031 L3.08710843,2.03138031 Z" id="形状"></path>
                    </g>
                    <path d="M6.7435174,0 C9.07121611,0.546239853 10.4419937,2.03788805 10.8558502,4.47494459" id="路径-6" class="svg_path_hover" stroke="#333333" stroke-width="1.7" stroke-linecap="round" stroke-linejoin="round"></path>
                  </g>
                </g>
              </g>
            </g>
          </svg>
        </div>
      </div>
      <div class="callphone-pingjia-box">
        <div class="cppj-title">点击评价</div>
        <div class="cppj-line cppj-fankui">报告问题</div>
        <div class="cppj-line cppj-callreload">再呼一个</div>
        <div class="cppj-zz">正在通话...</div>
      </div>
    </div>
    <div class="right_menu_item" id="layoutFeedbackModalID">
      <div class="right_menu_tips">意见反馈<div class="popper_arrow_icon"></div></div>
      <div class="right_menu_icon_box">
        <svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="RNSS-右侧导航栏修改" transform="translate(-1841.000000, -868.000000)">
              <g id="icon/意见反馈" transform="translate(1841.000000, 868.000000)">
                <rect id="矩形备份-16" fill="#FFFFFF" opacity="0" x="0" y="0" width="40" height="40"></rect>
                <g id="反馈" transform="translate(10.000000, 9.000000)">
                  <path d="M11,1.56482323 L11,3.25982323 L2.90000005,3.26 C2.23725832,3.26 1.7,3.79725832 1.7,4.46000005 L1.7,18.66 C1.7,19.3227417 2.23725832,19.86 2.90000005,19.86 L15.1,19.86 C15.7627417,19.86 16.3,19.3227417 16.3,18.66 L16.3,8.56482323 L18,8.56482323 L18,19.5648232 C18,20.6693927 17.1045695,21.5648232 16,21.5648232 L2,21.5648232 C0.8954305,21.5648232 1.3527075e-16,20.6693927 0,19.5648232 L0,3.56482323 C-1.3527075e-16,2.46025373 0.8954305,1.56482323 2,1.56482323 L11,1.56482323 Z" class="svg_icon_hover" id="形状结合" fill="#333333"></path>
                  <rect id="矩形" class="svg_icon_hover" fill="#333330" x="4" y="7.56482323" width="7" height="1.8" rx="0.9"></rect>
                  <rect id="矩形备份-22" class="svg_icon_hover" fill="#333330" transform="translate(16.292893, 4.171930) rotate(-45.000000) translate(-16.292893, -4.171930) " x="11.2928932" y="3.27193001" width="10" height="1.8" rx="0.9"></rect>
                  <rect id="矩形备份-21" class="svg_icon_hover" fill="#333330" x="4" y="13.5648232" width="10" height="1.8" rx="0.9"></rect>
                </g>
              </g>
            </g>
          </g>
        </svg>
      </div>
    </div>


  </div>
  <img class="right-collapse-btn" src="/images/layout-right-collapse-btn.png" />
</div>

<!-- 引入ai助理1-->

      <img class="right-collapse-btn" src="/images/layout-right-collapse-btn.png" />
    </div>

  </div>

  
  <div id="staffleveltip-box" style="display:none;">
    <div class="staffleveltip-p cf">
      <table class="staffleveltip-tab">
        <thead>
          <tr>
            <th>个人业绩</th>
            <th>等级</th>
          </tr>
        </thead>
        <tbody>
          <tr><td>1000万&amp;lt;业绩</td><td>LV8</td></tr>
          <tr><td>500万&amp;lt;业绩&amp;lt;1000万</td><td>LV7</td></tr>
          <tr><td>300万&amp;lt;业绩&amp;lt;500万</td><td>LV6</td></tr>
          <tr><td>100万&amp;lt;业绩&amp;lt;300万</td><td>LV5</td></tr>
          <tr><td>50万&amp;lt;业绩&amp;lt;100万</td><td>LV4</td></tr>
          <tr><td>30万&amp;lt;业绩&amp;lt;50万</td><td>LV3</td></tr>
          <tr><td>15万&amp;lt;业绩&amp;lt;30万</td><td>LV2</td></tr>
          <tr><td>1万&amp;lt;业绩&amp;lt;15万</td><td>LV1</td></tr>
          <tr><td>1万&amp;gt;业绩</td><td>无等级</td></tr>
        </tbody>
      </table>
      <div class="staffleveltip-con">
        <div class="question cf"><label>问：</label>怎样查看我的业绩？</div>
        <div class="daan cf"><label>答：</label><span>点系统右上角-我的头像-查看我的信息-业绩金额。</span></div>
      </div>
      <div class="staffleveltip-con">
        <p class="question cf"><label>问：</label>业绩统计时间范围是？</p>
        <div class="daan cf"><label>答：</label><span>从入职到现在的全部审核通过业绩。</span></div>
      </div>
      <div class="staffleveltip-con">
        <p class="question cf"><label>问：</label>多长时间更新一次数据？</p>
        <div class="daan cf"><label>答：</label><span>等级信息每天更新一次。</span></div>
      </div>
    </div>
  </div>

  
  <div class="modal fade index-staffcard ecm_modal r__rb_modal" style="z-index: 1042;" id="R_modal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
    <div class="modal-dialog" style="z-index:1050;margin-top: 150px;width:466px!important;">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
          <h4 class="modal-title">R币转账</h4>
        </div>
        <div class="modal-body">
          <div class="r_show">您当前共有<span id="Rnum" class="orange">12</span>R币</div>
          <div class="form-group clearfix">
            <label for="inputStaff" class="col-sm-2 control-label r_label">转出对象</label>
            <div class="col-sm-10 ecm_l">
              
              <div id="selTeam_R" class="cf text-2 selTeam" style="width: 253px !important;line-height: 30px !important; margin-left: 0 !important;height:auto; box-shadow: 0 0 0;">
                <span class="add-btn buttom-2 addTeam R_addTeam" id="addTeam">添加成员</span>
                <div id="selector_outerbox_0" class="cf outbox selector_outerbox" style="height:auto; min-height: 30px;">
                  <div class="outbox-tip">可以动态输入查询，也可右侧点击选择</div>
                  <ul class="outbox-item-list"></ul>
                  <input type="text" id="selector_input_0" style="height: 23px; width: 24px !important;padding: 10px 0px 0px 0px;margin: 0 4px;" class="selector_input">
                </div>
              </div>
            </div>
          </div>
          <div class="form-group clearfix">
            <label for="inputNum" class="col-sm-2 control-label r_label">转出数量</label>
            <div class="col-sm-10 ecm_l">
              <input type="number" name="clientName" value="" id="inputNum" autocomplete="off" class="form-control clientName" placeholder="请输入正整数" />
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn green" id="submitRpay">转账</button>
          <button type="button" class="btn dark btn-outline" data-dismiss="modal">
            取消
          </button>
        </div>
      </div>
      <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
  </div>

  <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        
        <div class="modal-body aa">
          <h3 style="text-align:center;">致新家人的一封信</h3>
          <p class="item1">亲爱的***</p>
          <p class="item2">&nbsp;&nbsp;&nbsp;&nbsp;热烈欢迎你加入我们锐仕方达这个朝气蓬勃的大家庭，成为我们第****位家人。</p>
          <p class="item2">&nbsp;&nbsp;&nbsp;&nbsp;恭喜你在众多的求职者中脱颖而出，我们也深信你具备我们所需的工作能力、经验及潜力。很高兴在接下来的日子里我们可以相互尊重、彼此信任、共同奋斗。</p>
          <p class="item2">&nbsp;&nbsp;&nbsp;&nbsp;　初初加入，为了更快的了解公司环境，更好的融入工作中，需要你熟知以下内容：公司制度、一般性职责、特别的工作任务、团队和个人项目。</p>
          <p class="item2">&nbsp;&nbsp;&nbsp;&nbsp; 你们怀揣着心中的梦想加入到我们这个大家庭，为我们这个正在高速发展中的公司注入了新的血液、增添了新的动力。事业是我们可能用一辈子去为之奋斗的事情，希望在未来相处的日子里，我们伴随着公司一起成长壮大，用我们的付出写出新的篇章。</p>
          <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;我们的使命是让每一个为梦想而奋斗的人更幸福的生活！</p>
        </div>
        <p class="item2" style="font-size:12px;margin-top:15px;">&nbsp;&nbsp;&nbsp;&nbsp;<input type="checkbox" name="name" value="" class="chech" /><span>我已阅读<a href="/apps/viewregulation.aspx?id=74" target="_Blank">《员工手册》</a></span></p>
        <div class="modal-footer mod-footer">
          <button type="button" class="btn btn-default but" data-dismiss="modal" style="border-radius:12px;" disabled>开启新的工作旅程</button>
        </div>
      </div>
    </div>
  </div>
  <div class="modal fade" id="chat-modal" tabindex="-1" role="dialog"></div>

  
  <div class="modal fade hm_center" id="_layoutFeedbackModal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
    <div class="modal-dialog" style="width:500px">
      <div class="modal-content">
        <div class="modal-header blue" style="padding: 14px 20px">意见反馈</div>
        <div class="modal-body" style="padding: 16px 20px">
          <div style="padding-bottom: 5px">
            反馈部门：
            <select id="selDepartments" class='select-box select-candidates-status'></select>
          </div>
        </div>
        <div class="modal-body" style="padding: 16px 20px">
          <div style="padding-bottom: 15px">请填写您的意见反馈：</div>
          <textarea id="_layoutModalfeedbackText" maxlength="1000" class="from__textarea" placeholder="请填写意见反馈" style="width: 100%;height: 120px; border: 1px solid #ddd; border-radius: 2px !important; padding: 15px"></textarea>
        </div>
        <div class="modal-footer" style="padding: 0 20px 20px 20px;border-top: none">
          <button type="button" style="background: #fff;border: 1px solid #ddd;padding: 6px 14px;margin-right: 10px" data-dismiss="modal" onclick="$('#_layoutModalfeedbackText').val('')">取消</button>
          <button type="button" style="background: #2A7FCC;border: none;padding: 7px 14px;color:#fff" class="btn" class="btn green" id="_layoutFeedbackModalSubmit">确定</button>
        </div>
      </div>
    </div>
  </div>
  <div id="modifyBug_20210721"></div>
  <div class="download-newCard">
    <div id="downloadNewCard">
      <div class="download-newCard-top">
        <div class="newCardD-top-img download-img">
          
          <img src="" class="imgclass" id="imgid" width="100%" height="100%" style="border-radius:50% !important;" />
          <i class="newCardD-top-grade">V<span class="v-level"></span> </i>
        </div>
        <div class="newCardD-top-information">
          <div class="newCard-people">
            <p class="newCard-name p"><span class="down-name"></span><span class="down-enName"></span></p>
            <p class="newCard-position p" style="margin-left: 10px;"></p>
            <p class="newCard-company p" style="margin-left: 4px;"></p>
          </div>
          <div class="newCard-phone">
            <i style="width:16px;height:16px;display:inline-block;">
              <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="vertical-align: sub;">
                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                  <g id="画板备份" transform="translate(-183.000000, -1156.000000)" fill-rule="nonzero">
                    <g id="编组-21备份-3" transform="translate(71.000000, 1098.000000)">
                      <g id="电话" transform="translate(112.000000, 58.000000)">
                        <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="16" height="16"></rect>
                        <path d="M5.8625,7.5125 C6.425,8.65 7.3375,9.5875 8.45,10.1625 L9.3125,9.275 C9.4125,9.1625 9.575,9.125 9.7125,9.175 C10.15,9.325 10.625,9.4 11.1125,9.4 C11.325,9.4 11.5,9.5875 11.5,9.8 L11.5,11.2 C11.5,11.425 11.325,11.6 11.1125,11.6 C7.425,11.6 4.4375,8.5375 4.4375,4.75 C4.4375,4.525 4.6125,4.35 4.825,4.35 L6.2,4.35 C6.4125,4.35 6.5875,4.525 6.5875,4.75 C6.5875,5.25 6.6625,5.7375 6.8125,6.2 C6.85,6.3375 6.825,6.5 6.7125,6.6125 L5.8625,7.5125 L5.8625,7.5125 Z M8,2 C4.6875,2 2,4.6875 2,8 C2,11.3125 4.6875,14 8,14 C11.3125,14 14,11.3125 14,8 C14,4.6875 11.3125,2 8,2 M8,1 C11.8625,1 15,4.1375 15,8 C15,11.8625 11.8625,15 8,15 C4.1375,15 1,11.8625 1,8 C1,4.1375 4.1375,1 8,1 Z" id="形状" fill="#FFFFFF"></path>
                      </g>
                    </g>
                  </g>
                </g>
              </svg>
            </i>
            <span class="span-mobilePhone"></span>

          </div>
          <div class="newCard-email">
            <i style="width:16px;height:16px;display:inline-block;">
              <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                  <g id="画板备份" transform="translate(-183.000000, -1186.000000)" fill-rule="nonzero">
                    <g id="编组-21备份-3" transform="translate(71.000000, 1098.000000)">
                      <g id="邮件" transform="translate(112.000000, 88.000000)">
                        <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="16" height="16"></rect>
                        <path d="M13.4484375,3.009375 L2.5578125,3.009375 C2.128125,3.009375 1.7796875,3.3578125 1.7796875,3.7875 L1.7796875,12.1875 C1.7796875,12.6171875 2.128125,12.965625 2.5578125,12.965625 L13.4484375,12.965625 C13.50625,12.965625 13.5640625,12.959375 13.6203125,12.946875 C13.71875,12.946875 13.8125,12.9109375 13.884375,12.8453125 L13.884375,12.821875 C14.0953125,12.6796875 14.2234375,12.4421875 14.2265625,12.1875 L14.2265625,3.7859375 C14.2265625,3.35625 13.878125,3.009375 13.4484375,3.009375 L13.4484375,3.009375 Z M8.003125,8.6875 L2.6546875,3.884375 L13.3828125,3.884375 L8.003125,8.6875 Z M2.5578125,4.840625 L6.14375,8.065625 L2.5578125,11.6390625 L2.5578125,4.840625 Z M6.7234375,8.5703125 L7.7578125,9.5 C7.9046875,9.63125 8.128125,9.63125 8.275,9.5 L9.3765625,8.515625 L12.7484375,12.1875 L3.10625,12.1875 L6.7234375,8.5703125 Z M9.946875,7.9953125 L13.446875,4.884375 L13.446875,11.8078125 L9.946875,7.9953125 Z" id="形状" fill="#FFFFFF"></path>
                      </g>
                    </g>
                  </g>
                </g>
              </svg>
            </i>
            <span class="span-email"></span>
          </div>
          <div class="newCard-feedback">
            <span class="newCard-span">反馈效率 <span class="fankui"></span> 天</span>
            <span class="newCard-span">推荐offer率 <span class="offerLv"></span>%</span>
          </div>
        </div>
        <div class="newCardD-top-download">
          <div class="download-img">
            <img src="" class="download-img" id="imgid" width="100%" height="100%" />
          </div>
        </div>
      </div>
      <div class="download-newCard-bottom">
        <div style="position:relative;margin-bottom:8px;">
          <span class="newCard-bottom-title">成功案例 </span>
          <i class="download-newCard-line"></i>
        </div>

        <div class="newCard-echarts" style="margin-bottom: 40px;">
          <div class="echarts-top">
            <div style="width: 280px; height: 180px; border-right: 1px solid #ddd; border-bottom: 1px solid #ddd;">
              <div id="downloadCardEcharts1" style="width: 260px; height: 180px; ">

              </div>
            </div>
            <div style="width: 280px; height: 180px; border-bottom: 1px solid #ddd;">
              <div id="downloadCardEcharts2" style="width: 260px; height: 180px; ">

              </div>
            </div>
          </div>
          <div class="echarts-bottom">
            <div style="width: 280px; height: 180px; border-right: 1px solid #ddd;">
              <div id="downloadCardEcharts3" style="width: 260px; height: 180px; ">

              </div>
            </div>
            <div style="width: 280px; height: 180px;">
              <div id="downloadCardEcharts4" style="width: 260px; height: 180px; ">

              </div>
            </div>
          </div>
        </div>

        <div class="bbb" style="color:#333;font-size:14px;font-weight:600;text-align:end;">锐仕方达人才集团有限公司</div>


      </div>
    </div>
  </div>

  
  <div class="modal fade bs-modal-sm index-staffcard staff_cards" id="NewCard" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
    <div class="modal-dialog modal-sm" style="width:600px;">
      <div class="modal-content" v-loading="NewCardLoading" element-loading-text="正在加载中" style="background: none; border: none; border-top-left-radius: 12px !important; border-top-right-radius: 12px !important; ">
        <div class="modal-body" style="padding:0;">
          <div class="newCardD-cont">
            <i class="close-newCard" @click="closeNewCard">
              <svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                  <g id="画板备份" transform="translate(-819.000000, -1098.000000)">
                    <g id="编组备份-5" transform="translate(819.000000, 1098.000000)">
                      <path d="M9,16.7142857 C13.2604843,16.7142857 16.7142857,13.2604843 16.7142857,9 C16.7142857,4.73951571 13.2604843,1.28571429 9,1.28571429 C4.73951571,1.28571429 1.28571429,4.73951571 1.28571429,9 C1.28571429,13.2604843 4.73951571,16.7142857 9,16.7142857 Z" id="路径" fill-opacity="0.194356425" fill="#000000"></path>
                      <line x1="11.18187" y1="6.81809143" x2="6.81801429" y2="11.1819471" id="路径" stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round"></line>
                      <line x1="6.81813" y1="6.81809143" x2="11.1819857" y2="11.1819471" id="路径" stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round"></line>
                    </g>
                  </g>
                </g>
              </svg>
            </i>

            <div class="newCardD-top">
              <div class="newCardD-top-img">
                <img src="" class="imgclass" width="100%" height="100%" style="border-radius:50% !important;cursor:pointer;" @click="jumpZhuye(basic.staffId)" alt="Alternate Text" />
                <i class="newCardD-top-grade">V{{basic.businessLevel}}</i>
              </div>
              <div class="newCardD-top-information">
                <div class="newCard-people">
                  <p class="newCard-name p" style="cursor: pointer;" @click="jumpZhuye(basic.staffId)">
                    {{basic.staffName}}({{basic.enName}})
                  </p>
                  <p class="newCard-position p" style="margin-left: 10px;">{{basic.positionName}} | &nbsp;</p>
                  <p class="newCard-company p" style="margin-left: 4px;"> {{basic.companyName}}</p>
                </div>
                <div class="newCard-phone">

                  <span class="sc-con sc-phone next-phone" style="display:none;">
                    <span class="sc-txt"></span>
                    <a style="display:inline-block;" href="javascript:void(0);" class="rnss-callphone show-phone" :data-transfernumber="basic.mobilePhone" data-usnumber="111" data-ustel="111" title="点击呼叫">
                      <i style="width:16px;height:16px;display:inline-block;">
                        <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <g id="画板备份" transform="translate(-183.000000, -1156.000000)" fill-rule="nonzero">
                              <g id="编组-21备份-3" transform="translate(71.000000, 1098.000000)">
                                <g id="电话" transform="translate(112.000000, 58.000000)">
                                  <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="16" height="16"></rect>
                                  <path d="M5.8625,7.5125 C6.425,8.65 7.3375,9.5875 8.45,10.1625 L9.3125,9.275 C9.4125,9.1625 9.575,9.125 9.7125,9.175 C10.15,9.325 10.625,9.4 11.1125,9.4 C11.325,9.4 11.5,9.5875 11.5,9.8 L11.5,11.2 C11.5,11.425 11.325,11.6 11.1125,11.6 C7.425,11.6 4.4375,8.5375 4.4375,4.75 C4.4375,4.525 4.6125,4.35 4.825,4.35 L6.2,4.35 C6.4125,4.35 6.5875,4.525 6.5875,4.75 C6.5875,5.25 6.6625,5.7375 6.8125,6.2 C6.85,6.3375 6.825,6.5 6.7125,6.6125 L5.8625,7.5125 L5.8625,7.5125 Z M8,2 C4.6875,2 2,4.6875 2,8 C2,11.3125 4.6875,14 8,14 C11.3125,14 14,11.3125 14,8 C14,4.6875 11.3125,2 8,2 M8,1 C11.8625,1 15,4.1375 15,8 C15,11.8625 11.8625,15 8,15 C4.1375,15 1,11.8625 1,8 C1,4.1375 4.1375,1 8,1 Z" id="形状" fill="#FFFFFF"></path>
                                </g>
                              </g>
                            </g>
                          </g>
                        </svg>
                      </i>
                    </a>
                  </span>
                  <span class="sc-con sc-phone all-titphone" style="color: #fff; width: 100%; display: block">
                    <span class="sc-txt-view" style="color: #fff; text-decoration: underline; cursor: pointer;" :data-StaffId="basic.staffId">点击查看</span>
                  </span>
                </div>
                <div class="newCard-email">
                  <i style="width:16px;height:16px;display:inline-block;">
                    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g id="画板备份" transform="translate(-183.000000, -1186.000000)" fill-rule="nonzero">
                          <g id="编组-21备份-3" transform="translate(71.000000, 1098.000000)">
                            <g id="邮件" transform="translate(112.000000, 88.000000)">
                              <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="16" height="16"></rect>
                              <path d="M13.4484375,3.009375 L2.5578125,3.009375 C2.128125,3.009375 1.7796875,3.3578125 1.7796875,3.7875 L1.7796875,12.1875 C1.7796875,12.6171875 2.128125,12.965625 2.5578125,12.965625 L13.4484375,12.965625 C13.50625,12.965625 13.5640625,12.959375 13.6203125,12.946875 C13.71875,12.946875 13.8125,12.9109375 13.884375,12.8453125 L13.884375,12.821875 C14.0953125,12.6796875 14.2234375,12.4421875 14.2265625,12.1875 L14.2265625,3.7859375 C14.2265625,3.35625 13.878125,3.009375 13.4484375,3.009375 L13.4484375,3.009375 Z M8.003125,8.6875 L2.6546875,3.884375 L13.3828125,3.884375 L8.003125,8.6875 Z M2.5578125,4.840625 L6.14375,8.065625 L2.5578125,11.6390625 L2.5578125,4.840625 Z M6.7234375,8.5703125 L7.7578125,9.5 C7.9046875,9.63125 8.128125,9.63125 8.275,9.5 L9.3765625,8.515625 L12.7484375,12.1875 L3.10625,12.1875 L6.7234375,8.5703125 Z M9.946875,7.9953125 L13.446875,4.884375 L13.446875,11.8078125 L9.946875,7.9953125 Z" id="形状" fill="#FFFFFF"></path>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </i>
                  {{basic.email}}
                </div>
                <div class="newCard-feedback">
                  <span class="newCard-span">反馈效率 {{basic.recommendInterviewDay}} 天</span>
                  <span class="newCard-span">推荐offer率 {{Number(basic.recommendOffer).toFixed(2)}}%</span>
                </div>

                <div class="newCard-top-btn">
                  
                  <el-button class="newCard-call showTeam-item" :data-id="basic.staffId">加入团队</el-button>
                  <el-button class="newCard-btn" onclick="app_2.showModeFn()">评价</el-button>
                </div>
              </div>
              <div class="newCardD-top-download">
                <div class="download-img">
                  <img :src="basic.weComQrCode" width="100%" height="100%" />
                </div>
                <i style="width:16px;height:16px;display:none;">
                  <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="vertical-align:top;">
                    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                      <g id="画板备份" transform="translate(-699.000000, -1254.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <g id="编组-21备份-3" transform="translate(71.000000, 1098.000000)">
                          <g id="编组-11" transform="translate(628.000000, 154.000000)">
                            <g id="下载" transform="translate(0.000000, 2.000000)">
                              <rect id="矩形" opacity="0" x="0" y="0" width="16" height="16"></rect>
                              <path d="M13.4828125,14.75 L2.4828125,14.75 C1.93052775,14.75 1.4828125,14.3022847 1.4828125,13.75 L1.4828125,3.75 C1.4828125,3.19771525 1.93052775,2.75 2.4828125,2.75 L5,2.75 C5.27614237,2.75 5.5,2.97385763 5.5,3.25 C5.5,3.52614237 5.27614237,3.75 5,3.75 L3.4828125,3.75 C2.93052775,3.75 2.4828125,4.19771525 2.4828125,4.75 L2.4828125,12.75 C2.4828125,13.3022847 2.93052775,13.75 3.4828125,13.75 L12.4828125,13.75 C13.0350972,13.75 13.4828125,13.3022847 13.4828125,12.75 L13.4828125,4.75 C13.4828125,4.19771525 13.0350972,3.75 12.4828125,3.75 L11,3.75 C10.7238576,3.75 10.5,3.52614237 10.5,3.25 C10.5,2.97385763 10.7238576,2.75 11,2.75 L13.4828125,2.75 C14.0350972,2.75 14.4828125,3.19771525 14.4828125,3.75 L14.4828125,13.75 C14.4828125,14.3022847 14.0350972,14.75 13.4828125,14.75 Z M7.64644661,10.7245716 L5.525,8.603125 C5.32997445,8.40809945 5.32997445,8.09190055 5.525,7.896875 C5.72004659,7.70182841 6.03626163,7.70177018 6.23138004,7.89674491 L8,9.6640625 L8,9.6640625 L9.76861996,7.89674491 C9.96373837,7.70177018 10.2799534,7.70182841 10.475,7.896875 C10.6700256,8.09190055 10.6700256,8.40809945 10.475,8.603125 L8.35355339,10.7245716 C8.15829124,10.9198338 7.84170876,10.9198338 7.64644661,10.7245716 Z M7.4994375,9.8786875 L7.4994375,1.8786875 C7.4994375,1.60254513 7.72329513,1.3786875 7.9994375,1.3786875 C8.27557987,1.3786875 8.4994375,1.60254513 8.4994375,1.8786875 L8.4994375,9.8786875 L8.4994375,9.8786875 L7.4994375,9.8786875 Z" id="形状"></path>
                            </g>
                          </g>
                        </g>
                      </g>
                    </g>
                  </svg>
                </i>
                <a onclick="downloadCard()" download="名片" style="color:#fff;display:none;">下载名片</a>

              </div>
            </div>

            <div class="newCard-moddle">
              <div>
                <el-tabs v-model="activeName" @tab-click="handleClick" class="newCard-tab">
                  <el-tab-pane label="个人介绍" name="first">
                    <div class="tab-pad">
                      <div class="newCard-introduce">
                        <div class="newCard-introduce-cont">
                          <img src="/images/newCard-introduce-1.png" width="38px" height="38px" alt="Alternate Text" />
                          <div>
                            <p class="newCard-introduce-p p">成功案例</p>
                            <p class="newCard-introduce-p p">
                              <span style="color: #46ADFE;font-weight:600;" v-if="basic.successCase">{{basic.successCase}}</span>个
                              <span style="color: #46ADFE; font-weight: 600;" v-else>0</span>个
                            </p>
                          </div>
                        </div>
                        <div class="newCard-introduce-cont">
                          <img src="/images/newCard-introduce-2.png" width="38px" height="38px" alt="Alternate Text" />
                          <div>
                            <p class="newCard-introduce-p p">成功合作</p>
                            <p class="newCard-introduce-p p">
                              <span style="color: #01B86C; font-weight: 600;" v-if="basic.cooperationCount">{{basic.cooperationCount}}</span>个
                              <span style="color: #01B86C; font-weight: 600;" v-else>0</span>个
                            </p>
                          </div>
                        </div>
                        <div class="newCard-introduce-cont">
                          <img src="/images/newCard-introduce-3.png" width="38px" height="38px" alt="Alternate Text" />
                          <div>
                            <p class="newCard-introduce-p p">合作中的职位</p>
                            <p class="newCard-introduce-p p">
                              <span style="color: #FE8447; font-weight: 600;" v-if="basic.cooperationingCount">{{basic.cooperationingCount}}</span>个
                              <span style="color: #FE8447; font-weight: 600;" v-else>0</span>个
                            </p>
                          </div>
                        </div>
                        <div class="newCard-introduce-cont">
                          <img src="/images/newCard-introduce-4.png" width="38px" height="38px" alt="Alternate Text" />
                          <div>
                            <p class="newCard-introduce-p p">平均成单周期</p>
                            <p class="newCard-introduce-p p">
                              <span style="color: #9872FC; font-weight: 600;" v-if="basic.avgCycle">{{basic.avgCycle}}</span>个
                              <span style="color: #9872FC; font-weight: 600;" v-else>0</span>天
                            </p>
                          </div>
                        </div>
                      </div>


                      <div class="newCard-type">
                        <div class="newCard-type-top">
                          <i class="newCard-type-line"></i>
                          <span class="newCard-font">偏好行业</span>
                        </div>
                        <div style="padding-left: 7px;margin-top:14px;">
                          <span class="newCard-type-tag" v-for="item in basic.goodIndustrys">{{item.text}}</span>
                        </div>
                      </div>

                      <div class="newCard-type">
                        <div class="newCard-type-top">
                          <i class="newCard-type-line"></i>
                          <span class="newCard-font">偏好职类</span>
                        </div>
                        <div style="padding-left: 7px;margin-top:14px;">
                          <span class="newCard-type-tag" v-for="item in basic.goodOccupations">{{item.text}}</span>
                        </div>
                      </div>

                      <div class="newCard-type">
                        <div class="newCard-type-top">
                          <i class="newCard-type-line"></i>
                          <span class="newCard-font">偏好地区/年薪</span>
                        </div>
                        <div style="padding-left: 7px;margin-top:14px;">
                          <span class="newCard-type-tag" v-for="item in basic.goodLocations">{{item.text}}</span>
                          <span class="newCard-type-tag" v-if="basic.goodSalary">{{basic.goodSalary}}</span>
                        </div>
                      </div>

                      <div class="newCard-type">
                        <div class="newCard-type-top">
                          <i class="newCard-type-line"></i>
                          <span class="newCard-font">专业标签</span>
                        </div>
                        <div style="padding-left: 7px;">
                          <span class="newCard-type-tag" style="margin-top: 14px;" v-for="item in basic.professionalLabels">{{item.text}}</span>
                        </div>
                      </div>

                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="成功案例" name="second">
                    <div class="tab-pad">
                      <div class="newCard-type-top">
                        <i class="newCard-type-line"></i>
                        <span class="newCard-font">案例分析</span>
                      </div>

                      <div class="newCard-echarts" v-if="echartsNoData">
                        <div class="echarts-top">
                          <div style="width: 280px; height: 180px; border-right: 1px solid #ddd; border-bottom: 1px solid #ddd;">
                            <div id="newCardEcharts" style="width: 260px; height: 180px; ">

                            </div>
                          </div>
                          <div style="width: 280px; height: 180px; border-bottom: 1px solid #ddd;">
                            <div id="newCardEcharts2" style="width: 260px; height: 180px; ">

                            </div>
                          </div>
                        </div>
                        <div class="echarts-bottom">
                          <div style="width: 280px; height: 180px; border-right: 1px solid #ddd;">
                            <div id="newCardEcharts3" style="width: 260px; height: 180px; ">

                            </div>
                          </div>
                          <div style="width: 280px; height: 180px;">
                            <div id="newCardEcharts4" style="width: 260px; height: 180px; ">

                            </div>
                          </div>
                        </div>

                      </div>
                      <div v-else>
                        <div class="noData" style="height: 140px;">
                          <div class="noData-content">
                            <img src="../images/h-nodata.png" class="h-nodata">
                            <p class="noData-content-p">暂无数据~</p>
                          </div>
                        </div>
                      </div>

                      <div class="newCard-type-top">
                        <i class="newCard-type-line"></i>
                        <span class="newCard-font">成功案例</span>
                      </div>

                      <div>
                        <el-table :data="AnliTablData"
                                  :header-cell-style="{background:'#2a7fcc',color:'#fff',border: '0.2px solid #ddd',height:'40px',padding:'0'}"
                                  :row-style="{height:'40px'}"
                                  :cell-style="{padding:'0px'}"
                                  class="cardTable"
                                  style="width: 100%;">
                          <el-table-column prop="JobTitle"
                                           label="职位名称"
                                           show-overflow-tooltip
                                           width="120">
                            <template slot-scope="scope">
                              <span style="color: #2A7FCC;cursor:pointer;" v-if="scope.row.jobTitle" @click="cardJobClick(scope.row)">{{scope.row.jobTitle}}</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="clientName"
                                           label="服务客户"
                                           show-overflow-tooltip
                                           width="160">
                            <template slot-scope="scope">
                              <span v-if="scope.row.clientName">{{scope.row.clientName}}</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="jobCandidateName"
                                           label="上岗人选"
                                           show-overflow-tooltip
                                           width="84">
                            <template slot-scope="scope">
                              <span v-if="scope.row.jobCandidateName">{{scope.row.jobCandidateName}}</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="salary"
                                           label="人选年薪"
                                           show-overflow-tooltip
                                           width="84">
                            <template slot-scope="scope">
                              <span v-if="scope.row.salary">{{scope.row.salary}}</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="nativeLocationText"
                                           label="工作地点"
                                           show-overflow-tooltip
                                           width="84">
                            <template slot-scope="scope">
                              <span v-if="scope.row.nativeLocationText">{{scope.row.nativeLocationText}}</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="industryName"
                                           label="所在行业"
                                           show-overflow-tooltip
                                           width="126">
                            <template slot-scope="scope">
                              <span v-if="scope.row.industryName">{{scope.row.industryName}}</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="cycle"
                                           label="职位周期"
                                           show-overflow-tooltip
                                           width="84">
                            <template slot-scope="scope">
                              <span style="color: #FAAD14; width: 100%; display: block; text-align: center; " v-if="scope.row.cycle">{{scope.row.cycle}}天</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="entryDate"
                                           label="案例日期"
                                           show-overflow-tooltip
                                           width="124">
                            <template slot-scope="scope">
                              <span v-if="scope.row.entryDate">{{scope.row.entryDate}}</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>

                      <div class="newCard-page">
                        <p class="p">共<span>{{anliPageTotal}}</span>条</p>
                        <el-pagination @current-change="anliPageChange" :current-page="anliPageIndex" :page-sizes="[10]"
                                       :page-size="anliPageSize" background layout="prev, pager, next" :total="anliPageTotal">
                        </el-pagination>
                      </div>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="职位需求" name="third">
                    <div class="tab-pad">
                      <div class="need-position">
                        <div style="width:160px;">职位需求标签分类：</div>
                        <div style="width:548px;" class="newCard-position-tag">
                          <span class="need-position-tag" v-for="item in jobTagsData">
                            <i style="width:14px;height:14px;display:inline-block;">
                              <svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="vertical-align:sub;">
                                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                  <g id="画板备份" transform="translate(-1924.000000, -1377.000000)">
                                    <g id="标签" transform="translate(1914.000000, 1372.000000)">
                                      <g id="编组备份-3" transform="translate(10.000000, 5.000000)">
                                        <rect id="矩形" x="0" y="0" width="14" height="14"></rect>
                                        <g id="编组-9" transform="translate(2.000000, 2.000000)">
                                          <path d="M9.70273413,6.03280616 L6.03791748,9.69762281 C5.84618617,9.88958128 5.58599157,9.99743014 5.31466566,9.99743014 C5.04333974,9.99743014 4.78317353,9.88958128 4.59141383,9.69762281 L0,5.1113203 L0,0 L5.1113203,0 L9.70273413,4.59141383 C10.0990886,4.99012521 10.0990886,5.63409478 9.70273413,6.03280616 Z" id="路径" stroke="#003683" stroke-linejoin="round"></path>
                                          <path d="M2.98160351,3.6915091 C3.37367017,3.6915091 3.6915091,3.37367017 3.6915091,2.98160351 C3.6915091,2.58953684 3.37367017,2.27169791 2.98160351,2.27169791 C2.58953684,2.27169791 2.27169791,2.58953684 2.27169791,2.98160351 C2.27169791,3.37367017 2.58953684,3.6915091 2.98160351,3.6915091 Z" id="路径" fill="#003683"></path>
                                        </g>
                                      </g>
                                    </g>
                                  </g>
                                </g>
                              </svg>
                            </i>
                            {{item.Name}}({{item.Count}})
                          </span>
                        </div>

                        <div style="width:433px;" class="newCard-hover-tag">
                          <span class="need-position-tag" v-for="item in jobTagsData">
                            <i style="width:14px;height:14px;display:inline-block;">
                              <svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="vertical-align:sub;">
                                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                  <g id="画板备份" transform="translate(-1924.000000, -1377.000000)">
                                    <g id="标签" transform="translate(1914.000000, 1372.000000)">
                                      <g id="编组备份-3" transform="translate(10.000000, 5.000000)">
                                        <rect id="矩形" x="0" y="0" width="14" height="14"></rect>
                                        <g id="编组-9" transform="translate(2.000000, 2.000000)">
                                          <path d="M9.70273413,6.03280616 L6.03791748,9.69762281 C5.84618617,9.88958128 5.58599157,9.99743014 5.31466566,9.99743014 C5.04333974,9.99743014 4.78317353,9.88958128 4.59141383,9.69762281 L0,5.1113203 L0,0 L5.1113203,0 L9.70273413,4.59141383 C10.0990886,4.99012521 10.0990886,5.63409478 9.70273413,6.03280616 Z" id="路径" stroke="#003683" stroke-linejoin="round"></path>
                                          <path d="M2.98160351,3.6915091 C3.37367017,3.6915091 3.6915091,3.37367017 3.6915091,2.98160351 C3.6915091,2.58953684 3.37367017,2.27169791 2.98160351,2.27169791 C2.58953684,2.27169791 2.27169791,2.58953684 2.27169791,2.98160351 C2.27169791,3.37367017 2.58953684,3.6915091 2.98160351,3.6915091 Z" id="路径" fill="#003683"></path>
                                        </g>
                                      </g>
                                    </g>
                                  </g>
                                </g>
                              </svg>
                            </i>
                            {{item.Name}}({{item.Count}})
                          </span>
                        </div>
                      </div>

                      <div class="newCard-echarts" v-if="echartsNoData2">
                        <div class="echarts-top">
                          <div style="width: 280px; height: 220px; border-right: 1px solid #ddd;">
                            <div id="newCardEcharts5" style="width: 260px; height: 220px; ">

                            </div>
                          </div>
                          <div style="width: 280px; height: 220px;">
                            <div id="newCardEcharts6" style="width: 260px; height:220px; ">

                            </div>
                          </div>
                        </div>
                      </div>
                      <div v-else>
                        <div class="noData" style="height: 140px;">
                          <div class="noData-content">
                            <img src="../images/h-nodata.png" class="h-nodata">
                            <p class="noData-content-p">暂无数据~</p>
                          </div>
                        </div>
                      </div>

                      <div style="margin-top:20px;">
                        <el-table :data="jobTable"
                                  :header-cell-style="{background:'#2a7fcc',color:'#fff',border: '0.2px solid #ddd',height:'40px',padding:'0'}"
                                  :row-style="{height:'40px'}"
                                  :cell-style="{padding:'0px'}"
                                  class="cardTable"
                                  style="width: 100%;">
                          <el-table-column prop="JobTitle"
                                           label="职位名称"
                                           show-overflow-tooltip
                                           width="120">
                            <template slot-scope="scope">
                              <span style="color: #2A7FCC;cursor:pointer;" @click="cardJobClick(scope.row)" v-if="scope.row.JobTitle" :title="scope.row.JobTitle">{{scope.row.JobTitle}}</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="Salay"
                                           label="职位年薪"
                                           show-overflow-tooltip
                                           width="84">
                            <template slot-scope="scope">
                              <span style="color: #FAAD14;cursor:pointer;" v-if="scope.row.Salay" :title="scope.row.Salay">{{scope.row.Salay}}</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="Location"
                                           label="地点"
                                           show-overflow-tooltip
                                           width="80">
                            <template slot-scope="scope">
                              <span v-if="scope.row.Location">{{scope.row.Location}}</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="NumberOfHiring"
                                           label="招聘人数"
                                           show-overflow-tooltip
                                           width="84">
                            <template slot-scope="scope">
                              <span style="color: #FAAD14; width: 100%; display: block; text-align: center; " v-if="scope.row.NumberOfHiring">{{scope.row.NumberOfHiring}}人</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="ClientName"
                                           label="招聘企业"
                                           show-overflow-tooltip
                                           width="170">
                            <template slot-scope="scope">
                              <span style="color: #FAAD14;" v-if="scope.row.ClientName" :title="scope.row.ClientName">{{scope.row.ClientName}}</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="TuiJianNumber"
                                           label="已推"
                                           show-overflow-tooltip
                                           width="56">
                            <template slot-scope="scope">
                              <span style="color: #FAAD14; width: 100%; display: block; text-align: center; " v-if="scope.row.TuiJianNumber">{{scope.row.TuiJianNumber}}</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="ChiefManagerName"
                                           label="执行团队"
                                           show-overflow-tooltip
                                           width="84">
                            <template slot-scope="scope">
                              <span v-if="scope.row.ChiefManagerName">{{scope.row.ChiefManagerName}}</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="CompanyName"
                                           label="归属公司"
                                           show-overflow-tooltip
                                           width="98">
                            <template slot-scope="scope">
                              <span v-if="scope.row.CompanyName">{{scope.row.CompanyName}}</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="LastUpdated"
                                           label="更新时间"
                                           show-overflow-tooltip
                                           width="124">
                            <template slot-scope="scope">
                              <span v-if="scope.row.LastUpdated">{{scope.row.LastUpdated}}</span>
                              <span v-else style="width: 100%; text-align: center; display: inline-block;">--</span>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                      <div class="newCard-page">
                        <p class="p">共<span>{{jobTotal}}</span>条</p>
                        <el-pagination @current-change="jobPageChange" :current-page="jobPageIndex" :page-sizes="[10]"
                                       :page-size="jobPageSize" background layout="prev, pager, next" :total="jobTotal">
                        </el-pagination>
                      </div>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="人选资源" name="fourth">
                    <div class="tab-pad">
                      <div class="need-position">
                        <div style="width:160px;">人选资源标签分类：</div>
                        <div style="width:548px;" class="newCard-position-tag">
                          <span class="need-position-tag" v-for="item in resumeTagsData">
                            <i style="width:14px;height:14px;display:inline-block;">
                              <svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="vertical-align:sub;">
                                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                  <g id="画板备份" transform="translate(-1924.000000, -1377.000000)">
                                    <g id="标签" transform="translate(1914.000000, 1372.000000)">
                                      <g id="编组备份-3" transform="translate(10.000000, 5.000000)">
                                        <rect id="矩形" x="0" y="0" width="14" height="14"></rect>
                                        <g id="编组-9" transform="translate(2.000000, 2.000000)">
                                          <path d="M9.70273413,6.03280616 L6.03791748,9.69762281 C5.84618617,9.88958128 5.58599157,9.99743014 5.31466566,9.99743014 C5.04333974,9.99743014 4.78317353,9.88958128 4.59141383,9.69762281 L0,5.1113203 L0,0 L5.1113203,0 L9.70273413,4.59141383 C10.0990886,4.99012521 10.0990886,5.63409478 9.70273413,6.03280616 Z" id="路径" stroke="#003683" stroke-linejoin="round"></path>
                                          <path d="M2.98160351,3.6915091 C3.37367017,3.6915091 3.6915091,3.37367017 3.6915091,2.98160351 C3.6915091,2.58953684 3.37367017,2.27169791 2.98160351,2.27169791 C2.58953684,2.27169791 2.27169791,2.58953684 2.27169791,2.98160351 C2.27169791,3.37367017 2.58953684,3.6915091 2.98160351,3.6915091 Z" id="路径" fill="#003683"></path>
                                        </g>
                                      </g>
                                    </g>
                                  </g>
                                </g>
                              </svg>
                            </i>
                            {{item.Name}}({{item.Count}})
                          </span>
                        </div>

                        <div style="width:433px;" class="newCard-hover-tag">
                          <span class="need-position-tag" v-for="item in resumeTagsData">
                            <i style="width:14px;height:14px;display:inline-block;">
                              <svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="vertical-align:sub;">
                                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                  <g id="画板备份" transform="translate(-1924.000000, -1377.000000)">
                                    <g id="标签" transform="translate(1914.000000, 1372.000000)">
                                      <g id="编组备份-3" transform="translate(10.000000, 5.000000)">
                                        <rect id="矩形" x="0" y="0" width="14" height="14"></rect>
                                        <g id="编组-9" transform="translate(2.000000, 2.000000)">
                                          <path d="M9.70273413,6.03280616 L6.03791748,9.69762281 C5.84618617,9.88958128 5.58599157,9.99743014 5.31466566,9.99743014 C5.04333974,9.99743014 4.78317353,9.88958128 4.59141383,9.69762281 L0,5.1113203 L0,0 L5.1113203,0 L9.70273413,4.59141383 C10.0990886,4.99012521 10.0990886,5.63409478 9.70273413,6.03280616 Z" id="路径" stroke="#003683" stroke-linejoin="round"></path>
                                          <path d="M2.98160351,3.6915091 C3.37367017,3.6915091 3.6915091,3.37367017 3.6915091,2.98160351 C3.6915091,2.58953684 3.37367017,2.27169791 2.98160351,2.27169791 C2.58953684,2.27169791 2.27169791,2.58953684 2.27169791,2.98160351 C2.27169791,3.37367017 2.58953684,3.6915091 2.98160351,3.6915091 Z" id="路径" fill="#003683"></path>
                                        </g>
                                      </g>
                                    </g>
                                  </g>
                                </g>
                              </svg>
                            </i>
                            {{item.Name}}({{item.Count}})
                          </span>
                        </div>

                      </div>

                      <div style="margin-top:4px;">
                        <el-table :data="peopleData"
                                  :header-cell-style="{background:'#2a7fcc',color:'#fff',border: '0.2px solid #ddd',height:'40px',padding:'0'}"
                                  :row-style="{height:'40px'}"
                                  :cell-style="{padding:'0px'}"
                                  class="cardTable"
                                  style="width: 100%;">
                          <el-table-column prop="Name"
                                           label="姓名"
                                           show-overflow-tooltip
                                           width="96">
                            <template slot-scope="scope">
                              <span style="position: relative; display: block; overflow: hidden; text-overflow: ellipsis; padding-right: 22px;">
                                <span style="color: #2A7FCC;cursor:pointer;" @click="cardNameClick(scope.row)">{{scope.row.Name}}</span>
                                <i class="ageIcon" v-if="scope.row.Gender==1">
                                  <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                      <g id="画板备份" transform="translate(-1344.000000, -3307.000000)" fill="#006CBE" fill-rule="nonzero">
                                        <g id="编组-19备份-2" transform="translate(1278.000000, 3255.000000)">
                                          <g id="编组" transform="translate(66.000000, 52.000000)">
                                            <path d="M13.239375,1.25 L11.0416667,1.25 C10.6964887,1.25 10.4166667,0.970177969 10.4166667,0.625 C10.4166667,0.279822031 10.6964887,-4.4408921e-16 11.0416667,-4.4408921e-16 L14.7916667,-4.4408921e-16 C15.1368446,-4.4408921e-16 15.4166667,0.279822031 15.4166667,0.625 L15.4166667,4.375 C15.4166667,4.72017797 15.1368446,5 14.7916667,5 C14.4464887,5 14.1666667,4.72017797 14.1666667,4.375 L14.1666667,2.09041668 L12.0989583,4.158125 C13.1666423,5.4023668 13.7524803,6.98837999 13.75,8.62791668 C13.75,12.4247917 10.671875,15.5029167 6.875,15.5029167 C3.078125,15.5029167 4.4408921e-16,12.4247917 4.4408921e-16,8.62791668 C4.4408921e-16,4.83104168 3.078125,1.75291668 6.875,1.75291668 C8.4511991,1.75057637 9.97993195,2.29216355 11.203125,3.28625 L13.239375,1.25 Z M6.875,14.2529167 C9.98166666,14.2529167 12.5,11.7345833 12.5,8.62791668 C12.5,5.52125002 9.98166668,3.00291668 6.875,3.00291668 C3.76833332,3.00291668 1.25,5.52125 1.25,8.62791668 C1.25,11.7345834 3.76833334,14.2529167 6.875,14.2529167 Z" id="形状"></path>
                                          </g>
                                        </g>
                                      </g>
                                    </g>
                                  </svg>
                                </i>
                                <i class="ageIcon" v-else>
                                  <svg width="13px" height="17px" viewBox="0 0 13 17" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                      <g id="画板备份" transform="translate(-1345.000000, -3347.000000)" fill="#E0206A" fill-rule="nonzero">
                                        <g id="编组-19备份-2" transform="translate(1278.000000, 3255.000000)">
                                          <g id="编组" transform="translate(67.000000, 92.000000)">
                                            <path d="M5.72916666,13.3333333 L5.72916666,12.4785417 C2.52104166,12.2139583 0,9.52645834 0,6.25 C0,2.798125 2.798125,-8.32667268e-16 6.25,-8.32667268e-16 C9.701875,-8.32667268e-16 12.5,2.798125 12.5,6.25 C12.5,9.45520834 10.0875,12.0966667 6.97916666,12.4579167 L6.97916666,13.3333333 L8.75,13.3333333 C9.09517797,13.3333333 9.375,13.6131554 9.375,13.9583333 C9.375,14.3035113 9.09517797,14.5833333 8.75,14.5833333 L6.97916666,14.5833333 L6.97916666,16.09375 C6.97916666,16.438928 6.69934463,16.71875 6.35416666,16.71875 C6.00898869,16.71875 5.72916666,16.438928 5.72916666,16.09375 L5.72916666,14.5833333 L3.75,14.5833333 C3.40482203,14.5833333 3.125,14.3035113 3.125,13.9583333 C3.125,13.6131554 3.40482203,13.3333333 3.75,13.3333333 L5.72916666,13.3333333 Z M6.25,11.25 C9.01145834,11.25 11.25,9.01145834 11.25,6.25 C11.25,3.48854166 9.01145834,1.25 6.25,1.25 C3.48854166,1.25 1.25,3.48854168 1.25,6.25 C1.25,9.01145832 3.48854166,11.25 6.25,11.25 Z" id="形状"></path>
                                          </g>
                                        </g>
                                      </g>
                                    </g>
                                  </svg>
                                </i>
                              </span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="RecentJobTitle"
                                           label="职位名称"
                                           show-overflow-tooltip
                                           width="110">
                            <template slot-scope="scope">
                              <span style="color: #2A7FCC; cursor: pointer;" @click="cardNameClick(scope.row)" :title="scope.row.RecentJobTitle">{{scope.row.RecentJobTitle}}</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="Age"
                                           label="年龄"
                                           show-overflow-tooltip
                                           width="56">
                            <template slot-scope="scope">
                              <span style="color: #FAAD14;">{{scope.row.Age}}岁</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="location"
                                           label="居住地"
                                           show-overflow-tooltip
                                           width="84">
                          </el-table-column>
                          <el-table-column prop="YearsExperience"
                                           label="工龄"
                                           show-overflow-tooltip
                                           width="56">
                            <template slot-scope="scope">
                              <span>{{scope.row.YearsExperience}}年</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="EducationLevel"
                                           label="学历"
                                           show-overflow-tooltip
                                           width="70">
                          </el-table-column>
                          <el-table-column prop="RecentCompany"
                                           label="公司"
                                           show-overflow-tooltip
                                           width="168">
                            <template slot-scope="scope">
                              <span style="color: #FAAD14;" :title="scope.row.RecentCompany">{{scope.row.RecentCompany}}</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="zip"
                                           label="记录"
                                           show-overflow-tooltip
                                           width="56">
                            <template slot-scope="scope">
                              <el-popover trigger="hover" placement="top">
                                <div slot="reference" class="name-wrapper" @hover="hoverTable(scope.row)">
                                  <div class="numCricle" :id="scope.row.ResumeId" onmouseover="ResumeOver(this.id)" onmouseout="ResumeOut(this.id)" style=" margin: 0 auto;display:block;">{{scope.row.MemoCount}}</div>
                                </div>
                                <div class="hover-table" v-html="tableDataa"></div>
                              </el-popover>
                            </template>
                          </el-table-column>
                          <el-table-column prop="Lastupdated"
                                           label="更新时间"
                                           show-overflow-tooltip
                                           width="124">
                          </el-table-column>
                        </el-table>
                      </div>
                      <div class="newCard-page">
                        <p class="p">共<span>{{peopleTotal}}</span>条</p>
                        <el-pagination @current-change="peoplePageChange" :current-page="peoplePageIndex" :page-sizes="[10]"
                                       :page-size="peoplePageSize" background layout="prev, pager, next" :total="peopleTotal">
                        </el-pagination>
                      </div>


                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade bs-modal-sm index-staffcard staff_cards" id="addTeamShow" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
    <div style="width:554px;background:#fff;margin:0 auto;">
      <div class="publisHead">
        <span class="publisHead-font">添加团队成员</span>
        <span class="publisHead-close" @click="closeAddTeam">×</span>
      </div>
      <div class="addTema-cont">
        <p style="font-size:12px;color:#999;margin-bottom:14px;margin-top:0;">提示：将此顾问添加至需要合作职位的执行团队</p>
        <div class="project-flex" style="align-items:center;">
          <div class="goodFont" style="width:72px;font-size:14px;color:#333;padding-top:0;">选择职位：</div>
          <div>
            <el-select v-model="projectVlu" filterable placeholder="请选择" @change="projectClick" class="project-Select">
              <el-option v-for="item in projectSelect"
                         :key="item.JobId"
                         :label="item.JobTitle"
                         :value="item.JobId">
              </el-option>
            </el-select>
          </div>
          
        </div>
        <div class="project-flex" style="align-items:center;">
          <div class="goodFont" style="width: 70px; font-size: 14px; color: #333; padding-top: 0;">选择员工：</div>

          <div class="cf selTeam tuijianstaff teamStaff" id="enterUserSel">
            
<form method="get">

  
  <div class="cf" >
    <span class="term-list-title"> </span>
    <div id="selTeam5" class="cf selTeam">
      <div id="selector_outerbox_0" class="cf outbox text-5 selector_outerbox">
        <div class="outbox-tip">请输入姓名</div>
        <ul class="outbox-item-list"></ul>
        <input type="text" id="selector_input_0" autocomplete="off" class="selector_input" style="height: 26px !important;padding: 2px 0px 0px 2px !important;margin-left: 4px; display: block;width: 12px;">
      </div>
      <span class="r-icon r-icon-135 addTeam" id="addTeam" title="点击可选择客户负责人"></span>
      <button class="btn btn-sm blue r-submit" type="submit" id="saveSubmit">确定</button>
    </div>
  </div>
</form>

            <span class="addTeam" title="点击可选择录入用户"></span>
          </div>
        </div>

        <div style="text-align:end;">
          <el-button class="addTeamBtn" @click="addTeamClick">确定</el-button>
        </div>
      </div>
    </div>
  </div>

  

  <script src="/js/bootstrap-star-rating.js"></script>
  <script type="text/javascript">
    var r_Lngs = 'zh';
    var path = "/locales/" + r_Lngs + "/translation.json";
    var jsonUrl = {
      searchUrl: 'https://es.risfond.com/api' + '/resume/search',
    };
    var config = {
      resGetPath: path,
      lng: r_Lngs,
      useCookie: false,
      lowerCaseLng: false,
      debug: false,
      useLocalStorage: true,
      resStore: false,
    };
    //$.i18n.init(config, function (err, t, d, c) {
    //  //var str = t('简历');
    //});
  </script>
  <script>
    //是否需要创建内部客户
      var pM_CanCreateInnerClient = false;
      var getApiUrl = 'https://staff-exam-api.risfond.com';
      var userstaffname = '顾开发ZJ1';
      var userstaffid = '18733';
      var usercompanyname = '南京公司';
      var datetime = '2025年04月17日 23时11分';
  </script>
  <script type="text/javascript">
    if (!$.browser) {
      $.browser = {};
    }
    $.browser.mozilla = /firefox/.test(navigator.userAgent.toLowerCase());
    $.browser.webkit = /webkit/.test(navigator.userAgent.toLowerCase());
    $.browser.opera = /opera/.test(navigator.userAgent.toLowerCase());
    $.browser.msie = /msie/.test(navigator.userAgent.toLowerCase());
    if (!$.fn.live) {
      $.fn.extend({
        live: function (types, data, fn) {
          jQuery(this.context).on(types, this.selector, data, fn);
          return this;
        },
        die: function (types, fn) {
          jQuery(this.context).off(types, this.selector || "**", fn);
          return this;
        }
      });
    }


    function ResumeOver(val) {
      console.log(val)
      resumeApp.tableDataId = val
      resumeApp.setMouseOut()
    }
    //获取div里面名为"qq"的鼠标移除是将其隐藏
    function ResumeOut(val) {
      console.log(val)
    }



  </script>
  <script>
    var PCChatUrl = 'https://elite-api.risfond.com:8015/';
  </script>
  <script type="text/javascript" src="/Scripts/bootstrap.js?cf1c9af1a6ae1477c31bcfd2bc5e19a2"></script>
<script type="text/javascript" src="/Scripts/respond.js?a0aca90a2d330c8a85059b3f042c650c"></script>


  <script type="text/javascript" src="/metronic/global/plugins/js.cookie.min.js?e27a03c88ae552007a14d5e0d5f27c34"></script>
<script type="text/javascript" src="/metronic/global/plugins/bootstrap-hover-dropdown/bootstrap-hover-dropdown.min.js?091df72fcbc32777fc7f4a7233eb3a20"></script>
<script type="text/javascript" src="/metronic/global/plugins/jquery-slimscroll/jquery.slimscroll.min.js?2da9262366b8ad0fa1054d1d73418a4a"></script>
<script type="text/javascript" src="/metronic/global/plugins/jquery.blockui.min.js?f96ebac0ed61197466d64b0da9c822e4"></script>
<script type="text/javascript" src="/metronic/global/plugins/uniform/jquery.uniform.min.js?a34fcb9003ae5024320bc84884b026af"></script>
<script type="text/javascript" src="/metronic/global/plugins/bootstrap-switch/js/bootstrap-switch.min.js?7ce5e522c406ca7a09a2a393f9d81cb6"></script>
<script type="text/javascript" src="/metronic/global/plugins/bootstrap-toastr/toastr.js?e237d5dfc30df6590e83e5c5e302733c"></script>
<script type="text/javascript" src="/metronic/global/plugins/bootbox/bootbox.min.js?********************************"></script>
<script type="text/javascript" src="/metronic/global/scripts/app.min.js?ba3ec161352dd6f2c0c30169860c6b92"></script>
<script type="text/javascript" src="/metronic/layouts/global/scripts/quick-sidebar.js?411d5363c843a3cc108bffefa754acf7"></script>
<script type="text/javascript" src="/metronic/layouts/layout4/scripts/layout.js?27dd42987ac36b3b07d06295196dc75b"></script>
<script type="text/javascript" src="/scripts/jquery-paged-scroll-custom.js?5a98c761bac69419c5d15fd52bad14ad"></script>
<script type="text/javascript" src="/static/js/rs_common.js?14bc324f5991a3dfa6622373c252b0f0"></script>
<script type="text/javascript" src="/static/util.js?1ab9d0bd6803228486ed59c5bf0b4be3"></script>
<script type="text/javascript" src="/static/msgdialog/dialog_v1.js?d67c5b77ac11b8a9271ae1d38fd93c2d"></script>
<script type="text/javascript" src="/scripts/common2.js?f46d28dfd109f11ed0a9b61f2ec2d9eb"></script>
<script type="text/javascript" src="/static/QTip/jquery.qtip.js?bc324c9716565398e7a2d006e5161efc"></script>
<script type="text/javascript" src="/js/jquery.arttxtcount.js?e86a0fe2ff3bcc15fec49041ebcb665b"></script>
<script type="text/javascript" src="/js/modalityboxes.js?********************************"></script>
<script type="text/javascript" src="/js/sms_v1.js?15620111fc0947224c7b165ec11ca2a8"></script>
<script type="text/javascript" src="/js/sms_simplification.js?8d259573a3ee08ce3db98f5cdd37e3fb"></script>
<script type="text/javascript" src="/js/emailsettingconfig.js?aeaccb44bf56118b1f12dfd11b8fbeb9"></script>
<script type="text/javascript" src="/js/emaileditor.js?6c10635675a9cccd55afe0e46238bfe3"></script>
<script type="text/javascript" src="/js/emaileditor_simplification.js?d12c4bac4ba8a2585400cdd38fbff0a5"></script>
<script type="text/javascript" src="/js/telphonecall.js?0b0c82cb50fd1f1aef01792ebec7d04d"></script>
<script type="text/javascript" src="/Scripts/autoCompleteTeam.js?4e4692d36ab2d8a8a5cf71c1c424185f"></script>
<script type="text/javascript" src="/Scripts/global/globalMethods.js?c793a8a11994f968190f97a4390dcecf"></script>
<script type="text/javascript" src="/js/CompleteTeam.js?dd500996941f746865a618bb72ffdfe5"></script>
<script type="text/javascript" src="/Scripts/vue2.6.14.js?b218285324028cc9737add035787fb10"></script>
<script type="text/javascript" src="/metronic/global/plugins/easemob/webim/webim.config.js?f556c1323cbc1a264d55a854a85751e5"></script>
<script type="text/javascript" src="/metronic/global/plugins/easemob/webim/strophe-1.2.8.min.js?4b0add4f8266486ccbdda731586a519c"></script>
<script type="text/javascript" src="/metronic/global/plugins/easemob/webim/strophe-1.2.8-g.js?5b81cb020ef412c4db47bac4c551c1fb"></script>
<script type="text/javascript" src="/metronic/global/plugins/easemob/webim/Easemob-chat-3.6.3.js?0c4d96bc1dde4bdc15655b1ededcbe9c"></script>
<script type="text/javascript" src="/metronic/global/plugins/easemob/webim/websdk.shim.js?82e24364483f9259229fb7b3810eaee6"></script>
<script type="text/javascript" src="/metronic/global/plugins/easemob/webim/api.js?2dd0f5e3cb448c2ff0baafeeaa2eb17b"></script>
<script type="text/javascript" src="/Scripts/App/chatNew.js?706a0382d1ffc5c6e565b1d5f7002cc5"></script>
<script type="text/javascript" src="/Scripts/App/siteLayout.js?********************************"></script>


  <script>
    var inviteCoorporationVue = new Vue({
      el: '#inviteCoorporationModal',
      data: function () {
        return {
          receiveStaffId: null,
          receiveStaffName: '',
          selectedJobList: [],
          jobList: [],
        };
      },
      methods: {
        getUnselectedJob: function (jobId) {
          var selectedJobMap = {};
          this.selectedJobList.forEach(function (elem) {
            selectedJobMap[elem.jobId] = true;
          });

          var list = [];
          this.jobList.forEach(function (item) {
            if (jobId == item.jobId || !selectedJobMap[item.jobId]) {
              list.push(item);
            }
          });

          return list;
        },
        validate: function () {
          var item = this;
          var $this = this;
          var errMsg = "";
          var idObj = {};
          this.selectedJobList.forEach(function (elem, index) {
            // 职位不允许重复
            if (idObj[elem.jobId] == undefined) {
              idObj[elem.jobId] = true;
            } else {
              errMsg = "有选择重复的职位，请重新选择";
              isValid = false;
            }
          });

          errMsg && r_Layout_BeautAlert.done(errMsg, "hits", 3000);
          return errMsg;
        },
        submit: function () {
          if (this.validate()) {
            return;
          }

          r_Layout_BeautAlert.done("数据请求中，请稍后", "hits");
          $.ajax({
            type: "post",
            url: "/cooperation/AddJobCooperation",
            dataType: "json",
            data: {
              receiveStaffId: this.receiveStaffId,
              jobs: this.selectedJobList,
            },
            success: function (res) {
              if (res.success) {
                r_Layout_BeautAlert.done("邀请合作成功，您可以通过电话或者发私信的方式提示对方，以便尽快达成合作", "hits", 3000);
                $("#inviteCoorporationModal").modal("hide");
              } else {
                r_Layout_BeautAlert.done(res.message, "hits", 3000);
              }
            }
          });
        },
        removeJob: function (index) {
          var $this = this;
          r_Tools.r_Confirm({
            titleTxt: "确认信息",
            Txt: "确定要删除此合作职位？",
            successCallbcak: function () {
              $this.selectedJobList.splice(index, 1);
            },
            cancelCallback: function () { }
          });

        },
        addJob: function () {
          var jobList = this.getUnselectedJob();
          var firstJobData = jobList[0];
          var defaultRatio = 0.08;
          this.selectedJobList.push({
            jobId: firstJobData.jobId,
            ratio: defaultRatio,
            reckonIncomeFrom: firstJobData.reckonIncomeFrom,
            reckonIncomeTo: firstJobData.reckonIncomeTo,
            reckonFrom: this.calReckonIncome(firstJobData.reckonIncomeFrom, defaultRatio),
            reckonTo: this.calReckonIncome(firstJobData.reckonIncomeTo, defaultRatio),
            remark: '',
          });
        },
        calReckonIncome: function (num, ratio) {
          return (num * ratio).toFixed(0);
        },
        changeRatio: function (item) {
          item.reckonFrom = this.calReckonIncome(item.reckonIncomeFrom, item.ratio);
          item.reckonTo = this.calReckonIncome(item.reckonIncomeTo, item.ratio);
        },
        changeJob: function (index) {
          var $this = this;
          var curSelected = this.selectedJobList[index];
          var jobId = curSelected.jobId;
          this.jobList.forEach(function (item) {
            if (item.jobId == jobId) {
              curSelected.reckonIncomeFrom = item.reckonIncomeFrom;
              curSelected.reckonIncomeTo = item.reckonIncomeTo;
              curSelected.reckonFrom = $this.calReckonIncome(curSelected.reckonIncomeFrom, curSelected.ratio);
              curSelected.reckonTo = $this.calReckonIncome(curSelected.reckonIncomeTo, curSelected.ratio);
            }
          });
        },
        checkSameCompany: function (staffId, callback) {
          $.ajax({
            type: 'post',
            url: '/Cooperation/CheckSameCompany?staffId=' + staffId,
            dataType: 'json',
            success: function (res) {
              if (res.success) {
                callback && callback();
              } else {
                r_Layout_BeautAlert.done(res.message, "hits", 3000);
              }
            }
          });
        },
        openInviteCooperation: function (data) {
          var $this = this;
          var data;
          if (!data) {
            data = $("#staffSmallCard").data("model");
          }

          if (data.CooperationStatus == 0 || data.CooperationStatus == 2) {
            r_Layout_BeautAlert.done('您选择的顾问暂时不对外合作，请选择其他顾问进行合作', "hits", 3000);
            return;
          }

          $("#staffSmallCard").modal("hide");
          this.checkSameCompany(data.StaffId, function () {
            $this.receiveStaffId = data.StaffId;
            $this.receiveStaffName = data.Name;
            $.ajax({
              type: "post",
              url: "/cooperation/GetOpenningJobs",
              dataType: "json",
              data: { staffId: data.StaffId },
              success: function (res) {
                if (res.success) {
                  if (!res.data || res.data.length == 0) {
                    r_Layout_BeautAlert.done('抱歉，您目前没有可以合作的职位', "hits", 3000);
                    return;
                  }
                  $("#inviteCoorporationModal").modal("show");
                  $this.selectedJobList = [];
                  $this.jobList = res.data;
                  $this.addJob();
                } else {
                  r_Layout_BeautAlert.done(res.message, "hits", 3000);
                }
              }
            });
          });
        },
      },
    });
    var app_2 = new Vue({
      el: "#app_2",
      data: {
        isMe: false,
        rateObj: [],
        DescriptionLength: 0,
      },
      methods: {
        getUrl: function (name) {
          var url = 'http://staff.risfond.com/staff/personalhomepage?id=' + newCardId
          if (url) {
            var arr1 = url.split("?")[1];
            var arr2 = arr1.split("=")[1];
            return arr2
          }
        },
        onTextChange() {
          app_2.DescriptionLength = $("#textareaValue").val().length;
        },
        onBlurFn() {
          var Description = $("#textareaValue").val(), error_tips = $("#error_tips");
          if (Description.length > 0) {
            if (Description.length < 10) {
              error_tips.text("评价内容不低于10个字");
            } else if (Description.length > 200) {
              error_tips.text("评论内容已超过200字");
            }
          } else {
            error_tips.text("");
          }
        },
        close_fn_app_2() {
          $("#Staff_comments").modal("hide");
          var input_length = app_2.rateObj.length || 5;
          for (var i = 0; i < input_length; i++) {
            $(`#input_${i}`).rating('update', 0);
          }
          app_2.DescriptionLength = 0;
          $("#textareaValue").val("");
          document.getElementsByName("checkbox_name")[0].checked = false;
          $('input[name="checkbox_name"]').parent().removeClass("checked");
          $("#error_tips").text("");
        },
        subValue() {
          // r_Layout_BeautAlert.tips("weikaishi")
          var url = "/StaffEvalutionManage/AddStaffEvalutionDetail",
            myHref = $("#is_id_url")[0].href,
            myId = "",
            data = {},
            IsAnonymous,
            _this = this;
          myId = app_2.getUrl("id", myHref)

          _this.rateObj.forEach((item, index) => {
            item.Score = $(`#input_${index}`)[0].value;
            if (item.Score.length === 0) {
              r_Layout_BeautAlert.done(`评分不完成整,选择评分~`, "hits", 3000);
              throw new Error(`第${index + 1}条的，评分不完成整,选择评分~`);
            }
          })
          data.ToStaffId = myId;
          data.ItemJson = _this.rateObj;
          data.Description = $("#textareaValue").val();
          IsAnonymous = document.getElementsByName("checkbox_name")[0].checked;
          data.IsAnonymous = IsAnonymous;

          if (data.Description.length === 0) {
            // 可以为空 非必填项
          } else if (data.Description.length < 10) {
            r_Layout_BeautAlert.done(`评价内容不低于10个字~`, "hits", 3000);
            return;
          } else if (data.Description.length > 200) {
            r_Layout_BeautAlert.done(`评论内容已超过200字~`, "hits", 3000);
            return;
          }

          $.ajax({
            url: url,
            type: "post",
            data: data,
            success: function (res) {
              if (res.Success) {
                var callbackAppList = "";
                if (typeof (appList) == 'undefined') {
                } else {
                  callbackAppList = appList.__init;
                }
                r_Layout_BeautAlert.done(res.Message, "hits", 3000, callbackAppList);
                $("#Staff_comments").modal("hide");
                var input_length = app_2.rateObj.length || 5;
                for (var i = 0; i < input_length; i++) {
                  $(`#input_${i}`).rating('update', 0);
                }
                app_2.DescriptionLength = 0;
                $("#textareaValue").val("");
                document.getElementsByName("checkbox_name")[0].checked = false;
                $('input[name="checkbox_name"]').parent().removeClass("checked");
              } else {
                r_Layout_BeautAlert.done(res.Message, "hits", 3000);
              }
              $("#error_tips").text("");
            }
          })
        },

        sendIMMsg() {
          $("#staffSmallCard").modal("hide");
          var data = $("#staffSmallCard").data("model");
          chatVue.openPanelAndChat(data.StaffId);
        },
        showModeFn() {
          var _this = this, myHref = $("#is_id_url")[0].href, myId = "";

          myId = app_2.getUrl("id", myHref)
          _this.init(myId);
        },
        xingxing(item) {
          $(item).rating({
            min: 0, max: 5, step: 1, size: 'xs', showClear: false,
            hoverEnabled: true,
            hoverChangeStars: true,
            starCaptions: { 1: '1分', 2: '2分', 3: '3分', 4: '4分', 5: '5分' },
            starCaptionClasses: { 0: 'text_color_000', 1: 'text_color_000', 2: 'text_color_000', 3: 'text_color_000', 4: 'text_color_000', 5: 'text_color_000' }
          })
        },
        getStaffEvalutionItems(tostaffId) {
          var _this = this;
          $.ajax({
            type: "post",
            url: "/StaffEvalutionManage/GetStaffEvalutionItems",
            data: { tostaffId: tostaffId },
            success: function (res) {
              if (res.Success) {
                _this.isMe = false;
                _this.rateObj = res.Data;
              } else {
                _this.isMe = true
                r_Layout_BeautAlert.done(res.Message, "hits", 3000);

              }
            }
          })
        },
        watch: {

        },
        init(tostaffId) {
          var _this = this;
          _this.getStaffEvalutionItems(tostaffId);

          setTimeout(() => {
            console.log(_this.isMe, 'isMe')
            if (_this.isMe == false) {
              setTimeout(() => {
                var input_length = app_2.rateObj.length || 5;
                for (var i = 0; i < input_length; i++) {
                  _this.xingxing(`#input_${i}`);
                }
                $("#Staff_comments").modal("show");
              }, 500)
            }
          }, 200)
        }
      },
      mounted() {

      },
      created() {

      }
    })
  </script>
  <script type="text/javascript">
    if (typeof beautAlert == "undefined") {
      beautAlert = r_Layout_BeautAlert;
    }
  </script>
  
  <script>
    $().ready(function () {
      $("#_layoutFeedbackModalSubmit").click(function () {
        $("#_layoutFeedbackModal").modal("hide");
        var text = $("#_layoutModalfeedbackText").val();
        var departmentId = $("#selDepartments").val();
        $("#_layoutModalfeedbackText").val("");
        $.ajax({
          type: "post",
          url: "/Home/AddSuggest",
          data: { content: text, departmentId: departmentId },
          success: function (res) {
            if (res.Success) {
              r_Layout_BeautAlert.done("反馈成功", "hits", "2000")
            }
          }
        })
      })

      $("#layoutFeedbackModalID").click(function () {
        $("#selDepartments").empty();
        //绑定部门值
        $.ajax({
          type: "post",
          url: "/Finance/GetAllDepartment",
          dataType: "json",
          success: function (res) {
            if (res.Success) {
              for (var i = 0; i < res.Data.length; i++) {
                $("#selDepartments").append("<option value=" + res.Data[i].Id + ">" + res.Data[i].DepartmentName + "</option>");
              }
            }
          }
        });
        $("#_layoutFeedbackModal").modal("show");
      });

    })
  </script>

  <!-- 添加水印JS -->
  <script src="/js/addwatermark/watermark.js?_v=20250318" ></script>
  
  
  <script>
    var staffIdp =18733;

  </script>
  <script src="/static/r-easyui/r.easyui.js?_v=20250318" ></script>
  <script src="/static/util.js?_v=20250318" ></script>
  <script src="/js/bt_datepicker/daterangepicker_zh.js?_v=20250318" ></script>
  <script src="/js/laydate/laydate.js?_v=20250318" ></script>
  <script src="/scripts/popupbox2.js?_v=20250318" ></script>
  <script src="/js/daterangeselect/daterangeselect.min.js?_v=20250318" ></script>
  <script src="/js/data.industry.js?_v=20250318" ></script>
  <script src="/js/data.languages.js?_v=20250318" ></script>
  <script src="/js/data.occupation2.js?_v=20250318" ></script>
  <script src="/js/data.location.js?_v=20250318" ></script>
  <script src="/js/data.companies.js?_v=20250318" ></script>
  <script src="/scripts/data.js?_v=20250318" ></script>
  <script src="/scripts/autocompleteclient.js?_v=20250318" ></script>
  <script src="/js/selectfilter.js?_v=20250318" ></script>
  <script src="/js/completeteam.js?_v=20250318" ></script>
  <script src="/static/js/common.js?_v=20250318" ></script>
  <script src="/static/js/commonfilterhelper.js?_v=20250318" ></script>
  <script src="/static/js/advancedselect.js?_v=20250318" ></script>
  <script src="/static/js/advancedselect2.js?_v=20250318" ></script>
  <script src="/scripts/raselect/raselect.js?_v=20250318" ></script>
  <script src="/js/data.location.js"></script>
  <script src="/Scripts/handleBubble.js"></script>
  <script src="../Scripts/vue2.6.14.js"></script>
  
  <script src="/scripts/element-ui2.15.13.js?_v=20250318" ></script>
  <script src="/scripts/app/employeecare.js?_v=20250318" ></script>
  <script src="/scripts/app/r_notice2.js?_v=20250318" ></script>
  <script src="/scripts/app/r_anniversary_notice.js?_v=20250318" ></script>
  <script src="/scripts/app/managenotice.js?_v=20250318" ></script>
  <script src="/scripts/app/indexbase.js?_v=20250318" ></script>



  <script>

    //var divscroll = document.getElementById('moddleListUl')
    //divscroll.onscroll = function () {
    //  var wholeHeight = divscroll.scrollHeight;
    //  var scrollTop = divscroll.scrollTop;
    //  var divHeight = divscroll.clientHeight;
    //  if (scrollTop + divHeight >= wholeHeight) {
    //    //alert('滚动到底部了！');
    //    concenter.moddleListShow = true
    //    concenter.InterDataIndex++
    //    concenter.InterDataPage()
    //    //concenter.GetPageInter()

    //    //这里写动态加载的逻辑，比如Ajax请求后端返回下一个页面的内容
    //  }
    //  //if (scrollTop == 0) {
    //  //  alert('滚动到头部了！');
    //  //}
    //}





    function addColor(item) {
      let list = document.getElementsByClassName('lianhe-list')
      for (let p = list.length; p--;) {
        if (list[p].id != item) {
          list[p].classList.remove("lianhe-list-on");
        }
        else {
          /*点击的*/
          list[p].classList.add("lianhe-list-on");
        }
      }
    }

    function searchColor(item) {
      let list = document.getElementsByClassName('hall-left-search')
      for (let p = list.length; p--;) {
        if (list[p].id != item) {
          list[p].classList.remove("hall-left-search-on");
        }
        else {
          /*点击的*/
          list[p].classList.add("hall-left-search-on");
        }
      }
    }
    function salaryColor(item) {
      let list = document.getElementsByClassName('salary-num')
      for (let p = list.length; p--;) {
        if (list[p].id != item) {
          list[p].classList.remove("salary-num-on");
        }
        else {
          /*点击的*/
          list[p].classList.add("salary-num-on");
        }
      }
    }
    function publisColor(item) {
      if (item == 1) {
        concenter.placeholderText = "请输入职位线索内容，我们建议格式如下：\n \n职位找人: EHS高级经理 / 总监, 要求化工行业背景, 持有注册安全工程师, 熟悉安全、环境、职业卫生、消防等管理标准, 硕士以上学历, base苏州+ 能接受到工厂出差, 男女不限, 薪资待遇open可谈, 食宿皆有, 欢迎合作！"
      } else {
        concenter.placeholderText = "请输入人选线索内容，我们建议格式如下：\n \n优秀人选看机会:采购总监,男,base地点南方,期望年薪80W/年,给公司创收1亿,有大厂合作的经验,供应链资源丰富,铁锂行业！"
      }
      let list = document.getElementsByClassName('publisTabs-list')
      for (let p = list.length; p--;) {
        if (list[p].id != item) {
          list[p].classList.remove("publisTabs-on");
        }
        else {
          /*点击的*/
          list[p].classList.add("publisTabs-on");
        }
      }
    }
    //获取当天、当周、当月、当年
    function choseDate(val) {
      var params;
      // var date = new Date();
      var now = new Date(); //当前日期
      var nowDayOfWeek = now.getDay(); //今天本周的第几天
      var nowDay = now.getDate(); //当前日
      var nowMonth = now.getMonth(); //当前月
      var nowYear = now.getYear(); //当前年
      nowYear += nowYear < 2000 ? 1900 : 0; //
      var lastMonthDate = new Date(); //上月日期
      lastMonthDate.setDate(1);
      lastMonthDate.setMonth(lastMonthDate.getMonth() - 1);
      var lastYear = lastMonthDate.getYear();
      var lastMonth = lastMonthDate.getMonth();
      //格式化日期：yyyy-MM-dd
      function formatDate(date) {
        var myyear = date.getFullYear();
        var mymonth = date.getMonth() + 1;
        var myweekday = date.getDate();
        if (mymonth < 10) {
          mymonth = "0" + mymonth;
        }
        if (myweekday < 10) {
          myweekday = "0" + myweekday;
        }
        return myyear + "-" + mymonth + "-" + myweekday;
      }
      function getMonthDays(myMonth) {
        var monthStartDate = new Date(nowYear, myMonth, 1);
        var monthEndDate = new Date(nowYear, myMonth + 1, 1);
        var days = (monthEndDate - monthStartDate) / (1000 * 60 * 60 * 24);
        return days;
      }
      //获得本季度的开始月份
      function getQuarterStartMonth() {
        var quarterStartMonth = 0;
        if (nowMonth < 3) {
          quarterStartMonth = 0;
        }
        if (2 < nowMonth && nowMonth < 6) {
          quarterStartMonth = 3;
        }
        if (5 < nowMonth && nowMonth < 9) {
          quarterStartMonth = 6;
        }
        if (nowMonth > 8) {
          quarterStartMonth = 9;
        }
        return quarterStartMonth;
      }

      function getDay(day) {
        var today = new Date();
        var targetday_milliseconds =
          today.getTime() + 1000 * 60 * 60 * 24 * day;
        today.setTime(targetday_milliseconds); //注意，这行是关键代码
        var tYear = today.getFullYear();
        var tMonth = today.getMonth();
        var tDate = today.getDate();
        tMonth = doHandleMonth(tMonth + 1);
        tDate = doHandleMonth(tDate);
        return tYear + "-" + tMonth + "-" + tDate;
      }

      function doHandleMonth(month) {
        var m = month;
        if (month.toString().length == 1) {
          m = "0" + month;
        }
        return m;
      }
      // var flag = parseInt(params);
      switch (val) {
        case 1:
          // 今日
          var weekStartDate = new Date(
            nowYear,
            nowMonth,
            nowDay
          );
          var startStr = formatDate(weekStartDate);
          var weekEndDate = new Date(
            nowYear,
            nowMonth,
            nowDay
          );
          var endStr = formatDate(weekEndDate);
          params = {
            startDate: startStr,
            endDate: endStr
          };
          break;
        case 2:
          // 本周
          var weekStartDate = new Date(
            nowYear,
            nowMonth,
            nowDay - nowDayOfWeek
          );
          var start = formatDate(weekStartDate);
          var weekEndDate = new Date(
            nowYear,
            nowMonth,
            nowDay + (6 - nowDayOfWeek)
          );
          var end = formatDate(weekEndDate);
          //   var start = getDay(-6);+ " " + "00:00:00"
          //   var end = getDay(0);+ " " + "23:59:59
          params = {
            startDate: start,
            endDate: end
          };
          break;
        case 3:
          // 本月
          var monthStartDate = new Date(nowYear, nowMonth, 1);
          var start = formatDate(monthStartDate);
          var monthEndDate = new Date(
            nowYear,
            nowMonth,
            getMonthDays(nowMonth)
          );
          var end = formatDate(monthEndDate);
          params = {
            startDate: start,
            endDate: end
          };
          break;
        case 4:
          // 本季度
          var quarterStartDate = new Date(nowYear, getQuarterStartMonth(), 1);
          var start = formatDate(quarterStartDate);
          var quarterEndMonth = getQuarterStartMonth() + 2;
          var quarterStartDate = new Date(
            nowYear,
            quarterEndMonth,
            getMonthDays(quarterEndMonth)
          );
          var end = formatDate(quarterStartDate);
          params = {
            startDate: start,
            endDate: end
          };
          break;
        case 5:
          // 本年
          var start = now.format("yyyy-01-01 00:00:00");
          var end = now.format("yyyy-12-31 23:59:59");
          params = {
            startDate: start,
            endDate: end
          };
          break;
        case 6:
          // 本年
          var start = "";
          var end = "";
          params = {
            startDate: start,
            endDate: end
          };
          break;
      }
      console.log('[ params ] >', params)
      concenter.startFrom = params.startDate
      concenter.startTo = params.endDate
      concenter.GetPageInter()
      return params;
    }

  </script>

  <script type="text/javascript">
  var staffIdp = '18733';
    $(function () {
      var staffId = '18733',
        staffTelNum = '',
        companyId = '8',
        stafflist_el = $("#lazyLoadContainer" + companyId + ""),
        stafflist_panel = stafflist_el.find(".index-colleaguelist"),
        staffMobileNum = '15605286175';

      layout_SiteReady.init(staffId, staffTelNum, companyId, stafflist_el, stafflist_panel, staffMobileNum);
      layout_SiteReadyNew.init(staffId, staffTelNum, companyId, stafflist_el, stafflist_panel, staffMobileNum);
      setTimeout(function () {
        layout_SiteReady.setStaffRCoin();
      }, 500);

    });
      var flag = false;

    $('.chech').change(function (e) {
      if (e.currentTarget.checked) {
        flag = true;
      } else {
        flag = false;
      }
    })

    var bool = 'True';
    if (bool == 'False') {
      $('#myModals').modal('show');
    }
      $('.bot').click(function () {
        if (flag) {
          $.post("/Staff/ReadHandbook", function (r) {
            $('#myModals').modal('hide');
          })
        } else {
          r_Layout_BeautAlert.done("请同意阅读员工手册")
        }
    })
  </script>

  <script src="/scripts/echarts4.2.1.min.js?_v=20250318" ></script>

  
  <script src="/scripts/element-ui2.15.13.js?_v=20250318" ></script>
  <script src="/scripts/app/html2canvas.js?_v=20250318" ></script>

  <script src="/scripts/smallcard.js?_v=20250318" ></script>
  


  <script>


    function downloadCard() {
      var obj = document.getElementById("imgid");
      var imgSrc = obj.src;//能获取到
      var pic1 = document.getElementById("downloadNewCard") //要生成图片的标签
      //生成canvas标签
      //html2canvas(pic1).then(function (canvas) {	//找到pic元素时，生成canvas元素。
      html2canvas(document.querySelector("#downloadNewCard"), {
        useCORS: true,
        proxy: imgSrc,
      }).then(function (canvas) {
        //var dataURL = canvas.toDataURL("image/png")	 // 获取canvas对应的base64编码
        //restoreImg(dataURL)	//下载canvas图片
        const imgData = canvas.toDataURL("image/jpeg");
        let aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = imgData;
        aLink.download = "个人名片.png";

        // 触发点击-然后移除
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink);

      });
    }

    function restoreImg(dataURL) {
      let href = dataURL
      let a = document.createElement('a') // 创建a标签
      a.download = "个人名片" // 设置图片名字
      a.href = href
      a.dispatchEvent(new MouseEvent('click'))	//模拟点击进行下载
    }
  </script>

  
  <script type="text/javascript">document.write(unescape("%3Cspan id='cnzz_stat_icon_1279333277'%3E%3C/span%3E%3Cscript src='https://s4.cnzz.com/z_stat.php%3Fid%3D1279333277' type='text/javascript'%3E%3C/script%3E"));</script>

  <!-- 日程提醒 1 JS -->
  <script src="/js/schedulereminder/schedulereminder.js?_v=20250318" ></script>
</body>
</html>