@charset "UTF-8";

[v-clock] {
  display: none;
}

/*禁用响应式布局 BEGIN*/
.container {
  width: 1270px !important;
}

.container1 {

}

.navbar {
  -moz-min-width: 1270px !important;
  -ms-min-width: 1270px !important;
  -o-min-width: 1270px !important;
  -webkit-min-width: 1270px !important;
  min-width: 1270px !important;
}

.page-boxed .page-header.navbar .page-actions {
  float: left !important;
  margin-left: 20px !important;
}

.page-header.navbar .menu-toggler.sidebar-toggler {
  display: block !important;
  margin-right: 4px !important;
  margin-top: 34px;
  /*float: left;
  margin: 4px 17px 0 0!important;*/
}

.page-header.navbar .page-top {
  width: auto !important;
}

.page-sidebar.navbar-collapse.collapse {
  display: block !important;
}

.page-sidebar {
  width: 152px !important;
}

.page-sidebar-menu.page-sidebar-menu-hover-submenu li:hover > .sub-menu {
  box-shadow: 5px 5px rgba(140,178,198,.2);
}

.page-sidebar-menu-hover-submenu li:hover > .sub-menu {
  background: #FFF;
}
/*禁用响应式布局 END*/

/* Cubic Bezier Transition */
body {
  padding-top: 50px;
  padding-bottom: 20px;
}

body, h1, h2, h3, h4, h5, h6 {
  font-family: "Open Sans","Arial","宋体","sans-serif";
}

/* Set padding to keep content from hitting the edges */
.body-content {
  padding-left: 15px;
  padding-right: 15px;
}
/* Override the default bootstrap behavior where horizontal description lists
   will truncate terms that are too long to fit in the left column
*/
.dl-horizontal dt {
  white-space: normal;
}
/* Set width on the form input elements since they're 100% wide by default */
.cf {
  zoom: 1;
}

  .cf:before {
    content: '';
    display: block;
    clear: both;
  }

  .cf:after {
    content: '';
    display: table;
    clear: both;
  }

.textoverflow {
  overflow: hidden;
  white-space: nowrap;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}

.page-boxed .page-header.navbar .page-logo {
  /*width: 474px;*/
  width: 410px;
}

.page-header.navbar .page-logo .logo-default {
  margin: 17px 2px 0 0;
  width: 340px;
}

.page-boxed .page-header.navbar .page-logo .logo-default2 {
  margin: 17px 2px 0 0;
  display: none;
}

.page-sidebar-closed.page-sidebar-closed-hide-logo .page-header.navbar .page-logo {
  padding: 0;
  width: 410px !important;
  /*width: 474px!important;*/
  padding-left: 20px;
  margin-right: 0;
  margin-left: 0;
}

  .page-sidebar-closed.page-sidebar-closed-hide-logo .page-header.navbar .page-logo .logo-default {
    display: none;
  }

  .page-sidebar-closed.page-sidebar-closed-hide-logo .page-header.navbar .page-logo .logo-default2 {
    display: block;
    width: 340px;
  }


/*BEGIN 搜索框*/
.page-header.navbar .search-form2 .input-group {
  border: 1px solid #ced6de;
  border-radius: 25px !important;
}

.page-header.navbar .search-form2 {
  margin: 22px 20px 0 0;
  display: inline-block;
  float: left;
  width: 240px;
}

  .page-header.navbar .search-form2 .search-form2-selected {
    border-width: 0;
    border-top-left-radius: 25px !important;
    border-bottom-left-radius: 25px !important;
  }

  .page-header.navbar .search-form2 .form-control {
    border-width: 0;
    border-left: 1px solid #ccc;
  }
/*END 搜索框*/

/*头部APP下载*/
.APPBox {
  width: 90px;
  height: 40px;
  float: left;
  margin-top: 20px;
  line-height: 37px;
  margin: 20px 10px 0 20px
}

  .APPBox a {
    font-size: 14px;
    display: block;
    height: 30px;
    overflow: hidden;
  }

    .APPBox a i {
      margin-right: 3px;
      font-size: 16px;
      margin-top: -3px;
      vertical-align: middle;
    }

    .APPBox a span {
      line-height: 30px;
      display: inline-block;
    }

.pageBox {
  width: 100%;
  height: auto;
  margin-top: 75px;
  position: relative;
}

  .pageBox .imgBox {
    margin: 0 auto;
    width: 1200px;
    overflow: hidden;
  }

  .pageBox .imgBox1 {
    position: absolute;
    left: 0;
    right: 0;
  }

    .pageBox .imgBox1 .BGIMG {
      width: 1200px;
    }

  .pageBox .bg1 {
    height: 523px;
    width: 100%;
    background: url('/images/aaabg1.png') repeat-x top;
    background-size: contain;
  }

.qrcodeBox {
  width: 120px;
  height: 120px;
  position: absolute;
}

  .qrcodeBox img {
    width: 120px;
    height: 120px;
    display: block;
    border: none;
  }

  .qrcodeBox p {
    width: 120px;
    text-align: center;
    color: #fff;
    font-size: 14px;
  }

.pageBox .iosBox {
  top: 289px;
  left: 586px;
  text-align: center;
}

.pageBox .androidBox {
  top: 289px;
  left: 759px;
}

.iOSTipsBox {
  position: absolute;
  z-index: 20;
  top: 450px;
  left: 575px;
}

  .iOSTipsBox a {
    color: #fff;
    text-decoration: underline;
  }

.pageBox .bg2 {
  height: 429px;
  width: 100%;
  background-color: #fff;
}

.pageBox .bg3 {
  height: 429px;
  width: 100%;
  background-color: #f6f6f6;
}

.pageBox .bg4 {
  height: 429px;
  width: 100%;
  background-color: #fff;
}


ul, ol {
  list-style: none;
  padding-left: 0;
  margin: 0;
  padding: 0;
  -webkit-padding-start: 0;
}
/**/
.dropdown-menu > li > a.r-btn-logout {
  color: #D91E18;
}

/*文字超出隐藏*/
.nodata {
  font-size: 16px;
  color: #999;
  padding: 5px 0;
  text-align: center;
}

.rnss-line {
  display: block;
  margin: 0;
  padding: 0 0 8px;
  line-height: 1.2em;
  font-size: 14px;
}

.index-staffcard {
}

  .index-staffcard .modal-dialog {
    width: 400px;
  }

  .index-staffcard .sc-line {
    width: 183px;
  }

  .index-staffcard .sc-line2 {
    width: 265px;
  }

  .index-staffcard .sc-photo {
    max-width: 100%;
    display: inline-block;
    vertical-align: middle;
    border-radius: 50% !important;
    border-radius: 50%;
    overflow: hidden;
    border: 1px solid #EBEBEB;
  }

  .index-staffcard .sc-title {
    float: left;
    width: 45px;
    overflow: hidden;
    white-space: nowrap;
  }
  /*.index-staffcard .sc-con { float: left; width: 75%; overflow: hidden; white-space: nowrap; -ms-text-overflow: ellipsis; -o-text-overflow: ellipsis; text-overflow: ellipsis; }*/
  .index-staffcard .sc-con {
    float: left;
    width: 138px;
    -moz-word-break: break-all;
    -o-word-break: break-all;
    -ms-word-break: break-all;
    word-break: break-all;
    -ms-word-wrap: break-word;
    word-wrap: break-word;
  }

    .index-staffcard .sc-con.sc-email {
      width: 220px;
    }

  .index-staffcard .md6-row .sc-title {
    width: 35%;
  }

  .index-staffcard .md6-row .sc-con {
    width: 65%;
  }

  .index-staffcard .sc-qq {
  }

hr, p {
  margin: 6px 0;
}

.index-staffcard .icon-staffcard-mobile {
  width: 14px;
  vertical-align: baseline;
}

.index-staffcard .width-45 {
  width: 45px !important;
}

.index-staffcard .staffcard-level-panel {
  position: relative;
}

.index-staffcard .staffcard-userimg {
  position: absolute;
  width: 56%;
  /*top: 19%;
  left: 16%;*/
  top: 12%;
  left: 11%;
}

.index-staffcard .staffcard-level-img {
 /* width: 100%;
  position: relative;*/
    width: 115%;
    position: relative;
    margin-top: -7px;
    margin-left: -8px;
}


.index-staffcard .staffcard-level-name {
  position: absolute;
  /*bottom: 13%;*/
  bottom: 15%;
  width: 100%;
  text-align: center;
  padding-right: 15px;
  color: #fff;
}

.index-staffcard .staffcard-level-panel.withoutlevel .staffcard-level-img {
  display: none;
}

.index-staffcard .staffcard-level-panel.withoutlevel .staffcard-level-name {
  display: none;
}

.index-staffcard .staffcard-level-panel.withoutlevel .staffcard-userimg {
  width: initial;
  left: 0;
  top: 0;
}

.index-staffcard .no-width {
  width: initial;
}

.index-staffcard .sc-evaluation-score {
  position: relative;
  top: -1px;
}

.index-staffcard .evaluation-score-star-img {
  height: 14px;
  margin-right: 7px;
  vertical-align: bottom;
}

.index-staffcard .evaluation-score-num {
  position: relative;
  top: 2px;
  color: #FF8800;
  font-weight: bold;
}

.index-staffcard .industry-label {
  background-color: #E8F4FF;
  color: #666;
  padding: 2px 7px;
  margin-right: 10px;
  display: inline-block;
  margin-bottom: 10px;
  border-radius: 2px !important;
}

.index-staffcard .modal-bottom-opt {
  text-align: right;
  border-top: solid 1px #eee;
  margin-left: -15px;
  margin-right: -15px;
  padding: 13px 16px 0 16px;
}

.index-staffcard .modal-bottom-btn {
  padding: 5px 10px;
  font-size: 12px;
  margin-left: 7px;
  border-radius: 2px !important;
}

.index-staffcard .modal-bottom-icon {
  height: 14px;
  vertical-align: text-top;
  margin-right: 7px;
}

.index-staffcard .modal-bottom-btn.btn-outline.green {
  border-color: #0AA92A;
  color: #0AA92A;
}

  .index-staffcard .modal-bottom-btn.btn-outline.green:hover {
    background-color: #fff;
  }

.index-staffcard .modal-bottom-btn.btn-outline.blue {
  border-color: #2A7FCC;
  color: #2A7FCC;
}

  .index-staffcard .modal-bottom-btn.btn-outline.blue:hover {
    background-color: #fff;
  }

.index-staffcard .modal-bottom-btn.btn-outline.orange {
  border-color: #FF6600;
  color: #FF6600;
}

  .index-staffcard .modal-bottom-btn.btn-outline.orange:hover {
    background-color: #fff;
  }

.index-staffcard .modal-bottom-btn.btn-outline.btn-disabled {
  border-color: #ddd;
  color: #cacaca;
  cursor: not-allowed;
}

  .index-staffcard .modal-bottom-btn.btn-outline.btn-disabled:hover {
  }

/* 邀请合作弹窗 start */
#inviteCoorporationModal,#inviteCoorporationJobDetailModal {
  font-family: '微软雅黑';
  color: #333;
}

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal * { /* 为了兼容global.css中把box-sizing改为content-box时显示导致的问题 */
    box-sizing: border-box;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .modal-dialog {
    width: 830px;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .color-blue {
    color: #2A7FCC;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .modal-title {
    font-weight: bold;
    color: #333;
    font-size: 14px;
    letter-spacing: 1px;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .modal-header {
    padding: 16px 20px 14px;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .close {
    margin-top: 6px !important;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .modal-body {
    padding: 18px 20px;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-notice {
    background-color: #FFF5ED;
    color: #FF3333;
    padding: 10px 9px;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-item {
    border-bottom: dashed 1px #ddd;
    padding-bottom: 20px;
  }

    #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-item:last-child {
      border-bottom: none;
      padding-bottom: initial;
    }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-row {
    display: flex;
    margin-top: 21px;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-main {
    display: flex;
    flex: 1;
    justify-content: space-between;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-col-6 {
    width: calc(50% - 23px);
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-col-3 {
    width: calc(25% - 23px);
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-form-group {
    display: flex;
    align-items: baseline;
    width: 100%;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-form-title {
    width: 70px;
    flex-shrink: 0;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-form-content {
    flex: 1;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-form-group.has-err .invitecoorporation-form-title {
    color: #ff0000;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-form-group.has-err .form-control {
    border-color: #ff0000;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-form-notice {
    color: #FF3333;
    margin-top: 8px;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-form-notice-icon {
    height: 14px;
    margin-right: 4px;
    position: relative;
    top: -1px;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .text-resize-none {
    resize: none;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-opt {
    width: 32px;
    text-align: right;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .invitecoorporation-opt-icon {
    height: 16px;
    position: relative;
    top: 6px;
    cursor: pointer;
  }

  #inviteCoorporationModal,#inviteCoorporationJobDetailModal .footer-btn {
    width: 90px;
    border-radius: 2px !important;
  }

    #inviteCoorporationModal,#inviteCoorporationJobDetailModal .footer-btn.btn-primary:hover {
      background-color: #2A7FCC;
      border-color: #2A7FCC;
      border-radius: 2px !important;
    }

    #inviteCoorporationModal,#inviteCoorporationJobDetailModal .footer-btn.btn-primary:hover {
      background-color: #205d95;
      border-color: #205d95;
    }
/* 邀请合作 end */

/*等级介绍提示信息*/
.staffleveltip-p {
  font-size: 12px;
  display: block;
}

  .staffleveltip-p .staffleveltip-con {
    line-height: 20px;
    margin: 6px auto 0;
    display: block;
  }

    .staffleveltip-p .staffleveltip-con p {
      float: none;
      display: block;
      line-height: inherit;
      height: auto;
      width: auto;
    }

    .staffleveltip-p .staffleveltip-con .question {
    }

      .staffleveltip-p .staffleveltip-con .question label {
        float: left;
        width: 25px;
        overflow: hidden;
        white-space: nowrap;
        font-weight: bold;
      }

      .staffleveltip-p .staffleveltip-con .question span {
        float: left;
        width: 192px;
      }

    .staffleveltip-p .staffleveltip-con .daan {
    }

      .staffleveltip-p .staffleveltip-con .daan label {
        float: left;
        width: 25px;
        overflow: hidden;
        white-space: nowrap;
        font-weight: bold;
      }

      .staffleveltip-p .staffleveltip-con .daan span {
        float: left;
        width: 192px;
      }

  .staffleveltip-p .staffleveltip-tab {
    border-spacing: 0;
    border-collapse: collapse;
    text-align: center;
  }

    .staffleveltip-p .staffleveltip-tab th, .staffleveltip-p .staffleveltip-tab td {
      text-align: left;
      padding-right: 20px;
    }

    .staffleveltip-p .staffleveltip-tab thead th {
      font-weight: bold;
    }

    .staffleveltip-p .staffleveltip-tab tr {
      height: 22px;
      line-height: 22px;
    }
/*首页功能图标*/
.index-iconinfo-box {
  text-align: center;
  margin: 0 auto;
  width: 100px;
  height: 100px;
  border-radius: 50% !important;
  overflow: visible;
  position: relative;
}

  .index-iconinfo-box .if-box {
    display: table-cell;
    vertical-align: middle;
    width: 100px;
    height: 100px;
    text-decoration: none;
  }

  .index-iconinfo-box .if-icon {
    font-size: 28px;
  }

    .index-iconinfo-box .if-icon img {
      width: 50px;
      height: 50px;
      background: none;
    }

  .index-iconinfo-box .index-iconinfo-con {
    font-size: 14px;
    color: white;
    font-weight: bold;
  }

  .index-iconinfo-box .new-add-tip {
    display: block;
    width: 34px;
    height: 16px;
    background-color: #ff2424;
    border-radius: 3px;
    font-size: 13px;
    color: #fff;
    line-height: 13px;
    text-align: center;
    position: absolute;
    right: -3px;
    top: 1px;
    vertical-align: sub;
    font-style: normal;
  }
/*首页样式*/
.index-box {
  margin: 0 0 20px;
}

  .index-box a:hover {
    text-decoration: none;
  }

.r-tb-cpt {
  table-layout: fixed;
  border-width: 0;
  border-top-width: 1px;
}

  .r-tb-cpt th, .r-tb-cpt td {
    overflow: hidden;
    white-space: nowrap;
    -ms-text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
  }

  .r-tb-cpt thead th {
    border-bottom: 1px solid #ddd !important;
    text-align: center
  }

  .r-tb-cpt > tbody td {
    border: 0 none !important;
    padding: 10px 6px !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .r-tb-cpt .r-status {
    width: 74px;
  }

  .r-tb-cpt tbody .r-status {
    font-weight: bold;
  }

  .r-tb-cpt .r-from {
    width: 108px;
  }

  .r-tb-cpt .r-to {
    width: 108px;
  }

  .r-tb-cpt .r-job {
    width: 102px;
  }

  .r-tb-cpt .r-amount {
    width: 93px;
  }

  .r-tb-cpt tbody .r-amount {
    font-weight: bold;
  }

  .r-tb-cpt tbody .r-amount {
    text-align: right;
    padding-right: 6px !important;
  }

  .r-tb-cpt .cpt__name {
    font-weight: bold;
  }

.cpt__wrapper {
  overflow: hidden;
  height: 240px;
  position: relative;
}

  .cpt__wrapper .cpt__box {
    position: relative;
  }

.r-tb-tuijian {
  table-layout: fixed;
  border-width: 0;
  border-top-width: 1px;
}

  .r-tb-tuijian .btn {
    padding: 0 5px;
    margin: 0;
  }

  .r-tb-tuijian th, .r-tb-tuijian td {
    overflow: hidden;
    white-space: nowrap;
    -ms-text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
  }

  .r-tb-tuijian thead th {
    border-bottom: 1px solid #ddd !important;
  }

  .r-tb-tuijian tbody td {
    border: 0 none !important;
  }

  .r-tb-tuijian .r-staff {
    width: 65px;
  }

  .r-tb-tuijian .r-salay {
    width: 55px;
  }

  .r-tb-tuijian tbody .r-salay {
    font-weight: bold;
  }

  .r-tb-tuijian .r-city {
    width: 86px;
  }

  .r-tb-tuijian .r-tuijianstaff {
    width: 70px;
  }

  .r-tb-tuijian .r-time {
    width: 55px;
  }

  .r-tb-tuijian .r-status {
    width: 55px;
  }

  .r-tb-tuijian td.r-status {
    font-weight: bold;
  }

.r-tb-daozhang {
  table-layout: fixed;
  border-width: 0;
  border-top-width: 1px;
}

  .r-tb-daozhang .btn {
    padding: 0 5px;
    margin: 0;
  }

  .r-tb-daozhang th, .r-tb-daozhang td {
    overflow: hidden;
    white-space: nowrap;
    -ms-text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
  }

  .r-tb-daozhang thead th {
    border-bottom: 1px solid #ddd !important;
  }

  .r-tb-daozhang tbody td {
    border: 0 none !important;
  }

  .r-tb-daozhang .r-staff {
    width: 78px;
  }

  .r-tb-daozhang .r-city {
    width: 86px;
    text-align: center
  }

  .r-tb-daozhang .r-time {
    width: 55px;
  }

  .r-tb-daozhang .r-amount {
    width: 85px;
  }

  .r-tb-daozhang td.r-amount {
    font-weight: bold;
    text-align: left;
  }

.index-stafflist-box .col-md-3 {
  width: 20%;
}

.index-staff-box {
  text-align: center;
  margin: 0 auto;
  width: 94px;
  height: 94px;
  border-radius: 50% !important;
  overflow: hidden;
  cursor: pointer;
}

  .index-staff-box .if-box {
    display: table-cell;
    vertical-align: middle;
    width: 94px;
    height: 94px;
  }

    .index-staff-box .if-box img {
      width: 92px;
      height: 92px;
      background: none;
      overflow: hidden;
      border-radius: 50%;
    }

.index-kechenglist-box .tjkecheng-col {
  margin-right: 27px;
}

.index-kechenglist-box:first-child {
  margin-left: -15px !important;
}

.index-kechenglist-box:last-child {
  margin-right: -55px !important;
}

.index-kechenglist-box .col-md-3 {
  width: 20%;
}

.index-kecheng-box {
  text-align: center;
  margin: 0 auto;
  display: block;
  width: 180px;
  height: 101px;
  border-radius: 8px !important;
  overflow: hidden;
}

  .index-kecheng-box .if-box {
    display: table-cell;
    vertical-align: middle;
    width: 180px;
    height: 101px;
  }

    .index-kecheng-box .if-box img {
      width: 180px;
      height: 101px;
      background: none;
      overflow: hidden;
      border-radius: 50%;
    }

.feeds li .col1 > .cont > .cont-col2 > .desc {
  -ms-word-break: break-all;
  word-break: break-all;
  -ms-word-wrap: break-word;
  word-wrap: break-word;
}

@media (min-width: 1200px) {
  .feeds li .col1 > .cont > .cont-col2 > .desc {
    width: 340px;
  }

    .feeds li .col1 > .cont > .cont-col2 > .desc a {
      display: block;
      white-space: nowrap;
      overflow: hidden;
      -ms-text-overflow: ellipsis;
      -o-text-overflow: ellipsis;
      text-overflow: ellipsis;
    }
}
/**/
.r-tb-qianyue {
  table-layout: fixed;
  border-width: 0;
  border-top-width: 1px;
}

  .r-tb-qianyue .btn {
    padding: 0 5px;
    margin: 0;
  }

  .r-tb-qianyue td {
    white-space: nowrap;
    overflow-x: hidden;
    -ms-text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
  }

  .r-tb-qianyue .r-client {
    text-align: left;
  }

  .r-tb-qianyue .r-guwen {
    width: 78px;
  }

  .r-tb-qianyue .r-company {
    width: 80px;
    text-align: center;
  }

  .r-tb-qianyue .r-qianyuedate {
    width: 60px;
    text-align: center;
  }

  .r-tb-qianyue thead th {
    border-bottom: 1px solid #ddd !important;
  }

  .r-tb-qianyue tbody td {
    border: 0 none !important;
  }
/*首页-最新登记的简历 start*/
.r-tb-jianli {
  table-layout: fixed;
  border-width: 0;
  border-top-width: 1px;
}

  .r-tb-jianli td {
    white-space: nowrap;
    overflow-x: hidden;
    -ms-text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
  }

  .r-tb-jianli .r-caption.r-xm {
    text-align: center;
  }

  .r-tb-jianli .r-xm {
    width: 100px;
  }

  .r-tb-jianli .r-city {
    width: 72px;
  }

  .r-tb-jianli .r-gl {
    width: 70px;
    text-align: center;
  }

  .r-tb-jianli .r-xl {
    width: 70px;
    text-align: center;
  }

  .r-tb-jianli .r-zw {
  }

  .r-tb-jianli thead th {
    border-bottom: 1px solid #ddd !important;
  }

  .r-tb-jianli tbody td {
    border: 0 none !important;
  }
/*首页-最新登记的简历 end*/

/*首页-悬赏猎聘 start*/
.r-tb-liepin {
  table-layout: fixed;
  border-width: 0;
  border-top-width: 1px;
  margin-bottom: 18px !important;
}

  .r-tb-liepin td {
    white-space: nowrap;
    overflow-x: hidden;
    -ms-text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
  }

  .r-tb-liepin .r-caption.r-xm {
    text-align: center;
  }

  .r-tb-liepin .r-zw {
    width: 190px;
  }

  .r-tb-liepin .r-salary {
    width: 80px;
  }

  .r-tb-liepin .r-tc {
    width: 50px;
    text-align: center;
  }

  .r-tb-liepin .r-city {
    width: 60px;
    text-align: center;
  }

  .r-tb-liepin .r-xsr {
  }

  .r-tb-liepin thead th {
    border-bottom: 1px solid #ddd !important;
  }

  .r-tb-liepin tbody td {
    border: 0 none !important;
    padding: 7px !important;
  }

  .r-tb-liepin .r-salary .l-salary {
    font-weight: bold;
  }

  .r-tb-liepin .r-tc .l-tc-ratio {
    font-weight: bold;
  }

  .r-tb-liepin .r-xsr .showCard-item {
    padding: 0 5px !important;
  }
/*首页-悬赏猎聘 end*/

.r-tb-zhiwei {
  table-layout: fixed;
}

  .r-tb-zhiwei td {
    white-space: nowrap;
    overflow-x: hidden;
    -ms-text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
  }

  .r-tb-zhiwei .r-nx {
    width: 95px;
  }

  .r-tb-zhiwei .r-city {
    width: 85px;
  }

  .r-tb-zhiwei thead th {
    border-bottom: 1px solid #ddd !important;
  }

  .r-tb-zhiwei tbody td {
    border: 0 none !important;
  }
/*首页-龙虎榜*/
.longhu-box {
}

  .longhu-box .portlet-title .actions {
    margin-left: 8px;
  }

.r-tb-longhu {
  table-layout: fixed;
  border-width: 0;
  border-top-width: 1px;
}

  .r-tb-longhu .btn {
    padding: 0 5px;
    margin: 0;
  }

#longhu-box .portlet.light > .portlet-title > .actions {
  padding-bottom: 12px;
}

.r-tb-longhu td {
  white-space: nowrap;
  overflow-x: hidden;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}

.r-tb-longhu thead th {
  border-bottom: 1px solid #ddd !important;
}

.r-tb-longhu tbody td {
  border: 0 none !important;
}

.r-tb-longhu .r-rank {
  width: 60px;
}

.r-tb-longhu .r-staffname {
  width: 75px;
}

.r-tb-longhu td.r-pamount {
  width: 85px;
  text-align: right;
}

.r-tb-longhu th.r-pamount {
  text-align: center;
}

.r-tb-longhu .r-bamount {
}

.r-tb-longhu .r-position {
  width: 75px;
}

.r-tb-longhu .r-company {
  width: 86px;
}

.r-tb-longhu .r-timetype {
  width: 65px;
}
/*首页实时互动*/
.r-i-interaction-list {
}

  .r-i-interaction-list .icon-magnifier {
    cursor: pointer;
  }

  .r-i-interaction-list .chats {
    min-height: 208px;
  }

  .r-i-interaction-list .r-i-item {
    position: relative;
  }

  .r-i-interaction-list .getmore {
    text-align: center;
    text-decoration: underline;
    cursor: pointer;
    font-size: 16px;
    color: #ccc;
  }

    .r-i-interaction-list .getmore:hover {
      text-decoration: none;
    }

  .r-i-interaction-list .companyName {
    font-size: 13px;
    color: #7B7B7B;
  }

  .r-i-interaction-list .chats li.in .message {
    margin-right: 43px;
  }

  .r-i-interaction-list .chats li.out .avatar {
    margin-right: 43px;
  }

  .r-i-interaction-list .chats li.out .message {
    margin-right: 108px;
  }
  /*.r-i-interaction-list .chats li .ilicon-box{float:right;width: 42px;text-align: center;display:table-cell;vertical-align:middle;}*/
  .r-i-interaction-list .chats li .ilicon-box {
    position: absolute;
    right: 0;
    top: 5px;
    bottom: 5px;
    width: 42px;
    text-align: center;
    display: table-cell;
    vertical-align: middle;
  }

    .r-i-interaction-list .chats li .ilicon-box img {
      vertical-align: middle;
    }

  .r-i-interaction-list .chat-form {
    overflow: visible;
  }

    .r-i-interaction-list .chat-form .sx-panel2 .selectfilter-box .selectfilter-tip {
      border-radius: 0 !important;
      border-color: #ddd;
      border-right: 0 none;
      width: 99px;
      background-color: #F7F7F7;
      height: 34px;
    }

    .r-i-interaction-list .chat-form .sx-panel2 {
      margin-right: 0;
    }

    .r-i-interaction-list .chat-form .input-cont .form-control {
    }

  .r-i-interaction-list .sx-panel2 .selectfilter-tip:before {
    display: none;
  }

  .r-i-interaction-list .sx-panel2 .selectfilter-tip:after {
    top: 14px;
    border-width: 4px 5px 0;
  }
/*公用footer*/
.fl {
  float: left;
}

.footer-box {
  display: block;
  background: #F1F5F8;
  font-size: 12px;
  border-bottom: 1px solid #D0D0D0;
  margin: 0 0 10px !important;
}

.site-footer {
  width: 1200px;
  margin: 0 auto;
  position: relative;
  padding: 25px 0;
  color: #999;
}

.site-footer-row1 {
  float: left;
  width: 425px;
  border-right: 1px solid #D0D0D0;
}

.site-footer .help {
  line-height: 16px;
  padding: 8px 0;
}

  .site-footer .help a {
    float: left;
    padding: 0 10px 0 0;
    margin: 0 10px 0 0;
    border-right: 1px solid #D0D0D0;
    color: #999;
  }

    .site-footer .help a:last-child {
      border: 0 none;
      padding: 0;
      margin: 0;
    }

.site-footer .copy {
  padding: 0 0 8px;
}

.site-footer .copy-txt {
}

.site-footer .copy-company {
  margin: 0 0 0 8px;
  padding: 0;
}

.site-footer .copy-hongdun {
  margin: 0 0 0 8px;
  padding: 0;
}

  .site-footer .copy-hongdun img {
    vertical-align: initial;
  }

.site-footer-row2 {
  float: left;
  width: 486px;
  border-right: 1px solid #D0D0D0;
  padding: 0 0 0 32px;
}

.site-footer .fengongsi {
  line-height: 16px;
  padding: 8px 0;
  margin: 0;
}

  .site-footer .fengongsi dt {
    display: inline;
    margin: 0;
    padding: 0;
  }

  .site-footer .fengongsi dt, .fengongsi dd {
    float: left;
    padding: 0;
    font-weight: normal;
  }

  .site-footer .fengongsi dd {
    margin: 0 8px 0 0;
  }

    .site-footer .fengongsi dd a {
      display: block;
      color: #999;
    }

      .site-footer .fengongsi dd a:hover {
        color: #4b72b0;
        text-decoration: underline;
      }

.site-footer .foot_site_more {
}

  .site-footer .foot_site_more img {
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    -webkit-transform: rotate(0);
    transform: rotate(0);
    -moz-transition: transform 0.2s ease-out 0; /* Firefox 4 */
    -webkit-transition: transform 0.2s ease-out 0; /* Safari 和 Chrome */
    -o-transition: transform 0.2s ease-out 0;
    transition: transform 0.2s ease-out 0; /* Opera */
  }

  .site-footer .foot_site_more.active img {
    -moz-transform: rotate(-180deg);
    -ms-transform: rotate(-180deg);
    -o-transform: rotate(-180deg);
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg);
  }

.site-footer .zizhi {
  font-size: 12px;
  padding: 0 0 8px;
  margin: 0;
  list-style: none;
}

  .site-footer .zizhi li {
    float: left;
    padding: 0;
  }

  .site-footer .zizhi .icpbei {
  }

  .site-footer .zizhi .gonganbei {
    margin-left: 8px;
  }

  .site-footer .zizhi .rencaizheng {
    margin-left: 8px;
  }

.site-footer-row3 {
  float: left;
  width: 286px;
  padding: 0 0 0 32px;
  margin: 0;
}

.site-footer .site-num {
  float: left;
  font-size: 30px;
  font-family: Arial;
  color: #EF514E;
  padding: 0;
  line-height: 60px;
  font-weight: bold;
}

.site-footer .site-code {
  float: right;
  width: 60px;
  height: 60px;
}

@media(max-width:1199px) {
  .footer-box {
    display: none;
  }
}

.sitemore-box {
  width: 471px;
  border: solid 1px #595959;
  background-color: #fff;
  line-height: 24px;
}

.sitemore-list {
  overflow: hidden;
  padding: 0 6px 0 0;
  margin: 0;
  list-style: none;
}

.sitemore-title {
  width: auto;
  background-color: #4298CD;
  height: 24px;
  color: #fff;
  padding: 0 0 0 6px;
  margin: 0;
}

.sitemore-item {
  background-color: #fff;
  height: 22px;
  color: #5A5A5A;
  float: left;
  margin-left: 6px;
}

  .sitemore-item a:hover {
    text-decoration: underline;
  }

.sitemore-title-close {
  background: url("images/round_delete.png") no-repeat scroll 0 0 transparent;
  float: right;
  height: 11px;
  margin: 6px 3px 0 0;
  width: 10px;
  cursor: pointer;
}

.sitemore-icon {
  position: relative;
}

/**/
.row.performance-summary .col-md-3 {
  padding: 0 5px;
}
/*喜报 start*/
.im-addearning-panel {
  background: #d7d7d7;
}

  .im-addearning-panel .dialog-footer {
    text-align: left;
    padding: 0 0 0 10px;
    background: #BCBCBC;
    font-size: 12px;
    font-weight: bold;
  }

    .im-addearning-panel .dialog-footer .my-dialog-form {
      margin-right: 10px;
    }

      .im-addearning-panel .dialog-footer .my-dialog-form .btn-submit {
        float: right;
        margin: 0;
      }

      .im-addearning-panel .dialog-footer .my-dialog-form .btn-cancel {
        float: right;
        margin: 0 6px 0 5px;
      }

.im-addearning-showpanel {
  margin: 8px;
  padding: 8px;
  border: 1px solid #797979;
  background: #F2F2F2;
}

  .im-addearning-showpanel .im-addearning-photopanel {
    float: left;
    width: 92px;
    height: 92px;
    overflow: hidden;
  }

  .im-addearning-showpanel .im-addearning-rpanel {
    float: left;
    width: 362px;
    overflow: hidden;
  }

  .im-addearning-showpanel .im-addearning-tab {
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 14px;
    font-weight: bold;
    width: 100%;
  }

    .im-addearning-showpanel .im-addearning-tab tr {
      line-height: 30px;
    }

    .im-addearning-showpanel .im-addearning-tab td {
      white-space: nowrap;
      text-align: left;
      overflow: hidden;
      max-width: 100%;
    }

    .im-addearning-showpanel .im-addearning-tab .im-title {
      float: left;
      width: 80px;
      padding: 0 10px 0 0;
      text-align: right;
    }

    .im-addearning-showpanel .im-addearning-tab span {
      color: #FB6633;
      float: left;
    }

    .im-addearning-showpanel .im-addearning-tab .cnspan {
      width: 272px;
      overflow: hidden;
    }

.im-addearning-savepanel {
}

.im-addearning-save {
  margin: 8px;
  padding: 8px;
  border: 1px solid #797979;
  background: #F2F2F2;
}

  .im-addearning-save .im-addearning-photopanel {
    float: left;
    width: 92px;
    height: 92px;
    overflow: hidden;
  }

  .im-addearning-save .im-addearning-rpanel {
    float: left;
    margin: 0 0 0 10px;
  }

  .im-addearning-save .im-addearning-tab {
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 18px;
    font-weight: bold;
    font-family: '微软雅黑', '宋体';
  }

    .im-addearning-save .im-addearning-tab tr {
      line-height: 30px;
    }

    .im-addearning-save .im-addearning-tab td {
      white-space: nowrap;
      text-align: left;
    }

    .im-addearning-save .im-addearning-tab .im-title {
      display: inline-block;
      width: auto;
      padding: 0;
      text-align: right;
    }

    .im-addearning-save .im-addearning-tab span {
      color: #FB6633;
    }
/*喜报 end*/
/*首页喜报 start*/
.home-earning-panel {
  width: 100%;
  height: 100%;
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 9996;
  overflow: hidden;
  background: #000;
  filter: alpha(opacity=50);
  -moz-opacity: 0.5;
  -khtml-opacity: 0.5;
  opacity: 0.5;
}

.hep-con-panel {
  width: 960px;
  display: none;
  margin: 0 auto;
  background-color: transparent;
  position: absolute;
  z-index: 9997;
  left: 50%;
  margin-left: -480px;
  top: 75px;
  overflow: hidden;
  font-size: 14px;
  font-family: 'Microsoft YaHei', '黑体';
}

  .hep-con-panel .hep-con-img1 {
    position: absolute;
    left: 0;
    top: 0;
    width: auto;
    height: 0;
    overflow: hidden;
    filter: alpha(opacity=100);
    -moz-opacity: 1;
    -khtml-opacity: 1;
    opacity: 1;
  }

  .hep-con-panel .hep-con-img2 {
    position: absolute;
    right: 0;
    bottom: 0;
    width: auto;
    height: 0;
    overflow: hidden;
  }

  .hep-con-panel .hep-con-info-p {
    display: block;
    padding: 63px 0 0;
    margin: 0;
  }

  .hep-con-panel .hep-con-info {
    width: 528px;
    height: auto;
    margin: 10px auto 0;
    background: #fff;
    border: 1px solid transparent;
    border-radius: 10px !important;
  }

    .hep-con-panel .hep-con-info .d-pic-p {
      text-align: center;
      margin-top: -63px;
    }

      .hep-con-panel .hep-con-info .d-pic-p img {
        width: 127px;
        height: 127px;
        border: 5px solid #ED5057;
        border-radius: 50% !important;
        overflow: hidden;
      }

    .hep-con-panel .hep-con-info .titlepanel {
      padding: 8px 0;
      ;
      margin: 7px 0;
      position: relative;
      text-align: center;
      font-family: 'Arial Negreta', 'Arial';
      text-indent: 1em;
      font-size: 32px;
      font-weight: bold;
    }

      .hep-con-panel .hep-con-info .titlepanel .d-b-line {
        position: absolute;
        width: 55px;
        height: 0;
        border-top: 2px solid #ddd;
        left: 50%;
        bottom: 0;
        margin-left: -27px;
      }

    .hep-con-panel .hep-con-info .d-amount-p {
      text-align: center;
      font-size: 22px;
      font-weight: normal;
      text-indent: 1em;
    }

    .hep-con-panel .hep-con-info .con-panel {
    }

      .hep-con-panel .hep-con-info .con-panel .table-scrollable {
        width: 80%;
        margin: 10px auto !important;
        border-top: 0 none;
      }

        .hep-con-panel .hep-con-info .con-panel .table-scrollable tbody > tr > td:first-child {
          text-align: right;
          min-width: 90px;
        }

    .hep-con-panel .hep-con-info .footer-info {
      padding: 6px 18px;
      overflow: hidden;
      background: #f5f5f5;
      border-top: 1px solid #E3E3E3;
    }

      .hep-con-panel .hep-con-info .footer-info label {
        float: left;
        font-weight: normal;
      }

      .hep-con-panel .hep-con-info .footer-info .btnclose {
        float: right;
        cursor: pointer;
        line-height: 100%;
      }

      .hep-con-panel .hep-con-info .footer-info .btninfo {
        float: right;
        margin: 4px 18px 0 0;
        font-size: 16px;
        color: #666;
        text-decoration: underline;
      }

        .hep-con-panel .hep-con-info .footer-info .btninfo:hover {
          color: #000;
          text-decoration: none;
        }

        .hep-con-panel .hep-con-info .footer-info .btninfo.nogo {
          color: #A9A9A9;
          text-decoration: none;
        }

          .hep-con-panel .hep-con-info .footer-info .btninfo.nogo:hover {
            color: #A9A9A9;
            text-decoration: none;
          }

    .hep-con-panel .hep-con-info .im-addearning-save .im-addearning-rpanel {
      width: 360px;
      overflow: hidden;
    }
/*首页喜报 end*/

/*列表页公用部分*/
.page-title > h1 > i {
  speak: none;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  display: inline-block;
  line-height: 14px;
  -webkit-font-smoothing: antialiased;
  color: #697882;
  font-size: 22px; /*width: 25px; height: 25px;*/
  margin: 0 8px 0 0;
  vertical-align: baseline;
}

.page-title .icon-resumesearch {
  background: transparent url("/Content/images/icon-search.png") center center no-repeat;
  width: 25px;
  height: 25px;
}

.manage-search-group {
  border: 2px solid #4898DB;
}

  .manage-search-group .msg-txt {
    border: 0 none;
    background: #F8FCFF;
  }

.termcustomlist {
  margin: 12px 0;
}

  .termcustomlist .tcl-item {
    display: inline-block;
    padding: 6px 34px 6px 10px;
    background-color: #3398DC;
    position: relative;
    color: white;
    -moz-transition: background-color 0.3s ease-in-out; /* Firefox 4 */
    -webkit-transition: background-color 0.3s ease-in-out; /* Safari 和 Chrome */
    -o-transition: background-color 0.3s ease-in-out;
    transition: background-color 0.3s ease-in-out;
    text-decoration: none;
    margin-right: 10px;
    margin-bottom: 8px;
    max-width: 100%;
    word-break: break-all;
    word-wrap: break-word;
  }

    .termcustomlist .tcl-item:hover {
      background-color: #147BC0;
    }

    .termcustomlist .tcl-item:last-child {
      margin-right: 0;
    }

  .termcustomlist .tcl-del {
    position: absolute;
    right: 8px;
    top: 10px;
    width: 11px;
    height: 11px;
    background: transparent url("/Content/images/tcl-del.png") center center no-repeat scroll;
  }

.tarmcustomsearch {
  margin: 0 0 0;
  border-top: 1px solid transparent;
  position: relative;
}

  .tarmcustomsearch .sx-panel {
    float: left;
    margin: 5px 12px 0 0;
    position: relative;
  }
    /*.tarmcustomsearch {margin: 8px 0 0;border-top: 1px solid transparent;position: relative;}*/
    /*.tarmcustomsearch .sx-panel {float: left;margin: 0 12px 0 0;position: relative;}*/
    /*.tarmcustomsearch .sx-panel:last-child { margin: 0; }*/

    .tarmcustomsearch .sx-panel .selectfilter-box .selectfilter-tip {
      position: relative;
      padding: 6px 30px 6px 12px;
      border-radius: 5px !important;
      border: 1px solid #C4C4C4;
      transition: border-color 0.2s ease-in-out;
      cursor: pointer;
      background: #fff;
      height: auto;
      font-weight: normal;
    }

    .tarmcustomsearch .sx-panel .selectfilter-tip::selection {
      background: #fff;
    }

    .tarmcustomsearch .sx-panel .selectfilter-tip:hover {
      border-color: #8A7A7A;
    }

    .tarmcustomsearch .sx-panel .selectfilter-tip:before {
      display: inline-block;
      position: absolute;
      right: 12px;
      top: 11px;
      width: 0;
      height: 0;
      border-color: #616161 transparent;
      border-style: solid dashed;
      border-width: 0 4px 4px;
      content: '';
      transition: border-color 0.3s ease-in-out;
    }

    .tarmcustomsearch .sx-panel .selectfilter-tip:hover:before {
      border-color: #8B8A8A transparent;
    }

    .tarmcustomsearch .sx-panel .selectfilter-tip:after {
      display: inline-block;
      position: absolute;
      right: 12px;
      top: 17px;
      width: 0;
      height: 0;
      border-color: #616161 transparent;
      border-style: solid dashed;
      border-width: 4px 4px 0;
      content: '';
      transition: border-color 0.3s ease-in-out;
    }

    .tarmcustomsearch .sx-panel .selectfilter-tip:hover:after {
      border-color: #8B8A8A transparent;
    }

    .tarmcustomsearch .sx-panel .selectfilter-box {
      background: url("/css/images/saffh2.jpg") 0 -55px repeat-x !important;
      margin-top: 0;
    }

      .tarmcustomsearch .sx-panel .selectfilter-box > select {
        display: none;
      }

      .tarmcustomsearch .sx-panel .selectfilter-box .selectfilter-list {
        padding: 0;
      }

    .tarmcustomsearch .sx-panel .sx-con {
      position: relative;
      padding: 6px 30px 6px 12px;
      border-radius: 5px !important;
      border: 1px solid #C4C4C4;
      transition: border-color 0.2s ease-in-out;
      cursor: pointer;
      background-color: #fff;
    }
      /*.tarmcustomsearch .sx-panel .sx-con2{padding-bottom:2px;}*/

      .tarmcustomsearch .sx-panel .sx-con::selection {
        background: #fff;
      }

      .tarmcustomsearch .sx-panel .sx-con:hover {
        border-color: #8A7A7A;
      }

      .tarmcustomsearch .sx-panel .sx-con:before {
        display: inline-block;
        position: absolute;
        right: 12px;
        top: 11px;
        width: 0;
        height: 0;
        border-color: #616161 transparent;
        border-style: solid dashed;
        border-width: 0 4px 4px;
        content: '';
        transition: border-color 0.3s ease-in-out;
      }

      .tarmcustomsearch .sx-panel .sx-con:hover:before {
        border-color: #8B8A8A transparent;
      }

      .tarmcustomsearch .sx-panel .sx-con:after {
        display: inline-block;
        position: absolute;
        right: 12px;
        top: 17px;
        width: 0;
        height: 0;
        border-color: #616161 transparent;
        border-style: solid dashed;
        border-width: 4px 4px 0;
        content: '';
        transition: border-color 0.3s ease-in-out;
      }

      .tarmcustomsearch .sx-panel .sx-con:hover:after {
        border-color: #8B8A8A transparent;
      }

    .tarmcustomsearch .sx-panel .submit {
      background: #21639c;
      border: 0 none;
      padding: 3px 8px;
      color: #fff;
      cursor: pointer;
    }

      .tarmcustomsearch .sx-panel .submit:hover {
        background: #236fb0;
      }

    .tarmcustomsearch .sx-panel .sx-item-panel {
      position: absolute;
      top: 33px;
      left: 0;
      padding: 5px;
      background: #fff;
      box-shadow: 5px 5px rgba(102, 102, 102, 0.1);
      border: 1px solid #eee;
      z-index: 5;
      display: none;
      -webkit-border-radius: 4px;
      -moz-border-radius: 4px;
      -ms-border-radius: 4px;
      -o-border-radius: 4px;
      border-radius: 4px;
    }

      .tarmcustomsearch .sx-panel .sx-item-panel.selected {
        display: block;
        z-index: 6;
      }

    .tarmcustomsearch .sx-panel .sx-item-tab {
      border-collapse: collapse;
      border-spacing: 0;
    }

      .tarmcustomsearch .sx-panel .sx-item-tab .radio input[type="radio"], .sx-panel .sx-item-tab .radio-inline input[type="radio"], .sx-panel .sx-item-tab .checkbox .tarmcustomsearch input[type="checkbox"], .sx-.tarmcustomsearch panel .sx-item-tab .checkbox-inline input[type="checkbox"] {
        margin-left: -10px;
      }

      .tarmcustomsearch .sx-panel .sx-item-tab tr {
        line-height: 26px;
      }

      .tarmcustomsearch .sx-panel .sx-item-tab td {
        border: 0 none;
        white-space: nowrap;
      }

      .tarmcustomsearch .sx-panel .sx-item-tab .radio span {
        display: inline-block;
        margin: 0;
      }

    .tarmcustomsearch .sx-panel .daterangepicker-text {
      display: none;
    }

    .tarmcustomsearch .sx-panel .text-2 {
      height: 22px;
      line-height: 22px;
      width: 120px;
      margin-right: 5px;
    }

    .tarmcustomsearch .sx-panel .selectfilter-ul {
      padding: 0;
    }

  .tarmcustomsearch .text-5 {
    height: 25px !important;
    margin-top: 1px;
    width: 170px;
    border: 1px solid #d3d3d3;
    border-radius: 5px;
    font-size: 12px;
    -moz-box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Firefox3.6+ */
    -webkit-box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Chrome5+, Safari5+ */
    box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Latest Opera */
  }

    .tarmcustomsearch .text-5 .outbox-item-list li {
      margin: 1px 3px 0 3px;
    }

    .tarmcustomsearch .text-5 .outbox-tip {
      margin-top: 0 !important;
      line-height: 25px;
    }

    .tarmcustomsearch .text-5 input {
      padding-top: 3px !important;
    }
  /*.tarmcustomsearch .sx-selectuser, .tarmcustomsearch .sx-selectuser *{box-sizing:content-box;}*/
  .tarmcustomsearch .sx-panel .sp-company-list {
    width: 525px;
    box-sizing: content-box;
  }

    .tarmcustomsearch .sx-panel .sp-company-list .sp-company-item {
      float: left;
      width: 105px;
      overflow: hidden;
      margin: 0 0 4px;
    }

      .tarmcustomsearch .sx-panel .sp-company-list .sp-company-item a {
        color: #21639C;
      }

        .tarmcustomsearch .sx-panel .sp-company-list .sp-company-item a:hover {
          color: #FC8B00;
        }

.xz_parent2 {
  width: 520px;
  position: absolute;
  top: 130px;
  display: none;
  z-index: 111;
  font-size: 12px;
}

  .xz_parent2 .xz_top {
    height: 5px;
    background: url(/static/style/115.png) no-repeat;
    line-height: 0;
    font-size: 0;
    clear: both;
  }

  .xz_parent2 .xz_cent {
    border-left: 3px #1a7abe solid;
    border-right: 3px #1a7abe solid;
  }

  .xz_parent2 .biaoti {
    height: 36px;
    background: url(/static/style/117.png) repeat-x 0 top;
  }

  .xz_parent2 .bt_left {
    width: 100px;
    float: left;
    font-weight: bold;
    font-size: 14px;
    color: #0b68ab;
    line-height: 35px;
    padding-left: 12px;
  }

  .xz_parent2 .bt_right {
    width: 160px;
    float: right;
    height: 36px;
    line-height: 35px;
  }

    .xz_parent2 .bt_right input {
      float: right;
      margin: 0 5px;
      background: url(/static/style/49.png) no-repeat;
      width: 50px;
      height: 21px;
      text-align: center;
      line-height: 21px;
      border: 0;
      color: #fff;
      font-weight: bold;
      margin-top: 7px;
      cursor: pointer;
    }

  .xz_parent2 .the_cont_rong {
    background: #fff;
    -webkit-filter: Alpha(Opacity=90);
    -moz-filter: Alpha(Opacity=90);
    -o-filter: Alpha(Opacity=90);
    filter: Alpha(Opacity=90);
    -ms-opacity: .9;
    opacity: .9;
    padding: 0 10px;
  }

  .xz_parent2 .txtcompanel {
    background: #fff;
    display: block;
    height: 30px;
    padding: 0 10px;
  }

    .xz_parent2 .txtcompanel .textbox {
      padding: 0;
      background-color: #fff;
      display: inline-block;
      white-space: nowrap;
      margin: 0;
      overflow: hidden;
      vertical-align: middle;
      border: 1px solid #ccc;
      -ms-border-radius: 3px;
      border-radius: 3px;
    }

      .xz_parent2 .txtcompanel .textbox .textbox-text {
        background: none;
        border: 0 none;
        width: 200px;
      }

    .xz_parent2 .txtcompanel .txtcomtip {
      display: inline-block;
      margin: 0;
    }

  .xz_parent2 .checked-area {
    padding: 10px 0;
    background: #fff;
    overflow: hidden;
    min-height: 22px;
    height: auto;
    overflow: hidden;
    width: 100%;
  }

    .xz_parent2 .checked-area span {
      height: 22px;
      line-height: 22px;
      white-space: nowrap;
      border: 1px solid #f46e6e;
      margin: 0 0 0 15px;
      overflow: hidden;
      position: relative;
      padding: 0 0 0 7px;
      display: inline-block;
      background-color: #f9d2ce;
    }

  .xz_parent2 .cancel-item {
    width: 12px;
    height: 11px;
    background: url(/css/images/dot.gif) no-repeat left top;
    cursor: pointer;
    margin: 0 8px;
    vertical-align: middle;
    display: inline-block;
  }

  .xz_parent2 .select-all {
    padding-left: 10px;
  }

  .xz_parent2 .reset-all {
    padding-left: 5px;
  }

  .xz_parent2 .list-wrap {
    padding: 8px 0;
    width: 494px;
    float: left;
  }

  .xz_parent2 .list-item1, .xz_parent2 .list-item0,
  .xz_parent2 .list-item2 {
    float: left;
    width: 164px;
    height: 28px;
    line-height: 28px;
    cursor: pointer;
    position: relative;
    list-style-type: none;
  }

  .xz_parent2 .list-item1 {
  }

    .xz_parent2 .list-item1:hover .second-select-list {
      display: block !important;
    }

  .xz_parent2 .list-wrap li span {
    float: left;
    color: #333;
  }

    .xz_parent2 .list-wrap li span label {
      display: block;
      float: left;
      padding: 0 3px;
      margin-top: 2px;
    }

      .xz_parent2 .list-wrap li span label input {
        display: inline;
        padding: 0;
        float: left;
        margin: 0;
        margin-top: 6px;
      }

    .xz_parent2 .list-wrap li span font {
      font-weight: normal;
      display: block;
      float: left;
      font-size: 12px;
      color: #333;
      height: 26px;
      width: 120px;
      overflow: hidden;
    }

  .xz_parent2 .list-wrap li input {
    width: 16px;
    height: 16px;
    margin-right: 10px;
    float: left;
    overflow: hidden;
    margin-top: 5px;
  }

  .xz_parent2 .list-wrap li .box-disabled, .input-box-disabled {
    background: none;
    background-color: #E6E6E6;
    border: #BCBCBC solid 1px;
    margin: 7px 2px 0 0;
    width: 12px;
    height: 12px; /*background:url(/css/images/disabled-input-icon.png)*/
  }

.input-box-disabled {
  float: left;
  height: 16px;
  margin-right: 10px;
  margin-top: 5px;
  overflow: hidden;
  width: 16px;
}

.xz_parent2 .list-item1 label,
.xz_parent2 .list-item2 label {
  height: 28px;
  float: left;
  overflow: hidden;
  width: 115px;
}

.xz_parent2 .list-wrap li span.jiantou {
  float: right;
  height: 26px;
  padding-right: 15px;
  background: url(/static/style/119.png) no-repeat left center;
}

.xz_parent2 .list-wrap li ul li input {
  float: left;
  color: #333;
  display: block;
}

.xz_parent2 .list-wrap li ul li span {
  float: left;
  color: #333;
  width: 130px;
  overflow: hidden;
  text-align: left;
  height: 26px;
  line-height: 26px;
}

.xz_parent2 .xz_bot {
  height: 5px;
  background: url(/static/style/116.png) no-repeat;
  line-height: 0;
  font-size: 0;
}

.xz_parent2 .list-item0 {
  width: 30px;
  text-align:center;
}

.xz_parent2 .active {
  color: blue;
  text-decoration: underline;
}
.xz_parent2 .list-item-hide {
  display:none;
}

.panel {
  overflow: hidden;
  text-align: left;
  margin: 0;
  border: 0;
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  -ms-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}

.combo-panel {
  background-color: #ffffff;
}

.panel-header, .panel-body {
  border: 1px solid #ececec;
}

.panel-header {
  padding: 5px;
  position: relative;
}

.panel-header-noborder {
  border-width: 0 0 1px 0;
}

.panel-body {
  overflow: auto;
  border-top-width: 0;
  padding: 0;
}

.panel-body-noheader {
  border-top-width: 1px;
}

.panel-body-noborder {
  border-width: 0;
}

.panel-body-nobottom {
  border-bottom-width: 0;
}

.combobox-item {
  font-size: 12px;
  padding: 3px;
  padding-right: 0;
  cursor: pointer;
}

  .combobox-item.combobox-item-selected {
    background: #efefef;
    color: #000000;
  }

  .combobox-item.combobox-item-hover {
    background-color: #eaf2ff;
    color: #000000;
  }
/*三级*/
.second-select-list {
  display: none;
  position: absolute;
  width: 484px;
  top: 0;
  left: 100px;
  float: left;
  z-index: 999;
  background: #fff;
  padding: 5px 10px 7px 10px;
  border: 1px #1a7abe solid;
}

  .second-select-list li {
    height: 24px;
    line-height: 24px;
    width: 115px !important;
    float: left;
    white-space: nowrap;
    list-style-type: none;
  }

    .second-select-list li label {
      float: left;
      height: 24px;
      line-height: 24px;
      width: 85px !important;
    }

      .second-select-list li label input {
        padding: 0;
        margin: 4px;
        margin-top: 5px;
        float: left;
        width: 16px;
        height: 16px;
      }

  .second-select-list label span {
    float: left;
    line-height: 24px;
    color: #333;
    width: 90px !important;
  }
/*搜索简历*/
.rnss-manage-page {
}

  .rnss-manage-page .actions .btn-group {
    margin-left: 10px;
  }

.order-list {
  min-width: 200px;
  padding: 0 0 6px;
}

  .order-list .order-item {
    padding: 0;
    margin: 8px 6px 0;
    white-space: nowrap;
  }

    .order-list .order-item .radio {
      display: none;
    }

  .order-list .order-title {
    text-align: right;
    width: 75px;
    display: inline-block;
  }

  .order-list .order-asc {
    margin-right: 6px;
  }

  .order-list .order-item .inputorder {
    display: none;
  }

.r-tb {
  table-layout: fixed;
}

  .r-tb .btn {
    padding: 0 5px;
    margin: 0;
  }

  .r-tb th, .r-tb td {
    overflow: hidden;
    white-space: nowrap;
    -ms-text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
  }

  .r-tb th {
    padding-left: 6px !important;
    padding-right: 6px !important;
  }

  .r-tb td {
    padding-left: 3px !important;
    padding-right: 3px !important;
  }

  .r-tb thead th {
    border-bottom: 1px solid #ddd !important;
  }

  .r-tb tbody td {
    border: 0 none !important;
  }

  .r-tb .r-bl {
    width: 6%;
  }

  .r-tb .r-sfk {
    width: 10%;
  }

  .r-tb .r-zffs {
    width: 18%;
  }

  .r-tb .r-by {
    width: 10%;
  }

  .r-tb .r-gw {
    width: 8%;
  }

  .r-tb .r-status {
    width: 10%;
  }

  .r-tb .r-id {
    text-align: center;
  }

  .r-tb tbody .r-id, .r-tb tbody .r_id {
    text-align: center;
    background: #EAEAEA;
    border: 1px solid #DBDBDB !important;
    width: 43px;
  }

  .r-tb td .take-notes {
    display: block;
    text-align: center;
  }

    .r-tb td .take-notes .r-tn {
      display: inline-block;
      vertical-align: middle;
      background: #4496E0;
      color: white;
      border-radius: 50% !important;
      overflow: hidden;
      width: 23px;
      height: 23px;
      text-align: center;
      line-height: 23px;
    }

.r-tb-mr {
  table-layout: fixed;
  border-width: 0;
}

  .r-tb-mr .btn {
    padding: 0 5px;
    margin: 0;
  }

  .r-tb-mr th, .r-tb-mr td {
    overflow: hidden;
    white-space: nowrap;
    -ms-text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
  }

  .r-tb-mr th {
    padding-left: 6px !important;
    padding-right: 6px !important;
  }

  .r-tb-mr td {
    padding-left: 3px !important;
    padding-right: 3px !important;
  }

  .r-tb-mr thead th {
    border: 1px solid #ddd !important;
  }

  .r-tb-mr tbody td {
    border: 0 none !important;
  }

  .r-tb-mr .r-gender {
    width: 82px;
    padding-left: 7px !important;
  }

    .r-tb-mr .r-gender i {
      display: inline-block;
      vertical-align: middle;
      min-height: 14px;
      min-width: 14px;
    }

    .r-tb-mr .r-gender a {
      font-weight: bold;
    }

  .r-tb-mr .r-age {
    width: 46px;
    text-align: center;
  }

  .r-tb-mr .r-edu {
    width: 60px;
    text-align: center;
  }

  .r-tb-mr .r-year {
    width: 46px;
    text-align: center;
  }

  .r-tb-mr .r-location {
    width: 65px;
  }

  .r-tb-mr .r-company {
  }

  .r-tb-mr .r-jobtitle {
  }

  .r-tb-mr .r-createstaff {
    width: 65px;
  }

  .r-tb-mr .r-tuijian {
    width: 60px;
  }

  .r-tb-mr .r-memo {
    width: 45px;
    text-align: center;
  }

  .r-tb-mr .r-time {
    width: 105px;
    text-align: right;
  }

  .r-tb-mr .resume-icon {
    margin: 0;
    display: inline-block;
    float: none;
    vertical-align: middle;
  }

.r-tb [class*=" fa-"]:not(.fa-stack),
.r-tb [class*=" glyphicon-"],
.r-tb [class*=" icon-"],
.r-tb [class^=fa-]:not(.fa-stack),
.r-tb [class^=glyphicon-],
.r-tb [class^=icon-] {
  vertical-align: middle;
}

.r-tb-item {
}

.r-tb-item-box:nth-child(1) {
  background-color: #FFD3C1!important;
}
.r-tb-item-box:nth-child(2) {
  background-color: #D0EDFF!important;
}
.r-tb-item-box:nth-child(3) {
  background-color: #E0FFF3!important;
}
  .r-tb-mr-summary {
    display: none;
  }

  .r-tb-mr-summary.open {
    display: table-row;
  }

.r-tb > tbody > .r-tb-item:nth-of-type(odd) {
  background-color: #F5F9FC;
}

  .r-tb > tbody > .r-tb-item:nth-of-type(odd):hover {
    background-color: #f5f5f5;
  }

.r-tb > tbody > .r-tb-mr-summary {
  background: #fff !important;
}

  .r-tb > tbody > .r-tb-mr-summary:hover {
    background: #fff !important;
  }

.r-tb-mr-summary .resume-detail-list-panel {
  background: #fff;
}

.r-tb-mr-summary:hover .resume-detail-list-panel { /*background: #fbfbfb;*/
}

.r-tb-mr-summary.open .resume-detail-list-panel {
  display: block;
}

.resume-detail-list-panel {
  overflow: hidden;
  padding: 10px 0 15px 0;
  line-height: 1.6em;
  min-height: 108px;
  position: relative;
  font-size: 12px;
  white-space: normal;
}

  .resume-detail-list-panel .rdsp-left {
    float: left;
    width: 49%;
  }

  .resume-detail-list-panel .rdsp-right {
    float: left;
    width: 50%;
  }

  .resume-detail-list-panel .rdsp-con-panel {
    padding: 0 10px;
  }

    .resume-detail-list-panel .rdsp-con-panel b {
      color: orangered;
    }

  .resume-detail-list-panel .rdsp-list-panel { /*border-left:2px solid #ddd;*/
  }

    .resume-detail-list-panel .rdsp-list-panel .rdsp-item-panel {
      margin: 0 0 5px 10px;
    }

  .resume-detail-list-panel .rdsp-line {
    width: 0;
    border-left: 2px solid #ddd;
    min-height: 108px;
    height: 86%;
    position: absolute;
    left: 49%;
    top: 7%;
  }
/*沟通记录*/
/*.r-tb .take-notes strong{color: #FF0000;font-weight: bold;}*/
.popUpBox {
  position: absolute;
  color: #000;
  z-index: 99;
  border-radius: 6px; /*box-shadow: 4px 4px 6px rgba(0, 0, 0, 0.40);*/
}
/*.popUpBoxWrpe {
    position: relative;
    padding: 10px;
    padding-right: 0;
    z-index: 99;
    background: #FBFDCB url(/css/images/reference-con-1.png) repeat-x center 100%;
    border: 0 none;
}*/
.popUpBoxWrpe {
  position: relative;
  padding: 10px;
  z-index: 99;
  background: #fcfcfc;
  border: 1px solid #ebeef5;
  background-color: #fafafa;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.3);
  transition: opacity .3s,transform .3s,left .3s,right .3s,top .4s,bottom .3s;
  border-radius: 6px !important;
  font-family: "微软雅黑";
  color: #606266;
}

.PB-content {
  font-family: "微软雅黑";
  color: #2BA0E3;
  line-height: 38px;
  font-size: 18px;
  border-bottom: 1px solid #eee;
}

.PB_zhiXingRen_staffname {
  font-size: 16px;
  color: #363636;
  position: relative;
  padding-left: 4px;
}

  .PB_zhiXingRen_staffname::before {
    content: '';
    position: absolute;
    top: 10px;
    left: -10px;
    width: 6px;
    height: 6px;
    background: #2BA0E3;
    border-radius: 50% !important;
  }

.popUpBoxJianTou {
  display: none;
  z-index: 101;
  position: absolute;
  top: -6px;
  left: 20%;
  height: 6px;
  width: 11px;
  background: url(/css/images/jiantou10.gif) no-repeat;
}

.jianLiBaoGao ul {
  max-width: 285px;
}

  .jianLiBaoGao ul li {
    display: inline;
    float: left;
    min-width: 50px;
    overflow: hidden;
    margin-right: 5px;
  }

.popUpBox .close {
  z-index: 100;
  cursor: pointer;
  position: absolute;
  top: 0;
  right: 6px;
  background: url(/css/images/reference-close.gif) no-repeat 10px 10px;
  width: 28px;
  height: 28px;
  float: right;
  margin-right: 0;
  text-indent: -500px;
  overflow: hidden;
}

.PB-zhiXingRen {
  /*float: left;*/
  width: 65px;
  text-align: left;
  margin: 0 0 0 10px;
}
.PB-zhiXingRen, PB-neiRong, PB-shiJian {
  display: block;
}
.PB-neiRong {
  /*float: left;*/
  width: 400px;
  overflow: hidden;
  padding: 0 10px;
  margin: 0 5px;
  white-space: normal !important;
}

.PB-shiJian {
  /*float: left;*/
  width: 102px;
}
.PB-zhiXingRen, .PB-neiRong, .PB-shiJian {
  display: inline-block;
}
/*分页*/
.rnss-paging {
  position: absolute;
  left: 15px;
  top: 3px;
}
/*客户管理*/
.r-tb-mc .r-id {
  width: 43px;
}

.r-tb-mc .r-cid {
  width: 66px;
  text-align: center;
}

.r-tb-mc .r-source {
  width: 43px;
  text-align: center;
}

.r-tb-mc .r-clientname {
}

  .r-tb-mc .r-clientname.rc_col_inner {
    padding-right: 67px !important;
    position: relative;
  }

    .r-tb-mc .r-clientname.rc_col_inner .btn {
      position: absolute;
      right: 2px;
      top: 9px;
    }

.r-tb-mc .r-clientIdentification {
  width: 54px;
  text-align: center;
}

.r-tb-mc .r-contactname {
  width: 65px;
}

.r-tb-mc .r-jobcount {
  width: 72px;
  text-align: center;
}

.r-tb-mc .r-teams {
  width: 73px;
}

.r-tb-mc .r-company-child {
  width: 62px;
  text-align: center;
}

.r-tb-mc .r-company {
  width: 82px;
  text-align: center;
}

.r-tb-mc .r-status {
  width: 43px;
  text-align: center;
}

.r-tb-mc .r-down {
  width: 43px;
  text-align: center;
}

.r-tb-mc .r-time {
  width: 110px;
}

.r-tb-mc .r-memo {
  width: 45px;
  border-right: 0 none;
  text-align: center;
}

.r-tb-mc .r-bj {
  width: 40px;
  text-align: center;
  border: 0 none;
}

.client-icon {
  display: inline-block;
  width: 16px;
  height: 16px !important;
  background-image: url(/css/images/client-icon.png);
  background-repeat: no-repeat;
  float: none;
  margin: 0;
  vertical-align: middle;
}

.rnss-manage-client {
}

  .rnss-manage-client .order-item .order-title {
    width: 105px;
  }
/*客户的小图标资源*/
.client-icon-1 {
  background-position: 0 0;
}

.client-icon-2 {
  background-position: 0 -16px;
}

.client-icon-3 {
  background-position: 0 -32px;
}

.client-icon-4 {
  background-position: 0 -48px;
}

.client-icon-5 {
  background-position: 0 -64px;
}

.client-icon-6 {
  background-position: 0 -80px;
}

.client-icon-7 {
  background-position: 0 -96px;
}

.client-icon-8 {
  background-position: 0 -112px;
}

.client-icon-9 {
  background-position: 0 -128px;
}

.client-icon-10 {
  background-position: 0 -144px;
}

.client-icon-11 {
  background-position: 0 -160px;
}

.client-icon-12 {
  background-position: 0 -176px;
}

.client-icon-27 {
  background-position: 0 -418px;
}

.client-icon-28 {
  background-position: 0 -434px;
}

.client-vipicon-1 {
  background-position: 0 -192px;
}
/*客户分级 start*/
.client-vipicon-2 {
  background-position: 0 -208px;
}

.client-vipicon-3 {
  background-position: 0 -224px;
}

.client-vipicon-4 {
  background-position: 0 -240px;
}

.client-vipicon-5 {
  background-position: 0 -256px;
}

.client-vipicon-6 {
  background-position: 0 -272px;
}

.client-vipicon-7 {
  background-position: 0 -288px;
}

.client-vipicon-8 {
  background-position: 0 -304px;
}

.client-vipicon-9 {
  background-position: 0 -320px;
}

.client-vipicon-10 {
  background-position: 0 -336px;
}

.client-vipicon-11 {
  background-position: 0 -352px;
}

.client-vipicon-12 {
  background-position: 0 -368px;
}

.client-vipicon-13 {
  background-position: 0 -384px;
}

.client-vipicon-14 {
  background-position: 0 -400px;
}
/*客户分级 end*/
.clientsource-icon-1 {
  background-image: url(/css/images/client-icon.png);
  background-repeat: no-repeat;
  background-position: 0 0;
}

.clientsource-icon-2 {
  background-image: url(/css/images/client-icon.png);
  background-repeat: no-repeat;
  background-position: 0 -16px;
}

.clientsource-icon-3 {
  background-image: url(/css/images/client-icon.png);
  background-repeat: no-repeat;
  background-position: 0 -32px;
}

.clientsource-icon-4 {
  background-image: url(/css/images/client-icon.png);
  background-repeat: no-repeat;
  background-position: 0 -48px;
}

.clientsource-initialpayment-1 {
  background-image: url(/css/images/client-icon.png);
  background-repeat: no-repeat;
  background-position: 0 -160px;
}

.clientsource-initialpayment-2 {
  background-image: url(/css/images/client-icon.png);
  background-repeat: no-repeat;
  background-position: 0 -176px;
}

/*职位管理 begin*/
.r-tb-managejob .r-id {
  width: 43px;
}

.r-tb-managejob .r-jobstatus {
  width: 65px;
  text-align: center;
  overflow: visible;
}

  .r-tb-managejob .r-jobstatus .r-icon {
    margin-right: 6px;
  }

.r-tb-managejob .r-publicstatus {
  width: 65px;
  text-align: center;
  overflow: visible;
}

.rnss-manage-job .dropdown-menu {
  min-width: 110px;
}

.r-tb-managejob .r-publicstatus .iconfont {
  margin-bottom: 0;
  margin-right: 6px;
}

  .r-tb-managejob .r-publicstatus .iconfont.icon-yanjing {
    margin-bottom: 4px;
  }

.r-tb-managejob .r-jobsalary {
  width: 80px;
}

.r-tb-managejob td.r-jobsalary {
  font-weight: bold;
  text-align: left;
  padding-left: 5px !important;
}

.r-tb-managejob th.r-jobsalary {
  text-align: center;
}

.r-tb-managejob .r-location {
  width: 50px;
  text-align: center;
}

.r-tb-managejob .r-company {
  width: 80px;
  text-align: center;
}

.r-tb-managejob .r-team {
  width: 75px;
}

.r-tb-managejob .r-jobtitle {
  width: 200px;
}

  .r-tb-managejob .r-jobtitle .r-icon {
    margin-right: 3px;
  }

.r-tb-managejob .r-lastcomm {
  width: 110px;
}

.r-tb-managejob .r-activation {
  width: 45px;
}

.rnss-mj-hj-panel {
  position: relative;
  float: right;
  width: 522px;
}

  .rnss-mj-hj-panel .r-title {
    position: absolute;
    left: -46px;
    top: 0;
  }

.rnss-manage-job .data-manipalation {
  overflow: visible;
}
/*职位管理 end*/



/*公用图标*/
.r-icon {
  display: inline-block;
  width: 16px !important;
  height: 16px !important;
  background-image: url(/css/images/risfondIcon.gif) !important;
  background-repeat: no-repeat !important;
  vertical-align: middle;
}

.r-icon-word {
  background-position: -64px -464px;
}

.r-icon-excel {
  background-position: -64px -480px;
}

.r-icon-ppt {
  background-position: -64px -496px;
}

.r-icon-pdf {
  background-position: -64px -512px;
}

.r-icon-rar {
  background-position: -64px -528px;
}

.r-icon-jpg {
  background-position: -64px -544px;
}

.r-icon-other {
  background-position: -64px -560px;
}

.r-icon-wu {
  background-position: -64px -576px;
}

.r-icon-details {
  background-position: -64px -96px;
}

.r-icon-fenXiang {
  display: block !important;
  width: 26px;
  height: 26px !important;
  background-image: url(/Css/images/risfondIcon.gif) !important;
  background-repeat: no-repeat !important;
}

.r-icon-fill-out {
  display: block !important;
  width: 62px;
  height: 47px !important;
  background-image: url(/Css/images/risfondIcon.gif) !important;
  background-repeat: no-repeat !important;
}

.r-icon-1 {
  background-position: 0 0;
}

.r-icon-2 {
  background-position: 0 -16px;
}

.r-icon-3 {
  background-position: 0 -32px;
}

.r-icon-4 {
  background-position: 0 -48px;
}

.r-icon-5 {
  background-position: 0 -64px;
}

.r-icon-6 {
  background-position: 0 -80px;
}

.r-icon-7 {
  background-position: 0 -96px;
}

.r-icon-8 {
  background-position: 0 -112px;
}

.r-icon-9 {
  background-position: 0 -128px;
}

.r-icon-10 {
  background-position: 0 -114px;
}

.r-icon-11 {
  background-position: 0 -160px;
}

.r-icon-12 {
  background-position: 0 -176px;
}

.r-icon-13 {
  background-position: 0 -192px;
}

.r-icon-14 {
  background-position: 0 -208px;
}

.r-icon-15 {
  background-position: 0 -224px;
}

.r-icon-16 {
  background-position: 0 -240px;
}

.r-icon-17 {
  background-position: 0 -256px;
}

.r-icon-18 {
  background-position: 0 -272px;
}

.r-icon-19 {
  background-position: 0 -288px;
}

.r-icon-20 {
  background-position: 0 -304px;
}

.r-icon-21 {
  background-position: 0 -320px;
}

.r-icon-22 {
  background-position: 0 -336px;
}

.r-icon-23 {
  background-position: 0 -352px;
}

.r-icon-24 {
  background-position: 0 -368px;
}

.r-icon-25 {
  background-position: 0 -384px;
}

.r-icon-26 {
  background-position: 0 -400px;
}

.r-icon-27 {
  background-position: 0 -416px;
}

.r-icon-28 {
  background-position: 0 -432px;
}

.r-icon-29 {
  background-position: 0 -448px;
}

.r-icon-30 {
  background-position: 0 -464px;
}

.r-icon-31 {
  background-position: 0 -480px;
}

.r-icon-32 {
  background-position: 0 -496px;
}

.r-icon-33 {
  background-position: 0 -512px;
}

.r-icon-34 {
  background-position: 0 -528px;
}

.r-icon-35 {
  background-position: 0 -544px;
}

.r-icon-36 {
  background-position: 0 -560px;
}

.r-icon-37 {
  background-position: 0 -576px;
}

.r-icon-38 {
  background-position: 0 -592px;
}

.r-icon-39 {
  background-position: 0 -608px;
}

.r-icon-40 {
  background-position: 0 -624px;
}

.r-icon-41 {
  background-position: 0 -641px;
}

.r-icon-42 {
  background-position: 0 -656px;
}

.r-icon-43 {
  background-position: 0 -672px;
}

.r-icon-44 {
  background-position: 0 -688px;
}

.r-icon-45 {
  background-position: 0 -704px;
}

.r-icon-46 {
  background-position: 0 -720px;
}

.r-icon-47 {
  background-position: 0 -736px;
}

.r-icon-48 {
  background-position: 0 -752px;
}

.r-icon-49 {
  background-position: 0 -768px;
}

.r-icon-50 {
  background-position: 0 -784px;
}

.r-icon-51 {
  background-position: -32px 0;
}

.r-icon-52 {
  background-position: -32px -16px;
}

.r-icon-53 {
  background-position: -32px -32px;
}

.r-icon-54 {
  background-position: -32px -48px;
}

.r-icon-55 {
  background-position: -32px -64px;
}

.r-icon-56 {
  background-position: -32px -80px;
}

.r-icon-57 {
  background-position: -32px -96px;
}

.r-icon-58 {
  background-position: -32px -112px;
}

.r-icon-59 {
  background-position: -32px -128px;
}

.r-icon-60 {
  background-position: -32px -144px;
}

.r-icon-61 {
  background-position: -32px -160px;
}

.r-icon-62 {
  background-position: -32px -176px;
}

.r-icon-63 {
  background-position: -32px -192px;
}

.r-icon-64 {
  background-position: -32px -208px;
}

.r-icon-65 {
  background-position: -32px -224px;
}

.r-icon-66 {
  background-position: -32px -240px;
}

.r-icon-67 {
  background-position: -32px -256px;
}

.r-icon-68 {
  background-position: -32px -272px;
}

.r-icon-69 {
  background-position: -32px -288px;
}

.r-icon-70 {
  background-position: -32px -304px;
}

.r-icon-71 {
  background-position: -32px -320px;
}

.r-icon-72 {
  background-position: -32px -336px;
}

.r-icon-73 {
  background-position: -32px -352px;
}

.r-icon-74 {
  background-position: -32px -368px;
}

.r-icon-75 {
  background-position: -32px -384px;
}

.r-icon-76 {
  background-position: -32px -400px;
}

.r-icon-77 {
  background-position: -32px -416px;
}

.r-icon-78 {
  background-position: -32px -432px;
}

.r-icon-79 {
  background-position: -32px -448px;
}

.r-icon-80 {
  background-position: -32px -464px;
}

.r-icon-81 {
  background-position: -32px -480px;
}

.r-icon-82 {
  background-position: -32px -496px;
}

.r-icon-83 {
  background-position: -32px -512px;
}

.r-icon-84 {
  background-position: -32px -528px;
}

.r-icon-85 {
  background-position: -32px -544px;
}

.r-icon-86 {
  background-position: -32px -560px;
}

.r-icon-87 {
  background-position: -32px -576px;
}

.r-icon-88 {
  background-position: -32px -592px;
}

.r-icon-89 {
  background-position: -32px -608px;
}

.r-icon-90 {
  background-position: -32px -624px;
}

.r-icon-91 {
  background-position: -32px -640px;
}

.r-icon-92 {
  background-position: -32px -656px;
}

.r-icon-93 {
  background-position: -774px 0;
}

.r-icon-94 {
  background-position: -774px -26px;
}

.r-icon-95 {
  background-position: -774px -52px;
}

.r-icon-96 {
  background-position: -774px -78px;
}

.r-icon-97 {
  background-position: -774px -104px;
}

.r-icon-98 {
  background-position: -774px -130px;
}

.r-icon-99 {
  background-position: -774px -156px;
}

.r-icon-100 {
  background-position: -774px -182px;
}

.r-icon-101 {
  background-position: -774px -208px;
}

.r-icon-102 {
  background-position: -774px -234px;
}

.r-icon-103 {
  background-position: -774px -260px;
}

.r-icon-104 {
  background-position: -774px -286px;
}

.r-icon-105 {
  background-position: -774px -312px;
}

.r-icon-106 {
  background-position: -774px -338px;
}

.r-icon-107 {
  background-position: -774px -364px;
}

.r-icon-108 {
  background-position: -774px -390px;
}

.r-icon-109 {
  background-position: -774px -416px;
}

.r-icon-110 {
  background-position: -774px -442px;
  cursor: pointer;
}

.r-icon-111 {
  background-position: -208px 0;
}

.r-icon-112 {
  background-position: -32px -672px;
}

.r-icon-113 {
  background-position: -32px -688px;
  margin-top: 7px;
  float: right;
}

.r-icon-114 {
  background-position: -32px -704px;
}

.r-icon-117 {
  background-position: -774px -468px;
}

.r-icon-118 {
  background-position: -32px -752px;
}

.r-icon-120 {
  background-position: -64px 0;
}

.r-icon-121 {
  background-position: -32px -784px;
}

.r-icon-124 {
  background-position: -64px -48px;
}

.r-icon-telephone-go {
  background-position: -64px -16px;
}

.r-icon-telephone {
  background-position: -64px -32px;
}

.r-icon-suppliers {
  background-position: -64px -47px;
}

.r-icon-group-link {
  background-position: -64px -63px;
}

.r-icon-updateTime {
  background-position: -64px -79px;
}

.r-icon-128 {
  background-position: -64px -112px;
}

.r-icon-129 {
  background-position: -64px -128px;
}

.r-icon-130 {
  background-position: -64px -144px;
}

.r-icon-131 {
  background-position: -64px -160px;
}

.r-icon-132 {
  background-position: -64px -176px;
}

.r-icon-133 {
  background-position: -64px -192px;
}

.r-icon-134 {
  background-position: -64px -208px;
}

.r-icon-135 {
  background-position: -64px -224px;
}

.r-icon-137 {
  background-position: -64px -256px;
}

.r-icon-138 {
  background-position: -64px -272px;
}

.r-icon-139 {
  background-position: -64px -288px;
}

.r-icon-140 {
  background-position: -64px -304px;
}

.r-icon-141 {
  background-position: -64px -320px;
}

.r-icon-142 {
  background-position: -64px -336px;
}

.r-icon-143 {
  background-position: -64px -352px;
}

.r-icon-144 {
  background-position: -64px -368px;
}

.r-icon-145 {
  background-position: -64px -384px;
}

.r-icon-146 {
  background-position: -64px -400px;
  cursor: pointer;
}

.r-icon-147 {
  background-position: -64px -416px;
  cursor: pointer;
}

.r-icon-148 {
  background-position: -64px -432px;
  cursor: pointer;
}

.r-icon-149 {
  background-position: -64px -448px;
}

.r-icon-150 {
  background-position: -64px -592px;
}

.r-icon-151 {
  background-position: -64px -608px;
}

.r-icon-152 {
  background-position: -64px -624px;
}

.r-icon-153 {
  background-position: -64px -640px;
}

.r-icon-154 {
  background-position: -64px -656px;
  cursor: pointer;
}

.r-icon-155 {
  background-position: -64px -672px;
  cursor: pointer;
}

.r-icon-156 {
  background-position: -64px -688px;
  cursor: pointer;
}

.r-icon-157 {
  background-position: -64px -704px;
  cursor: pointer;
}

.r-icon-158 {
  background-position: -64px -720px;
  cursor: pointer;
  display: inline-block !important;
  width: 12px !important;
  height: 16px !important;
  float: right !important;
  padding-right: 3px;
  margin-top: 8px;
}

.r-icon-159 {
  background-position: -64px -736px;
  cursor: pointer;
  display: inline-block !important;
  width: 12px !important;
  height: 16px !important;
  float: right !important;
  padding-right: 3px;
  margin-top: 8px;
}

.r-icon-160 {
  background-position: -64px -751px;
  cursor: pointer;
  display: inline-block !important;
  width: 12px !important;
  height: 16px !important;
  float: right !important;
  padding-right: 3px;
  margin-top: 8px;
}

.r-icon-161 {
  background-position: -496px -565px;
  cursor: pointer;
  display: block !important;
  width: 2px !important;
  height: 31px !important;
  background-repeat: no-repeat !important;
}

.r-icon-162 {
  background-position: -64px -767px;
}

.r-icon-163 {
  background-position: -64px -783px;
}

.r-icon-164 {
  background-position: -100px -350px;
}

.r-icon-165 {
  background-position: -100px -366px;
}

.r-icon-166 {
  background-position: -100px -382px;
}

.r-icon-167 {
  background-position: -100px -400px;
}

.r-icon-168 {
  background-position: -100px -416px;
}

.r-icon-169 {
  background-position: -100px -432px;
}

.r-icon-170 {
  background-position: -100px -448px;
}

.r-icon-171 {
  background-position: -778px -513px;
  width: 22px !important;
  height: 19px !important;
}

.r-icon-172 {
  background-position: -100px -464px;
}

.r-icon-173 {
  background-position: -100px -481px;
}

.r-icon-174 {
  background-position: 0 0;
  display: block;
  width: 16px;
  height: 16px;
  background: url(/css/images/icon-sms.png);
  background-repeat: no-repeat;
}

.r-icon-175 {
  background: url(/css/images/icon-sms.png) no-repeat 0 -16px;
  display: block;
  height: 16px;
  width: 16px;
  cursor: pointer;
}

.r-icon-176 {
  background-position: -100px -497px;
}

.r-icon-177 {
  background-position: -100px -738px;
}

.r-icon-178 {
  background-position: -100px -752px;
}

.r-icon-179 {
  background-position: -100px -768px;
}

.r-icon-180 {
  background-position: -100px -783px;
}

.r-icon-181 {
  background-position: -132px -320px;
}

.r-icon-182 {
  background-position: -132px -336px;
}

.r-icon-183 {
  background-position: -132px -352px;
}

.r-icon-184 {
  background-position: -132px -368px;
}

.r-icon-185 {
  background-position: -132px -384px;
}

.r-icon-186 {
  background-position: -132px -400px;
}

.r-icon-187 {
  background-position: -132px -416px;
}

.r-icon-188 {
  background-position: -132px -432px;
}

.r-icon-189 {
  background-position: -132px -448px;
}

.r-icon-190 {
  background-position: -132px -464px;
}

.r-icon-191 {
  background-position: -132px -480px;
}

.r-icon-192 {
  background-position: -132px -496px;
}

.r-icon-193 {
  background-position: -132px -512px;
}

.r-icon-194 {
  background-position: -132px -528px;
}

.r-icon-195 {
  background-position: -132px -544px;
}

.r-icon-196 {
  background-position: -132px -560px;
}

.r-icon-197 {
  background-position: -132px -576px;
}

.r-icon-198 {
  background-position: -132px -592px;
}

.r-icon-199 {
  background-position: -132px -608px;
}

.r-icon-200 {
  background-position: -132px -624px;
}

.r-icon-201 {
  background-position: -132px -640px;
}

.r-icon-202 {
  background-position: -132px -656px;
}

.r-icon-caogao {
  display: inline-block;
  width: 16px !important;
  height: 16px !important;
  background-image: url(/css/images/rnss-caogao.png) !important;
  background-repeat: no-repeat !important;
  background-size: 100% 100%;
  vertical-align: middle;
}

.r-icon-yunzuo {
  display: inline-block;
  width: 16px !important;
  height: 16px !important;
  background-image: url(/css/images/rnss-yunzuo.png) !important;
  background-repeat: no-repeat !important;
  background-size: 100% 100%;
  vertical-align: middle;
}

.r-icon-guanbi {
  display: inline-block;
  width: 16px !important;
  height: 16px !important;
  background-image: url(/css/images/rnss-guanbi.png) !important;
  background-repeat: no-repeat !important;
  vertical-align: middle;
  background-size: 100% 100%;
}

.r-icon-jieshu {
  display: inline-block;
  width: 16px !important;
  height: 16px !important;
  background-image: url(/css/images/rnss-jieshu.png) !important;
  background-repeat: no-repeat !important;
  background-size: 100% 100%;
  vertical-align: middle;
}

.r-icon-zanting {
  display: inline-block;
  width: 16px !important;
  height: 16px !important;
  background-image: url(/css/images/rnss-zanting.png) !important;
  background-repeat: no-repeat !important;
  background-size: 100% 100%;
  vertical-align: middle;
}
/*公用提示*/
/*.reference-wrap { position: absolute; color: #000; width: 537px; z-index: 99; border-radius: 6px; box-shadow: 4px 4px 6px rgba(0, 0, 0, 0.40); }
.reference-wrap .close { cursor: pointer; background: url(/css/images/reference-close.gif) no-repeat 10px 10px; width: 28px; height: 28px; float: right; margin-right: 10px; }
.reference-wrap .reference-title { float: left; width: 100%; height: 29px; line-height: 28px; background: url(/css/images/reference-title.png) repeat-x; }
.reference-title h4 { padding-left: 20px; float: left; font-size: 12px;}
.reference-con { clear: both; padding: 8px 20px 15px 20px; line-height: 25px; background: #FBFDCB url(/css/images/reference-con-1.png) repeat-x center 100%; }
.reference-foot { background: url(/css/images/reference-con-2.png) repeat scroll center bottom transparent; height: 7px; }*/
.gouTongJiLu {
  max-width: 630px;
  color: #000 !important;
}

  .gouTongJiLu .popUpBoxJianTou {
    top: 7px;
    right: -30px;
    left: auto;
    background: url(/css/images/jiantouleft.gif) no-repeat;
    height: 15px;
    width: 30px;
  }

  .gouTongJiLu .popUpBoxCon {
    word-break: break-all; /*支持IE，chrome，FF不支持*/
    word-wrap: break-word; /*支持IE，chrome，FF*/
  }

  .gouTongJiLu .popUpBoxJianTouRight {
    top: 7px;
    right: auto;
    left: -6px;
    background: url(/css/images/jiantouright.gif) no-repeat;
    height: 15px;
    width: 6px;
  }

  .gouTongJiLu ul {
    max-width: 600px;
    min-width: 200px;
    overflow: hidden;
    text-align: left;
  }

  .gouTongJiLu li {
    float: left;
    display: inline;
    width: 100%;
    line-height: 25px;
    overflow: hidden;
    margin-bottom: 16px;
  }

    .gouTongJiLu li:hover {
      background: none !important;
    }

.popupTitle-box.popup-showtitle {
  word-break: break-all; /*支持IE，chrome，FF不支持*/
  word-wrap: break-word; /*支持IE，chrome，FF*/
  white-space: normal;
}

.r-reference {
}

  .r-reference .modal-body {
  }

    .r-reference .modal-body p {
      margin: 0 0 10px;
      text-indent: .5em;
    }

    .r-reference .modal-body div {
      margin: 0 0 20px;
      text-indent: .5em;
    }

    .r-reference .modal-body table {
      table-layout: fixed;
      width: 100%;
      margin: 0;
      padding: 0;
      border-spacing: 0;
      border-collapse: collapse;
    }

      .r-reference .modal-body table th, .r-reference .modal-body table td {
        text-align: center;
      }

      .r-reference .modal-body table tbody td {
        padding: 6px 0 0;
      }
/*标记重要*/
.topmosticon {
  display: block;
  width: 16px !important;
  height: 16px !important;
  background-image: url(/css/images/risfondIcon.gif) !important;
  background-repeat: no-repeat !important;
  margin: 0 auto;
}

.Topmostgray {
  display: none;
  background-position: -64px -688px;
  cursor: pointer;
}

.Topmostgold {
  display: block;
  background-position: -64px -704px;
  cursor: pointer;
}

.markimportpanel .Topmostgray {
  display: inline-block;
  background-position: -64px -688px;
  cursor: pointer;
  vertical-align: middle;
}

.markimportpanel .Topmostgold {
  background-position: -64px -704px;
}

.markimportpanel .noTopmostgray {
  display: inline-block;
  background-position: -64px -688px;
  cursor: default;
}

.markimportpanel .noTopmostgold {
  background-position: -64px -704px;
}

.r-actionbar .data-manipalation {
  margin-bottom: 20px;
}

/*操作列表功能样式*/
.data-manipalation {
  overflow: hidden;
  padding: 0 32px;
  font-weight: bold;
}

  .data-manipalation span.check-all {
    height: 14px;
    line-height: 14px;
    margin-top: 12px;
  }

    .data-manipalation span.check-all input {
      vertical-align: middle;
    }

  .data-manipalation .btn {
    margin-left: 10px;
  }

  .data-manipalation .btn-group {
    margin-left: 10px;
  }

/*分页*/
.rnss-pager-p {
  position: relative;
  text-align: center;
  width: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
  min-height: 20px;
}

.rnss-pager {
  display: inline-block;
  vertical-align: middle;
  white-space: nowrap;
  text-align: center;
  margin-left: 100px;
}

  .rnss-pager a {
    display: inline-block;
    vertical-align: middle;
    padding: 4px 10px;
    background-color: #ddd;
    transition: background-color 0.3s ease-in-out;
    color: #616161;
  }

    .rnss-pager a:hover {
      background-color: #BEBDBD;
    }

  .rnss-pager .current {
    display: inline-block;
    vertical-align: middle;
    padding: 4px 10px;
    background-color: #3799DA;
    color: white;
  }

  .rnss-pager a[disabled] {
    display: none;
  }

  .rnss-pager .item {
    float: none;
    display: inline-block;
    vertical-align: middle;
  }

.paging2 {
  margin: 0 auto;
  padding: 12px 36px;
  width: 570px;
  display: block;
  overflow: hidden;
}

  .paging2 li.first.current,
  .paging2 li.last.current {
    border: 1px solid #C5C5C5;
    color: #C5C5C5;
    background: #ffffff;
  }

    .paging2 li.first.current a,
    .paging2 li.last.current a {
      color: #C5C5C5;
    }

  .paging2 li {
    display: inline;
    margin-left: 10px;
    line-height: 100%;
    padding: 5px 8px;
    border: 1px solid #026FB2;
    float: left;
    font-size: 12px;
    cursor: pointer;
    color: #026FB2;
  }

    .paging2 li:hover {
      background: #026FB2;
      color: #ffffff;
    }

      .paging2 li:hover a {
        color: #ffffff;
      }

  .paging2 a {
    display: block;
    text-decoration: none;
    font-size: 12px;
    color: #026FB2;
  }

  .paging2 .current {
    background: #026FB2;
    color: #ffffff;
  }

    .paging2 .current a {
      color: #ffffff;
    }

  .paging2 .pagerBottom_input {
    height: 18px;
    width: 45px;
    height: 24px;
  }

  .paging2 .pagerBottom {
    height: 24px;
    line-height: 24px;
    padding: 0 6px;
  }

  .paging2 li.none {
    border: 0 none;
    padding: 0;
  }

    .paging2 li.none:hover {
      background: none;
      color: #666
    }

/*旧版控件样式----------------------start*/
.text-5 {
  height: 25px !important;
  margin-top: 1px;
  width: 170px;
  border: 1px solid #d3d3d3;
  border-radius: 5px;
  font-size: 12px;
  -moz-box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Firefox3.6+ */
  -webkit-box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Chrome5+, Safari5+ */
  box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Latest Opera */
}

  .text-5 .outbox-item-list li {
    margin: 1px 3px 0 3px;
  }

  .text-5 .outbox-tip {
    margin-top: 0 !important;
    line-height: 25px;
  }

  .text-5 input {
    padding-top: 3px !important;
  }

.selTeam {
  position: relative !important;
  float: left !important;
  line-height: 24px !important;
  margin: 3px !important;
  padding: 0 !important;
  width: 224px !important;
}

  .selTeam .r-icon {
    position: absolute;
    left: 152px;
    top: 6px;
  }

  .selTeam .r-submit {
    float: right;
  }

.leaderId {
  position: relative !important;
  float: left !important;
  line-height: 24px !important;
  margin: 3px !important;
  padding: 0 !important;
  width: 224px !important;
}

  .leaderId .r-icon {
    position: absolute;
    left: 152px;
    top: 6px;
  }

  .leaderId .r-submit {
    float: right;
  }


.Rs_MsgBox_Layer_Wrap dl {
  margin-bottom: 6px;
}

.Rs_MsgBox_Layer_Wrap ul, .Rs_MsgBox_Layer_Wrap ol {
  margin-bottom: 2px;
}

.selectBox-1 select {
  cursor: pointer;
  margin: 5px 0 0 1px;
  background: #fff;
  border: none;
  width: 93%;
}

.selectBox-1 {
  float: left;
  height: 28px;
  width: 245px;
  border: 1px solid #d3d3d3;
  background: #FFFFFF;
  border-radius: 5px;
  font-size: 12px;
  -moz-box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Firefox3.6+ */
  -webkit-box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Chrome5+, Safari5+ */
  box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Latest Opera */
}

  .selectBox-1.error {
    border-color: #FCAF51;
  }

.selectBox-2 select {
  cursor: pointer;
  margin: 5px 0 0 0;
  background: #fff;
  border: none;
  width: 93%;
}

.selectBox-2 {
  float: left;
  height: 28px;
  width: 270px;
  border: 1px solid #d3d3d3;
  background: #FFFFFF;
  border-radius: 5px;
  font-size: 12px;
  -moz-box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Firefox3.6+ */
  -webkit-box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Chrome5+, Safari5+ */
  box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Latest Opera */
}

  .selectBox-2.error {
    border-color: #FCAF51;
  }

.selectSize-1 {
  width: 60px !important;
}

.selectSize-2 {
  width: 90px !important;
}

.selectSize-3 {
  width: 234px !important;
}

.selectSize-4 {
  width: 225px !important;
}

.selectSize-5 {
  width: 78px !important;
}

.selectSize-6-box {
  width: 250px;
  float: left;
  padding: 0;
}

.selectSize-6 {
  width: 230px !important;
}

.selectSize-7-box {
  width: 270px;
}

.selectSize-7 {
  width: 255px !important;
}

.selectSize-8-box {
  width: 100px;
}

.selectSize-8 {
  width: 85px !important;
}
/*集成textarea样式*/
textarea.textarea-1 {
  float: left;
  border: none;
  background: none;
  border: 1px solid #d3d3d3;
  background: #FFFFFF;
  border-radius: 5px;
  padding: 10px;
  height: 104px;
  line-height: 24px;
  font-size: 12px;
  overflow-y: auto;
  overflow-x: hidden;
  -moz-box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Firefox3.6+ */
  -webkit-box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Chrome5+, Safari5+ */
  box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Latest Opera */
}

  textarea.textarea-1.error {
    border-color: #FCAF51;
  }
/*集成按钮样式*/
.text-2 {
  border: none;
  background: none;
  padding: 0 10px;
  width: 90px;
  height: 28px;
  line-height: 28px;
  font-size: 12px;
  border: 1px solid #d3d3d3;
  background: #FFFFFF;
  border-radius: 5px;
  font-size: 12px;
  -moz-box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Firefox3.6+ */
  -webkit-box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Chrome5+, Safari5+ */
  box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Latest Opera */
}

input.text-2 {
  border: none;
  background: none;
  padding: 0 10px;
  width: 90px;
  height: 28px;
  line-height: 28px;
  font-size: 12px;
  border: 1px solid #d3d3d3;
  background: #FFFFFF;
  border-radius: 5px;
  font-size: 12px;
  -moz-box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Firefox3.6+ */
  -webkit-box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Chrome5+, Safari5+ */
  box-shadow: 1px 1px 1px #D3D3D3 inset; /* For Latest Opera */
}

.btn-submit {
  background: url("/css/images/btn_submit.jpg") repeat-x scroll 0 0 transparent;
  color: #fff;
  cursor: pointer;
  font-size: 13px;
  height: 23px;
  line-height: 23px;
  width: 49px;
  text-align: center;
  margin: 0 5px;
  float: left;
  border: medium none;
  padding: 0;
}

.btn-cancel {
  background: url("/css/images/btn_cancel.jpg") repeat-x scroll 0 0 transparent;
  color: #444445;
  cursor: pointer;
  font-size: 13px;
  height: 23px;
  line-height: 23px;
  width: 49px;
  text-align: center;
  margin: 0 5px;
  float: left;
  border: medium none;
  padding: 0;
}

.my-short-message .btn-submit, .my-short-message .btn-cancel {
  padding: 0;
}
/*弹出框样式*/
.fill-out {
  border-radius: 6px;
  padding-bottom: 5px;
  background: #666;
  z-index: 101;
  position: absolute;
  font-size: 12px;
  font-family: Arial,"宋体";
  line-height: 25px;
}

  .fill-out, .fill-out * {
    box-sizing: content-box;
  }

    .fill-out .icon-fenXiang {
      position: absolute;
      right: 5px;
      top: 5px;
      display: block !important;
      width: 26px;
      height: 26px !important;
      background-image: url(/css/images/risfondIcon.gif) !important;
      background-repeat: no-repeat !important;
      background-position: -774px -442px;
      cursor: pointer;
    }

.foc-box {
  text-align: center;
}

.foc__icon {
  width: 40px;
  height: 40px;
  margin: 28px auto 13px;
}

.foc__mes {
  line-height: 22px;
  margin: 0 auto 36px;
  color: #0C8CCC;
}

.foc__btn {
  width: 155px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  border: 1px solid transparent;
  border-radius: 3px;
  display: block;
  margin: 0 auto;
  transition: all .2s ease-in-out;
}

  .foc__btn:hover {
    text-decoration: none;
  }

  .foc__btn.foc__btn--xq {
    background-color: #0C8CCC;
    color: white;
    margin-bottom: 13px;
  }

    .foc__btn.foc__btn--xq:hover {
      background-color: #18678e;
      text-decoration: underline;
    }

  .foc__btn.foc__btn--continue {
    background-color: white;
    color: #0C8CCC;
    border: 1px solid #D7D7D7;
    margin-bottom: 36px;
  }

    .foc__btn.foc__btn--continue:hover {
      background-color: #dfdcdc;
    }

#bgWrapWait {
  cursor: wait;
  width: 100%;
  background: #000;
  filter: alpha(opacity=0);
  opacity: 0;
  z-index: 100;
  position: fixed;
  left: 0;
  top: 0;
  display: none;
}

#bgWrap {
  width: 100%;
  background: #000;
  filter: alpha(opacity=30);
  z-index: 100;
  position: fixed;
  left: 0;
  top: 0;
  display: none;
  overflow: hidden;
  opacity: .3;
  visibility: visible;
}

#bgWrapTxtPanel {
  width: 100%;
  text-align: center;
  position: fixed;
  left: 0;
  top: 100px;
  font-size: 26px;
  filter: alpha(opacity=100);
  color: #000;
}

  #bgWrapTxtPanel .txtpanel {
    width: 270px;
    text-align: center;
    margin: 0 auto;
    height: 80px;
    line-height: 80px;
    background: #F6F6F6;
    border: 1px solid #d3d3d3;
    border-radius: 4px;
    padding: 0 10px 0 30px;
  }

#bgWrapLoading {
  width: 200px;
  height: auto;
  padding: 20px 0;
  background: #fff;
  position: fixed;
  z-index: 101;
  top: 45%;
  left: 50%;
  margin-left: -100px;
  line-height: 32px;
  text-align: center;
  color: #333;
  border: 1px solid #f2f2f2;
  font-size: 14px;
}

  #bgWrapLoading img {
    width: 32px;
    height: 32px;
    margin-right: 15px;
    background: none;
    border: 0 none;
    display: inline-block;
    vertical-align: middle;
  }

.fill-out .paging-center {
  width: 450px;
  margin: 0 auto;
}

.fill-out .paging {
  padding: 0;
  padding-bottom: 5px;
  width: 490px;
  margin: 0 5px;
}

  .fill-out .paging a {
    padding: 5px 6px;
  }

.fill-out .paging-num {
  width: auto;
}

.fill-out-title {
  margin: 5px 5px 0 5px;
  position: relative;
  line-height: 35px;
  background: #6d84b4;
  color: #FFF;
  overflow: hidden;
  cursor: move;
  font-size: 12px;
}

  .fill-out-title h2 {
    float: left;
    font-size: 12px;
    display: inline;
    margin: 0 0 0 10px;
    padding: 10px 0;
  }

  .fill-out-title .r-icon-fenXiang {
    position: absolute;
    right: 5px;
    top: 5px;
  }

.fill-out-con {
  background: #FFFFFF;
  overflow: hidden;
  margin: 0 5px;
  text-align: center;
}

.fill-out-countDown {
  border-top: 1px solid #a6a5a5;
  margin: 0 5px;
  background: #eeeeee;
  height: 31px;
  line-height: 31px;
}

  .fill-out-countDown p {
    text-align: center;
    margin: 0;
  }

    .fill-out-countDown p b {
      color: red;
    }

.fill-out-con p {
  text-align: center;
}

.fill-out-con b {
  color: #3b5998;
  font-size: 16px;
}

.fill-out-con .textarea-1 {
  width: 355px;
}

.fill-out-con .select-client {
  margin-top: 20px;
  height: 50px;
}

.search #popup1 .fill-out-con {
  padding-left: 30px;
}

.fill-con-title {
  position: relative;
  overflow: hidden;
  background: #ffffff;
  padding-top: 20px;
  height: 67px;
  line-height: 47px;
  margin-left: 35px;
}

.fill-con-title-1 {
  position: relative;
  overflow: hidden;
  background: #ffffff;
  height: 47px;
  line-height: 47px;
}

.fill-con-title .r-icon-fill-out {
  position: absolute;
  left: 8%;
  top: 20px;
}

.fill-con-title h3,
.fill-con-title-1 h3 {
  text-align: center;
  color: #3b5998;
  background: #FFFFFF;
  font-size: 18px;
  line-height: 47px;
  margin: 0;
  font-weight: bold;
}

  .fill-con-title h3 b,
  .fill-con-title-1 h3 b {
    color: #3b5998;
  }

.fill-con-link {
  margin-top: 20px;
  padding-bottom: 20px;
  text-align: center;
  background-color: #FFFFFF;
}

  .fill-con-link a {
    font-weight: bold;
    margin: 0 10px;
  }

    .fill-con-link a:hover {
      text-decoration: underline;
    }

.fill-out-btn {
  background: #eeeeee;
  margin: 0 5px;
  padding: 4px 0;
  border-top: 1px solid #a6a5a5;
}

  .fill-out-btn .button-5 {
    margin-left: 5px;
  }

.fill-out-con .source-list li {
  display: inline;
  float: left;
  margin-right: 10px;
}

.fill-con-wordage {
  margin-top: 10px;
}

  .fill-con-wordage p.p-center {
    text-align: center;
  }

  .fill-con-wordage p.p-left {
    padding-left: 30px;
    text-align: left;
  }

.publicclienttransfer-panel {
  overflow: hidden;
}

  .publicclienttransfer-panel .pct-tab {
    width: 100%;
  }

    .publicclienttransfer-panel .pct-tab tr {
      line-height: 35px;
    }

    .publicclienttransfer-panel .pct-tab textarea {
      background: none repeat scroll 0 0 #FFFFFF;
      border: 1px solid #D3D3D3;
      border-radius: 5px;
      box-shadow: 1px 1px 1px #D3D3D3 inset;
      font-size: 12px;
      margin: 10px 0 0;
      width: 92%;
      height: 100px;
      line-height: 20px;
      resize: none;
      padding: 0 10px;
    }

    .publicclienttransfer-panel .pct-tab .title {
      float: left;
      font-weight: bold;
    }
/*旧版控件样式----------------------end*/
/*公司收入*/
.rnss-manage-income {
}

.r-tb-im {
}

  .r-tb-im .r-id {
    width: 43px;
    text-align: center;
  }

  .r-tb-im .r-it {
    width: 73px;
    text-align: center;
  }

  .r-tb-im .r-amount {
    width: 112px;
    font-weight: bold;
    text-align: right;
  }

  .r-tb-im .r-pm {
    width: 43px;
    text-align: center;
  }

  .r-tb-im .r-source {
    width:170px;
  }

  .r-tb-im .btn-group {
    margin-left: 10px;
  }

  .r-tb-im .r-recordtime {
    width: 86px;
  }

  .r-tb-im .r-company {
    width: 75px;
    text-align: center;
  }
  .r-tb-im .r-company1 {
    width: 50px;
    text-align: center;
  }

  .r-tb-im .r-staff {
    width: 73px;
  }

  .r-tb-im .r-consultantstaff {
    width: 73px;
  }

  .r-tb-im .r-memo {
    width: 45px;
    text-align: center;
  }

  .r-tb-im .r-info {
    width: 55px;
    text-align: center;
  }

  .r-tb-im .r-refund {
    width: 73px;
    text-align: center;
  }

/*公司支出 by pzj*/
.r-tb-me th, .r-tb-me td {
  text-align: center;
}

.r-tb-me .r-id {
  width: 56px;
  text-align: left;
  overflow: hidden;
  position: relative;
}

.r-tb-me .choise {
  text-align: left !important;
}

.r-tb-me .Exp_showmore {
  border-radius: 50% !important;
  width: 15px;
  height: 14px;
  background-color: #a4b6ca;
  line-height: 13px;
  margin: 3px 5px 0 0;
  color: #FDFFFE;
  text-align: center;
  font-size: 13px;
  position: absolute;
  top: 8px;
  right: 4px;
}

.r-tb-me .showmore_act {
  background-color: #31C3D2 !important;
}

.r-tb-me .r-appamount {
  width: 100px;
}

.r-tb-me td.r-appamount {
  text-align: right;
  color: #D91E18 !important;
}

.r-tb-me .r-appers {
  width: 65px;
}

.r-tb-me .r-source {
  text-align: left;
}
/*.r-tb-me .r-Audittime{width:120px;text-align:center;}*/
.r-tb-me .r-Audittime {
  width: 90px;
  text-align: center;
}

.r-tb-me .r-company {
  width: 82px;
}

.r-tb-me .r-staff {
  text-align: left;
  width: 70px;
}

.r-tb-me .r-payways {
  width: 80px;
  text-align: center;
}
/*.r-tb-me .r-status{width:82px;overflow:visible;}*/
.r-tb-me .r-status {
  width: 75px;
  overflow: visible;
}

.r-tb-me .r-memo {
  width: 85px;
}

  .r-tb-me .r-memo .take-notes {
    display: block;
    text-align: center;
  }

    .r-tb-me .r-memo .take-notes .r-tn {
      display: inline-block;
      vertical-align: middle;
      background: #4496E0;
      color: white;
      border-radius: 50% !important;
      overflow: hidden;
      width: 23px;
      height: 23px;
      text-align: center;
      line-height: 23px;
    }

.r-tb-me .r-category {
  width: 105px;
}

  .r-tb-me .r-category .take-notes {
    display: block;
    text-align: center;
  }

    .r-tb-me .r-category .take-notes .r-tn {
      display: inline-block;
      vertical-align: middle;
      background: #4496E0;
      color: white;
      border-radius: 50% !important;
      overflow: hidden;
      width: 23px;
      height: 23px;
      text-align: center;
      line-height: 23px;
    }

.r-tb-me .r-info {
  width: 62px;
  overflow: visible;
}

.r-tb-me .det_box {
  width: 97%;
  text-align: initial;
  padding-left: 3%;
  position: relative;
  margin-top: 10px;
}

  .r-tb-me .det_box strong {
    margin-right: 15px;
    color: #222;
    display: inline-block;
    width: 75px;
    text-align: right;
    vertical-align: top;
  }

  .r-tb-me .det_box span {
    font-size: 14px;
    color: #333;
  }

  .r-tb-me .det_box h3 {
    margin: 0;
    font-size: 16px;
  }

.det_box .det_line1 .r-appreason {
  display: inline-block;
  color: #535353;
  width: 645px;
  white-space: normal;
}

.det_box .det_line1 .edit_time {
  position: absolute;
  top: 0;
  right: 0
}

.det_box .det_line1 p span {
  color: #999;
}

.det_box .det_line4 .det_downloadFile {
  padding-right: 15px;
  position: relative;
  margin-right: 35px;
}

.det_line4 .det_downloadFile span {
  background: url(images/det_download.png) no-repeat left center;
  width: 16px;
  height: 16px;
  display: inline-block;
  margin-top: 3px;
  overflow: hidden;
  position: absolute;
  right: -6px;
  top: -3px;
}

.r-tb-me .det_box .line {
  margin-bottom: 5px;
}

.det_box .det_line5 span {
  display: inline-block;
  width: 180px;
}

.r-tb-me .r-tb-detail {
  display: none;
}
/*支出状态*/
.r-tb-me .det_status1_1 {
  position: absolute;
  right: 60px;
  top: 0;
}

  .r-tb-me .det_status1_1 span {
    font-size: 26px;
    color: #999;
    display: block;
    width: 30px;
    padding-left: 33px;
  }

.r-tb-me .det_status1_2 {
  position: absolute;
  right: 60px;
  top: 0;
}

  .r-tb-me .det_status1_2 span {
    font-size: 26px;
    color: #f00;
    display: block;
    width: 30px;
    padding-left: 33px;
  }

.r-info .handle-menu {
  min-width: 80px;
}

.r-tb-me .det_status1_3 {
  position: absolute;
  right: 50px;
  top: 28px;
}

  .r-tb-me .det_status1_3 span {
    font-size: 26px;
    color: #f00;
    display: block;
    width: 30px;
    padding-left: 33px;
  }

.r-info .handle-menu {
  min-width: 80px;
}

.handle-menu li {
  cursor: pointer;
  padding: 5px 10px;
}

  .handle-menu li:hover {
    background-color: #f6f6f6;
    color: #555;
  }
/*支出详细 pzj*/
.zjWidth {
  width: 794px;
  margin-top: 75px;
}

.modal-dialog .zj-content {
  border-radius: 5px !important;
}

.mod-lines {
  height: 38px;
  text-align: initial;
  letter-spacing: -3px;
  overflow: hidden;
  border: 1px solid #c7ecfc;
}

  .mod-lines p {
    text-align: center;
    line-height: 30px;
    display: inline-block;
    border-right: 1px solid #c7ecfc;
    letter-spacing: 0;
  }

.zj-content p {
  margin: 0;
  color: #989898;
  font-size: 14px;
}

  .zj-content p span {
    color: #3f3f3f;
  }

.zj-content .no-rBor {
  border-right: none;
}

.zj-content .no-bBor {
  border-bottom: none;
}

.zj-content .mod-title {
  background-color: #EFF8FD;
  height: 48px;
  text-align: center;
  color: #1167a2;
  line-height: 48px;
  font-size: 18px;
  border: 1px solid #c7ecfc;
}

.zj-content .modal-title {
  font-weight: 500;
  font-size: 20px;
}

.mod-line1 {
  height: 38px;
  border: 1px solid #c7ecfc;
  border-top: none;
  padding: 0 20px;
}

  .mod-line1 p {
    line-height: 38px;
    display: block;
  }

    .mod-line1 p:nth-child(1) {
      float: left;
    }

    .mod-line1 p:nth-child(2) {
      float: right;
    }

.mod-line2 p {
  width: 32%;
}

.mod-line3 p {
  width: 49%;
}

.mod-line4 p {
  width: 49%;
}

.modal-body .mod-line5 p {
  width: 80%;
  text-align: initial;
  overflow: hidden;
  text-overflow: ellipsis;
  /*white-space: nowrap;*/
  margin-left: 60px;
}

.mod-line6 {
  border: 1px solid #c7ecfc;
  border-top: none;
  padding: 12px 30px 10px 60px;
}

  .mod-line6 p {
    display: inline-block;
    width: 320px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .mod-line6 .line6-1 p:nth-child(2) {
    text-indent: 1em;
  }

  .mod-line6 .line6-2 {
    margin-top: 10px;
  }

.result-box {
  background-color: #FAFAFA;
  border: 1px solid #f0f0f0;
  margin-top: 20px;
  padding: 18px 0 14px 40px;
}

  .result-box p {
    display: inline-block;
    width: 230px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    color: #1167a0;
  }

  .result-box .result-line2 {
    margin-top: 10px;
  }

.result-line1 p:nth-child(3) {
  text-indent: 1em;
}

.result-line2 p:nth-child(1) {
  text-indent: 1em;
}

.result-box2 {
  background-color: #FAFAFA;
  height: 88px;
  border: 1px solid #f0f0f0;
  margin-top: 20px;
  padding: 18px 0 20px 60px;
}

  .result-box2 p {
    display: inline-block;
    width: 230px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    color: #1167a0;
  }

  .result-box2 .result-line2 {
    text-indent: -1em;
    margin-top: 10px;
    width: 700px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

    .result-box2 .result-line2 p {
      width: 700px;
      text-overflow: ellipsis;
      overflow: hidden;
      overflow: hidden;
    }
/*支出审核模态框 pzj*/
.checkexp-cot {
  border-radius: 5px !important;
}

  .checkexp-cot .no-rBor {
    border-right: none;
  }

  .checkexp-cot .no-bBor {
    border-bottom: none;
  }

  .checkexp-cot p {
    margin: 0;
    color: #989898;
    font-size: 14px;
  }

    .checkexp-cot p span {
      color: #3f3f3f;
    }

  .checkexp-cot .modal-header {
    text-align: center;
    font-weight: bold;
    font-size: 20px;
    background-color: #3398dc;
    border-radius: 5px 5px 0 0 !important;
    color: #fff;
    border-bottom: none;
    height: 44px;
    padding: 8px 15px 10px 8px;
  }

  .checkexp-cot .mod-title {
    background-color: #EFF8FD;
    height: 40px;
    text-align: center;
    color: #1167a2;
    line-height: 40px;
    font-size: 18px;
    border: 1px solid #c7ecfc;
  }

  .checkexp-cot .modal-header .close {
    background-image: none !important;
    color: #fff;
    text-indent: 0;
    opacity: 1;
    font-weight: normal;
    width: 15px;
    height: 15px
  }

  .checkexp-cot .checkExp-options {
    background-color: #fff;
    border: 3px solid #3398dc;
    border-top: none;
    padding: 10px 25px 12px 25px;
  }

.checkExp-options label {
  margin: 0;
  color: #333;
}

.checkline1 .checkExp-aslabel {
  margin-right: 20px;
}

.checkline2 {
  margin: 15px 0;
  overflow: hidden;
}

.checkexp-cot .checkline2 input {
  width: 200px;
  height: 32px;
  cursor: pointer;
}

.checkline2 label:nth-child(1) {
  margin-right: 70px;
}

.checkline2 label span {
  width: 30px;
  text-align: center;
  color: #f00;
  font-size: 20px;
  line-height: 28px;
  display: inline-block;
}

.checkline4 .checkExp_btnbox {
  margin: 0 auto;
  display: block;
  width: 200px;
}

  .checkline4 .checkExp_btnbox button {
    height: 28px;
    width: 78px;
    line-height: 16px !important;
  }

.check_RejectBox {
  display: none;
  overflow: hidden;
  margin: 15px 0;
}

  .check_RejectBox label {
    display: inline-block;
    height: 50px;
    float: left;
    margin-right: 30px;
  }

  .check_RejectBox textarea {
    width: 500px;
    height: 60px;
    display: inline-block;
    float: left
  }

/*调整整站的导航栏 start zsl*/
.ie8 .page-sidebar {
  width: 165px;
}

.page-content-wrapper .page-content {
  margin-left: 165px;
}

.page-sidebar-menu.page-sidebar-menu-hover-submenu.page-sidebar-menu-en {
}

.page-sidebar-menu.page-sidebar-menu-hover-submenu li .sub-menu {
  width: 190px;
  margin-left: 165px;
}
/*.page-sidebar-menu.page-sidebar-menu-hover-submenu li .sub-menu{width:210px;margin-left:165px;}*/
.page-sidebar-menu.page-sidebar-menu-hover-submenu > li:hover > .sub-menu {
  margin-left: 152px;
}

.page-sidebar-menu.page-sidebar-menu-hover-submenu > li:nth-of-type(1n+7) {
  position: relative;
}

  .page-sidebar-menu.page-sidebar-menu-hover-submenu > li:nth-of-type(1n+7):hover > .sub-menu {
    margin-top: -130%;
    bottom: 0;
    top: initial !important;
    margin-top: 0;
    margin-bottom: -1px;
  }

.page-sidebar-menu.page-sidebar-menu-hover-submenu > li > .sub-menu > .nav-item {
  width: 95px;
  float: left;
}
/*.page-sidebar-menu.page-sidebar-menu-hover-submenu>li>.sub-menu>.nav-item{width:105px;float:left;}*/
.page-sidebar-menu.page-sidebar-menu-hover-submenu.page-sidebar-menu-en > li > .sub-menu > .nav-item {
  width: auto;
  float: none;
}

.page-sidebar-menu.page-sidebar-menu-hover-submenu > li > .sub-menu > .nav-item > a {
  padding: 9px 14px 9px 14px;
}

.page-sidebar-menu.page-sidebar-menu-hover-submenu > li > .sub-menu > .nav-item:nth-of-type(even) > a {
  padding: 9px 14px 9px 14px;
}

.page-sidebar-closed .page-sidebar .page-sidebar-menu.page-sidebar-menu-closed > li:not(.heading):hover {
  width: 247px !important;
}

  .page-sidebar-closed .page-sidebar .page-sidebar-menu.page-sidebar-menu-closed > li:not(.heading):hover > .sub-menu {
    width: 192px;
  }

    .page-sidebar-closed .page-sidebar .page-sidebar-menu.page-sidebar-menu-closed > li:not(.heading):hover > .sub-menu::after {
      display: table;
      content: ' ';
      clear: both;
    }

.page-sidebar-closed .page-sidebar-menu.page-sidebar-menu-closed > li:hover {
  box-shadow: 5px 0 rgba(140,178,198,.2);
}
/*调整整站的导航栏 end zsl*/
/*自定义栅格系统 start zsl*/
.col-md-2dot4 {
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}

.col-lg-3dot4 {
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}

.col-md-2dot4 {
  float: left;
  width: 14.28571427%;
}

.col-md-pull-2dot4 {
  right: 14.28571427%;
}

.col-md-push-2dot4 {
  left: 14.28571427%;
}

.col-md-offset-2dot4 {
  margin-left: 14.28571427%;
}

.col-lg-3dot4 {
  float: left;
  width: 20%;
}

.col-lg-pull-3dot4 {
  right: 20%;
}

.col-lg-push-3dot4 {
  left: 20%;
}

.col-lg-offset-3dot4 {
  margin-left: 20%;
}

/*自定义栅格系统 end zsl*/
/*互动信息管理 start zsl*/
.rnss-manage-interaction {
}

.r-tb-mi {
}

  .r-tb-mi th, .r-tb-mi td {
    text-align: center;
  }

  .r-tb-mi .r-id {
    width: 43px;
  }

  .r-tb-mi .r-con {
    padding: 8px !important;
  }

  .r-tb-mi .r-infotype {
    width: 65px;
    vertical-align: middle;
    font-size: 12px;
    color: #333;
  }

  .r-tb-mi .r-category-box {
    padding-top: 2px;
  }

.r-tb.r-tb-mi > tbody > .r-tb-item:nth-of-type(odd) {
  background-color: transparent;
}

.table-hover.r-tb-mi > tbody > tr:hover {
  background-color: #f9f9f9 !important;
}
/*.r-tb-mi { margin: 0; padding: 0; margin-top: -15px; }*/
.r-tb-mi .mi-item {
  list-style: none;
  padding: 3px 0;
  margin: 0 auto;
  font-size: 12px;
}

  .r-tb-mi .mi-item .body {
    display: block;
    word-break: break-all;
    word-wrap: break-word;
    white-space: normal;
    line-height: 1.6;
  }

  .r-tb-mi .mi-item .avatar {
    height: 45px;
    width: 45px;
    -webkit-border-radius: 50% !important;
    -moz-border-radius: 50% !important;
    border-radius: 50% !important;
  }

  .r-tb-mi .mi-item.in .avatar {
    float: left;
    margin-right: 10px;
  }

  .r-tb-mi .mi-item.out .avatar {
    float: right;
    margin-left: 10px;
  }

  .r-tb-mi .mi-item .name {
    color: #3590c1;
    font-size: 13px;
    font-weight: 400;
  }

  .r-tb-mi .mi-item .company {
    font-size: 13px;
    font-weight: 400;
    color: #7B7B7B;
  }

  .r-tb-mi .mi-item .datetime {
    font-size: 13px;
    font-weight: 400;
  }

  .r-tb-mi .mi-item .message {
    display: block;
    padding: 5px;
    position: relative;
    line-height: 1.8;
  }

  .r-tb-mi .mi-item.in .message {
    text-align: left;
    border-left: 2px solid #1BBC9B;
    margin-left: 65px;
  }

    .r-tb-mi .mi-item.in .message .arrow {
      display: block;
      position: absolute;
      top: 5px;
      left: -8px;
      width: 0;
      height: 0;
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
      border-right: 8px solid #1BBC9B;
    }

  .r-tb-mi .mi-item.out .message {
    border-right: 2px solid #F3565D;
    margin-right: 65px;
    text-align: right;
  }

    .r-tb-mi .mi-item.out .message .arrow {
      display: block;
      position: absolute;
      top: 5px;
      right: -8px;
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
      border-left: 8px solid #F3565D;
    }

  .r-tb-mi .mi-item.out .name,
  .r-tb-mi .mi-item.out .datetime {
    text-align: right;
  }
/*互动信息管理 end zsl*/

/*接单中心 start zsl*/
.rnss-manage-consultant {
}

.r-tb-ct {
}

  .r-tb-ct .r-photo {
    width: 100px;
    text-align: center;
  }

  .r-tb-ct .r-con {
  }

.jd-item {
  position: relative;
  font-size: 14px;
  padding: 8px;
  border: 1px solid #eef1f5;
  margin-bottom: 8px;
}

  .jd-item .jd-left {
    float: left;
    width: 162px;
    text-align: center;
  }

  .jd-item .jd-photo {
    width: 100px;
    height: 100px;
    border: 1px solid transparent;
    border-radius: 50% !important;
    overflow: hidden;
    display: block;
    margin: 15px auto;
    text-align: center;
    line-height: 100px;
    overflow: hidden;
    font-size: 12px;
  }

  .jd-item .jd-right {
    position: relative;
    margin-left: 162px;
  }

  .jd-item .jd-hot {
    position: absolute;
    right: 0;
    top: 0;
  }

    .jd-item .jd-hot .jd-hot-btn {
      width: 88px;
      height: 72px;
      background: transparent url("images/jd-hot-back.png") center center;
      font-size: 38px;
      text-align: center;
      line-height: 65px;
      cursor: pointer;
      opacity: 1;
    }

      .jd-item .jd-hot .jd-hot-btn.jd-hot-action {
        transition: opacity 0.3s;
      }

        .jd-item .jd-hot .jd-hot-btn.jd-hot-action:hover {
          opacity: .8;
        }

  .jd-item .jd-line {
    margin-right: 100px;
    margin-bottom: 4px;
    padding: 6px 0 0;
    overflow: hidden;
  }

    .jd-item .jd-line::after {
      clear: both;
      content: ' ';
      display: block;
    }

    .jd-item .jd-line.jd-bb {
      border-bottom: 1px solid #EFEFEF;
      padding: 6px 0;
    }

    .jd-item .jd-line .jd-line-item {
      display: inline-block;
      vertical-align: middle;
    }

  .jd-item .jd-name {
    display: inline-block;
    margin: 0;
    padding: 0;
    font-size: 18px;
    font-weight: bold;
  }

  .jd-item .jd-lv {
    padding: 0px 6px;
    font-size: 14px;
    font-weight: bold;
  }

  .jd-item .jd-company {
    margin-left: 10px;
  }

  .jd-item .jd-hr {
    display: inline-block;
    height: 26px;
    width: 0;
    border-right: 1px solid #DCDCDC;
    margin: 0 15px;
  }

  .jd-item .btn-send {
    float: right;
    margin: -6px 0 0 0;
  }

  .jd-item .jd-title {
    float: left;
  }

  .jd-item .jd-r-row {
    margin-left: 68px;
    list-style-type: disc;
    list-style-position: inside;
    box-sizing: content-box;
  }

    .jd-item .jd-r-row .jd-r-item {
      float: left;
      color: white;
      background: #26C281;
      position: relative;
      margin: 0 8px 6px 12px;
      padding: 2px 10px 2px 0;
      box-sizing: content-box;
      position: relative;
      color: white;
      white-space: nowrap;
    }

      .jd-item .jd-r-row .jd-r-item:before {
        display: block;
        content: ' ';
        position: absolute;
        left: -24px;
        top: 0;
        width: 0;
        height: 0;
        border-width: 12px;
        border-style: solid;
        border-color: transparent #26C281 transparent transparent;
      }

  .jd-item .jd-r-btn {
    padding: 0 5px;
    margin: 0;
    font-size: 22px;
    line-height: 100% !important;
  }

  .jd-item .jd-r-row .jd-r-item.deltag {
    cursor: pointer;
  }

    .jd-item .jd-r-row .jd-r-item.deltag:hover {
      text-decoration: underline;
    }

    .jd-item .jd-r-row .jd-r-item.deltag:after {
      position: absolute;
      right: 2px;
      top: 0;
      content: '×';
      display: none;
    }

    .jd-item .jd-r-row .jd-r-item.deltag:hover:after {
      display: block;
    }

  .jd-item .jd-s-row {
    margin-left: 68px;
    list-style: none;
    box-sizing: content-box;
    line-height: 1.6;
  }

  .jd-item .jd-s-date {
    float: left;
  }

  .jd-item .jd-s-pf-btn {
    padding: 2px 6px;
    margin-right: 6px;
  }

  .jd-item .jd-s-item {
    float: left;
    padding: 0;
    margin: 0 6px 6px 0;
    font-size: 0;
  }

  .jd-item .jd-s-item-txt {
    display: inline-block;
    background: #4B77BE;
    color: white;
    text-align: center;
    padding: 2px 6px;
    margin: 0;
    font-size: 14px;
  }

  .jd-item .jd-s-item-count {
    display: inline-block;
    text-align: center;
    padding: 2px 6px;
    margin: 0;
    font-size: 14px;
  }

  .jd-item .icon-magnifier {
    cursor: pointer;
  }

  .jd-item .chats {
  }

  .jd-item .r-i-item {
  }

  .jd-item .getmore {
    text-align: center;
    text-decoration: underline;
    cursor: pointer;
    font-size: 16px;
    color: #ccc;
  }

    .jd-item .getmore:hover {
      text-decoration: none;
    }

  .jd-item .companyName {
    font-size: 13px;
    color: #7B7B7B;
  }
/*接单中心 end zsl*/
/*数控中心 start zsl*/
.r-dc-box {
}

.sx-panel2 {
  float: left;
  margin: 0 14px 0 0;
  position: relative;
}

  .sx-panel2:last-child {
    margin: 0;
  }

  .sx-panel2 .selectfilter-box .selectfilter-tip {
    position: relative;
    padding: 6px 30px 6px 12px;
    border-radius: 5px !important;
    border: 1px solid #C4C4C4;
    transition: border-color 0.2s ease-in-out;
    cursor: pointer;
    background: #fff;
    height: auto;
    line-height: initial;
    font-weight: normal;
  }

  .sx-panel2 .selectfilter-tip::selection {
    background: #fff;
  }

  .sx-panel2 .selectfilter-tip:hover {
    border-color: #8A7A7A;
  }

  .sx-panel2 .selectfilter-tip:before {
    display: inline-block;
    position: absolute;
    right: 12px;
    top: 11px;
    width: 0;
    height: 0;
    border-color: #616161 transparent;
    border-style: solid dashed;
    border-width: 0 4px 4px;
    content: '';
    transition: border-color 0.3s ease-in-out;
  }

  .sx-panel2 .selectfilter-tip:hover:before {
    border-color: #8B8A8A transparent;
  }

  .sx-panel2 .selectfilter-tip:after {
    display: inline-block;
    position: absolute;
    right: 12px;
    top: 17px;
    width: 0;
    height: 0;
    border-color: #616161 transparent;
    border-style: solid dashed;
    border-width: 4px 4px 0;
    content: '';
    transition: border-color 0.3s ease-in-out;
  }

  .sx-panel2 .selectfilter-tip:hover:after {
    border-color: #8B8A8A transparent;
  }

  .sx-panel2 .selectfilter-box {
    background: url("/css/images/saffh2.jpg") 0 -55px repeat-x !important;
    margin-top: 0;
  }

    .sx-panel2 .selectfilter-box > select {
      display: none;
    }

    .sx-panel2 .selectfilter-box .selectfilter-list {
      padding: 0;
    }

  .sx-panel2 .sx-con {
    position: relative;
    padding: 6px 30px 6px 12px;
    border-radius: 5px !important;
    border: 1px solid #C4C4C4;
    transition: border-color 0.2s ease-in-out;
    cursor: pointer;
    background-color: #fff;
  }

    .sx-panel2 .sx-con::selection {
      background: #fff;
    }

    .sx-panel2 .sx-con:hover {
      border-color: #8A7A7A;
    }

    .sx-panel2 .sx-con:before {
      display: inline-block;
      position: absolute;
      right: 12px;
      top: 11px;
      width: 0;
      height: 0;
      border-color: #616161 transparent;
      border-style: solid dashed;
      border-width: 0 4px 4px;
      content: '';
      transition: border-color 0.3s ease-in-out;
    }

    .sx-panel2 .sx-con:hover:before {
      border-color: #8B8A8A transparent;
    }

    .sx-panel2 .sx-con:after {
      display: inline-block;
      position: absolute;
      right: 12px;
      top: 17px;
      width: 0;
      height: 0;
      border-color: #616161 transparent;
      border-style: solid dashed;
      border-width: 4px 4px 0;
      content: '';
      transition: border-color 0.3s ease-in-out;
    }

    .sx-panel2 .sx-con:hover:after {
      border-color: #8B8A8A transparent;
    }

  .sx-panel2 .submit {
    background: #21639c;
    border: 0 none;
    padding: 3px 8px;
    color: #fff;
    cursor: pointer;
  }

    .sx-panel2 .submit:hover {
      background: #236fb0;
    }

  .sx-panel2 .sx-item-panel {
    position: absolute;
    top: 33px;
    left: 0;
    padding: 5px;
    background: #fff;
    box-shadow: 5px 5px rgba(102, 102, 102, 0.1);
    border: 1px solid #eee;
    z-index: 5;
    display: none;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    border-radius: 4px;
  }

    .sx-panel2 .sx-item-panel.selected {
      display: block;
      z-index: 6;
    }

  .sx-panel2 .sx-item-tab {
    border-collapse: collapse;
    border-spacing: 0;
  }

    .sx-panel2 .sx-item-tab .radio input[type="radio"], .sx-panel2 .sx-item-tab .radio-inline input[type="radio"], .sx-panel2 .sx-item-tab .checkbox .tarmcustomsearch input[type="checkbox"], .sx-.tarmcustomsearch panel .sx-item-tab .checkbox-inline input[type="checkbox"] {
      margin-left: -10px;
    }

    .sx-panel2 .sx-item-tab tr {
      line-height: 26px;
    }

    .sx-panel2 .sx-item-tab td {
      border: 0 none;
      white-space: nowrap;
    }

    .sx-panel2 .sx-item-tab .radio span {
      display: inline-block;
      margin: 0;
    }

  .sx-panel2 .daterangepicker-text {
    display: none;
  }

  .sx-panel2 .text-2 {
    height: 22px;
    line-height: 22px;
    width: 120px;
    margin-right: 5px;
  }

  .sx-panel2 .selectfilter-ul {
    padding: 0;
  }
/*数控中心 end zsl*/
/*简历页面所有图标 start zsl*/
.resume-icon {
  display: block;
  width: 16px;
  height: 16px;
  background-image: url(/static/images/resume-source.gif);
  background-repeat: no-repeat;
  float: left;
  margin: 0 5px 0 0;
}

.resume-icon-1 {
  background-position: 0 0;
}

.resume-icon-2 {
  background-position: 0 -16px;
}

.resume-icon-3 {
  background-position: 0 -32px;
}

.resume-icon-4 {
  background-position: 0 -48px;
}

.resume-icon-5 {
  background-position: 0 -64px;
}

.resume-icon-6 {
  background-position: 0 -80px;
}

.resume-icon-7 {
  background-position: 0 -96px;
}

.resume-icon-8 {
  background-position: 0 -112px;
}

.resume-icon-9 {
  background-position: 0 -128px;
}

.resume-icon-10 {
  background-position: 0 -144px;
}

.resume-icon-11 {
  background-position: 0 -160px;
}

.resume-icon-12 {
  background-position: 0 -176px;
}

.resume-icon-13 {
  background-position: 0 -192px;
}

.resume-detail .resume-icon {
  margin-top: 3px;
}
/*简历页面所有图标 end zsl*/
/*简历详情 start zsl*/
.vr {
  overflow: hidden;
  margin: 1px auto;
  margin-bottom: 3px;
  padding: 0 18px;
  border: 1px solid #DCE5EA;
}

  .vr .logo {
    display: block;
    height: 65px;
    width: 210px;
  }

  .vr .content-1 {
    padding: 0 0 10px;
  }

  .vr h1 {
    display: block;
    border-bottom: 1px solid #EEEEEE;
    font-size: 12px;
    font-weight: 400;
    line-height: 40px;
  }

  .vr .name {
    float: left;
    padding: 0 0 0 15px;
    height: 50px;
    line-height: 50px;
    width: 326px;
    font-size: 22px;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
  }

  .vr .information {
    float: right;
    height: 50px;
    line-height: 50px;
    width: 318px;
    text-align: right;
  }

  .vr .main-body {
    clear: both;
    margin: 0 auto;
  }

  .vr .condition {
    clear: both;
    display: block;
  }

    .vr .condition dt {
      float: left;
      width: 60px;
      padding-left: 5px;
      font-weight: bold;
    }

    .vr .condition dd {
      float: right;
      padding-left: 5px;
    }

    .vr .condition b {
      color: #fc8e00;
    }

    .vr .condition strong {
    }

    .vr .condition .rz-con {
      float: right;
    }

    .vr .condition .rz-condition-item {
      float: left;
    }

.rz-condition-item .paging2 {
  text-align: center;
  width: auto;
}

.vr .condition .rz-prompt-item {
  float: left;
  width: 799px;
  border-bottom: 1px dashed #b7b7b7;
}

  .vr .condition .rz-prompt-item:last-child {
    border-bottom: 0 none;
  }

  .vr .condition .rz-prompt-item .icon-17, .vr .condition .rz-prompt-item .icon-138 {
    margin-right: 0;
    margin-left: 4px;
  }

.vr .condition span.icon-3 {
  float: right;
}

.h-auto {
  height: auto !important;
}

.vr .condition .condition-name {
  float: left;
  margin-left: 5px;
  margin-right: 10px;
  font-weight: normal;
}

.vr .condition .rz-info {
  float: left;
  width: 370px;
  overflow: hidden;
}

  .vr .condition .rz-info span, .vr .condition .rz-info strong {
    float: left;
    height: 25px;
    overflow: hidden;
  }

.vr .condition .rzcg-con {
  max-width: 185px;
  margin-right: 10px;
}

.vr .condition .zwmc-con {
  max-width: 75px;
  margin-right: 10px;
}

.vr .condition .rz-info strong {
  max-width: 65px;
}

.vr .condition .rz-time {
  float: left;
  width: 120px;
  height: 25px;
  overflow: hidden;
}

.vr .condition .icon {
  float: left;
  margin: 5px 4px 0 0;
}

.vr .condition .rz-referrer {
  float: left;
  width: 113px;
  height: 25px;
  overflow: hidden;
}

.vr .condition .condition-item {
  padding: 8px 0 6px 1.5em;
}

.vr .condition-item:first-child {
  padding-bottom: 0;
}

.vr {
}

.r-v .r-v-title-p {
  margin: 0 0 8px;
  line-height: 1.1;
}

.r-v .r-v-title {
  float: left;
  margin: 0;
  line-height: 1;
  font-weight: normal;
  font-size: 36px;
  color: #666;
}

.r-v .r-v-certificationstatus {
  vertical-align: bottom;
  margin-left: 15px;
}

.r-v .search_BG {
  display: inline-block;
  margin-left: 8px;
  margin-top: 4px;
}

  .r-v .search_BG i {
    margin-right: 4px;
  }

  .r-v .search_BG .btn {
    margin-left: 8px;
    border-radius: 16px !important;
    font-size: 12px;
    padding: 4px 10px;
  }

.vr .vr-inputuser-p {
  float: right;
}

.vr .vr-gk {
  border-left: 4px solid #3398DC;
}

.r-v .detail {
  display: block;
  margin-top: 18px;
}
/*.vr .detail h2 { overflow: hidden; height: 30px; line-height: 30px; width: 85px; color: #FFFFFF; font-size: 14px;font-weight:bold; text-align: center; background: none repeat scroll 0 0 #32C6D2; }*/
.vr .detail .time {
  width: 120px !important;
}

.vr .detail .school {
  width: 200px;
}

.vr .detail .major {
  width: 240px;
}

.vr .detail .degree {
  width: 108px;
}

.vr .detail ul {
  margin: 0;
  padding: 0;
}

  .vr .detail ul li {
    list-style: none;
  }

.vr .contact-p1 {
  float: left;
  width: 380px;
}

.vr .contact-p2 {
  float: left;
  width: 350px;
}

.vr .contact-p3 {
  float: left;
  width: 380px;
}

.vr .contact-p4 {
  float: left;
  width: 350px;
}

.vr .contact-source-item {
  display: block;
}

  .vr .contact-source-item span {
    float: left;
    margin-right: 6px;
  }

.vr .csi-title {
  float: left;
  width: 65px;
}

.vr .csi-rbox {
  float: left;
  width: 295px;
}

  .vr .csi-rbox .contact-source-item {
    float: left;
    white-space: nowrap;
    margin-right: 20px;
    margin-top: 4px; /*background-color: #f2f2f2;*/ /*border-bottom:1px solid #f2f2f2;*/
  }

    .vr .csi-rbox .contact-source-item .resume-icon {
      margin: 0 5px 0 0;
    }

    .vr .csi-rbox .contact-source-item .csi-rsource {
      color: #555;
      line-height: 18px;
    }

      .vr .csi-rbox .contact-source-item .csi-rsource a {
        text-decoration: underline;
      }

        .vr .csi-rbox .contact-source-item .csi-rsource a:hover {
          text-decoration: none;
        }

.vr .contact-p-title {
  float: left;
  width: 75px;
  overflow: hidden;
}

.vr .contact-p1 .contact-p-list {
  float: left;
  width: 295px;
}

.vr .contact-p2 .contact-p-list {
  float: left;
  width: 265px;
}

.vr .contact-item {
  line-height: 20px;
  padding-bottom: 0;
}

.vr .contact-item-title {
  word-break: break-all;
  word-wrap: break-word;
  display: block;
}

.vr .contact-item .r-icon {
  float: left;
  margin: 2px 5px 0 0;
}

.vr .contact-item .defaulticon {
  float: left;
  width: 16px;
  height: 16px;
  margin: 0 5px 0 0;
}

.vr .contact-item .r-icon.resume-view-operation-icon {
  float: none;
  margin: 0 0 3px 3px;
}

.vr .resume-view-operation-icon {
  margin: 0 0 3px 3px;
}

.vr .rnss-callphone-icon {
  margin: 0 0 3px 3px;
}

.vr .hide {
  display: none;
}

.vr .contact-p3 {
  overflow: hidden;
  clear: both;
}

.vr .csi-id-p {
}

.vr .status dl {
  float: left;
  overflow: hidden;
  width: 300px;
}

.vr .status dt {
  float: left;
  width: 60px;
  text-align: right;
}

.vr .status dd {
  float: left;
  width: 195px;
}

.vr .status {
  position: relative;
}

  .vr .status .user-details span {
    float: left;
    width: 350px; /*margin-bottom:12px;*/
  }

  .vr .status .picture {
    position: absolute;
    right: 0;
    top: 50px;
    padding: 0;
    border: 0;
    width: 126px;
    height: 126px;
  }

    .vr .status .picture img {
      width: 126px;
      height: 126px;
      border-radius: 50% !important;
      overflow: hidden;
      text-align: center;
    }

.r-v .user-details {
  line-height: 2;
}

.vr .intention dl {
  display: block;
  overflow: hidden;
  margin-bottom: 15px;
}

.vr .intention dt {
  float: left;
  width: 75px;
}

.vr .intention dd {
  float: left;
  width: 604px;
  text-align: left;
}
/*相似简历 start zsl*/
.vr .xs_box {
  display: none
}

  .vr .xs_box .portlet-title {
    cursor: pointer;
  }
  /*.vr .xs_box .portlet-body{display:none;}*/
  .vr .xs_box.on {
    display: block;
  }
  /*.vr .xs_box.open{width:283px;}
.vr .xs_box.open .portlet-body{display:block;}*/
  .vr .xs_box .xs_lbox {
    position: fixed;
    right: 0;
    top: 145px;
    width: 303px;
    background: #fff;
    display: none;
  }

  .vr .xs_box .table-scrollable {
    margin: 0 !important;
  }

  .vr .xs_box .xs_tab th, .vr .xs_box .xs_tab td {
    padding: 6px 4px;
    vertical-align: middle;
  }

  .vr .xs_box .td1 {
    width: 77px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
  }

  .vr .xs_box .td2 {
    width: 73px;
    text-align: center
  }

  .vr .xs_box .td3 {
  }

  .vr .xs_box .xs_lbox.on {
    display: block;
  }

.vr .xs_tipbox {
  position: fixed;
  right: 0;
  top: 145px;
  width: 62px;
  background: #fff;
  display: none;
  padding: 16px 10px 16px 30px;
  cursor: pointer;
  font-size: 16px;
  color: #e7505a;
  font-weight: 700;
  transition: background-color .2s ease-in-out;
}

  .vr .xs_tipbox.on {
    display: block;
  }

  .vr .xs_tipbox:hover {
    background: #efefef;
    background-size: 50% 100%;
  }

  .vr .xs_tipbox > i {
    position: absolute;
    left: 5px;
    top: 50%;
    font-size: 18px;
    margin-top: -7px;
  }

  .vr .xs_tipbox .xs_iopen {
    display: block !important
  }

.vr .xs_box .xs_lbox {
  display: none;
  padding-left: 40px;
}

.vr .xs_box .xs_tipbox {
  display: block;
}

.vr .xs_box.open .xs_lbox {
  display: block;
}

.vr .xs_box.open .xs_tipbox {
  right: 272px;
  width: 36px;
  display: none
}

  .vr .xs_box.open .xs_tipbox > .xs_iopen {
    display: none !important
  }

  .vr .xs_box.open .xs_tipbox > .xs_iclose {
    display: block !important
  }

  .vr .xs_box.open .xs_tipbox > span {
    display: none;
  }

.vr .xs_box .xs_tip {
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  font-size: 16px;
  color: #e7505a;
  font-weight: 700;
  width: 36px;
  transition: background-color .2s ease-in-out;
}

  .vr .xs_box .xs_tip:hover {
    background: #efefef;
    cursor: pointer;
  }

  .vr .xs_box .xs_tip > i {
    position: absolute;
    left: 10px;
    top: 50%;
    margin-top: -7px;
  }
/*相似简历 end  zsl*/

.r-v .detail.vr_tag_b {
  margin-top: 8px;
}

.vr_tag_b .tag_btn_p {
  position: relative;
}

.vr_tag_b .tag_btn_add {
  float: right;
  position: absolute;
  right: 5px;
  top: -36px;
}

.tag_btn_add {
  font-size: 14px;
}

  .tag_btn_add > i {
    margin-right: 4px;
  }

.vr_tag_l {
}

  .vr_tag_l .vr_tag_i {
    float: left;
    line-height: 34px;
    cursor: pointer;
    margin: 8px 15px 0 0;
    transition: background-color 0.2s;
  }

  .vr_tag_l .vr_tag_xj {
    float: left;
    color: #fff;
    background-color: #4998DB;
    text-align: center;
    cursor: pointer;
    margin: 0;
    padding: 0 10px;
  }

  .vr_tag_l .vr_tag_txt {
    float: left;
    background-color: #e1e5ec;
    padding: 0 15px;
    text-align: center;
    transition: background-color 0.2s;
  }

.r-vc .vr_tag_l .vr_tag_d {
  border-left: 4px solid #2BA0E3;
}

.vr_tag_l .vr_tag_d {
}

  .vr_tag_l .vr_tag_d:hover .vr_tag_txt {
    background-color: #c2cad8;
  }

.vr_tag_modal {
}

  .vr_tag_modal .target-demo {
    display: inline-block;
    vertical-align: middle;
  }

  .vr_tag_modal .hint {
    border-radius: 5px;
    color: #333;
    display: inline-block;
    height: 26px;
    line-height: 26px;
    text-align: center;
    vertical-align: middle;
    margin-left: 8px;
    font-weight: bold;
    color: #EDA100;
  }

  .vr_tag_modal .tt-suggestion.tt-cursor {
    cursor: pointer;
    background: #eee;
  }

  .vr_tag_modal .vr_tag_th_b {
    position: relative;
  }

  .vr_tag_modal .vr_tag_th_spinner {
    display: none;
    position: absolute;
    right: 25px;
    top: 10px;
  }

  .vr_tag_modal .tt-hint {
    padding-right: 33px;
  }

.vr_tag_detail {
}

  .vr_tag_detail .target-demo {
    display: inline-block;
    vertical-align: middle;
  }

  .vr_tag_detail .hint {
    border-radius: 5px;
    color: #333;
    display: inline-block;
    height: 26px;
    line-height: 26px;
    text-align: center;
    vertical-align: middle;
    margin-left: 8px;
    font-weight: bold;
    color: #EDA100;
  }

  .vr_tag_detail .tt-suggestion.tt-cursor {
    cursor: pointer;
    background: #eee;
  }

  .vr_tag_detail .vr_tag_th_b {
    position: relative;
  }

  .vr_tag_detail .vr_tag_th_spinner {
    display: none;
    position: absolute;
    right: 25px;
    top: 10px;
  }

  .vr_tag_detail .tt-hint {
    padding-right: 33px;
  }

.vtd_v_item {
}

  .vtd_v_item .v_left {
    float: left;
    width: 90px;
    position: relative;
  }

  .vtd_v_item .v_pic {
    display: block;
    margin: 0 auto;
    width: 65px;
    height: 65px;
    overflow: hidden;
    border-radius: 50% !important;
    text-align: center;
  }

  .vtd_v_item .v_position {
    margin: 10px auto 0;
    text-align: center;
  }

    .vtd_v_item .v_position > span {
      line-height: 100%;
      padding: 3px 6px;
      background-color: #D3EEFF;
      color: #3696E0;
      display: inline-block;
    }

  .vtd_v_item .v_right {
    position: relative;
    margin-left: 98px;
  }

  .vtd_v_item .v_staffname {
    margin-bottom: 3px;
  }

  .vtd_v_item .v_star {
    margin-bottom: 4px;
  }

  .vtd_v_item .v_hint {
  }

  .vtd_v_item .v_con {
    word-break: break-all;
    word-wrap: break-word;
  }

  .vtd_v_item .v_time_b {
  }

  .vtd_v_item .v_time {
    float: left;
    color: #999;
  }

  .vtd_v_item .v_btn {
    float: right;
    margin-left: 15px;
    text-decoration: underline;
    color: #2369B1;
  }

    .vtd_v_item .v_btn:hover {
      text-decoration: none;
    }

.vtd_e_item {
  display: none;
}

  .vtd_e_item .v_left {
    float: left;
    width: 90px;
    position: relative;
  }

  .vtd_e_item .v_pic {
    display: block;
    margin: 0 auto;
    width: 65px;
    height: 65px;
    overflow: hidden;
    border-radius: 50% !important;
    text-align: center;
  }

  .vtd_e_item .v_position {
    margin: 10px auto 0;
    text-align: center;
  }

    .vtd_e_item .v_position > span {
      line-height: 100%;
      padding: 3px 6px;
      background-color: #D3EEFF;
      color: #3696E0;
      display: inline-block;
    }

  .vtd_e_item .v_right {
    position: relative;
    margin-left: 98px;
  }

  .vtd_e_item .v_staffname {
    margin-bottom: 3px;
  }

  .vtd_e_item .v_star {
    margin-bottom: 4px;
    display: inline-block;
  }

  .vtd_e_item .v_hint {
    display: inline-block;
  }

  .vtd_e_item .vtd_e_sub {
    margin-right: 12px;
  }

  .vtd_e_item .vtd_e_l {
    margin-top: 8px;
  }

  .vtd_e_item .v_con {
    margin-top: 8px;
  }

  .vtd_e_item .vtd_e_con {
    resize: none;
  }

.vr_bindCompany {
}

  .vr_bindCompany .control-label {
    margin-top: 8px;
  }

    .vr_bindCompany .control-label i {
      cursor: pointer;
    }

.qcvr-b {
  border: 1px solid #E7505A;
  line-height: 34px;
  margin-bottom: 11px;
}

  .qcvr-b .qc-tip {
    float: left;
  }

    .qcvr-b .qc-tip i {
      margin: 0 6px 0 0;
      vertical-align: middle;
    }

  .qcvr-b .qc-r-b {
    margin-left: 160px;
    padding: 0;
  }

  .qcvr-b .qc-r-r {
    float: right;
    margin-right: 30px;
  }

    .qcvr-b .qc-r-r a {
      margin-left: 6px;
      padding: 0px;
    }

      .qcvr-b .qc-r-r a img {
        vertical-align: middle;
      }

.vr .experience dl {
  display: block;
  overflow: hidden;
  margin-bottom: 10px;
}

  .vr .experience dl:last-child {
    margin-bottom: 0;
  }

.vr .experience dt {
  float: left;
  width: 80px;
  font-weight: normal;
}

.vr .experience dd {
  margin-left: 80px;
  line-height: 1.7;
}

.r-tb-vr-oe {
  margin-bottom: -1px;
}

  .r-tb-vr-oe thead th {
    text-align: center;
    font-weight: normal !important;
  }

  .r-tb-vr-oe > tbody > tr > td {
    padding-left: 9px !important;
    padding-right: 9px !important;
    white-space: normal;
  }

  .r-tb-vr-oe .r-company {
  }

  .r-tb-vr-oe .r-jobtitle {
  }

  .r-tb-vr-oe .r-time {
    width: 138px;
  }

  .r-tb-vr-oe .r-date {
    width: 106px;
  }

  .r-tb-vr-oe .r-search {
    width: 120px;
  }

    .r-tb-vr-oe .r-search a {
      font-size: 16px;
    }

      .r-tb-vr-oe .r-search a:nth-child(1) {
        margin-right: 8px;
      }

  .r-tb-vr-oe .r-btn {
    background-color: #EEF3F7;
    width: 46px;
    cursor: pointer;
  }

.r-tb-vr-ope {
  margin-bottom: -1px;
}

  .r-tb-vr-ope thead th {
    text-align: center;
    font-weight: normal !important;
  }

  .r-tb-vr-ope > tbody > tr > td {
    padding-left: 9px !important;
    padding-right: 9px !important;
    white-space: normal;
  }

  .r-tb-vr-ope .r-time {
    width: 138px;
  }

  .r-tb-vr-ope .r-project {
  }

  .r-tb-vr-ope .r-jobtitle {
  }

  .r-tb-vr-ope .r-btn {
    background-color: #EEF3F7;
    width: 46px;
    cursor: pointer;
  }

.r-tb-vr-oebd {
  margin-bottom: -1px;
}

  .r-tb-vr-oebd thead th {
    text-align: center;
    font-weight: normal !important;
  }

  .r-tb-vr-oebd > tbody > tr > td {
    padding-left: 9px !important;
    padding-right: 9px !important;
    white-space: normal;
  }

  .r-tb-vr-oebd .r-time {
    width: 138px;
  }

  .r-tb-vr-oebd .r-institution {
  }

  .r-tb-vr-oebd .r-studyfield {
  }

  .r-tb-vr-oebd .r-educationlevel {
  }

.r-tb-vr-jc {
  margin-bottom: 10px;
}

  .r-tb-vr-jc thead th {
    font-weight: normal !important;
  }

  .r-tb-vr-jc > tbody > tr > td {
  }

  .r-tb-vr-jc th, .r-tb-vr-jc td {
    text-align: center;
  }

  .r-tb-vr-jc .r-title {
    width: 86px;
    text-align: center;
    border-right: 0 none;
  }

  .r-tb-vr-jc .r-step {
    width: 86px;
    text-align: center;
    border-left: 0 none;
  }

  .r-tb-vr-jc .r-status {
    width: 70px;
    text-align: center;
  }

  .r-tb-vr-jc .r-clientname {
    text-align: center;
  }

  .r-tb-vr-jc .r-jobtitle {
    text-align: center;
  }

  .r-tb-vr-jc .r-time {
    width: 150px;
    text-align: center;
  }

  .r-tb-vr-jc .r-staff {
    width: 112px;
    text-align: left;
  }

  .r-tb-vr-jc .r-btn {
    background-color: #EEF3F7;
    width: 46px;
    cursor: pointer;
  }

  .r-tb-vr-jc tbody .r-btn {
    background: none;
  }

  .r-tb-vr-jc tbody .r-zheng {
    width: 46px;
  }

.r-tb-vr-ja {
  margin-bottom: 10px;
}

  .r-tb-vr-ja thead th {
    font-weight: normal !important;
  }

  .r-tb-vr-ja > tbody > tr > td {
  }

  .r-tb-vr-ja th, .r-tb-vr-jc td {
    text-align: center;
  }

  .r-tb-vr-ja .r-title {
    width: 86px;
    text-align: right;
    border-right: 0 none;
  }

  .r-tb-vr-ja .r-status {
    width: 70px;
    text-align: center;
  }

  .r-tb-vr-ja .r-clientname {
    text-align: left;
  }

  .r-tb-vr-ja .r-jobtitle {
    text-align: left;
  }

  .r-tb-vr-ja .r-created {
    width: 150px;
  }

  .r-tb-vr-ja .r-staffname {
    width: 112px;
    text-align: left;
  }

  .r-tb-vr-ja .r-btn {
    background-color: #EEF3F7;
    width: 46px;
    cursor: pointer;
  }

  .r-tb-vr-ja tbody .r-btn {
    background: none;
  }

.r-tb-vr-fj {
}

  .r-tb-vr-fj th, .r-tb-vr-fj td {
    text-align: center;
  }

  .r-tb-vr-fj .r-staff {
    width: 112px;
  }

  .r-tb-vr-fj .r-filename {
    text-align: left;
  }

  .r-tb-vr-fj .r-time {
    width: 150px;
  }

  .r-tb-vr-fj .r-con {
    text-align: left;
    cursor: pointer;
  }

.r-tb-vr-crl {
}

  .r-tb-vr-crl th, .r-tb-vr-crl td {
    text-align: center;
  }

  .r-tb-vr-crl .r-photo {
    width: 80px;
  }

    .r-tb-vr-crl .r-photo img {
      background-image: url(/css/images/default_small.gif);
    }

.r-tb-vr-rel {
}

  .r-tb-vr-rel > tbody > tr:hover {
    background-color: #fff;
  }

  .r-tb-vr-rel th, .r-tb-vr-crl td {
    text-align: center;
  }

  .r-tb-vr-rel .r-photo {
    width: 80px;
  }

    .r-tb-vr-rel .r-photo img {
      background-image: url(/css/images/default_small.gif);
    }

  .r-tb-vr-rel .r-staffname {
    width: 80px;
  }

  .r-tb-vr-rel .r-time {
    width: 160px;
  }

  .r-tb-vr-rel td.r-change {
    position: relative;
    padding-bottom: 30px;
  }

  .r-tb-vr-rel .r-change .r-change-box {
    max-height: 180px;
    overflow: hidden;
    white-space: normal;
  }

  .r-tb-vr-rel .r-change .rpchangeitem {
    padding: 4px 8px;
  }

.r-change .hide-article-box {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding-top: 40px;
  background-image: -webkit-gradient(linear,left top, left bottom,from(rgba(255,255,255,0.6)),color-stop(50%, #fff));
  background-image: linear-gradient(-180deg,rgba(255,255,255,0.6) 0%,#fff 50%);
}

  .r-change .hide-article-box.active {
    background-image: none;
  }


.r-tb-vr-rel .r-change .showAllbtn:hover {
  background-color: #f3f3f3 !important;
}

.r-tb-vr-rel .r-change .showAllbtn {
  cursor: pointer;
  /*color: #a7a7a7;*/
  color: #9e9e9e;
  border: none;
  /*background-color:#f3f3f3;*/
  background-color: #fff;
  height: 26px;
  line-height: 26px;
  margin-bottom: 5px;
  font-weight: bolder;
  font-family: '微软雅黑';
}


.r-tb-vr-rel .title {
  color: #666;
  font-weight: bold;
}

.r-tb-vr-rel .vrel-itemlist li {
  margin-left: 1em;
}

.r-tb-vr-rel .vrel-itemlist .labicon {
  color: #2BA0E3;
}

.r-tb-vr-rel .rpchangeitem .beforevalue {
  text-decoration: line-through;
  color: #b8b8b8;
}

/*简历评估列表*/
.r-tb-vr-re {
}

  .r-tb-vr-re td {
    white-space: normal;
  }

  .r-tb-vr-re .r-consultant {
    width: 220px;
  }

    .r-tb-vr-re .r-consultant img {
      margin: 20px auto;
      border-radius: 50% !important;
    }

    .r-tb-vr-re .r-consultant h3 {
      text-align: center;
      font-size: 18px;
      color: #4d4d4d;
    }

    .r-tb-vr-re .r-consultant p {
      text-align: center;
      color: #999;
    }

  .r-tb-vr-re .r-job {
    width: 248px;
    text-align: center;
  }

    .r-tb-vr-re .r-job .r-job-title {
      margin-top: 58px;
      color: #4f4f4f;
      font-size: 20px;
      font-weight: bold;
    }

    .r-tb-vr-re .r-job p {
      margin: 12px auto;
    }

  .r-tb-vr-re .r-eva {
    position: relative;
  }

  .r-tb-vr-re td.r-eva {
    padding-bottom: 15px;
  }

  .r-tb-vr-re .r-eva p {
    max-height: 200px;
    overflow: hidden;
  }

  .r-tb-vr-re .r-eva .seemorebtn {
    position: absolute;
    bottom: 0;
    right: 7px;
    font-family: 'Microsoft YaHei';
    font-weight: bold;
    color: #659ac8;
  }

    .r-tb-vr-re .r-eva .seemorebtn:hover {
      text-decoration: none;
    }

.r-eva .seemorebtn:active {
  text-decoration: none;
}

.r-eva .seemorebtn:visited {
  text-decoration: none;
}

.r-eva .seemorebtn:focus {
  text-decoration: none;
}

.r-tb-vr-memos {
  margin-bottom: 10px;
}

  .r-tb-vr-memos th, .r-tb-vr-memos td {
    text-align: center;
  }

  .r-tb-vr-memos .r-staff {
    width: 75px;
  }

  .r-tb-vr-memos .r-company {
    width: 90px;
  }

  .r-tb-vr-memos .r-postion {
    width: 90px;
  }

  .r-tb-vr-memos .r-con {
    text-align: left;
    cursor: pointer;
    overflow: hidden;
  }

  .r-tb-vr-memos .r-time {
    width: 150px;
  }

.r-tb-vr-error {
}

  .r-tb-vr-error th, .r-tb-vr-error td {
    text-align: center;
  }

  .r-tb-vr-error .r-type {
  }

  .r-tb-vr-error .r-staffname {
  }

  .r-tb-vr-error .r-time {
    width: 150px;
  }

.vr .language span.title {
}

.vr .language .l-list {
}

.vr .language .l-item {
  width: 100%;
  padding: 0 11px;
  text-align: left;
  border: 1px solid #DDDDDD;
  margin-right: -1px;
  margin-bottom: -1px;
  display: flex;
  height: 36px;
  line-height: 36px;
}

.r-v .nav-pills > li > a > .badge, .r-m .nav-tabs > li > a > .badge {
  margin-top: 0;
}

.r-v .tabbable-line > .nav-tabs > li > a {
  padding-left: 6px;
  padding-right: 6px;
}

.bofang-box {
  box-sizing: border-box;
}

  .bofang-box * {
    box-sizing: border-box;
  }

.item-name-2 {
  padding: 0 0 0 32px;
  border: 1px solid #d0cecf;
  height: 36px;
  line-height: 36px;
  font-weight: bold;
  background: url(/css/images/list-item-2.gif) repeat-x;
}

  .item-name-2 span {
    display: inline;
    float: left;
    overflow: hidden;
  }

.recruiting-situation .item-name-2 .item-6 {
  overflow: visible;
}

.item-name-2 .select-classify {
  position: relative;
  white-space: nowrap;
  z-index: 2;
  float: left;
  margin-top: 4px;
}

  .item-name-2 .select-classify dt {
    float: left;
    padding: 0 20px 0 5px;
    border: 1px solid transparent;
    border-bottom: 0;
    height: 22px;
    line-height: 21px;
    cursor: pointer;
    background: url(images/close.gif) no-repeat 90% 50%;
    font-weight: 600;
  }

  .item-name-2 .select-classify dd {
    display: none;
    overflow: hidden;
    min-width: 100%;
    border: 1px solid #b7b7b7;
    padding: 5px 0;
    white-space: nowrap;
    *white-space: pre-wrap;
    *word-wrap: break-word;
    border-top: 0;
    position: absolute;
    top: 23px;
    left: 0;
    background: #ffffff;
    z-index: 9;
    text-align: left;
  }

  .item-name-2 .select-classify.hover {
    background: #b7b7b7;
  }

    .item-name-2 .select-classify.hover dd {
      display: block;
    }

    .item-name-2 .select-classify.hover dt {
      background: #ffffff url(images/open.gif) no-repeat 90% 50%;
      border: 1px solid #b7b7b7;
    }

  .item-name-2 .select-classify dd a {
    display: block;
    white-space: nowrap;
    line-height: 21px;
    font-weight: normal;
    padding-left: 10px;
    padding-right: 5px;
    cursor: pointer;
  }

    .item-name-2 .select-classify dd a:hover {
      color: #fc8e00;
    }

.list-item-2 {
  display: block;
  width: 100%;
}

  .list-item-2 li {
    float: left;
    margin-bottom: 1px;
    width: 100%;
    height: 29px;
  }

.resume-detail .list-item-2 li {
  height: 22px;
  line-height: 22px;
}

.resume-operate .list-item-2 {
  background: #ffffff;
}

li.odd {
  background: #f8f8f8;
}

li.even {
  background: #edecec;
}

.index .title {
  position: relative;
}

.index .list-item-2 li {
  height: 23px;
  line-height: 23px;
}

  .index .list-item-2 li .item {
    overflow: hidden;
    height: 20px;
  }

.list-item-2 li a {
  display: block;
  overflow: hidden;
  padding: 0 0 0 32px;
  color: #000000;
  cursor: pointer;
}

  .list-item-2 li a:hover {
    background: url(images/saffh2.jpg) 0 -55px repeat-x;
  }

.index .list-item-2 li a {
  padding: 0;
}

  .index .list-item-2 li a:hover {
    background: none;
    color: #4b72b0;
  }

.list-item-4 {
  display: block;
  width: 100%;
  background: #FFFFFF;
  line-height: 0;
  font-size: 0;
}

  .list-item-4 li {
    float: left;
    margin-top: 1px;
    line-height: 30px;
    font-size: 12px;
  }

.list-item-3 {
  display: block;
  width: 100%;
  background: #FFFFFF;
  line-height: 0;
  font-size: 0;
}

  .list-item-3 li {
    margin-top: 1px;
    padding: 0 0 0 32px;
    line-height: 34px;
    font-size: 12px;
    float: left;
  }

.vr .r-th-information {
  line-height: 100%;
  padding: 0 0 10px;
}

.vr .tonghualog-box .item {
  float: left;
  overflow: hidden;
  text-align: center;
  min-height: 10px;
  height: 34px;
}

  .vr .tonghualog-box .item.item-huchuuser {
    width: 95px;
  }
  /*.vr .tonghualog-box .item.item-huchunum{width:85px;}*/
  .vr .tonghualog-box .item.item-company {
    width: 110px;
  }

  .vr .tonghualog-box .item.item-touser {
    width: 85px;
  }

  .vr .tonghualog-box .item.item-tousernum {
    width: 125px;
  }

  .vr .tonghualog-box .item.item-huchutime {
    width: 175px;
  }

  .vr .tonghualog-box .item.item-time {
    width: 120px;
  }

  .vr .tonghualog-box .item.item-status {
    width: 60px;
  }

  .vr .tonghualog-box .item.item-download {
    width: 60px;
  }

  .vr .tonghualog-box .item.item-luyin {
    width: 40px;
  }

.vr .tonghualog-box .luyinbtn-box.selected {
  height: 34px;
}

.vr .tonghualog-box .list-item-3 .calllogsitem:hover {
  background-color: #f5f5f5;
}

.r-v .rnss-line {
  margin-top: 8px;
}

.r-v .nav-tabs > .r-bb-p {
  float: right;
  margin-top: 5px;
}

  .r-v .nav-tabs > .r-bb-p:hover {
    border-bottom-color: transparent;
  }

.vr-info-list {
}

  .vr-info-list > li {
    float: left;
    width: 50%;
  }

.vr .tabbable-line > .tab-content {
  padding-top: 18px;
  min-height: 210px;
}
/*简历详情 end zsl*/
.r-con-t-p {
  display: block;
  margin-top: 3px;
  height: 35px;
  border-bottom: 1px solid #EBF0F4;
  margin-bottom: 11px;
}

  .r-con-t-p .r-h {
    float: left;
    padding: 10px 12px;
    background: none repeat scroll 0 0 #32C6D2;
    color: #FFFFFF;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    line-height: 100% !important;
    height: auto !important;
    margin-bottom: 0;
    margin-top: 0;
  }

.sug-app-title {
  position: relative;
}

  .sug-app-title::before {
    content: "";
    position: absolute;
    left: 312px;
    top: 0;
    bottom: 0;
    margin: auto 0;
    width: 100px;
    height: 2px;
    background-color: #50cafc;
    font-size: 0;
    font-size-adjust: none;
  }

  .sug-app-title::after {
    content: "";
    position: absolute;
    right: 312px;
    top: 0;
    bottom: 0;
    margin: auto 0;
    width: 100px;
    height: 2px;
    background-color: #50cafc;
    font-size: 0;
    font-size-adjust: none;
  }

  .sug-app-title.sug-app-title-short::before {
    left: 284px;
  }

  .sug-app-title.sug-app-title-short::after {
    right: 284px;
  }

.suggestion-box {
  font-size: 16px;
  padding: 15px 0 0;
  box-sizing: border-box;
}

  .suggestion-box ul.suggestion-list {
    margin-bottom: 36px;
  }

  .suggestion-box .suggestion-list .suggestion {
    padding: 15px 10px;
    position: relative;
  }

    .suggestion-box .suggestion-list .suggestion em {
      font-style: normal;
    }

.suggestion-list .suggestion:nth-child(odd) {
  background-color: #f6f6f6;
}

.suggestion-box .txtover {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.suggestion-box .sl-t .job {
  color: #282828;
  height: 28px;
  width: 346px;
  display: inline-block;
}

.suggestion-box .sl-t .salary {
  display: inline-block;
  width: 140px;
  color: #f46245;
  text-align: right;
}

.suggestion-box .sl-b .company {
  color: #666;
  width: 244px;
  margin-right: 18px;
  display: inline-block;
}

.suggestion-box .sl-b-info1 {
  display: inline-block;
  color: #717171;
  text-align: right;
}

.suggestion-box .sl-b .location {
  width: 108px;
  display: inline-block;
  margin-right: 14px;
}

.suggestion-box .sl-b .sline {
  font-size: 14px;
}

.suggestion-box .sl-b-info1 .stime {
  display: inline-block;
  width: 96px;
}

.suggestion-box .suggestion .join_team_btn {
  position: absolute;
  right: 108px;
  top: 28px;
  height: 36px;
  width: 110px;
  border-radius: 3px !important;
  background-color: #3ba8f0;
  color: #fff;
  font-size: 16px;
  display: none;
  box-sizing: border-box;
}

.suggestion-box .suggestion:hover .join_team_btn {
  display: block;
}


.suggestion .open-btn-box {
  position: absolute;
  top: 28px;
  right: 25px;
  color: #ccc;
  font-size: 18px;
  height: 28px;
  width: 28px;
  border: 2px solid #ccc;
  cursor: pointer;
  border-radius: 50% !important;
  text-align: center;
  line-height: 16px;
  box-sizing: border-box;
}

  .suggestion .open-btn-box.up {
    line-height: 28px;
  }

.suggestion-box .suggestion .belongs {
  position: absolute;
  top: 34px;
  right: 214px;
  width: 198px;
  color: #666;
  font-size: 16px;
}

  .suggestion-box .suggestion .belongs.closer-right {
    right: 214px;
    width: 174px;
  }

.suggestion-box .suggestion .sl_hpart {
  font-size: 14px; /*margin-top:15px;*/
  display: none;
}
/*.suggestion-box .suggestion .sl_hpart.open{transition:all .5s;display:block;}*/
.sl-hpart-info > span {
  margin-right: 12px;
}

.suggestion .sl_hpart .con_det {
  max-height: 70px;
  overflow: hidden;
  padding-right: 40px;
}

  .suggestion .sl_hpart .con_det.lower {
    max-height: 68px;
  }


.nodata .animated {
  -webkit-animation-duration: 1.5s;
  animation-duration: 1.5s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

  .nodata .animated.infinite {
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
  }

@-webkit-keyframes tada {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }

  10%, 20% {
    -webkit-transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
  }

  30%, 50%, 70%, 90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  40%, 60%, 80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

@keyframes tada {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }

  10%, 20% {
    -webkit-transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
  }

  30%, 50%, 70%, 90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  40%, 60%, 80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

.tada {
  -webkit-animation-name: tada;
  animation-name: tada;
}

/*小圆边框分页*/
.hiddenPager {
  visibility: hidden;
}

.jqpager-round {
  position: relative;
  width: 100%;
  height: 40px;
  background-color: #FFF;
}

  .jqpager-round .pagination {
    float: initial;
    display: inline-block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    text-align: center;
  }

    .jqpager-round .pagination li {
      display: inline-block;
      margin: 0 5px;
    }

      .jqpager-round .pagination li a {
        padding: 4px 9px;
        border-radius: 4px !important;
        color: #606266;
        background-color: #f4f4f5;
        font-size: 12px;
      }

      .jqpager-round .pagination li.active a {
        background-color: #409eff;
        border-color: #409eff;
        color: #fff;
      }

      .jqpager-round .pagination li.disabled a {
        color: #c0c4cc
      }

/*简历详情旧版样式调用 start zsl*/
.jiaRuXiangMu {
  width: 570px;
}

  .jiaRuXiangMu .fill-out-title {
    width: 560px;
    height: 35px;
  }

  .jiaRuXiangMu .popup-list-content {
    background: #edecec;
  }

  .jiaRuXiangMu .line {
    padding-left: 12px;
  }

    .jiaRuXiangMu .line .label-1 {
      margin-top: 6px;
    }

  .jiaRuXiangMu .button-6 {
    margin-top: 6px;
  }

#optJobSearchType {
  float: left;
}

.jiaRuXiangMu .itme-1 {
  width: 37px;
  margin-left: 10px;
  text-align: center;
}

.jiaRuXiangMu .itme-2 {
  width: 122px;
}

  .jiaRuXiangMu .itme-2 a {
    text-decoration: none;
    color: #333;
  }

    .jiaRuXiangMu .itme-2 a:hover {
      text-decoration: none;
    }

.jiaRuXiangMu .itme-3 {
  width: 240px;
}

  .jiaRuXiangMu .itme-3 a {
    text-decoration: none;
    color: #333;
  }

    .jiaRuXiangMu .itme-3 a:hover {
      text-decoration: none;
    }

.jiaRuXiangMu .itme-4 {
  width: 75px;
}

.jiaRuXiangMu .itme-5 {
  width: 60px;
  text-align: right;
  margin-right: 6px;
}

.jiaRuXiangMu .addMessage {
  padding-top: 20px;
  background: #fff;
}

.jiaRuXiangMu .textarea-1 {
  width: 415px;
}

.jiaRuXiangMu .select-message {
  float: left;
  padding: 5px 0 5px 100px;
  font-weight: bold;
}

  .jiaRuXiangMu .select-message input {
    margin-right: 10px;
  }

.jiaRuXiangMu .button-box-11 {
  clear: both;
  background: #FFFFFF;
  margin: 0 5px;
  text-align: right;
  padding: 0 30px 20px 0;
}

.label-1 {
  float: left;
  font-weight: bold;
  width: 82px;
  margin: 0 15px 0 0;
  text-align: right;
  cursor: text;
}

.r-row label {
  float: left;
  font-weight: bold;
  width: 82px;
  margin: 0 15px 0 0;
  text-align: right;
  cursor: text;
}

.addMessage {
  background: #ffffff;
  margin: 0 5px;
  border-top: 1px dashed #999;
}

  .addMessage .textarea-1 {
    margin-left: 15px;
    *margin-left: 10px;
    display: inline;
    width: 440px;
    height: 90px;
  }

  .addMessage h3 {
    margin-left: 15px;
    display: inline;
    font-size: 12px;
    float: left;
  }

  .addMessage .addMessage-list {
    float: left;
    width: 100%;
    font-weight: bold;
  }

.popup-list-content {
  background: #FFFFFF;
  overflow: hidden;
  margin: 0 5px;
}

  .popup-list-content h3 {
    color: #21639c;
    height: 53px;
    line-height: 53px;
    margin-left: 15px;
  }

  .popup-list-content .popup-list-title {
    font-weight: bold;
    width: 100%;
    background: #e1e1e1;
    overflow: hidden;
  }

  .popup-list-content .paging {
    background: #FFF;
    height: 55px;
  }

  .popup-list-content .popup-btn {
    padding: 4px 10px;
    background: #f2f0f0;
    border-top: 1px solid #a6a5a5;
  }

  .popup-list-content .popup-list-item {
    float: left;
    width: 100%;
    background: #ffffff;
  }

    .popup-list-content .popup-list-item a {
      color: #026fb2;
      cursor: pointer;
    }

      .popup-list-content .popup-list-item a:hover {
        text-decoration: underline;
        color: #ff9313;
      }

  .popup-list-content .popup-list-title span {
    display: block;
    float: left;
  }

  .popup-list-content .popup-list-item li {
    width: 100%;
    float: left;
    overflow: hidden;
  }

  .popup-list-content .popup-list-item .list-con-1 {
    width: 490px;
  }

  .popup-list-content .popup-list-item li:hover .list-con-1 {
    background: url(images/saffh2.jpg) 0 -55px repeat-x;
  }

.join-title-box {
  width: 350px;
  margin-top: 5px;
  margin-right: 8px;
}

  .join-title-box .type-input {
    float: left;
    width: 80px !important;
  }

  .join-title-box .title-line {
    border-right: 1px solid #c2c2c3;
    float: left;
    height: 28px;
    width: 10px;
  }

  .join-title-box .my-calendar-title {
    width: 240px !important;
  }

.my-joinProject-title {
  float: left;
  border-left: solid 1px #817b7b;
  background: none repeat scroll 0 0 transparent;
  border-bottom: medium none;
  border-top: medium none;
  border-right: medium none;
  height: 28px;
  line-height: 28px;
  overflow: hidden;
  text-indent: 10px;
}

.item {
  float: left;
  height: 29px;
  overflow: hidden;
}
/*日程管理-我的日程*/
.my-calendar {
  width: 470px;
  margin: 0 auto;
}

  .my-calendar * {
    box-sizing: content-box;
  }

  .my-calendar .line {
    width: 100%;
    margin: 10px 0;
  }

    .my-calendar .line label {
      float: left;
      margin-right: 0;
      font-weight: bold;
      width: 80px;
      text-align: right;
      padding-right: 10px;
    }

    .my-calendar .line .text-2 {
      float: left;
      width: 100px;
    }

  .my-calendar .textarea-1 {
    width: 345px;
  }

  .my-calendar .data-list {
    background: #eeeeee;
  }

  .my-calendar .list-item-3 li {
    width: 896px;
  }

  .my-calendar .calendar-title .text-2 {
    margin-right: 5px;
  }

  .my-calendar .calendar-title span {
    float: left;
    margin: 0 8px;
    font-weight: bold;
  }

  .my-calendar .item-1 {
    width: 36px;
  }

  .my-calendar .item-2 {
    width: 212px;
  }

  .my-calendar .item-3 {
    width: 455px;
  }

  .my-calendar .item-4 {
    width: 88px;
    text-align: center;
  }

  .my-calendar .item-5 {
    width: 80px;
    text-align: center;
  }

.my-calendar-title {
  float: left;
  width: 260px;
  border-left: solid 1px #817b7b;
  background: none repeat scroll 0 0 transparent;
  border-bottom: medium none;
  border-top: medium none;
  border-right: medium none;
  height: 28px;
  line-height: 28px;
  overflow: hidden;
  text-indent: 10px;
}

.my-joinProject-title {
  float: left;
  border-left: solid 1px #817b7b;
  background: none repeat scroll 0 0 transparent;
  border-bottom: medium none;
  border-top: medium none;
  border-right: medium none;
  height: 28px;
  line-height: 28px;
  overflow: hidden;
  text-indent: 10px;
}

.my-calendar-date {
  width: 100px;
  margin-right: 10px;
}

.my-calendar .dateymd {
  position: absolute;
  left: 100px;
  top: 7px;
}

.my-calendar .dateymd-box {
  float: left;
  position: relative;
}

.my-calendar .datehsm-box {
  float: left;
  position: relative;
}

.my-calendar .type-box {
  width: 90px;
  float: left;
  margin-right: 10px;
}

.my-calendar .type-input {
  width: 80px !important;
  float: left;
}

.my-calendar .title-line {
  float: left;
  border-right: solid 1px #C2C2C3;
  width: 10px;
  height: 28px;
}

.my-calendar .title-box {
  width: 365px;
}

.calendar-footer {
  background: none repeat scroll 0 0 #eee;
  border-top: 1px solid #a6a5a5;
  height: 40px;
  line-height: 40px;
  width: 100%;
  float: left;
}

  .calendar-footer .my-calendar-form {
    width: 120px;
    margin-right: 40px;
    float: right;
    margin-top: 8px;
  }

.btn-submit {
  background: url("/Css/images/btn_submit.jpg") repeat-x scroll 0 0 transparent;
  color: #fff;
  cursor: pointer;
  font-size: 13px;
  height: 23px;
  line-height: 23px;
  width: 49px;
  text-align: center;
  margin: 0 5px;
  float: left;
  border: medium none;
}

.btn-cancel {
  background: url("/Css/images/btn_cancel.jpg") repeat-x scroll 0 0 transparent;
  color: #444445;
  cursor: pointer;
  font-size: 13px;
  height: 23px;
  line-height: 23px;
  width: 49px;
  text-align: center;
  margin: 0 5px;
  float: left;
  border: medium none;
}
/*通话*/
.my-callpho-commentbox {
  height: auto;
  overflow: hidden;
  display: block;
  padding: 10px 15px;
}

.my-callpho-commentbox-con {
  display: block;
  overflow: hidden;
}

.my-callpho-commentbox .line {
  float: none;
  margin: 0;
  text-align: left;
}

.my-callpho-commentbox .mcp-zlcomment {
  display: inline-block;
  padding: 3px 5px;
  cursor: pointer;
  vertical-align: middle;
  margin-right: 10px;
}

  .my-callpho-commentbox .mcp-zlcomment:hover {
    background: #FCF3DA;
  }

  .my-callpho-commentbox .mcp-zlcomment input {
    display: inline-block;
    vertical-align: initial;
    margin-right: 5px;
    cursor: pointer;
  }

  .my-callpho-commentbox .mcp-zlcomment label {
    display: inline-block;
    vertical-align: middle;
    width: auto;
    height: auto;
    cursor: pointer;
    background-color: transparent;
  }

.my-callpho-commentbox .mcp-wtcomment {
}

  .my-callpho-commentbox .mcp-wtcomment input {
    float: left;
    margin: 6px 5px 0 0;
    border: 1px solid #ccc;
    border-radius: 3px;
  }

  .my-callpho-commentbox .mcp-wtcomment label {
    float: left;
    margin: 0;
    cursor: pointer;
  }

.my-callpho-commentbox .mcp-qtcomment {
  line-height: 18px;
  border: 1px solid #d3d3d3;
  border-radius: 5px;
  padding: 5px;
  resize: none;
  box-shadow: 1px 1px 1px #d3d3d3 inset;
}

.my-callpho-commentbox .mcp-submit-box {
  margin: 6px 0 0;
  text-align: center;
}

.my-callpho-commentbox .whitebtn {
  padding: 2px 26px;
}
/*简历详情旧版样式调用 end zsl*/
/*猎问管理 start zsl*/
/*锐仕方达分支合伙人制度*/
.modal.dlg-hhrzd .modal-header .close {
  width: 19px;
  height: 19px;
  background-repeat: no-repeat !important;
  text-indent: -10000px;
  outline: 0;
  background-image: url(images/close_btn.png) !important;
  opacity: 1;
  margin-top: 3px !important;
}

.modal.dlg-hhrzd .modal-dialog {
  width: 895px;
}
/*锐仕方达猎头SOP流程 start zsl*/
.modal.dlg-rnsssop .modal-header .close {
  width: 19px;
  height: 19px;
  background-repeat: no-repeat !important;
  text-indent: -10000px;
  outline: 0;
  background-image: url(images/close_btn.png) !important;
  opacity: 1;
  margin-top: 3px !important;
}

.modal.dlg-rnsssop .modal-dialog {
  width: 729px;
}

.modal.dlg-rnsssop .modal-header {
  background-color: #368dc4;
  color: #fff;
}

  .modal.dlg-rnsssop .modal-header .modal-title {
    font-weight: 700;
  }

.modal.dlg-rnsssop .modal-body img {
  max-width: 100%;
}

.modal.dlg-rnsssop .modal-body { /*overflow-y:scroll;max-height:700px;*/
}

.tuwenpanel, .tuwenpanel * {
  box-sizing: content-box;
}

.tuwenpanel {
  position: relative;
  display: block;
  padding: 0;
  border: 1px solid #ccc;
  min-height: 500px;
  overflow: hidden;
}

  .tuwenpanel .tuwen {
    display: block;
    margin: 0 auto;
    padding: 13px 6px 5px 6px;
    border: 0 none;
    border-bottom: 1px solid #E0E0E0;
    height: auto;
    overflow: hidden; /*background:rgba(53, 152, 220, 0.6)!important;filter:alpha(opacity=60);*/
  }

    .tuwenpanel .tuwen li {
      float: left;
      min-width: 46px;
      _width: 64px;
      white-space: nowrap;
      position: relative;
    }

      .tuwenpanel .tuwen li a {
        display: block;
        margin: 0 0 10px 0;
        border-right: 1px solid #D7D7D7;
        padding: 0 10px 0 10px;
        line-height: 100%;
        float: left;
        font-weight: normal;
        cursor: pointer;
        color: #333;
        font-size: 16px;
      }

        .tuwenpanel .tuwen li a.noline {
          border-right: 0 none;
        }

        .tuwenpanel .tuwen li a:hover {
          color: #3498db;
        }

      .tuwenpanel .tuwen li span {
        display: block;
        float: left;
        padding-left: 7px;
        color: red;
        font-family: "宋体";
      }

      .tuwenpanel .tuwen li.select a {
        color: #3498db;
      }

  .tuwenpanel .slides {
    position: relative;
    width: 100%;
    overflow: hidden;
  }

    .tuwenpanel .slides li img {
      width: 100%;
      border: 0 none;
    }

  .tuwenpanel .flexsliders {
    overflow: hidden;
    width: 724px;
    position: relative;
    margin: 0 auto;
  }

    .tuwenpanel .flexsliders .flex-viewport {
      border-top: 1px solid #dedede;
    }

    .tuwenpanel .flexsliders .navpanel {
      z-index: 10;
      color: #fff;
      background-color: #fff;
      zoom: 1; /*触发IE下块级元素*/
      filter: alpha(opacity=0);
      -moz-opacity: 0;
      opacity: 0;
      visibility: visible;
      position: absolute;
      height: 100%;
      width: 50%;
    }

      .tuwenpanel .flexsliders .navpanel.prev {
        cursor: url(/static/style2/prev.cur),auto;
        left: 0;
        top: 0;
        float: left;
      }

      .tuwenpanel .flexsliders .navpanel.next {
        cursor: url(/static/style2/next.cur),auto;
        right: 0;
        top: 0;
        float: right;
      }

  .tuwenpanel .bxslider-box {
    background: #FBFBFB;
    padding: 15px;
    height: auto;
  }

  .tuwenpanel .bxslider {
  }

    .tuwenpanel .bxslider li {
      min-height: 100px;
      text-align: center;
    }

      .tuwenpanel .bxslider li img {
        max-width: 100%;
        height: auto;
        vertical-align: middle;
        clear: both;
        display: inline-block;
      }

  .tuwenpanel .bx-wrapper .bx-prev, .tuwenpanel .bx-wrapper .bx-next {
    -moz-transition: opacity 0.5s ease-in;
    -webkit-transition: opacity 0.5s ease-in;
    -o-transition: opacity 0.5s ease-in;
    transition: opacity 0.5s ease-in-out;
    filter: alpha(opacity=30);
    -moz-opacity: 0.3;
    -khtml-opacity: 0.3;
    opacity: 0.3;
  }

    .tuwenpanel .bx-wrapper .bx-prev:hover, .tuwenpanel .bx-wrapper .bx-next:hover {
      filter: alpha(opacity=100);
      -moz-opacity: 1;
      -khtml-opacity: 1;
      opacity: 1;
    }
/*锐仕方达猎头SOP流程 end zsl*/

/*猎问管理 start zsl*/
.r-tb-qm {
  margin-bottom: 10px;
}

  .r-tb-qm th, .r-tb-qm td {
    text-align: center;
  }

  .r-tb-qm .r-id {
    width: 43px;
  }

  .r-tb-qm .r-category {
    width: 102px;
  }

  .r-tb-qm .r-title {
    text-align: left;
  }

  .r-tb-qm tbody .r-title {
    cursor: pointer;
  }

  .r-tb-qm .r-info {
    width: 150px;
  }

  .r-tb-qm .r-con {
    text-align: left;
    white-space: normal;
    line-height: 1.8;
  }

  .r-tb-qm .r-detail {
    display: none;
  }

.r-tb.r-tb-qm > tbody > .r-tb-item:nth-of-type(odd) {
  background-color: transparent;
}

.r-tb.r-tb-qm > tbody > .r-tb-item.odd {
  background-color: #F5F9FC !important;
}

.r-tb.r-tb-qm > tbody > .r-tb-item:hover {
  background-color: #f5f5f5 !important;
}

.r-tb-am {
  margin-bottom: 10px;
}

  .r-tb-am th, .r-tb-am td {
    text-align: center;
  }

  .r-tb-am .r-id {
    width: 43px;
  }

  .r-tb-am .r-category {
    width: 102px;
  }

  .r-tb-am .r-title {
    text-align: left;
  }

  .r-tb-am tbody .r-title {
    cursor: pointer;
  }

  .r-tb-am .r-info {
    width: 100px;
  }

  .r-tb-am .r-con {
    text-align: left;
    white-space: normal;
    line-height: 1.8;
  }

  .r-tb-am .r-detail {
    display: none;
  }

.r-tb.r-tb-am > tbody > .r-tb-item:nth-of-type(odd) {
  background-color: transparent;
}

.r-tb.r-tb-am > tbody > .r-tb-item.odd {
  background-color: #F5F9FC !important;
}

.r-tb.r-tb-am > tbody > .r-tb-item:hover {
  background-color: #f5f5f5 !important;
}
/*猎问管理 end zsl*/
/*详情页公用*/
.r-v-tb {
  margin-bottom: -1px;
}

  .r-v-tb .r-v-tb-t {
    float: left;
    margin-right: 20px;
  }

  .r-v-tb .r_v_tb_con {
    margin-left: 90px;
  }

  .r-v-tb thead th {
    font-weight: normal !important;
  }

  .r-v-tb.r-v-tb-b > tbody > tr > td {
    border: 1px solid #ddd !important;
    padding: 8px !important;
    line-height: 1.8;
    white-space: normal;
  }
/*客户详情*/
.r-tb-vc-lxr .r_btn_bjwj {
  margin-right: 6px;
  border-radius: 4px !important;
}

.r-tb-vc-lxr .r_b_bjbox {
  padding: 0;
  border: 0 none;
  width: 0;
  height: 0;
  transition: all .2s;
  -moz-transition: all .2s;
  -webkit-transition: all .2s;
  -o-transition: all .2s;
  position: absolute;
  right: -248px;
  top: 29px;
  overflow: hidden;
  background-color: #fff;
  z-index: 10;
}

  .r-tb-vc-lxr .r_b_bjbox.on {
    width: 630px;
    height: auto;
  }

  .r-tb-vc-lxr .r_b_bjbox .r_bj_b {
    margin: 0;
    position: relative;
    border: 1px solid #217ebd;
    border-top-width: 3px;
  }

  .r-tb-vc-lxr .r_b_bjbox .r_bj_close {
    position: absolute;
    right: 8px;
    top: 6px;
    font-size: 24px;
    line-height: 100%;
    color: #333;
    font-weight: normal;
  }

    .r-tb-vc-lxr .r_b_bjbox .r_bj_close:hover {
      text-decoration: none;
      color: #E87E04;
    }

  .r-tb-vc-lxr .r_b_bjbox .r_bj_title {
    line-height: 36px;
    text-align: center;
  }

.r-tb-vc-rcd {
  margin-bottom: 10px;
}

  .r-tb-vc-rcd th, .r-tb-vc-rcd td {
    text-align: center;
  }

  .r-tb-vc-rcd .r-name {
    width: 82px;
  }

  .r-tb-vc-rcd .r-lt {
    width: 132px;
  }

  .r-tb-vc-rcd .r-jn {
  }

  .r-tb-vc-rcd .r-info {
    width: 62px;
  }

.r-tb-vc-lxr {
}

  .r-tb-vc-lxr .rnss-callphone {
    margin-left: 0;
    margin-right: 3px;
  }

  .r-tb-vc-lxr th, .r-tb-vc-lxr td {
    padding-left: 3px !important;
    padding-right: 3px !important;
  }

  .r-tb-vc-lxr .r-lxr { /*width:73px;*/
    width: 95px;
    text-align: center;
  }

  .r-tb-vc-lxr .r-zw {
    width: 73px;
  }

  .r-tb-vc-lxr .r-sj {
    width: 148px;
  }

    .r-tb-vc-lxr .r-sj > b {
      margin-right: 3px;
    }

  .r-tb-vc-lxr .r-zj {
    width: 110px;
  }

  .r-tb-vc-lxr .r-email {
    width: 120px;
  }

  .r-tb-vc-lxr .r-qq {
    width: 130px;
  }

  .r-tb-vc-lxr .r-wx {
    width: 86px;
  }

  .r-tb-vc-lxr .r-qx {
    width: 110px;
  }

    .r-tb-vc-lxr .r-qx .sethasjobs {
      float: right;
    }

  .r-tb-vc-lxr .r-info {
    overflow: visible;
    position: relative;
    width: 66px;
    text-align: center;
  }

.r-vc .baomiinfopanel {
  margin: 10px 0;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
}

  .r-vc .baomiinfopanel img {
    float: none;
    display: inline-block;
    vertical-align: middle;
    width: 35px;
    height: 35px;
  }

  .r-vc .baomiinfopanel span {
    display: inline-block;
    vertical-align: middle;
    float: none;
  }

  .r-vc .baomiinfopanel label {
    float: none;
  }

  .r-vc .baomiinfopanel .np-qq {
    display: inline-block !important;
    vertical-align: middle;
  }

    .r-vc .baomiinfopanel .np-qq .icon-85 {
      margin-top: 0;
    }

  .r-vc .baomiinfopanel .np-txt-1 {
    color: #ff6600;
  }

  .r-vc .baomiinfopanel .np-txt-2 {
    color: #333;
  }

  .r-vc .baomiinfopanel .np-txt-3 {
    color: #ff6600;
  }

.r-tb-vc-qy th, .r-tb-vc-qy td {
  text-align: center;
}

.r-tb-vc-td {
}

  .r-tb-vc-td th, .r-tb-vc-td td {
    text-align: left;
  }

  .r-tb-vc-td .r-name {
    width: 73px;
    text-align: center;
  }

  .r-tb-vc-td .r-role {
    width: 83px;
    text-align: center;
  }

.r-tb-vc-memos {
  margin-bottom: 10px;
}

  .r-tb-vc-memos th, .r-tb-vc-memos td {
    text-align: center;
  }

  .r-tb-vc-memos .r-staff {
    width: 112px;
  }

  .r-tb-vc-memos .r-con {
    text-align: left;
    cursor: pointer;
  }

  .r-tb-vc-memos .r-time {
    width: 150px;
  }

.r-tb-vc-files {
}

  .r-tb-vc-files th, .r-tb-vc-files td {
    text-align: center;
  }

  .r-tb-vc-files .r-staff {
    width: 73px;
  }

  .r-tb-vc-files .r-filename {
    text-align: left;
  }

  .r-tb-vc-files .r-time {
    width: 105px;
  }

.r-vc .r-th-information {
  line-height: 100%;
  padding: 0 0 10px;
}

.r-vc .tonghualog-box .item {
  float: left;
  overflow: hidden;
  text-align: center;
  min-height: 10px;
  height: 34px;
}

  .r-vc .tonghualog-box .item.item-huchuuser {
    width: 95px;
  }
  /*.vr .tonghualog-box .item.item-huchunum{width:85px;}*/
  .r-vc .tonghualog-box .item.item-company {
    width: 110px;
  }

  .r-vc .tonghualog-box .item.item-touser {
    width: 85px;
  }

  .r-vc .tonghualog-box .item.item-tousernum {
    width: 125px;
  }

  .r-vc .tonghualog-box .item.item-huchutime {
    width: 175px;
  }

  .r-vc .tonghualog-box .item.item-time {
    width: 120px;
  }

  .r-vc .tonghualog-box .item.item-status {
    width: 60px;
  }

  .r-vc .tonghualog-box .item.item-download {
    width: 60px;
  }

  .r-vc .tonghualog-box .item.item-luyin {
    width: 40px;
  }

.r-vc .tonghualog-box .luyinbtn-box.selected {
  height: 34px;
}

.r-vc .tonghualog-box .list-item-3 .calllogsitem:hover {
  background-color: #f5f5f5;
}

.r-vc-viptip {
  width: 100%;
  font-size: 14px;
}

  .r-vc-viptip table {
    table-layout: fixed;
    width: 100%;
  }

    .r-vc-viptip table th, .r-vc-viptip table td {
      padding: 5px;
      text-align: center;
    }

    .r-vc-viptip table, .r-vc-viptip table td, .r-vc-viptip table th {
      border-collapse: collapse;
      border-spacing: 0;
    }

  .r-vc-viptip div {
    display: block;
    margin: 15px auto 0;
    line-height: 22px;
  }

.email-box .line {
  float: left;
}

.r-vc .assessment-performance-status-box {
  float: none;
}

.r-vc .qianyue-performance-tongguo {
  color: #1BBC9B;
}

.selTeam .icon {
  display: inline-block;
  width: 16px !important;
  height: 16px !important;
  background-image: url(/css/images/risfondIcon.gif) !important;
  background-repeat: no-repeat !important;
  vertical-align: middle;
}

.selTeam .icon-135 {
  background-position: -64px -224px;
}

.leaderId .icon {
  display: inline-block;
  width: 16px !important;
  height: 16px !important;
  background-image: url(/css/images/risfondIcon.gif) !important;
  background-repeat: no-repeat !important;
  vertical-align: middle;
}

.leaderId .icon-135 {
  background-position: -64px -224px;
}

.r-tb-vc-sg {
  margin-bottom: 0;
}

  .r-tb-vc-sg thead th {
    font-weight: normal !important;
  }

  .r-tb-vc-sg > tbody > tr > td {
    border: 1px solid transparent !important;
  }

  .r-tb-vc-sg th, .r-tb-vr-jc td {
    text-align: left;
  }

  .r-tb-vc-sg .r-tt {
    width: 104px;
    text-align: center;
  }

  .r-tb-vc-sg .r-time {
    width: 106px;
  }

  .r-tb-vc-sg .r-rx {
    width: 146px;
  }

  .r-tb-vc-sg .r-tjr {
    width: 146px;
  }

  .r-tb-vc-sg thead .r-tt {
    background-color: #2BA0E3;
    color: #fff;
  }

  .r-tb-vc-sg .r-btn {
    background-color: #EEF3F7;
    width: 46px;
    cursor: pointer;
    text-align: center;
  }

  .r-tb-vc-sg tbody .r-btn {
    background: none;
  }

.r-vc .condition {
  overflow: hidden;
}

  .r-vc .condition .r-vc-p {
    margin-bottom: 10px;
  }

.r-tb-vc-fk {
  margin-bottom: 0;
}

  .r-tb-vc-fk thead th {
    font-weight: normal !important;
  }

  .r-tb-vc-fk > tbody > tr > td {
    border: 1px solid transparent !important;
  }

  .r-tb-vc-fk th, .r-tb-vr-jc td {
    text-align: left;
  }

  .r-tb-vc-fk .r-tt {
    width: 104px;
    text-align: center;
  }

  .r-tb-vc-fk .r-time {
    width: 106px;
  }

  .r-tb-vc-fk .r-gw {
    width: 146px;
  }

  .r-tb-vc-fk .r-staff {
    width: 146px;
  }

  .r-tb-vc-fk .r-fp {
    width: 106px;
  }

  .r-tb-vc-fk thead .r-tt {
    background-color: #2BA0E3;
    color: #fff;
  }

  .r-tb-vc-fk .r-btn {
    background-color: #EEF3F7;
    width: 46px;
    cursor: pointer;
    text-align: center;
  }

  .r-tb-vc-fk tbody .r-btn {
    background: none;
  }

.r-vc .tabbable-line > .nav-tabs > li > a {
  padding-left: 6px;
  padding-right: 6px;
}

.r-vc .dashboard-stat2 {
  margin-bottom: 10px;
}
/*【客户管理-客户信息】合作职位情况 推荐详情 start*/
.jobcandidatebyjobpanel {
  display: block;
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 240px;
}

  .jobcandidatebyjobpanel .item-name-2 {
    padding: 0;
  }

    .jobcandidatebyjobpanel .item-name-2 span {
      overflow: visible;
    }

  .jobcandidatebyjobpanel .list-item-3 {
    display: block;
    margin: 0;
    padding: 0;
    width: 100%;
  }

    .jobcandidatebyjobpanel .list-item-3 li {
      margin-top: 1px;
      padding: 0;
      line-height: 30px;
      font-size: 12px;
      width: 100%;
    }

    .jobcandidatebyjobpanel .list-item-3 .summary.list-con-1 {
      display: block;
      margin: 0;
      padding: 0;
      width: 100%;
    }

  .jobcandidatebyjobpanel .item {
    text-align: left;
  }

  .jobcandidatebyjobpanel .item-1 {
    width: 52px;
    text-align: center;
  }

  .jobcandidatebyjobpanel .item-2 {
    width: 62px;
    text-align: center;
    margin-right: 5px;
  }

  .jobcandidatebyjobpanel .item-3 {
    width: 135px;
    text-align: left;
  }

  .jobcandidatebyjobpanel .item-4 {
    width: 125px;
    padding-left: 5px;
  }

  .jobcandidatebyjobpanel .item-5 {
    width: 90px;
  }

  .jobcandidatebyjobpanel .item-6 {
    width: 75px;
    text-align: center;
    margin: 0 5px;
  }

  .jobcandidatebyjobpanel .item-7 {
    width: 70px;
    text-align: center;
  }

  .jobcandidatebyjobpanel .item-11 {
    width: 58px;
    padding: 0;
    word-break: break-all;
    word-wrap: break-word;
    text-align: center;
  }

    .jobcandidatebyjobpanel .item-11 .item.take-notes {
      text-align: center;
    }

.list-item-3 .take-notes {
  cursor: pointer;
  margin-left: 15px;
  margin-right: 11px;
  width: 30px;
  text-align: center;
  margin-top: 4px;
  border-top: 1px solid #bfbfbf;
  border-left: 1px solid #eeeeee;
  border-right: 1px solid #eeeeee;
  height: 18px;
  line-height: 18px;
  background: #FFF;
}

  .list-item-3 .take-notes strong {
    color: #FF0000;
    font-weight: bold;
  }

/*10.14BY PZJ*/

/*支出申请 zsl start*/
.r_Layout_eea {
}

  .r_Layout_eea .control-label {
    padding-left: 10px;
    padding-right: 10px;
    margin-top: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: clip;
    text-align: right;
  }

  .r_Layout_eea .r_x {
    color: #F20000;
    font-size: 18px;
    margin-left: 6px;
    vertical-align: middle;
    height: 20px;
    display: inline-block;
  }

  .r_Layout_eea .form-control {
    resize: none;
  }

  .r_Layout_eea .selTeam {
    margin: 0 !important;
  }

    .r_Layout_eea .selTeam * {
      box-sizing: content-box;
    }

    .r_Layout_eea .selTeam .r-submit {
      display: none;
    }

    .r_Layout_eea .selTeam .outbox-tip {
      margin-top: 4px !important;
    }

    .r_Layout_eea .selTeam .selector_input {
      padding-top: 8px !important
    }

    .r_Layout_eea .selTeam .addTeam {
      top: 11px;
      left: 156px;
    }

    .r_Layout_eea .selTeam .category-list {
      top: 30px;
    }

    .r_Layout_eea .selTeam .selector_outerbox {
      border: 1px solid #c2cad8;
      box-shadow: none;
      width: 100%;
      height: 34px !important;
    }

    .r_Layout_eea .selTeam .outbox-item-list {
      margin: 4px 0 0 4px;
    }

  .r_Layout_eea .leaderId {
    margin: 0 !important;
  }

    .r_Layout_eea .leaderId * {
      box-sizing: content-box;
    }

    .r_Layout_eea .leaderId .r-submit {
      display: none;
    }

    .r_Layout_eea .leaderId .outbox-tip {
      margin-top: 4px !important;
    }

    .r_Layout_eea .leaderId .selector_input {
      padding-top: 8px !important
    }

    .r_Layout_eea .leaderId .addTeam {
      top: 11px;
      left: 156px;
    }

    .r_Layout_eea .leaderId .category-list {
      top: 30px;
    }

    .r_Layout_eea .leaderId .selector_outerbox {
      border: 1px solid #c2cad8;
      box-shadow: none;
      width: 100%;
      height: 34px !important;
    }

    .r_Layout_eea .leaderId .outbox-item-list {
      margin: 4px 0 0 4px;
    }

.r_manage_ea_tabs {
  position: relative;
}

  .r_manage_ea_tabs > .btn {
    position: absolute;
    right: 0;
    top: 8px;
  }

  .r_manage_ea_tabs .r_tabs {
    height: 48px;
    margin-bottom: -1px;
  }

    .r_manage_ea_tabs .r_tabs > li {
      float: left;
    }

      .r_manage_ea_tabs .r_tabs > li > a {
        display: block;
        padding: 4px 24px;
        font-size: 16px;
        height: 48px;
        border: 1px solid transparent;
        line-height: 48px;
      }

        .r_manage_ea_tabs .r_tabs > li > a.action {
          color: #333;
          border: 1px solid #e7ecf1;
          background: #fff;
          border-bottom: 1px solid transparent;
        }

.r_manage_ea {
}

  .r_manage_ea .ea_hz {
    line-height: 1.8;
    margin: 8px 0;
  }

    .r_manage_ea .ea_hz .hz_label {
      display: inline-block;
      vertical-align: middle;
      margin-bottom: 0;
    }

    .r_manage_ea .ea_hz .hz_con {
      display: inline-block;
      vertical-align: middle;
      margin-right: 18px;
    }

      .r_manage_ea .ea_hz .hz_con > span {
        margin-right: 3px;
      }

.r-tb-ea {
  table-layout: fixed;
}

  .r-tb-ea th, .r-tb-ea td {
    text-align: center;
  }

  .r-tb-ea .r-id {
    width: 56px;
    text-align: left;
    overflow: hidden;
    position: relative;
  }

.app_showmore {
  border-radius: 50% !important;
  width: 15px;
  height: 14px;
  background-color: #a4b6ca;
  line-height: 13px;
  margin: 3px 5px 0 0;
  color: #FDFFFE;
  text-align: center;
  font-size: 13px;
  position: absolute;
  top: 8px;
  right: 4px;
}

.r-tb-ea .showmore_act {
  background-color: #31C3D2 !important;
}

.r-tb-ea .r-status {
  width: 65px;
}

.r-tb-ea .r-company {
}

.r-tb-ea .r-squser {
  width: 65px;
}

.r-tb-ea .r-sqtime {
}
/*.r-tb-ea .r-sqamout{width:108px;text-align:left;}*/
.r-tb-ea th.r-sqamout {
  width: 108px;
  text-align: center;
}

.r-tb-ea .r-sqamout {
  width: 108px;
  text-align: right;
}
/*.r-tb-ea .r-type{width:108px;}*/
.r-tb-ea .r-type {
  width: 85px;
}

.r-tb-ea .r-category {
  width: 80px;
}

.r-tb-ea .r-tjuser {
  width: 65px;
}

.r-tb-ea .r-reason {
  text-align: left;
  width: 120px;
}

.r-tb-ea .r-info {
  width: 58px;
  overflow: visible;
}

.r-tb-ea .dropdown-menu {
  min-width: 85px;
}

.r_ea_sx_status {
  margin-bottom: 4px;
}

  .r_ea_sx_status ul {
    border-bottom: 1px solid #DDE0E5;
  }

    .r_ea_sx_status ul > li {
      float: left;
    }

      .r_ea_sx_status ul > li.action {
      }

      .r_ea_sx_status ul > li > a {
        display: block;
        height: 46px;
        line-height: 46px;
        border-bottom: 2px solid transparent;
        padding: 0 16px;
        color: #666;
      }

      .r_ea_sx_status ul > li.action > a {
        border-bottom: 2px solid #3BC3D1;
        color: #333;
      }

  .r_ea_sx_status .badge {
    margin-left: 6px;
  }

.r-tb-eash {
}

  .r-tb-eash th, .r-tb-eash td {
    overflow: visible;
    white-space: normal;
    text-overflow: initial;
  }

  .r-tb-eash thead {
    background-color: #F0F9FE
  }

    .r-tb-eash thead th {
      border: 1px solid #C4ECF8 !important;
      height: 46px;
      line-height: 46px;
      vertical-align: middle !important;
      text-align: center;
    }

  .r-tb-eash tbody td {
    border: 1px solid #C4ECF8 !important;
    height: 40px;
    text-align: left;
  }

  .r-tb-eash .r_wrap {
    white-space: normal;
  }

  .r-tb-eash .r_tl {
    text-align: left;
  }

  .r-tb-eash .r_title {
    display: inline-block;
    width: 122px;
    text-align: right;
  }

  .r-tb-eash .r_nl {
    height: 0;
    overflow: hidden;
  }

    .r-tb-eash .r_nl th {
      height: 0;
      padding: 0;
      border: 0 none !important;
    }

  .r-tb-eash .r_dlink {
    margin-right: 6px;
  }

.r_Layout_shea .control-label {
  text-align: right;
}

.r_Layout_shea .r_x {
  color: #F20000;
  font-size: 18px;
  margin-left: 6px;
  vertical-align: middle;
  height: 20px;
  display: inline-block;
}

.r_Layout_shea .r_info {
}

  .r_Layout_shea .r_info .btn {
    margin-right: 8px;
  }

.r_ea_print_box {
  margin-bottom: 16px;
}

  .r_ea_print_box .r_t {
    vertical-align: middle !important;
    text-align: center;
    font-size: 22px;
    position: relative;
    height: 50px;
    line-height: 50px;
  }

    .r_ea_print_box .r_t img {
      display: inline-block;
      vertical-align: middle;
      width: 190px;
      position: absolute;
      left: 0;
      top: 0;
    }

  .r_ea_print_box .r_title {
    display: inline-block;
    width: 112px;
    text-align: right;
    position: absolute;
    left: 0;
    top: 0;
    text-indent: 0;
    line-height: 40px;
  }

  .r_ea_print_box .r_title1 {
    width: auto;
    text-align: left;
    display: inline-block;
    margin-right: 5px;
  }

  .r_ea_print_box .r_con {
    margin-left: 108px;
    line-height: 24px;
    padding-top: 8px;
  }

  .r_ea_print_box .r_con1 {
    line-height: 24px;
    padding-top: 8px;
    display: inline-block;
  }

  .r_ea_print_box .r_box {
    position: relative;
  }

    .r_ea_print_box .r_box .r-tit {
      margin-right: 50px;
    }

  .r_ea_print_box .r_col {
    float: left;
    margin: 0;
    padding: 0;
    position: relative;
    min-height: 46px;
    line-height: 46px;
    padding-left: 6px;
  }

  .r_ea_print_box .r_col1 {
    width: 100%;
  }

  .r_ea_print_box .r_col2 {
    width: 50%;
  }

  .r_ea_print_box .r_col3 {
    width: 33.33333333%;
  }

  .r_ea_print_box .r_col6 {
    width: 66.66666667%;
  }

  .r_ea_print_box .r_border {
    border-left: 1px solid #333;
    border-top: 1px solid #333;
  }

  .r_ea_print_box .r_rbborder {
    border-right: 1px solid #333;
    border-bottom: 1px solid #333;
  }

  .r_ea_print_box .r_qz {
    margin-top: 22px;
    width: 100%;
    margin-bottom: 10px;
    float: right;
  }

    .r_ea_print_box .r_qz p {
      float: right;
      width: 168px;
    }

.r_fa_box {
  width: 350px;
}

  .r_fa_box .r_fa_link {
    line-height: 26px;
    float: left;
    padding: 3px 8px;
    width: 110px;
  }

  .r_fa_box .r_fa_p {
    clear: both;
    text-align: center;
  }

  .r_fa_box .r_fa_control {
    display: inline-block;
    vertical-align: middle;
    height: 30px;
    line-height: 30px;
  }

  .r_fa_box .r_fa_from {
    width: 96px;
    padding: 3px 6px;
    border: 1px solid #C0C7D7;
  }

  .r_fa_box .r_fa_c {
    padding: 0 6px;
    background-color: #eee;
    border-top: 1px solid #C0C7D7;
    border-bottom: 1px solid #C0C7D7;
  }

  .r_fa_box .r_fa_to {
    width: 96px;
    padding: 3px 6px;
    border: 1px solid #C0C7D7;
  }

  .r_fa_box .r_fa_sub {
    padding: 0 12px;
    background-color: #3398DC;
    color: #fff;
    border: 0 none;
  }

.other-con {
  display: inline;
  float: left;
  padding: 1px 10px;
  min-height: 20px;
  border: 1px solid #D0D0D0;
  width: 475px;
  background: #fff;
}
  /*集成上传提示列表样式*/
  .other-con li {
    position: relative;
    overflow: hidden;
    height: 25px;
    line-height: 25px;
    white-space: nowrap;
  }

    .other-con li span {
      display: inline;
      font-size: 12px;
      float: left;
      white-space: nowrap;
    }

  .other-con .attachment .r-icon {
    margin: 3px 4px 0 0;
  }

  .other-con li label {
    float: left;
  }

  .other-con li .other-con-name {
    max-width: 500px;
    overflow: hidden;
  }

  .other-con .point {
    color: #999999;
  }

  .other-con .icon {
    margin-top: 5px;
    margin-right: 5px;
  }

  .other-con .office-word {
    background: url(images/icon.gif) no-repeat;
  }

  .other-con .phone {
    background: url(images/icon.gif) no-repeat 0 -18px;
  }

  .other-con .qq {
    background: url(images/icon.gif) no-repeat 0 -36px;
  }

  .other-con .otherLine {
    background: url(images/icon.gif) no-repeat 0 -90px;
  }

  .other-con .telephone {
    background: url(images/icon.gif) no-repeat 0 -72px;
  }

  .other-con .msn {
    background: url(images/icon.gif) no-repeat 0 -54px;
  }

  .other-con .imgLoding .imgLoding-img {
    float: left;
    margin: 5px 0 0 5px;
  }

  .other-con .imgLoding .imgLoding-text {
    float: left;
  }

span.delete, .other-button li input {
  float: left;
  border: none;
  margin-top: 7px;
  margin-left: 5px;
  width: 12px !important;
  height: 11px;
  padding: 0;
  background: url(/css/images/error.gif) no-repeat;
  cursor: pointer;
}

.uploadBtn {
  float: left;
}

  .uploadBtn * {
    box-sizing: content-box;
  }

  .uploadBtn .button {
    border: none;
    padding: 0 8px;
    cursor: pointer;
    overflow: visible !important;
    background-image: url(/Css/images/risfondButton.gif) !important;
    background-repeat: repeat-x !important;
    background-position: 0 -252px;
    line-height: initial;
    height: 29px;
    width: 46px;
  }
/*支出申请 zsl end*/


/*支出申请  pzj*/
.r-tb-appdetails {
  display: none;
}

.r-tb-item .choise {
  text-align: left !important;
}

.application_box {
  width: 97%;
  text-align: initial;
  padding-left: 3%;
  position: relative;
}

  .application_box .apps-line {
    margin-bottom: 10px;
    padding-top: 5px;
  }

  .application_box .apps-line3 .has_left_mrg {
    margin-left: 20px;
  }

  .application_box strong {
    margin-right: 12px;
    color: #222;
    display: inline-block;
    width: 80px;
    text-align: right;
    vertical-align: top;
  }

  .application_box span {
    font-size: 14px;
    color: #333;
  }

  .application_box .App-Reason {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 206px;
    display: inline-block;
    vertical-align: top;
  }

  .application_box h3 {
    margin: 0;
    font-size: 16px;
  }

  .application_box .apps_line1 h3 {
    width: 600px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    float: left;
  }

  .application_box .p_reason {
    display: inline-block;
    width: 800px;
    white-space: normal;
  }

  .application_box .apps_line1 p span {
    color: #999;
  }

  .application_box .App-attachment a {
    display: inline-block;
    margin-right: 10px;
  }

  .application_box .app_result {
    border-top: 1px dashed #ccc;
    padding-top: 10px;
  }

  .application_box .application_box .app_result h3 {
    display: block;
    margin-bottom: 5px;
    width: 800px;
  }

  .application_box .app_result h3 label {
    display: inline;
    width: 75px;
    float: left;
    text-align: right;
    margin-right: 20px;
  }

  .application_box .app_result h3 p {
    width: 800px;
    white-space: normal;
    float: left;
  }

.app_result .apps_p_details {
  float: left;
  margin-top: 5px;
}

  .app_result .apps_p_details p {
    display: inline-block;
    width: 280px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

.app_resultBox .app_result label {
  margin-right: 20px;
  color: #222;
  display: inline-block;
  width: 75px;
  text-align: right;
  font-weight: bold;
}

.application_box .app_result {
  position: relative;
}

  .application_box .app_result .app_t {
    margin-right: 12px;
    color: #222;
    width: 75px;
    text-align: right;
    float: left;
  }

  .application_box .app_result .app_con {
    display: block;
    margin-left: 78px;
  }

  .application_box .app_result .app_reason {
    display: inline-block;
    width: 660px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .application_box .app_result .app_staff {
  }

  .application_box .app_result .app_status {
    color: #f00;
    display: block;
    padding-left: 33px;
    position: absolute;
    right: 0;
    top: 32px;
    font-size: 22px;
    line-height: 100%;
  }

  .application_box .app_result .app_time {
    color: #999;
    position: absolute;
    right: 0;
    top: 8px;
    width: 204px;
  }

/*官网管理 start zsl*/
.r_mws_b {
}

.r-tb-mws th, .r-tb-mws td {
  text-align: center;
}

.r-tb-mws .r_id {
  width: 43px;
}

.r-tb-mws .r_status {
  width: 73px;
}

.r-tb-mws .r_module {
  width: 108px;
}

.r-tb-mws .r_istop {
  width: 43px;
}

.r-tb-mws .r_title {
  text-align: left;
}

.r-tb-mws .r_url {
  text-align: left;
}

.r-tb-mws .r_mblink {
  text-align: left;
}

.r-tb-mws .r_time {
  width: 128px;
}

.r-tb-mws .r_info {
  width: 68px;
}
/*官网管理 end zsl*/

/*裁剪图片（旧） start zsl*/
.editPhoto {
  width: 594px;
  height: 420px;
}

  .editPhoto .fill-out-title {
    width: 584px;
  }

.editPhotoFun {
  padding: 15px 35px;
  background: #FFFFFF;
  width: 514px;
  margin: 0 5px;
  height: 350px;
}

.editPhoto-con {
  position: relative;
  float: left;
}

  .editPhoto-con .movePhoto {
    position: absolute;
    top: 60px;
    left: 60px;
    cursor: pointer;
    width: 174px;
    height: 176px;
  }

    .editPhoto-con .movePhoto img {
      float: right;
    }

  .editPhoto-con .makePhoto {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 54px;
    height: 22px;
    background: url(images/xuanZhuan.gif) no-repeat;
    cursor: pointer;
  }

  .editPhoto-con .spin-left,
  .editPhoto-con .spin-right {
    float: left;
    width: 27px;
    height: 22px;
  }

  .editPhoto-con .tuoFang {
    float: right;
    display: block;
    margin-top: -13px;
    background: url(/css/images/tuoFang.gif) no-repeat;
    width: 13px;
    height: 13px;
  }

.viewPhoto {
  float: left;
  margin-left: 20px;
  position: relative;
  width: 190px;
  height: 320px;
}

  .viewPhoto img {
    cursor: pointer;
  }

  .viewPhoto .photo126 {
    position: absolute;
    top: 0;
    left: 0;
  }

  .viewPhoto .photo92 {
    position: absolute;
    top: 143px;
    left: 0;
  }

  .viewPhoto .photo50 {
    position: absolute;
    top: 252px;
    left: 0;
  }
/*不显示预览框*/
.editPhotoFun.unview {
  width: 300px !important;
  height: 330px !important;
}

  .editPhotoFun.unview #savePhoto {
    float: left;
  }

.photoBtn {
  float: left;
  width: 100%;
  margin: 8px 0;
}

  .photoBtn .button-none {
    border: none;
    background: none;
    color: #21639c;
    margin-left: 10px;
  }

.fill-out-win {
  padding: 20px;
  background: #000;
  font-size: 14px;
  height: 47px;
  line-height: 47px;
  filter: alpha(opacity=40);
  opacity: .4;
  float: left;
  color: #FFFFFF;
}

  .fill-out-win .icon-fill-out {
    opacity: 1;
    float: left;
    margin-right: 20px;
  }

.editPhoto.editpagelogin-editphoto1 {
  width: 674px;
}

  .editPhoto.editpagelogin-editphoto1 .editPhotoFun {
    width: auto;
    padding: 15px 25px;
  }

  .editPhoto.editpagelogin-editphoto1 .fill-out-title {
    width: auto;
  }

.editPhoto.editpagelogin-editphoto2 {
  width: 674px;
}

  .editPhoto.editpagelogin-editphoto2 .editPhotoFun {
    width: auto;
    padding: 15px 25px;
  }

  .editPhoto.editpagelogin-editphoto2 .fill-out-title {
    width: auto;
  }

.editPhoto.editpagelogin-editphoto3 {
  width: 674px;
}

  .editPhoto.editpagelogin-editphoto3 .editPhotoFun {
    width: auto;
    padding: 15px 25px;
  }

  .editPhoto.editpagelogin-editphoto3 .fill-out-title {
    width: auto;
  }
/*头像上传*/ #editPhotoWrap {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.imgareaselect-outer {
  background-color: #000;
  -webkit-filter: alpha(opacity=40);
  -moz-filter: alpha(opacity=40);
  -o-filter: alpha(opacity=40);
  filter: alpha(opacity=40);
  -ms-opacity: .4;
  opacity: .4;
  position: absolute;
  z-index: 10;
}

#srcImgWrap {
  font-size: 0;
  position: relative;
  margin-bottom: 10px;
  overflow: hidden;
  float: left;
  margin-right: 40px;
}

.preImgWrap {
  border: 1px solid #ccc;
  margin-bottom: 20px;
  overflow: hidden;
}

.move-resize {
  position: absolute;
  cursor: se-resize;
  width: 13px;
  height: 13px;
  right: 0;
  bottom: 0;
  background: url(/css/images/tuoFang.gif) no-repeat;
}
/*裁剪图片（旧） end zsl*/

/*官网banner修改 start zsl*/
.r_eb_b {
}

  .r_eb_b .r_chkzd {
  }

    .r_eb_b .r_chkzd .checkbox {
      float: left;
      margin-right: 8px;
    }
/*官网banner修改 end zsl*/
/*系统公告 start zsl*/
.rnss-manage-notice {
}

.notice-list-wrap {
  position: relative;
  padding-top: 80px;
}

.notice-list-detail-big {
  display: inline-block;
  width: 240px;
  position: relative;
}

.listHtml_m {
  padding-top: 63px;
}

.notice-list-detail-big .notice-touxiang {
  width: 240px;
  height: 240px;
}

.notice-list-detail-big .notice-list-header-img {
  position: absolute;
  top: -80px;
  left: 0;
}

.notice-list-detail-midum .notice-year {
  font-size: 22px;
  width: 100%;
  text-align: center;
  color: #fff;
  top: -16px;
}

  .notice-list-detail-midum .notice-year .font {
    font-size: 30px;
  }

.notice-list-detail-midum .notice-name-w .level {
  font-size: 12px;
  padding: 1px 8px;
}

.listHtml_b .notice-name-w .level {
  font-size: 12px;
}

.notice-list-detail-small {
  display: inline-block;
  width: 100px;
  margin: 0 8px;
  position: relative;
  text-align: center;
  margin-bottom: 40px;
}

  .notice-list-detail-small .notice-touxiang {
    width: 90px;
    height: 90px;
  }

  .notice-list-detail-small .notice-list-header-img {
    width: 100%;
  }

  .notice-list-detail-small .notice-name-w {
    padding: 12px 0;
  }

.r-tb-mn {
}

  .r-tb-mn th, .r-tb-mn td {
    text-align: center;
  }

  .r-tb-mn .r-id {
    width: 43px;
  }

  .r-tb-mn .r-type {
    width: 108px;
    text-align: left;
  }

  .r-tb-mn .r-title {
    text-align: left;
  }

  .r-tb-mn .r-status {
    width: 73px;
  }

  .r-tb-mn .r-time {
    width: 148px;
  }

  .r-tb-mn .r-staff {
    width: 73px;
  }

  .r-tb-mn .r-info {
    width: 108px;
  }

.home_notice_mask {
  width: 100%;
  height: 100%;
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 9996;
  overflow: hidden;
  background: #000;
  filter: alpha(opacity=50);
  -moz-opacity: 0.5;
  -khtml-opacity: 0.5;
  opacity: 0.5;
}

.home_notice_box {
  width: 860px;
  margin: 0 auto;
  display: none;
  background-color: transparent;
  position: absolute;
  z-index: 9997;
  left: 50%;
  margin-left: -348px;
  top: 75px;
  font-size: 14px;
  font-family: 'Microsoft YaHei', '黑体';
  padding-bottom: 36px;
}

  .home_notice_box .hep_con_img1 {
    position: absolute;
    left: 0;
    top: 0;
    width: auto;
    height: 0;
    overflow: hidden;
    filter: alpha(opacity=100);
    -moz-opacity: 1;
    -khtml-opacity: 1;
    opacity: 1;
  }

  .home_notice_box .hep_con_img2 {
    position: absolute;
    right: -80px;
    top: 195px;
    width: auto;
    height: 0;
    overflow: hidden;
  }

  .home_notice_box .hep_con_info {
  }

    .home_notice_box .hep_con_info .d-pic-p {
      text-align: center;
      margin-top: -110px;
    }

      .home_notice_box .hep_con_info .d-pic-p img {
        width: 127px;
        height: 127px;
        border: 5px solid #ED5057;
        border-radius: 50% !important;
        overflow: hidden;
      }

    .home_notice_box .hep_con_info .titlepanel {
      padding: 8px 0;
      margin: 7px 0;
      position: relative;
      text-align: center;
      font-family: 'Arial Negreta', 'Arial';
      text-indent: 1em;
      font-size: 32px;
      font-weight: bold;
    }

      .home_notice_box .hep_con_info .titlepanel .d-b-line {
        position: absolute;
        width: 55px;
        height: 0;
        border-top: 2px solid #ddd;
        left: 50%;
        bottom: 0;
        margin-left: -27px;
      }

    .home_notice_box .hep_con_info .d-amount-p {
      text-align: center;
      font-size: 22px;
      font-weight: normal;
      text-indent: 1em;
    }

    .home_notice_box .hep_con_info .con-panel {
    }

      .home_notice_box .hep_con_info .con-panel .table-scrollable {
        width: 80%;
        margin: 10px auto !important;
        border-top: 0 none;
      }

        .home_notice_box .hep_con_info .con-panel .table-scrollable tbody > tr > td:first-child {
          text-align: right;
          min-width: 90px;
        }

  .home_notice_box .hn_page_btn {
    position: absolute;
    width: 40px;
    height: 80px;
    top: 50%;
    margin-top: -40px;
    left: 10px;
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    opacity: 0.5;
    transition: opacity .3s ease-in;
  }

    .home_notice_box .hn_page_btn:hover {
      -moz-opacity: 1;
      -khtml-opacity: 1;
      opacity: 1;
    }

    .home_notice_box .hn_page_btn.hn_next {
      left: auto;
      right: 10px;
      z-index:150;
    }

    .home_notice_box .hn_page_btn > img {
      max-width: 100%;
    }

    .home_notice_box .hn_page_btn.nogo:hover {
      -moz-opacity: 0.5;
      -khtml-opacity: 0.5;
      opacity: 0.5;
    }

  .home_notice_box .hn_pager {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    line-height: 36px;
    color: white;
    font-size: 24px;
    text-align: center
  }

    .home_notice_box .hn_pager > span {
      margin: 0 6px;
    }

.home_notice_panel {
  width: 696px;
  margin: 0 auto;
  background: #fff;
  border: 1px solid transparent;
  border-radius: 10px 10px 0 0 !important;
  position: relative;
  padding-top: 48px;
}

.home_notice_panel1 {
  padding-top: 210px;
  border: none
}

.notice-headerImg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  margin-top: -62px;
}

.home_notice_panel .hnp_close {
  font-size: 36px;
  color: #676767;
  position: absolute;
  right: 10px;
  top: 0;
  z-index: 10
}

  .home_notice_panel .hnp_close:hover {
    text-decoration: none
  }

.home_notice_panel .hnp_con {
  padding: 0 28px 0 30px;
  max-height: 600px;
  overflow: auto;
}

  .home_notice_panel .hnp_con.hnp_con_dsy {
    overflow: visible;
  }

.home_notice_panel .hnp_c_b {
  border: 1px solid transparent;
  word-wrap: break-word;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}

  .home_notice_panel .hnp_c_b.hcon {
    height: 230px;
  }

.home_notice_panel .hnp_title {
  padding: 0;
  margin: 0;
  position: relative;
  text-align: left;
  font-family: 'Arial Negreta', 'Arial';
  font-size: 28px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.home_notice_panel .hnp_title_date {
  font-size: 16px;
  margin-left: 5px;
  text-align: right;
}

.home_notice_panel .hnp_creater {
  margin-left: 5px;
}

.home_notice_panel .hnp_c {
  min-height: 150px;
  margin: 3px 0 6px;
  transition: height .5s;
  -moz-transition: height .5s;
  -webkit-transition: height .5s;
  -o-transition: height .5s;
}

  .home_notice_panel .hnp_c img {
    max-width: 100%;
  }

.home_notice_panel .hn_footer {
  position: relative;
  border-top: 1px solid #efefef;
  padding-bottom: 6px;
}

  .home_notice_panel .hn_footer .hn_Btn_Sub {
    border-radius: 4px !important
  }

.home_notice_panel .hn_Txt_Pl {
  width: 499px;
  margin: 10px 10px 0 48px;
  border: 1px solid #E3E3E3;
  border-radius: 4px !important;
  padding: 6px 8px;
  resize: none;
  height: 36px;
  -webkit-transition: height .5s ease-in-out;
  transition: height .5s ease-in-out;
}

  .home_notice_panel .hn_Txt_Pl.hn_Txt_Open {
    height: 116px;
  }

.home_notice_panel .hn_Btn_Sub {
  border: 1px solid #0D69BE;
  border-radius: 10px !important;
  padding: 6px 30px;
  position: absolute;
  bottom: 10px;
  right: 42px;
  background-color: #0D69BE;
  color: #fff;
  -webkit-transition: background-color .3s ease-in;
  transition: background-color .3s ease-in;
}

  .home_notice_panel .hn_Btn_Sub:hover {
    background-color: #0F589B
  }

.home_notice_panel .pl_box {
  position: relative;
  text-align: left;
  line-height: 36px;
  margin: 0 0 3px 0;
  z-index:1;
}

  .home_notice_panel .pl_box .pl_qw_box {
    text-align: center;
    line-height: 30px;
  }

@keyframes noticeblink {
  0% {
    opacity: 0;
  }

  25% {
    opacity: 1;
  }

  50% {
    opacity: 1;
  }

  75% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.home_notice_panel .pl_box .pl_qw {
  animation: noticeblink 2.2s linear 0s infinite;
  display: inline-block;
}

  .home_notice_panel .pl_box .pl_qw .pl_qw_img {
    vertical-align: middle;
    margin: 0 0 0 6px;
    width: 14px;
    height: 14px;
    transition: all .25s ease-in-out;
  }

  .home_notice_panel .pl_box .pl_qw.down .pl_qw_img {
    transform: rotate(180deg);
  }

  .home_notice_panel .pl_box .pl_qw .pl_qw_txt {
    display: inline-block;
    vertical-align: middle;
  }

.home_notice_panel .pl_box .pl_info {
  float: right;
  margin: 0;
  position: relative;
}

.home_notice_panel .pl_box .pl_info_b {
  position: absolute;
  right: 0;
  bottom: 0;
  height: 34px;
  line-height: 34px;
  background-color: #0D69BE;
  border-radius: 6px !important;
  width: 0;
  transition: width .2s ease-in-out;
  overflow: hidden;
}

.home_notice_panel .pl_box .pl_info:hover .pl_info_b {
  width: 178px;
}

.home_notice_panel .pl_box .pl_info_line {
  float: left;
  height: 18px;
  width: 0;
  border-left: 2px solid #0A5DAB;
  margin-top: 9px;
}

.home_notice_panel .pl_box .pl_info_btn {
  width: 88px;
  float: left;
  color: #fff;
  text-align: center;
}

  .home_notice_panel .pl_box .pl_info_btn > img {
    margin-right: 4px;
  }

.home_notice_panel .dz_box {
  position: relative;
  background: #F0F0F0;
  padding: 8px 0 8px 52px;
  border-bottom: 1px solid #ddd;
}

  .home_notice_panel .dz_box .dz_sl {
    position: absolute;
    left: 6px;
    top: 14px;
  }

    .home_notice_panel .dz_box .dz_sl > img {
      margin-right: 3px;
    }

  .home_notice_panel .dz_box .dz_list {
    overflow: hidden;
  }

  .home_notice_panel .dz_box .dz_item {
    float: left;
    margin: 0 6px 6px 0;
    width: 45px;
    height: 45px;
    position: relative;
  }

    .home_notice_panel .dz_box .dz_item.dz_item_r {
    }

    .home_notice_panel .dz_box .dz_item > img {
      max-width: 100%;
    }

  .home_notice_panel .dz_box .dz_con {
    display: none;
    position: absolute;
    left: 45px;
    top: 5px;
    padding: 3px 6px;
    background: #0D69BE;
    color: white;
    z-index: 9;
    width: 80px;
  }

  .home_notice_panel .dz_box .dz_item:hover .dz_con {
    display: block;
  }

  .home_notice_panel .dz_box .dz_con p {
    margin: 0;
    font-size: 12px;
  }

  .home_notice_panel .dz_box .dz_item.dz_item_r .dz_con {
    left: auto;
    right: 45px;
  }

.home_notice_panel .dz_nodata {
  line-height: 32px;
  text-align: left;
  color: #666;
  font-size: 16px;
}

.home_notice_panel .p_box {
  position: relative;
  background: #F0F0F0;
  padding: 8px 0 8px 52px;
  border-bottom: 1px solid #ddd;
}

  .home_notice_panel .p_box .p_sl {
    position: absolute;
    left: 6px;
    top: 14px;
  }

    .home_notice_panel .p_box .p_sl > img {
      margin-right: 3px;
    }

  .home_notice_panel .p_box .p_list {
    overflow: hidden;
  }

  .home_notice_panel .p_box .p_item {
    margin: 0 6px 6px 0;
    position: relative;
    border-bottom: 1px solid #DEDEDE;
    padding-left: 50px;
  }

    .home_notice_panel .p_box .p_item > img {
      position: absolute;
      left: 0;
      top: 0;
      width: 45px;
    }

  .home_notice_panel .p_box .p_con {
    padding: 3px 6px;
    position: relative;
  }

  .home_notice_panel .p_box .p_title {
    color: #086BBD;
  }

  .home_notice_panel .p_box .p_time {
    color: #CBCBCB;
    position: absolute;
    right: 0;
    top: 6px;
  }

  .home_notice_panel .p_box .p_content {
  }

.home_notice_panel .p_nodata {
  line-height: 32px;
  text-align: left;
  color: #666;
  font-size: 16px;
}

/*大鲨鱼特殊样式补救*/
.home_notice_panel .hnp_con_dsy .wrapperCot {
  max-height: 230px;
  overflow-y: auto;
  overflow-x: hidden;
}
/*业绩系统弹窗样式*/
.home_notice_panel_yj .hnp_con {
  width: calc(100% + 2px);
  background: url(/images/yjBg1.png) no-repeat;
  background-size: cover;
  margin: -50px 1px 0 -1px;
}
.home_notice_panel_yj_2 .hnp_con {
  background: url(/images/yjBg2.png) no-repeat;
  background-size: cover;
}
.home_notice_panel_yj .font-yellow-intense {
  color: #EBDA99 !important;
  font-size: 30px !important;
  margin-top: 192px !important;
  text-indent: 0 !important;
}
.home_notice_panel_yj .font-yellow-haze {
  color: #EBDA99 !important;
  text-indent: 0 !important;
}
.home_notice_panel_yj .pl_info_b {
  background: #EBDA99 !important;
}
.home_notice_panel_yj .pl_info_btn {
  color: #988025 !important;
}
.home_notice_panel_yj .pl_info_line {
  border-color: #C9B464 !important;
}
.home_notice_panel_yj .hnp_close {
  color: #fff !important;
}
.home_notice_panel_yj .hn_Btn_Sub {
  right: 30px !important;
}
.home_notice_panel_yj .hn_Txt_Pl {
  margin-left: 30px !important;
  width: 530px !important;
}
.home_notice_panel_yj .wrapperCot {
  max-height: 202px;
  overflow-y: auto;
  overflow-x: hidden;
}
.home_notice_panel_yj .pl_info img,
.home_notice_panel_yj .font-yellow-haze img {
  width: 24px;
}
.home_notice_panel_yj .pl_info_btn img{
  width:16px;
}
/*系统公告 end zsl*/
/* 创建内部客户提示 start zsl */
.home_notice_box.r_userlogin_box {
  margin: -186px 0 0 -348px;
  position: fixed;
  top: 50%;
}

.r_userlogin_box .home_notice_panel {
  border-radius: 10px !important;
  width: 536px;
}

.r_userlogin_box .rul_title {
  font-size: 26px;
  padding: 0;
  margin: 10px 0 30px;
  text-align: center;
}

.r_userlogin_box .rul_con {
  font-size: 14px;
  text-indent: 2em;
  line-height: 1.8;
}

.r_userlogin_box .rul_bbox {
  text-align: center;
}

  .r_userlogin_box .rul_bbox > a {
    display: inline-block;
    width: 182px;
    height: 40px;
    line-height: 40px;
    background: rgba(22, 155, 213, 1);
    color: #fff;
    border-radius: 6px !important;
    transition: background ease-in-out .2s;
  }

.notice-list-wrap {
  text-align: center;
}

.notice-name-w {
  text-align: center;
  padding-top: 20px;
  font-size: 16px;
  color: #333;
}

  .notice-name-w .level {
    display: inline-block;
    padding: 1px 8px;
    color: #fff;
    border-radius: 3px !important;
    background: #0D69BE;
    margin-left: 8px;
  }

.notice-touxiang {
  display: inline-block;
  border-radius: 50% !important;
}

.notice-year {
  position: absolute;
  top: -27px;
  left: 0;
  width: 100%;
  text-align: center;
}

.listHtml_b .notice-year {
  font-size: 28px;
  color: #fff;
}

.listHtml_b .font {
  font-size: 36px;
}

.notice-list-detail-midum {
  position: relative;
  display: inline-block;
  width: 302px;
}

  .notice-list-detail-midum .notice-list-header-img {
    width: 194px;
    height: 112px;
    position: absolute;
    top: -59px;
    left: 56px;
  }

  .notice-list-detail-midum .notice-touxiang {
    width: 190px;
    height: 190px;
  }

.listHtml_s {
  padding-top: 35px;
  text-align: left;
  max-height: 340px;
  overflow: hidden;
  overflow-y: scroll;
}

.notice-list-detail-small .notice-list-header-img {
  position: absolute;
  top: -33px;
  left: 0;
}

.listHtml_s .notice-year {
  font-size: 12px;
  top: -12px;
  color: #fff;
}

  .listHtml_s .notice-year .font {
    font-size: 16px;
  }

.notice-list-detail-small .notice-name-w {
  font-size: 16px;
}

.home_notice_panel .hnp_close_notice {
  top: 13px;
  right: 20px;
  cursor: pointer;
}

.notice-list-detail-small .notice-name-w .level {
  font-size: 12px;
  padding: 1px 8px;
}

.r_userlogin_box .rul_bbox > .rul_btn:hover,
.r_userlogin_box .rul_bbox > .rul_btn:active,
.r_userlogin_box .rul_bbox > .rul_btn:focus {
  text-decoration: none;
}

.r_userlogin_box .rul_bbox > .rul_btn:hover {
  background: rgba(22, 94, 125, 1);
}

.r_userlogin_box .rul_tip {
  margin: 10px 0 35px;
  text-align: center;
  font-size: 13px;
  color: #999999;
  text-align: center;
}
/* 创建内部客户提示 end zsl */

/*资产管理 start zsl*/
.r_ma_b {
}

  .r_ma_b .ma_f_zc {
    table-layout: fixed;
    min-width: 200px;
  }

    .r_ma_b .ma_f_zc .vfrom, .r_ma_b .ma_f_zc .vto {
      width: 100px;
    }

    .r_ma_b .ma_f_zc td {
      padding: 0 6px 6px;
    }

.r-tb-ma th, .r-tb-ma td {
  text-align: center;
}

.r-tb-ma .r-id {
  width: 43px;
}

.r-tb-ma .r-type {
  width: 83px;
}

.r-tb-ma .r-name {
  text-align: left;
}

.r-tb-ma .r-value {
  width: 118px;
}

.r-tb-ma .r-time {
  width: 86px;
}

.r-tb-ma .r-source {
  width: 86px;
}

.r-tb-ma .r-companyname {
  text-align: left;
}

.r-tb-ma .r-description {
  text-align: left;
}

.r-tb-ma .r-info {
  width: 70px;
  overflow: visible;
}

.r-tb-mat {
}

  .r-tb-mat th {
    text-align: center;
  }

  .r-tb-mat .r-t {
    width: 108px;
    background: #EAEAEA;
  }

  .r-tb-mat .r-zic-t {
  }
/*资产管理 end zsl*/
/*投资管理 start zsl*/
.r-tb-mim {
}

  .r-tb-mim th, .r-tb-mim td {
    text-align: center;
  }

  .r-tb-mim .r-id {
    width: 43px;
  }

  .r-tb-mim .r-atype {
    width: 83px;
  }

  .r-tb-mim .r-name {
    text-align: left;
  }

  .r-tb-mim .r-value {
    width: 108px;
  }

  .r-tb-mim .r-type {
    width: 83px;
  }

  .r-tb-mim .r-category {
    text-align: left;
  }

  .r-tb-mim .r-time {
    width: 83px;
  }

  .r-tb-mim .r-proname {
    text-align: left;
  }

  .r-tb-mim .r-prouser {
    width: 108px;
  }

  .r-tb-mim .r-explain {
    text-align: left;
  }

  .r-tb-mim .r-info {
    width: 70px;
    overflow: visible;
  }
/*投资管理 end zsl*/

/*客户详情-推荐面试入职offer PZJ*/
.tb-steps .r-referees {
  text-align: center;
  width: 80px
}

.tb-steps .r-position {
  text-align: center;
  width: 80px
}

.tb-steps .r-tel {
  width: 100px;
}

.tb-steps .r-steps {
  text-align: center;
}

.tb-steps .r-memos {
  width: 82px;
  text-align: center;
}

  .tb-steps .r-memos p {
    width: 20px;
    height: 20px;
    color: #fff;
    background-color: #4496E0;
    border-radius: 50% !important;
    margin: 0 auto;
  }

.tb-steps .r-detail {
  width: 60px;
  text-align: center;
}

.
/*业绩实况 start zsl*/
.r_pt_b {
}

.r_pt_b .r_range {
  position: relative;
}

.r_pt_b .r_sx_date {
  display: inline-block;
  width: auto;
}

.r_pt_b .page-toolbar {
  float: right;
  width: 440px;
}

  .r_pt_b .page-toolbar .btn {
    float: right;
    margin-left: 16px;
  }

.pt_menu {
  float: left;
}

  .pt_menu li {
    float: left;
  }

    .pt_menu li a {
      display: block;
      padding: 8px 26px;
      color: #333;
      background-color: white;
    }

      .pt_menu li a:hover {
        text-decoration: underline;
      }

    .pt_menu li.cur {
    }

      .pt_menu li.cur a {
        background-color: #5c9bd1;
        color: white;
      }

.r_tb_pt {
}

  .r_tb_pt thead th {
    color: white;
  }

  .r_tb_pt th, .r_tb_pt td {
    text-align: center;
  }

  .r_tb_pt tbody td { /*border-bottom:1px solid transparent!important;*/
  }

  .r_tb_pt tfoot {
    color: white
  }

  .r_tb_pt .r_jd_bl {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    background: #8ECDE9;
    z-index: 1;
    -moz-transition: width 0.3s ease-in-out; /* Firefox 4 */
    -webkit-transition: width 0.3s ease-in-out; /* Safari 和 Chrome */
    -o-transition: width 0.3s ease-in-out;
    transition: width 0.3s ease-in-out;
  }

.r_tb_pt1 {
}

  .r_tb_pt1 thead th {
    cursor: pointer;
    background: #5C9BD1;
  }

  .r_tb_pt1 tfoot td {
    background: #5C9BD1;
  }

  .r_tb_pt1 .r_pm {
    width: 53px;
  }

  .r_tb_pt1 .r_dq {
    width: 73px;
  }

  .r_tb_pt1 .r_jd {
    position: relative;
  }

  .r_tb_pt1 .r_jd_txt {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    text-align: center;
    line-height: 36px;
    z-index: 2;
    text-align: right;
    padding-right: 6px;
  }

  .r_tb_pt1 .r_yj {
    padding-right: 6px;
    text-align: right;
  }

  .r_tb_pt1 .r_rw {
    padding-right: 6px;
    text-align: right;
  }

.r_tb_pt2 {
}

  .r_tb_pt2 thead th {
    cursor: pointer;
    background: #4c87b9;
  }

  .r_tb_pt2 tfoot td {
    background: #5C9BD1;
  }

  .r_tb_pt2 .r_pm {
    width: 53px;
  }

  .r_tb_pt2 .r_dq {
    width: 73px;
  }

  .r_tb_pt2 .r_gs {
    width: 73px;
  }

  .r_tb_pt2 .r_vp {
    width: 63px;
  }

  .r_tb_pt2 .r_rw {
    text-align: right;
    padding-right: 6px;
  }

  .r_tb_pt2 .r_yj {
    text-align: right;
    padding-right: 6px;
  }

  .r_tb_pt2 .r_vpyj {
    text-align: right;
    padding-right: 6px;
  }

  .r_tb_pt2 .r_jd {
    position: relative;
  }

  .r_tb_pt2 .r_jd_txt {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    text-align: right;
    line-height: 36px;
    z-index: 2;
    padding-right: 6px;
  }

  .r_tb_pt2 .r_vpyjzb {
    position: relative;
    width: 114px;
  }

.r_tb_pt3 {
}

  .r_tb_pt3 thead th {
    cursor: pointer;
    background: #5C9BD1;
  }

  .r_tb_pt3 tfoot td {
    background: #5C9BD1;
  }

  .r_tb_pt3 .r_pm {
    width: 53px;
  }

  .r_tb_pt3 .r_dq {
    width: 73px;
  }

  .r_tb_pt3 .r_gs {
    width: 73px;
  }

  .r_tb_pt3 .r_vp {
    width: 63px;
  }

  .r_tb_pt3 .r_rw {
    text-align: right;
    padding-right: 6px;
  }

  .r_tb_pt3 .r_yj {
    text-align: right;
    padding-right: 6px;
  }

  .r_tb_pt3 .r_jd {
    text-align: right;
    padding-right: 6px;
  }

  .r_tb_pt3 .r_vpyj {
    text-align: right;
    padding-right: 6px;
  }

  .r_tb_pt3 .r_vpyjzb {
    text-align: right;
    padding-right: 6px;
  }

  .r_tb_pt3 .r_jd {
    position: relative;
  }

  .r_tb_pt3 .r_jd_txt {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    text-align: center;
    line-height: 36px;
    z-index: 2;
    text-align: right;
    padding-right: 6px;
  }

  .r_tb_pt3 .r_vpyjzb {
    position: relative;
    width: 114px;
  }

.r_sx_list {
}

  .r_sx_list li {
    float: left;
  }

    .r_sx_list li > a {
      color: #333;
      display: block;
      padding: 6px 8px;
    }

      .r_sx_list li > a:hover {
        text-decoration: underline;
      }

    .r_sx_list li.cur > a {
      color: #4C87B9;
    }

.r_la_dqlist {
  margin: 10px 0;
}

  .r_la_dqlist li {
    float: left;
    padding: 5px 16px;
    color: #333;
    border: 1px solid #E4E4E4;
    margin-right: -1px;
  }

    .r_la_dqlist li.cur {
      background: #5C9BD1;
      color: white
    }
/*业绩实况 end zsl*/
/*认购管理 start zsl*/
.r-tb-rg {
}

  .r-tb-rg th, .r-tb-rg td {
    text-align: center;
  }

  .r-tb-rg .r-id {
    width: 43px;
  }

  .r-tb-rg .r-code {
    width: 143px;
  }

  .r-tb-rg .r-companyId {
    width: 73px;
  }

  .r-tb-rg .r-position {
    width: 73px;
  }

  .r-tb-rg .r-staffName {
    width: 73px;
  }

  .r-tb-rg .r-buytime {
    width: 90px;
  }

  .r-tb-rg .r-source {
    width: 73px;
  }

  .r-tb-rg .r-operation {
    width: 63px;
  }

.r_mrg_e {
}

  .r_mrg_e .other-con {
    padding: 4px 10px;
  }

  .r_mrg_e .uploadBtn .button {
    background: #B8D8F5 !important;
    height: 33px;
    border: 1px solid #AFCEE9;
    border-left: 0 none;
    cursor: pointer;
    -moz-transition: background-color 0.2s ease;
    -webkit-transition: background-color 0.2s ease;
    -o-transition: background-color 0.2s ease;
    transition: background-color 0.2s ease;
  }

  .r_mrg_e .uploadBtn:hover .button {
    background: #8DB4D8 !important;
  }

.r_e_bs {
}

  .r_e_bs .other-con {
    padding: 4px 10px;
  }

  .r_e_bs .uploadBtn .button {
    background: #B8D8F5 !important;
    height: 33px;
    border: 1px solid #AFCEE9;
    border-left: 0 none;
    cursor: pointer;
    -moz-transition: background-color 0.2s ease;
    -webkit-transition: background-color 0.2s ease;
    -o-transition: background-color 0.2s ease;
    transition: background-color 0.2s ease;
  }

  .r_e_bs .uploadBtn:hover .button {
    background: #8DB4D8 !important;
  }
/*认购管理 end zsl*/
/*大区业绩排名 start zsl*/
.r-tb-apr {
}

  .r-tb-apr th, .r-tb-apr td {
    text-align: center;
  }

  .r-tb-apr .r-name { /*width:73px;*/
  }

  .r-tb-apr .r-sort {
    width: 73px;
  }

  .r-tb-apr tbody .r-amount {
    text-align: right;
  }
/*大区业绩排名 end zsl*/
/*大区签约公共客户池 start zsl*/
.r-tb-apc {
}

  .r-tb-apc th, .r-tb-apc td {
    text-align: center;
  }

  .r-tb-apc .r-source {
    width: 43px;
  }

  .r-tb-apc .r-sf {
    width: 93px;
  }

  .r-tb-apc .r-fuwu {
    width: 93px;
  }

  .r-tb-apc .r-scope {
    width: 93px;
  }

  .r-tb-apc .r-status {
    width: 43px;
  }

  .r-tb-apc .r-staff {
    width: 73px;
  }

  .r-tb-apc .r-cn {
    width: 93px;
  }

  .r-tb-apc .r-pt {
    width: 93px;
  }

  .r-tb-apc .r-memo {
    width: 43px;
  }

  .r-tb-apc .r-reason {
    width: 83px;
  }

  .r-tb-apc .r-info {
    width: 67px;
  }

.r-tb-apca {
}

  .r-tb-apca th, .r-tb-apca td {
    text-align: center;
  }

  .r-tb-apca .r-selected {
    width: 43px;
  }

  .r-tb-apca .r-id {
    width: 63px;
  }

  .r-tb-apca .r-source {
    width: 43px;
  }

  .r-tb-apca .r-cc {
    width: 73px;
  }

  .r-tb-apca .r-status {
    width: 43px;
  }

  .r-tb-apca .r-cms {
    width: 73px;
  }

  .r-tb-apca .r-sn {
    width: 73px;
  }

  .r-tb-apca .r-at {
    width: 93px;
  }

  .r-tb-apca .r-memo {
    width: 43px;
  }

  .r-tb-apca .r-check {
    width: 43px;
  }

  .r-tb-apca .r-csn {
    width: 73px;
  }

  .r-tb-apca .r-cdt {
    width: 93px;
  }

  .r-tb-apca .r-info {
    width: 108px;
  }
/*大区签约公共客户池 end zsl*/
/*邮件签名生成 start zsl*/
.mc_PreviewBox {
  font-size: 14px;
}

  .mc_PreviewBox .modal-dialog {
    width: 698px;
  }

  .mc_PreviewBox .modal-body {
    padding: 0;
    overflow: hidden;
  }

  .mc_PreviewBox .mc_view {
    color: #595757;
    height: 259px;
    overflow: hidden;
    line-height: 1.6;
    background: #fff;
    font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  }
    .mc_PreviewBox .mc_view .content {
      padding: 26px 22px 26px 30px;
    }

    .mc_PreviewBox .mc_logo {
    }

    .mc_PreviewBox .mc_logo img {
      width: 413px;
      height:26px;
    }

  .mc_PreviewBox .mc_title {
    font-size: 16px;
    font-weight: bold;
    color: #383838;
    margin: 25px 0 7px;
  }

  .mc_PreviewBox .mc_line {
    line-height: 1.6;
  }

    .mc_PreviewBox .mc_line label {
      margin: 0;
    }

    .mc_PreviewBox .mc_line span {
      margin-right: 8px;
    }

  .mc_PreviewBox .mc_erweima {
    position: absolute;
    bottom: 77px;
    right: 30px;
    width: 100px;
    height: 100px;
  }

  .mc_PreviewBox .mc_tip {
    text-align: center;
    line-height: 36px;
  }
/*邮件签名生成 end zsl*/
/*客户修改日志 start zsl*/
.r_client_editlog {
}

.r_cel_item {
  position: relative;
  padding: 8px 0;
}

  .r_cel_item .r_sphoto {
    width: 92px;
    height: 92px;
    float: left;
  }

  .r_cel_item .r_t_b {
    float: left;
    width: 120px;
    text-align: center;
    margin: 0 8px;
    padding-top: 4px;
  }

  .r_cel_item .r_time {
    margin-bottom: .8em;
  }

  .r_cel_item .r_name {
    line-height: 1.8;
  }

  .r_cel_item .r_c_b {
    float: left;
    width: 741px;
    background-color: #f5f6fa;
    padding: 4px;
  }

  .r_cel_item .r_b2_item {
    margin-bottom: 8px;
  }

    .r_cel_item .r_b2_item .r_title {
      font-size: 14px;
      font-weight: bold;
      color: #666;
    }

    .r_cel_item .r_b2_item .r_oldV {
      float: left;
      width: 49%;
      text-decoration: line-through;
    }

      .r_cel_item .r_b2_item .r_oldV > .r_line > label {
        text-decoration: line-through;
      }

    .r_cel_item .r_b2_item .r_newV {
      float: right;
      width: 49%;
    }

    .r_cel_item .r_b2_item hr {
      border-color: #ddd;
    }
/*客户修改日志 end zsl*/
/*积分商城-发布 start zsl*/
.r_sp_b {
}

  .r_sp_b .r_xx {
    font-size: 22px;
    color: #f00;
    vertical-align: middle;
    margin-right: 8px;
  }

  .r_sp_b .form-horizontal .radio, .r_sp_b .form-horizontal .checkbox {
    padding-top: 1px !important;
  }

  .r_sp_b .form-horizontal .control-label {
    padding-top: 4px;
  }

  .r_sp_b .cm_pd {
    padding-top: 7px;
  }

  .r_sp_b .selTeam {
    margin: 0 !important;
    width: 100% !important
  }

    .r_sp_b .selTeam * {
      box-sizing: content-box;
    }

    .r_sp_b .selTeam .r-submit {
      display: none;
    }

    .r_sp_b .selTeam .outbox-tip {
      margin-top: 4px !important;
    }

    .r_sp_b .selTeam .selector_input {
      padding-top: 8px !important
    }

    .r_sp_b .selTeam .addTeam {
      top: 11px;
      left: 156px;
    }

    .r_sp_b .selTeam .category-list {
      top: 30px;
    }

    .r_sp_b .selTeam .selector_outerbox {
      border: 1px solid #c2cad8;
      box-shadow: none;
      width: 100%;
      height: 34px !important;
    }

    .r_sp_b .selTeam .outbox-item-list {
      margin: 4px 0 0 4px;
    }

.select_popup {
  padding: 8px;
  border: 1px solid #ccc;
  cursor: pointer;
  min-height: 38px;
}

  .select_popup .sp_list {
  }

    .select_popup .sp_list > li {
      float: left;
      margin: 0 12px 0 0;
      line-height: 1.4;
    }

.r_sp_b .sp_pic_i {
  display: inline-block;
  margin: 0 12px 12px 0;
}

  .r_sp_b .sp_pic_i > img {
    width: 125px;
  }

  .r_sp_b .sp_pic_i .sp_l {
    margin: 6px 0;
  }

    .r_sp_b .sp_pic_i .sp_l > a {
      float: right;
    }

  .r_sp_b .sp_pic_i .sp_lb {
    display: inline-block;
  }

    .r_sp_b .sp_pic_i .sp_lb > input {
    }

    .r_sp_b .sp_pic_i .sp_lb > label {
    }

.r_sp_b .sp_ips {
  display: inline-block;
}
/*积分商城-发布 end zsl*/


/*简历评价 start */

.resume_icon_tousu {
  margin-left: 5px;
  width: 25px;
  height: 25px;
  text-decoration: none;
  margin-top: 4px;
  /*background: url(/images/tousu.png) no-repeat;*/
  display: inline-block;
}

  .resume_icon_tousu.default {
    background: url(/images/tousu.png) no-repeat;
    background-size: cover;
  }

  .resume_icon_tousu.default_active {
    background: url(/images/tousu-active.png) no-repeat;
    background-size: cover;
  }

  .resume_icon_tousu.tousu_bubble {
    background: url(/images/tousu-bubble.png) no-repeat;
    background-size: cover;
  }

  .resume_icon_tousu.tousu_bubble_active {
    background: url(/images/tousu-bubble-active.png) no-repeat;
    background-size: cover;
  }

  .resume_icon_tousu.tousu_shield {
    background: url(/images/tousu-shield.png) no-repeat;
    background-size: cover;
  }

  .resume_icon_tousu.tousu_shield_active {
    background: url(/images/tousu-shield-active.png) no-repeat;
    background-size: cover;
  }

  .resume_icon_tousu.police {
    background: url(/images/police.png) no-repeat;
    background-size: cover;
  }

  .resume_icon_tousu.police_user {
    background: url(/images/police-user.png) no-repeat;
    background-size: cover;
  }

  .resume_icon_tousu.police_station {
    background: url(/images/police-station.png) no-repeat;
    background-size: cover;
  }

.resume_icon_set_good {
  /*margin-left: 5px;*/
  width: 40px;
  height: 40px;
  text-decoration: none;
  background: url(/images/set-good.png) no-repeat;
  background-size: 40px;
  display: inline-block;
}

.resume_icon_set_good_active {
  /*margin-left: 5px;*/
  width: 40px;
  height: 40px;
  text-decoration: none;
  background: url(/images/set-good-active.png) no-repeat;
  background-size: 40px;
  display: inline-block;
}
/*简历评价 end */


/*简历详情修改增加 Start*/
.jibenxinxi_bianji {
  display: flex;
  float: right;
  margin: 10px 0;
  cursor: pointer;
}

  .jibenxinxi_bianji b {
    background: url(/images/bianji_11.png) no-repeat;
    background-size: 16px;
    width: 16px;
    height: 16px;
    margin: 2px 0;
  }

  .jibenxinxi_bianji i {
    background: url(/images/xinzeng_22.png) no-repeat;
    background-size: 16px;
    width: 16px;
    height: 16px;
    margin: 2px 0;
  }

  .jibenxinxi_bianji span {
    height: 20px;
    font-size: 14px;
    font-weight: 400;
    color: #026FB2;
    margin-left: 6px;
    line-height: 20px;
  }

.gongzuojingli b {
  cursor: pointer;
  display: inline-block;
  vertical-align: middle;
  margin: 0 6px 0 0;
}

.gongzuojingli .b_edit {
  background: url(/images/bianji_11.png) no-repeat;
  width: 16px;
  height: 16px;
  background-size: 16px;
}

.gongzuojingli .b_del {
  background: url(/images/shanchu_33.png) no-repeat;
  width: 17px;
  height: 17px;
  background-size: 17px;
  margin-bottom: 1px;
}

.margin_6 {
  margin-right: 7px;
  color: #539dcb !important;
}

[class^=icon-] {
  line-height: 13px
}
/*body.page-boxed*/
.mask {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  background-color: rgba(0,0,0,0.2);
  z-index: -2
}

#editPhotoWrap {
  left: 0 !important;
  right: 0 !important;
  margin: auto !important;
}
/*.big_show {
  position: absolute !important;
  left: 0 !important;
  background: #fff !important;
  right: 0 !important;
  top: 50px !important;
  margin: auto !important;
  transform:none !important;
}

.mask .open_div {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  width: 800px;
  height: 564px;
  background: #fff;
}*/

.mask .open_div_new {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  width: 800px;
  height: 564px;
  background: #fff;
}

.mask .open_div {
  position: fixed;
  top: calc(50vh + 38px);
  left: 50%;
  transform: translate(-50%,-50%);
  width: 800px;
  background: #fff;
}
/*.mask .open_div {
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  transform: none;
  width: 800px;
  height: 564px;
  background: #fff;
}*/
.div_row {
  display: flex;
  margin-bottom: 16px;
}

.div_col_right {
  margin-left: 38px;
}

.mask .close {
  background-image: url(/images/guanbi_2.png) !important;
  background-size: 12px;
  opacity: 1;
  width: 12px;
  height: 12px;
  margin: 3.5px 0;
}

.open_div_title {
  margin: 15px 20px;
}

  .open_div_title div {
    font-size: 14px;
    font-weight: bold;
  }

.open_div_border {
  width: 100%;
  height: 1px;
  background: #EEEEEE;
}

.open_div_body {
  padding: 20px 0 0 20px;
  max-height: 403px;
  overflow: hidden;
  overflow-y: scroll;
}

.open_div_left {
}

.open_div_right {
  position: absolute;
  top: 77px;
  width: 100px;
  right: 22px;
}

.lable_photo {
  width: 100px;
  line-height: 100px;
  height: 100px;
  text-align: center;
  margin-bottom: 16px;
  border-radius: 50% !important;
}

.photo_but {
  width: 82px;
  height: 26px;
  border-radius: 2px !important;
  border: 1px solid #026FB2;
  line-height: 26px;
  text-align: center;
  font-size: 13px;
  color: #026FB2;
  margin: 0 auto;
}

.lable_name::before {
  content: "*";
  color: #FE1818;
  margin-right: 8px;
  vertical-align: sub;
}

.lable_name {
  width: 74px;
  display: inline-block;
  margin-right: 18px;
  color: #333333;
  font-size: 14px;
  font-weight: 400;
  height: 36px;
  line-height: 36px;
}

.div_col_left .div_col_right {
  height: 36px;
  line-height: 36px;
}

.lable_name_no {
  width: 60px;
  display: inline-block;
  margin-right: 18px;
  margin-left: 14px;
  color: #333333;
  font-size: 14px;
  font-weight: 400;
  height: 36px;
  line-height: 36px;
}

.cur_pointer {
  cursor: pointer;
}

.lable_input {
  width: 200px;
  height: 36px;
  background: rgba(255,255,255,1);
  border-radius: 2px !important;
  border: 1px solid #DDD;
  padding: 0 11px;
}

.pad0_11 {
  padding: 0 11px;
}

.lable_input option:nth-child(1) {
  display: none;
}

.lable_textarea {
  resize: none;
  width: 534px;
  height: 120px;
  background: #FFFFFF;
  border-radius: 2px !important;
  border: 1px solid #DDDDDD;
  padding: 12px 11px;
}

.lable_textarea_text {
  text-align: right;
  margin: 4px 0 0 0;
  color: #999999
}

.div_col_right_textarea {
  margin-left: 4px;
}

.ui-datepicker select.ui-datepicker-month, .ui-datepicker select.ui-datepicker-year {
  color: #000
}

.alert_but {
  width: 70px !important;
  height: 30px !important;
  line-height: 30px !important;
}

.lable_but {
  width: 90px;
  height: 34px;
  line-height: 34px;
  border-radius: 2px !important;
  text-align: center;
  float: right;
  margin: 12px 0;
}

.lable_but_close {
  border: 1px solid #DDD;
  color: #333333;
  font-size: 14px;
}

.lable_but_add {
  border: 1px solid #DDD;
  color: #FFFFFF;
  font-size: 14px;
  background: #2A7FCC;
  margin-right: 25px;
  margin-left: 10px;
}

.mask .open_div_qiuzhiyixiang {
  width: 580px !important;
  /*height: 344px;*/
}

.label_input_2 {
  width: 365px;
  line-height: 36px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.lable_name_no_2 {
  height: 36px;
  line-height: 36px;
  vertical-align: top;
  margin-left: 0;
}

.pla999 {
  color: #777;
}

.lable_buxian {
  height: 36px;
  line-height: 36px;
  vertical-align: top;
  margin-left: 16px;
  margin-bottom: 0;
}

.lable_buxian_input {
  margin-left: 3px
}

.mask .open_div_gongzuojingli {
  width: 560px;
  /*height: 561px;*/
}

.zhi_style {
  margin: 0 18px;
  line-height: 36px;
}

/*简历详情修改增加 End*/

.modal_photo {
  display: block;
  text-align: center;
  position: relative;
}

  .modal_photo .modal_photo__btn {
    color: #fff;
    background-color: #337ab7;
    line-height: 1;
    width: 60px;
    height: 32px !important;
    z-index: 2;
    border: 0 none;
    text-align: center;
    text-indent: 0;
  }

  .modal_photo > .handle_input_file {
  }


/**员工关怀弹窗样式*/
.home_care_box {
  width: 710px;
  margin: 0 auto;
  /*display: block;*/
  background-color: transparent;
  position: absolute;
  z-index: 99997;
  left: 50%;
  margin-left: -348px;
  top: 75px;
  font-size: 14px;
  font-family: 'Microsoft YaHei', '黑体';
  padding-bottom: 36px;
}

.home_notice_mask_box {
  width: 100%;
  height: 100%;
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 99996;
  overflow: hidden;
  background: #000;
  filter: alpha(opacity=50);
  -moz-opacity: 0.5;
  -khtml-opacity: 0.5;
  opacity: 0.5;
}

.home_care_panel {
  position: relative;
}

.care_hn_page_btn {
  position: absolute;
  width: 18px;
  height: 38px;
  bottom: 200px;
  margin-top: -40px;
  /*-moz-opacity: 0.5;
  -khtml-opacity: 0.5;
  opacity: 0.5;*/
  transition: opacity .3s ease-in;
}

.hn_prev {
  left: 37px;
}

.hn_next {
  right: 13px;
}

.care_hn_page_btn img {
  width: 18px;
  height: 38px;
}

.care_hnp_guang {
  width: 710px;
  height: 430px;
  background: url('/images/employeeCare/guang.png') no-repeat left top;
  background-size: 100%;
  z-index: 1;
}

.care_hnp_dangao,
.care_hnp_dangao img {
  width: 380px;
  height: 400px;
}

.care_hnp_dangao {
  margin-left: 176px;
  position: absolute;
  z-index: 5;
  top: 95px;
}

.girl-card-body-wrap {
  width: 558px;
  height: 378px;
  background: url('/images/employeeCare/xinfeng.png') no-repeat left top;
  background-size: 100%;
  margin-left: 86px;
  z-index: 8;
  position: relative;
  margin-top: -94px;
}

.girl-card-head-wrap {
  position: relative;
  left: 45px;
  width: 90px;
  top: -25px;
}

.girl-card-head-tou {
  width: 90px;
  height: 90px;
  border-radius: 50% !important;
  border: 2px solid #FFADC2;
  background: #FFADC2;
}

.girl-card-tou-img {
  width: 86px;
  height: 86px;
  border-radius: 50% !important;
}

.girl-card-head-mao {
  position: absolute;
  left: -19px;
  top: -35px;
}

.girl-card-mao-img {
  width: 66px;
  height: 70px;
}

.girl-card-title-wrap {
  width: 100%;
  text-align: center;
  margin-top: -38px;
}

.girl-card-title-name {
  font-size: 18px;
  line-height: 18px;
  font-family: Source Han Sans CN;
  font-weight: 800 !important;
  color: #E22C57;
  margin: 13px auto 0 auto;
}

  .girl-card-title-name:first-child {
    margin-top: 0;
  }

.girl-card-content-wrap {
  text-align: center;
  margin-top: .195rem;
  padding: 0 59px 0 60px;
}

.girl-which-year-txt {
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #E22C57;
  line-height: 34px;
  text-align: left;
}

  .girl-which-year-txt:first-child {
    margin-top: 28px;
  }

.card-time-box {
  width: 100%;
  text-align: center;
  line-height: 16px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #E22C57;
  margin-top: 14px;
}

.care_hnp_dangao_box {
  position: relative;
}

.care_hnp_close {
  position: absolute;
  right: 80px;
  bottom: 132px;
}

  .care_hnp_close,
  .care_hnp_close img {
    width: 36px;
    height: 36px;
  }

.home_care_box .hn_pager {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  line-height: 36px;
  color: white;
  font-size: 24px;
  text-align: center;
}



/** 动效*/
.yah {
  display: block;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 9998;
}

.care_hnp_box {
  z-index: 9;
  opacity: 0;
}

.care_hnp_one {
  width: 46px;
  height: 40px;
  position: absolute;
  left: 173px;
  top: 14px;
  background: url('/images/employeeCare/huang.png') no-repeat center center;
  background-size: 100%;
  animation: oneMove 2s linear 1;
}

.care_hnp_two {
  width: 40px;
  height: 46px;
  position: absolute;
  right: 172px;
  top: 0;
  background: url('/images/employeeCare/lan.png') no-repeat center center;
  background-size: 100%;
  animation: twoMove 1.8s linear 1;
}

.care_hnp_three {
  width: 40px;
  height: 46px;
  position: absolute;
  left: 38px;
  top: 103px;
  background: url('/images/employeeCare/hong.png') no-repeat center center;
  background-size: 100%;
  animation: threeMove 2s linear 1;
}

.care_hnp_four {
  width: 34px;
  height: 26px;
  position: absolute;
  right: 77px;
  top: 106px;
  background: url('/images/employeeCare/hong_san.png') no-repeat center center;
  background-size: 100%;
  animation: fourMove 1.8s linear 1;
}

.care_hnp_five {
  width: 32px;
  height: 42px;
  position: absolute;
  left: 26px;
  top: 244px;
  background: url('/images/employeeCare/huang_san.png') no-repeat center center;
  background-size: 100%;
  animation: fiveMove 1.5s linear 1;
}

.care_hnp_six {
  width: 46px;
  height: 40px;
  position: absolute;
  right: 0px;
  top: 205px;
  background: url('/images/employeeCare/huang.png') no-repeat center center;
  background-size: 100%;
  animation: sixMove 1.5s linear 1;
}

@keyframes oneMove {
  from {
    top: 14px;
    opacity: 1;
  }

  to {
    top: calc(100% - 50px);
    opacity: 0;
  }
}

@keyframes twoMove {
  from {
    top: 0;
    opacity: 1;
  }

  to {
    top: calc(100% - 50px);
    opacity: 0;
  }
}

@keyframes threeMove {
  from {
    top: 103px;
    opacity: 1;
  }

  to {
    top: calc(100% - 50px);
    opacity: 0;
  }
}

@keyframes fourMove {
  from {
    top: 106px;
    opacity: 1;
  }

  to {
    top: calc(100% - 50px);
    opacity: 0;
  }
}

@keyframes fiveMove {
  from {
    top: 244px;
    opacity: 1;
  }

  to {
    top: calc(100% - 50px);
    opacity: 0;
  }
}

@keyframes sixMove {
  from {
    top: 205px;
    opacity: 1;
  }

  to {
    top: calc(100% - 50px);
    opacity: 0;
  }
}

.girl-card-kt-wrap {
  width: 135px;
  height: 105px;
  position: absolute;
  right: 14px;
  bottom: 16px;
}
.boy-card-kt-wrap {
  width: 112px;
  height: 156px;
  position: absolute;
  right: 6px;
  bottom: 16px;
}
.girl-card-kt-gif {
  width: 100%;
  height: 100%;
}

/**
  23.10.30换肤样式
*/
.huanfu-dropdown-link {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #6E6E6E;
  line-height: 20px;
}
.huanfu-img {
  background-image: url(../../img/sidebar-toggle-light.png);
}
.com-flex {
  display: flex;
}
.menu-icon-box {
  display: block !important;
  margin-right: 0 !important;
  float: left;
  margin-top: 30px;
  margin-left: 10px;
}
.huanfu-box {
  float: right;
  cursor: pointer;
}
.huanfu-dropdown-link {
  display: flex;
  align-items: center;
}
.huanfu-dropdown-link svg {
  margin-right: 4px;
}
.border-ra {
  border-radius: 8px!important;
}
.mr-bt25 {
  margin-bottom: 25px;
}
.border-top-ra {
  border-top-left-radius: 8px!important;
  border-top-right-radius: 8px!important;
}
.border-bottom-ra {
  border-bottom-left-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}

.huanfu-base-head-img {
  width: calc(100% - 374px);
  position: absolute;
  right: 0px;
  z-index: 99;
}
.page-sidebar-closed .huanfu-base-head-img {
  width: calc(100% - 262px);
}

.huanfu-base-head-chun {
  background: url('../images/index/chun_head.png') no-repeat left top;
  background-size: 100%;
  height: 44px;
  top: 0px;
}

.huanfu-base-head-xia {
  background: url('../images/index/xia_base_head.png') no-repeat left top;
  background-size: 100%;
  height: 32px;
  top: 2px;
}

.huanfu-base-head-qiu {
  background: url('../images/index/qiu_base_head.png') no-repeat left top;
  background-size: 100%;
  height: 41px;
  top: 2px;
}

.huanfu-base-head-dong {
  background: url('../images/index/dong_base_head.png') no-repeat left top;
  background-size: 100%;
  top: 5px;
  height: 29px;
}

.huanfu-boss-head-img {
  width: calc(100% - 192px);
  position: absolute;
  right: 5px;
  z-index: 99;
}
.page-sidebar-closed .huanfu-boss-head-img {
  width: calc(100% - 80px);
}

.huanfu-boss-head-chun {
  background: url('../images/index/chun_boos_head.png') no-repeat left top;
  background-size: 100%;
  height: 44px;
  top: 0px;
}

.huanfu-boss-head-xia {
  background: url('../images/index/xia_boos_head.png') no-repeat left top;
  background-size: 100%;
  height: 32px;
  top: 2px;
}

.huanfu-boss-head-qiu {
  width: calc(100% - 192px);
  background: url('../images/index/qiu_boos_head.png') no-repeat left top;
  background-size: 100%;
  height: 42px;
  top: 0px;
}

.huanfu-boss-head-dong {
  background: url('../images/index/dong_boos_head.png') no-repeat left top;
  background-size: 100%;
  top: 5px;
  height: 29px;
}

.huanfu-icon-chun {
  background: url('../images/index/chun_icon.png') no-repeat left top;
  position: absolute;
  background-size: 100%;
  width: 264px;
  height: 48px;
  top: 24px;
  right: 346px;
  z-index: 4;
}

.huanfu-icon-xia {
  background: url('../images/index/xia_icon.png') no-repeat left top;
  background-size: 100%;
  width: 93px;
  height: 64px;
  position: absolute;
  right: 44px;
  top: 25px;
  z-index: 4;
}

.huanfu-icon-qiu {
  background: url('../images/index/qiu_icon.png') no-repeat left top;
  position: absolute;
  background-size: 100%;
  width: 104px;
  height: 92px;
  top: 4px;
  right: 22px;
  z-index: 4;
}

.huanfu-icon-dong {
  background: url('../images/index/dong_icon.png') no-repeat left top;
  position: absolute;
  background-size: 100%;
  width: 122px;
  height: 115px;
  top: 20px;
  right: 88px;
  z-index: 4;
}
.table-box-new .portlet-body {
  height: 254px;
  overflow: hidden;
  position: relative;
}
.tbl-header {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9;
}
  .tbl-header thead {
    background: #fff;
  }
  .tbl-header tbody {
    opacity: 0;
  }

  .tbl-body {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }

.wait_loading_new {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 254px;
}
.index-paihang-table-new {
  display: flex;
  flex-wrap: wrap;
}
.index-paihang-box-new {
  display: flex;
}
.clientSource-icon {
  display: inline-block;
  width: 20px;
  height: 20px !important;
  float: none;
  margin: 0;
  vertical-align: middle;
}
.clientSource-p {
  background: url(../images/client/clientSource-p.png) no-repeat;
  background-size: cover;
}

.clientSource-other {
  background: url(../images/client/clientSource-other.png) no-repeat;
  background-size: cover;
}

.clientSource-g {
  background: url(../images/client/clientSource-g.png) no-repeat;
  background-size: cover;
}

.clientSource-k {
  background: url(../images/client/clientSource-k.png) no-repeat;
  background-size: cover;
}