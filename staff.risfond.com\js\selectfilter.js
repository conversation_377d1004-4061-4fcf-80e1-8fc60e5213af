/// <reference path="../Scripts/jquery-1.8.2.js" />
/// <reference path="/static/i18next-1.10.3/i18next-1.10.3.min.js" />
/// <reference path="/static/js/rs_common.js" />
/**
 * 功能：下拉框美化插件
 * 文档：https://www.jq22.com/jquery-info17583
 */
(function ($) {
	var SelectFilter = function () {
		var
		getSelectOptions = function (el) {
			var optionArray = [];
			var optionselected=[]
			$.each(el.find("option"), function (index,value) {
				optionArray.push({ value: $(this).val(), text: $(this).html(),index:index });
				if (value.outerHTML.indexOf("selected") > -1 || this.selected) {
					optionselected = { value: $(this).val(),text: $(this).html(),index:index };
				}
			})
			return { option: optionArray, selected: optionselected };
		},
		getSelectUl = function (data) {
			var html = '<ul class="selectfilter-ul" style="max-height:300px;overflow:auto;">';
			$.each(data.option, function (index, value) {
				var selected='';
				if (data.selected.value == this.value) {
					selected = 'selected';
				}
				html += '<li data-value="' + this.value + '" class="selectfilter-li ' + selected + '">' + this.text + '</li>';
			})
			html += '</ul>';
			return html;
		}
		
	
		GetSelectFilter = function (element, self) {
			el = $(element);
			self.box =el.parent();
			var dataoption = getSelectOptions(el);
			self.currEl = $("<div>",
							{ "class": el.attr("class") })
							.addClass(self.currentCss)
							.append(
							'<span data-value="' +
							dataoption.selected.value
							+ '" class="selectfilter-current">' +
							dataoption.selected.text
							+ '</span>');
			self.containerEl = $("<div> ",
								{ "class": self.containerCss })
								.width(el.width()+10)
								.append(getSelectUl(dataoption));

			el.after(self.containerEl).hide();
			if (self.isShowCurrent) { el.after(self.currEl); }
			self.box.find("." + self.containerCss).find(".selectfilter-li")
			.hover(function () {
				$(this).addClass("itemhover");
			}, function () {
				$(this).removeClass("itemhover");
			})
			.click(function () {
				$(this).addClass("selected").siblings().removeClass("selected");
				self.box.removeClass("hover");
				var value=$(this).attr("data-value");
				el.val(value);
				self.callback.call(this, value);
			});

			switch (self.showdialogevent) {
				case "hover":
					$(self.box).hover(function () {
						$(this).addClass("hover");
						$(this).find("." + self.containerCss).css("top", $(self.box).find(self.showdialogEl).outerHeight());
					}, function () {
						$(this).removeClass("hover");
					});
					break;
				default:
					//showDialogFocus(self);
					{
						var box = self.box;
						var zindex = $(box).css("z-index")? parseInt($(box).css("z-index"), 10): 99;
						//$(self.box).toggle(function (e) {
						//	e = e || event;
						//	//e.stopPropagation();
						//	$(this).addClass("hover");
						//	$(this).find("." + self.containerCss).css("top", $(self.box).find(self.showdialogEl).height());
						//	//return false;
						//}, function (e) {
						//	e = e || event;
						//	//e.stopPropagation();
						//	$(this).removeClass("hover");
						//	//return false;
					  //});
						$(box).unbind();
						$(box).click(function (e) {
						  e = e || event;
							if ($(this).hasClass("hover")) {
								$(this).removeClass("hover");
								$(box).css("z-index", zindex);
							}
							else {
								$(this).addClass("hover");
								$(this).find("." + self.containerCss).css("top", $(box).find(self.showdialogEl).outerHeight());
								$(box).css("z-index", zindex + 1);
							}
						});
						$(document).click(function (e) {
							e = e || event;
							if (e.target != box.get(0) && e.target != box.find(self.showdialogEl).get(0)) {
								$(box).removeClass("hover");
								$(box).css("z-index", zindex);
							}
						});
					}
					break;
			}
		}
		;
		return {
			Init: function (params) {
				var opts =
				{
					containerCss: "selectfilter-list",
					currentCss: "",
					isShowCurrent: false,
					showdialogEl: ".selectfilter-tip",
					showdialogevent:"hover",//or click
					callback: function () { }
				}
				var self = this.params = $.extend({}, opts, params || {});
				$.each(this, function () {
					GetSelectFilter(this,self);
				});
			},
			GetSelectItem: function () {
				return $(this).parent().find(".selectfilter-ul .selected").attr("data-value") || 0;
			}
		}
	}(jQuery);
	var SelectVerify = function () {
		//首先取到select数据，然后加载成一个html
		var select = {
			//获取select的数据
			//返回一个select的option数据的数组
			getSelectOptions: function (el) {
				var optionArray = [];
				var optionselected = []
				var $this = null;
				if (typeof el == "string") {
					$this = $(el);
				}
				else if (typeof el == "object") {
					$this = el;
				}
				else {
					return null;
				}
				$.each($this.find("option"), function (index, value) {
					optionArray.push({ value: $(this).val(), text: $(this).html(), index: index, selected: value.outerHTML.indexOf("selected") > -1 || this.selected });
				})
				return optionArray;
			},
			//获取html数据
			//返回string格式的html
			getSelectHTML: function (el, PrimaryKey) {
				var data = this.getSelectOptions(el);
				var list = '';
				for (var i = 0; i < data.length; i++) {
					if (data[i].selected) {
						list += '<li data-status="' + data[i].value + '" class="select-verify-item current">' + data[i].text + '</li>';
					} else {
						list += '<li data-status="' + data[i].value + '" class="select-verify-item">' + data[i].text + '</li>';
					}
				}
				var $html = $('<div class="select-verify-dialog">' +
							'<ul class="select-verify-list">' + list + '</ul>' +
							'<div class="select-verify-btn-list-box cf">' +
							'<ul>' +
							'<li>' +
							'<input type="button" value="' + setRnssLanguage('确定') + '" class="select-verify-confim"></li>' +
							'<li>' +
							'<input type="button" value="' + setRnssLanguage('取消') + '" class="select-verify-cancel"></li>' +
							'</ul>' +
							'</div>' +
							'</div>');
				return $html.addClass(PrimaryKey);
			},
			getRandom:function(){
				return parseInt(10000000 * Math.random());
			},
			defaultCancel: function () {
				$(this).closest(".select-verify-dialog").remove();
			},
			defaultPosition: function (btn,el) {
				return el.css({ "position": "absolute", "top": btn.position().top + btn.height()-2, "left": btn.position().left });
			},
			getSelectedItem: function (box) {
				var el=$(box).find(".select-verify-list .select-verify-item.current")
				return { el: el, value: el.attr("data-status"),text:el.text() }
			}
		};

		return {
			selectInit: function (params) {
				this.templatebox = "template_" + select.getRandom();
				this.primarykey = this.templatebox + '-listBox-selectverify';
				this.box = '.' + this.primarykey;//弹出块box
				var opts =
				{
					isAllowRepetition:false,//是否允许一个页面显示多个
					divEl: ".select-verify-btn-box",
					cancelcallback: function () {},
					verifycallback:function(){}
				}
				var self = this.params = $.extend({}, opts, params || {});
				var $this = this;
				var html = select.getSelectHTML(this, this.primarykey);
				if (!self.isAllowRepetition) { $(".select-verify-dialog").remove(); }
				$(this).after(select.defaultPosition($(this).prev(this.divEl), html));//加载出新元素
				$(this.box).find(".select-verify-cancel").click(function () { self.cancelcallback.call(this); select.defaultCancel.call(this); });//绑定取消事件
				$(this.box).find(".select-verify-confim").click(function () { self.verifycallback.call(this, select.getSelectedItem($this.box)); });//绑定确定事件
				$(this.box).find(".select-verify-item").hover(function () { $(this).addClass("itemhover"); }, function () { $(this).removeClass("itemhover") });
				$(this.box).find(".select-verify-item").click(function () { $(this).addClass("current").siblings().removeClass("current"); });

			}
		};
	}(jQuery)
	$.fn.extend({
		SelectFilter: SelectFilter.Init,
		SelectFilterGetValue: SelectFilter.GetSelectItem,
		SelectVerify: SelectVerify.selectInit
	});
})(jQuery);